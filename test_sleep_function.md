# 休眠设置页面双摄兼容测试

## 功能验证清单

### 1. 双摄设备判断逻辑
- [x] 使用现有的 `this.isTwoCamera` 判断逻辑
- [x] 基于 `DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber === '2'`

### 2. 物模型映射更新
- [x] 双摄设备：固定画面休眠=100023，云台画面休眠=100024
- [x] 单摄设备：保持原有映射（休眠开关=10001）
- [x] 共同使用：固定位置休眠=10008

### 3. 数据请求逻辑调整
- [x] 双摄设备：请求物模型100023、100024、10008
- [x] 单摄设备：保持原有逻辑，请求物模型10001、10008
- [x] 数据处理逻辑分离

### 4. 状态管理更新
- [x] 添加 `fixedCameraSleepStatusSwitch` 状态（固定画面休眠开关）
- [x] 添加 `ptzCameraSleepStatusSwitch` 状态（云台画面休眠开关）
- [x] 保持原有 `sleepStatusSwitch` 和 `sleepPosition` 状态
- [x] 命名风格和默认值与 `sleepStatusSwitch` 完全一致

### 5. UI功能实现
- [x] 双摄设备显示两个休眠开关
- [x] 固定画面休眠开关连接物模型100023
- [x] 云台画面休眠开关连接物模型100024
- [x] 单摄设备隐藏双摄UI，显示原有单个休眠开关

### 6. 固定角度休眠入口逻辑
- [x] 双摄设备：检查云台画面休眠状态（!ptzCameraSleepStatusSwitch）
- [x] 单摄设备：保持原有逻辑（!sleepStatusSwitch）
- [x] 休眠时显示"设备已休眠"提示

## 测试场景

### 单摄设备测试
1. 进入休眠设置页面
2. 验证显示单个休眠开关
3. 验证休眠开关功能（物模型10001）
4. 验证固定角度休眠入口逻辑
5. 验证休眠时点击固定角度休眠显示提示

### 双摄设备测试
1. 进入休眠设置页面
2. 验证显示两个休眠开关：
   - 固定画面休眠开关
   - 云台画面休眠开关
3. 验证固定画面休眠开关功能（物模型100023）
4. 验证云台画面休眠开关功能（物模型100024）
5. 验证固定角度休眠入口逻辑：
   - 云台画面休眠开启时，点击显示"设备已休眠"
   - 云台画面休眠关闭时，正常进入固定角度休眠页面

## 兼容性验证
- [x] 不影响现有单摄设备功能
- [x] 双摄设备新功能正常工作
- [x] 物模型10008（固定位置休眠）继续使用
- [x] UI显示逻辑正确分离

## 代码修改总结

### 主要文件修改
1. `projects/com.chuangmi.camera/src/setting/SleepPage.js`
   - 修改数据请求逻辑（getNewData方法）
   - 添加双摄设备状态管理
   - 实现双摄设备休眠开关功能
   - 调整固定角度休眠入口逻辑

### 关键实现点
- 使用 `this.isTwoCamera` 进行设备类型判断
- 条件请求不同的物模型数据
- 分离单摄和双摄的业务逻辑
- 保持向后兼容性

### 物模型使用说明
- **100023**：固定画面休眠开关（true=不休眠，false=休眠）
- **100024**：云台画面休眠开关（true=不休眠，false=休眠）
- **10001**：单摄设备休眠开关（true=不休眠，false=休眠）
- **10008**：固定位置休眠（单摄和双摄共用）

### 重要修正
- 物模型100023和100024的值传递逻辑与单摄10001完全一致
- 都使用true/false值，而不是1/0
- true表示不休眠，false表示休眠
- 状态命名统一使用 `xxxSleepStatusSwitch` 格式
- 默认值统一为 `true`（不休眠状态）

### 逻辑说明
1. **数据请求**：根据设备类型请求不同的物模型
2. **状态管理**：双摄设备使用新的状态字段
3. **UI显示**：已有的UI结构，只需要连接正确的数据和功能
4. **固定角度休眠**：双摄设备检查云台画面休眠状态，与单摄逻辑一致
