{"_from": "react-native-video@^4.0.0", "_id": "react-native-video@4.4.5", "_inBundle": false, "_integrity": "sha512-jm1Yf1S3ZqrsbUo+3v9nUaQz/eD4kytzkm8jfDEvKzg/EEzci1EzjLSZqQcNtyLg1fvfkyRbvvzUYhS2SjdKPA==", "_location": "/react-native-video", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "react-native-video@^4.0.0", "name": "react-native-video", "escapedName": "react-native-video", "rawSpec": "^4.0.0", "saveSpec": null, "fetchSpec": "^4.0.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/react-native-video/-/react-native-video-4.4.5.tgz", "_shasum": "d0db799293f8622c7894dd0f8875cb41bfafbde8", "_spec": "react-native-video@^4.0.0", "_where": "D:\\shd_work\\chuangmiCode_android\\branch\\imihome_rn", "author": {"name": "Brent <PERSON>", "email": "<EMAIL>", "url": "https://github.com/brentvatne"}, "bugs": {"url": "https://github.com/react-native-community/react-native-video/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "isaiah<PERSON><PERSON>@gmail.com"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"keymirror": "^0.1.1", "prop-types": "^15.5.10", "shaka-player": "^2.4.4"}, "deprecated": false, "description": "A <Video /> element for react-native", "devDependencies": {"babel-eslint": "5.0.0-beta8", "eslint": "1.10.3", "eslint-config-airbnb": "4.0.0", "eslint-plugin-react": "3.16.1", "react": "^16.7.0", "react-dom": "^16.7.0", "react-hot-loader": "^4.6.3", "react-native": "^0.57.8"}, "files": ["android-exoplayer", "android", "dom", "ios", "windows", "FilterType.js", "TextTrackType.js", "VideoResizeMode.js", "react-native-video.podspec"], "homepage": "https://github.com/react-native-community/react-native-video#readme", "license": "MIT", "main": "Video.js", "name": "react-native-video", "repository": {"type": "git", "url": "git+ssh://**************/react-native-community/react-native-video.git"}, "rnpm": {"android": {"sourceDir": "./android-exoplayer"}}, "scripts": {"test": "eslint *.js"}, "version": "4.4.5"}