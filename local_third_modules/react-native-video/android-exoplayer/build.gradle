apply plugin: 'com.android.library'

def safeExtGet(prop, fallback) {
    rootProject.ext.has(prop) ? rootProject.ext.get(prop) : fallback
}

android {
    compileSdkVersion safeExtGet('compileSdkVersion', 28)
    buildToolsVersion safeExtGet('buildToolsVersion', '28.0.3')
 
    compileOptions {
        targetCompatibility JavaVersion.VERSION_1_8
        sourceCompatibility JavaVersion.VERSION_1_8
    }

    defaultConfig {
        minSdkVersion safeExtGet('minSdkVersion', 16)
        targetSdkVersion safeExtGet('targetSdkVersion', 28)
        versionCode 1
        versionName "1.0"
    }
}

dependencies {
    implementation "com.facebook.react:react-native:${safeExtGet('reactNativeVersion', '+')}"
    implementation('com.google.android.exoplayer:exoplayer:2.9.3') {
        exclude group: 'com.android.support'
    }

    // All support libs must use the same version
    implementation "com.android.support:support-annotations:${safeExtGet('supportLibVersion', '28.0.0')}"
    implementation "com.android.support:support-compat:${safeExtGet('supportLibVersion', '28.0.0')}"
    implementation "com.android.support:support-media-compat:${safeExtGet('supportLibVersion', '28.0.0')}"

    //版本号修改防止和本地阿里库冲突
    implementation('com.google.android.exoplayer:extension-okhttp:2.10.5') {
        exclude group: 'com.squareup.okhttp3', module: 'okhttp'
    }
    implementation 'com.squareup.okhttp3:okhttp:3.12.1'

}
