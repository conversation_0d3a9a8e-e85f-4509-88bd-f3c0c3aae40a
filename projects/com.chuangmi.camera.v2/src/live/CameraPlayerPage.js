import React, {Component} from 'react';
import {
    Image,
    View,
    Dimensions,
    TouchableWithoutFeedback,
    ImageBackground,
    TouchableOpacity,
    Text,
    StatusBar,
    StyleSheet,
    DeviceEventEmitter,
    ScrollView,
    Animated, TouchableHighlight
} from 'react-native';

const {width,height} = Dimensions.get('window');
let SCREEN_WIDTH = height>width?width:height;
let SCREEN_HEIGHT = height;

let IphoneXSeries= isIphoneXSeries()?14:0;//ios底部按钮黑线遮挡
let cur_direction = -1;//当前云台转动方位
let ptz_pressed = false; //当前是否在操作云台
let ptz_pressedPanoramic = false; //当前是否在操作全景云台

import {colors, RNLine, showLoading, showToast,RoundedButtonView} from '../../../../imilab-design-ui';
import I18n, {locales, stringsTo} from "../../../../globalization/Localize";
import Orientation from 'react-native-orientation';

import HomePageNewLivePlayerComponent
    from "../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageNewLivePlayerComponent";
import ImageButton from "../../../com.chuangmi.door/src/CommonView/ImageButton/ImageButton"
import WaveView from '../../../../imilab-design-ui/src/widgets/WaveView'
import {
    LetDevice,
    IMIGotoPage,
    BaseDeviceComponent,
    imiAlarmEventCloudApi, IMIPackage, AlarmType, LetIMIIotRequest, aliAlarmEventCloudApi,
} from '../../../../imilab-rn-sdk';
import JoystickControlView from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/ui/JoystickControlView/JoystickControlView";
import Toast from "react-native-root-toast";
import PanoramicView from "./PanoramicView";
import AlarmListPage from "../alarmList/AlarmListPage";
import { XImage,XText,XView} from 'react-native-easy-app';
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {isAndroid, isIos, isIphoneXSeries} from "../../../../imilab-rn-sdk/utils/Utils";
import {GotoPageInNative, IMIStorage, LetIProperties} from "../../../../imilab-rn-sdk";
import NetInfo from "@react-native-community/netinfo";
import {IMINativeLifeCycleEvent} from "../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import moment from "moment";
import GiftDialog from "../../../../imilab-design-ui/src/widgets/settingUI/GiftDialog";
import IMILog from "../../../../imilab-rn-sdk/native/local-kit/IMILog";
import AlertDialog from "../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";
import {configCameraProjectDefault} from "../../../../config/camera/configProject";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import IMIHost from "../../../../imilab-rn-sdk/native/local-kit/IMIHost";
import IMICameraVideoView from "../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import NewGiftDialog from "../../../../imilab-design-ui/src/widgets/settingUI/NewGiftDialog";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import LinearGradient from "react-native-linear-gradient";
import AngleDataManager from "../setting/angel/AngelDataManager"
import IMIFile from "../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import IMIIotRequest from "../../../../imilab-rn-sdk/native/iot-kit/IMIIotRequest";
import IMIDownload from "../../../../imilab-rn-sdk/native/local-kit/IMIDownload";
import MessageDialog from "../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog";
import RNFS from "react-native-fs";
import GlobalUtil from "../utils/GlobalUtil";
import {fastForWait, isWait} from "../utils/FastClickUtils";
import DowaloadType from "../utils/DownloadType";
import NeedFormatType from "../utils/NeedFormatType";
import DateHasManager from "../utils/DateHasManager";
import VerticalSlider from "../../../../imilab-design-ui/src/widgets/settingUI/VerticalSlider";

const EventName = "EventName-PanoramaCompoundFinishE";
const lensCorrect_x = 880.0 / 2560.0;
const lensCorrect_y = 64.0 / 1440.0;
let audioNeedRecovery = true; //通话结束是否需要恢复监听状态
let isCheckingPermission = false;
//Android 全屏请求权限，切竖屏，再切横屏 包括权限请求成功、拒绝等状态都应该处于true
let checkPermissionForFullScreen = false;
// 导航栏相关
let StatusBarHeight = 20;
const iconSize = 50; // 图标尺寸
const iconButtonSize = 50;
let change_snapArea_quality = false;//重点区域切换分辨率
const AREA_PATH_PREFIX = `${IMIFile.storageBasePath}/area/`;

const EVENT_NAME = "IMIDownloadImgPathScheduler --- ";
let bgColor = "transparent";
let CLOUD_BUG="link://app/pages/toCloudBuyActivity";

export default class CameraPlayerPage extends BaseDeviceComponent {

    constructor(props, context) {
        super(props, context);

        GlobalUtil.isCalling = false;

        this.state = {
            currentStatus:HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED,
            isFullScreen: false,
            isCalling: false,//是否正在通话
            isCallingStatue:false,//点击通话后延迟2秒
            isSleep: false,//设备是否休眠
            spotlightSwitch: false, //聚光灯开关
            spotlightSliderVisible: false, //控制聚光灯亮度滑动条的隐现
            spotlightBrightness: 0,
            isPanoramic:false,
            sdCardStatus:1,//默认为sd正常,预防断网
            isDataUsage:false,
            vipState:-1,
            storageType:0,
            panoramicType:3,
            tempPanoramicType:3,
            overAllImgStoreUrl:null,
            leftValue:1,
            rightValue:90,
            lensCorrect:false,
            isMoveStatus:false,//全屏长按触摸判断
            step:0,
            isOnline:LetDevice.isOnline,
            stateType:undefined,
            netConnected:true,
            oSDSwitch:false,//时间水印是否打开
            showFreeCloud:false,//免费云存储选择
            giftDialog:false, //免费云存领取提示
            expiredDialog:false, //云存储即将过期的弹窗提醒
            cloudExpireRemainDays:null,
            cloudExpireRemainDaysLocal:null, //本地存储的云存到期天数
            giftName:"",
            currency:1,//人民币
            giftPrice:0,
            giftId:null,
            deviceName:"",
            SDCardAbnormalDialog:false,//SD卡异常弹出
            showTargetPushView:false,//点击暂不后，在屏幕中提示
            sdMessage:'',//SD卡异常的信息提示
            needFormatValue:NeedFormatType.NORMAL,
            isShowAngle:false,//显示常用角度
            areaArrData:[],//常用角度数据
            areaSelectIndex:-1,//重点区域激活位置
            addAreaIndex: -1, // 添加重点区域index
            recording:false,//录像状态
            isWarning:false,//报警状态
            showWarningDialog:false,//报警状态
            dynamicHeight:new Animated.Value(0),
            qualityDataIndex:2,// 060项目默认高分辨率
            playerZoomScale:1.0,//
            showDeleteDialog:false,
            deleteIndex:-1,
            showMsgDialog:false,//显示激活/失效弹窗
            msgTitle:'',
            showNormalNav:false,//显示正常导航栏


        }
        this.isForegroundPage = true;
        this.isFcous=true;
        this.isForm=this.props.route.params ? this.props.route.params.isForm?true:false:false;
        this.isNewFullScreen = false;
        this.videoPortraitScale = 1;
        this.isShowSecLandToolBar = false;//显示第二行工具栏
        this.downloadArr = [];//需要去下载图片的常用角度
        this.shouldRefreshAngleView = false;
        this.isStartFormat = false;//标记是否处于格式化中
        this.iosRetryCount = 0;
        this.getStatusBarHeight();

        this.targetpushItem = null;
    }

    // 动画设置高度
    startViewAnimation(isShow) {
        this.isShowSecLandToolBar = isShow;
        this.setState({});
        let targetHeight = isShow ? 55:0;
        Animated.timing(
            this.state.dynamicHeight,{
                toValue:targetHeight, //目标值，动画变化后的最终值
                duration:500,  // 动画持续时长
                useNativeDriver: false
            }
        ).start()
    }

    getStatusBarHeight(){
        if (isIos()) {
            StatusBarHeight = isIphoneXSeries()?47:20;
        } else {
            StatusBarHeight = StatusBar.currentHeight;
        }
    }

    componentDidMount() {
        // this.IMIVideoView.prepare();
        this._subscribe_focus = this.props.navigation.addListener('focus', () => {
            this.isFcous=true;
            this.isForegroundPage = true;
            StatusBar.setBackgroundColor("transparent");
            if (this.state.playerZoomScale <= 1.03 || this.state.isSleep) {
                // StatusBar.setBackgroundColor("#FFFFFF");
                StatusBar.setBarStyle('dark-content');
            }else {
                // StatusBar.setBackgroundColor("transparent");
                StatusBar.setBarStyle('light-content');
            }
            if (this.shouldRefreshAngleView){
                //需要刷新下常用角度数据
                this.shouldRefreshAngleView = false;
                let tempData = AngleDataManager.getInstance().getAngleData();
                if (tempData){
                    this.setState({areaArrData: tempData});
                    //激活位置
                    this.getActiveIndex();
                }
            }
            this.homePageNewLivePlayerComponent.setIsForegroundPage(this.isForegroundPage);//设置是否在前台
            if (this.state.isOnline){
                this.getNetWork();
            }
            this.getSameData();
            // this.getSDCardStatus();
        });
        //监听是否有常用角度数据更新了，主要用于首页点击更多设置，更改常用角度后，返回首页，首页没有及时刷新问题
        this.angleChangeListener = DeviceEventEmitter.addListener('angleHasChange', (dic) => {
            //刷新数据
            this.shouldRefreshAngleView = true;
        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            this.isFcous=false;
            this.isForm=false;
            // this.IMIVideoView && this.state.currentStatus===HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING && this.IMIVideoView.stop();
            this.isForegroundPage = false;
            this.homePageNewLivePlayerComponent.setIsForegroundPage(this.isForegroundPage);//设置是否在前台

            this.iosRetryCount = 0;
            this.IMIVideoView && this.IMIVideoView.stop();
            this.intervalQuerySD && clearInterval(this.intervalQuerySD);
        });

        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            if (isAndroid() && checkPermissionForFullScreen){
                //进入后台不做任何处理
                return;
            }
            this.isForegroundPage = false;
            this.isForm=false;
            this.homePageNewLivePlayerComponent.setIsForegroundPage(this.isForegroundPage);//设置是否在前台
            //获取录屏状态,锁屏缩略图黑屏问题
            this._dealWithErrorOrPause();
            this.isNewFullScreen = this.homePageNewLivePlayerComponent.getFullscreen();
            this.intervalQuerySD && clearInterval(this.intervalQuerySD);
        });

        this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(()=>{
            if (isAndroid() && checkPermissionForFullScreen){
                //进入前台，重置这个状态
                checkPermissionForFullScreen = false;
                return;
            }
            this.isForm=false;
            if (!this.isFcous){
                return
            }
            // if (!this.isForegroundPage)return;
            this.isForegroundPage = true;
            this.homePageNewLivePlayerComponent.setIsForegroundPage(this.isForegroundPage);//设置是否在前台
            if (this.isNewFullScreen){
                // 设置全屏
                setTimeout(()=>{
                    this.homePageNewLivePlayerComponent.enterFullScreen();
                },100);
            }else {
                // 设置竖屏
                setTimeout(()=>{
                    this.homePageNewLivePlayerComponent.quitFullScreen();
                },100);
            }
            this.getSameData();
            if (this.state.isOnline){
                this.getNetWork();
            }
            //如果显示云存过期提醒，则回到此页面刷新一下，防止购买云存回来没有移除提示页面
            if(this.state.cloudExpireRemainDays!=null&&this.state.cloudExpireRemainDays<0){
                this._getCloudExpiredRemainDays();
            }
        });
        this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(()=>{
            this.isForegroundPage = false;
            this.isForm=false;
            this.homePageNewLivePlayerComponent.setIsForegroundPage(this.isForegroundPage);//设置是否在前台
             this._dealWithErrorOrPause();
        });

        this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(()=>{
            //20220323@byh 如果无焦点，后面的resume不在执行，解决开启声音，去到非rn页面返回，不在首页能听到首页声音
            this.isForm=false;
            if (!this.isFcous){
                return;
            }

            // if (isIos()){
            //     //ios相册返回颜色状态栏出现错误
            //     StatusBar.setBarStyle("light-content")
            // }
            this.isForegroundPage = true;
            this.homePageNewLivePlayerComponent.setIsForegroundPage(this.isForegroundPage);//设置是否在前台
            this.getSameData();
            if (this.state.isOnline){
                this.getNetWork();
            }
        });
        // Subscribe
        this.unsubscribe = NetInfo.addEventListener(state => {
            if (this.state.stateType !== state.type){
                this.setState({stateType:state.type,netConnected:state.isConnected});
                this.getNetWork(state.isConnected);
            }
            //监听防止通话未挂断
            if (state.isConnected==false){
                this._doCallClose(true)
            }

            console.log("Is connected?", state.isConnected);
        });

        // 获取云存储相关信息npm
        this.getCloudInfo();

        this.getPanoramaProperty();

        LetDevice.registerDeviceEventChangeListener((data)=>{
            console.log('registerDeviceEventChangeListener-----',data);
            let {iotId,identifier,value} = JSON.parse(data);
            if (iotId == LetDevice.deviceID){
                if (identifier=='PanoramaCompoundFinishE'){
                    let {isSuccess} = value;
                    this.intervalID&&clearInterval(this.intervalID);//清除
                    DeviceEventEmitter.emit('PanoramaCompoundFinishE', isSuccess);
                    if (parseInt(isSuccess)){
                        this.getPanoramaProperty();
                        showToast(stringsTo('panoramicSuccess'));
                        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
                            showLoading(false);
                            this.setState({panoramicType:parseInt(res.status),tempPanoramicType:parseInt(res.status),overAllImgStoreUrl:res.url});
                        }).catch(error=>{
                            showLoading(false);
                        });
                    }else {
                        if (this.state.panoramicType!=3){
                            showToast(stringsTo('panoramicError'));
                        }
                        this.setState({panoramicType:3,tempPanoramicType:3});
                    }
                }
                // if (identifier == 'PTZUpdateCoordinates'){
                //     let {coordinates} = value;
                //     if (coordinates.substr(0,3)=='-1,'){
                //         showToast(stringsTo('direction_end_009'));
                //     }
                //     // alert(coordinates);
                //
                // }
            }
        });
        this.devicePropertyListener = LetIProperties.addPropertyChangeListener((event)=>{
            let data = typeof(event)=='object'?event:JSON.parse(event);
            //监听清晰度回调
            if (data.StreamVideoQuality!=undefined){
                if (!change_snapArea_quality){
                    this.setState({qualityDataIndex:parseInt(data.StreamVideoQuality)});
                    if (data.StreamVideoQuality=="1"){
                        this.homePageNewLivePlayerComponent.setQualityIndex(0);
                    }else  if (data.StreamVideoQuality=="2"){
                        this.homePageNewLivePlayerComponent.setQualityIndex(1);
                    }else  if (data.StreamVideoQuality=="3"){
                        this.homePageNewLivePlayerComponent.setQualityIndex(2);
                    }
                }
            }
            if (data.SleepStatus!=undefined){
                console.log('监听设备状态---',data.SleepStatus,data);
                if (data.SleepStatus == "0"|| data.SleepStatus==0){
                    this.setState({isSleep:true});
                    this.IMIVideoView&&this.IMIVideoView.stop();
                }else {
                    if (this.isForegroundPage) { //不在前台 不走
                        this.setState({isSleep:false});
                        this.IMIVideoView&&this.IMIVideoView.prepare();
                    }
                }
            }
            if (data.StorageStatus!=undefined){
                if (this.state.sdCardStatus != data.StorageStatus && data.StorageStatus == 1){
                    //说明SD卡的状态有变更，并且是从其他状态，转变成正常状态
                    //监听到SD卡状态发生变更，进入回看页面需要清空列表重新拉取数据
                    //这个状态就标记SD卡需要重新拉取数据，清空列表
                    GlobalUtil.needClearSDData = true;
                    //清除月份数据
                    DateHasManager.removeData();
                    //重新去请求一下月份数据
                    DateHasManager.fetchSDDateDataV2(null);
                }else if (this.state.sdCardStatus != data.StorageStatus
                    && (data.StorageStatus ==0 || data.StorageStatus == 5)){
                    //卡状态变更，变更后的状态为无卡、或者推出存储卡状态
                    //需要检测定向通知是否处于展示状态，是则需要隐藏
                    if (this.state.showTargetPushView){
                        this.setState({showTargetPushView:false});
                    }
                    if (this.state.isShowAbnormalSDDialog){
                        this.setState({isShowAbnormalSDDialog:false});
                    }
                }
                this.setState({sdCardStatus:data.StorageStatus});
            }

            if (data.iotId == LetDevice.deviceID){
                if (data.items){
                    if (data.items.SleepStatus){
                        if (data.items.SleepStatus.value == "0"|| data.items.SleepStatus.value==0){
                            this.setState({isSleep:true});
                            this.IMIVideoView&&this.IMIVideoView.stop();
                        }else {
                            if (this.isForegroundPage){ //不在前台 不走
                                this.setState({isSleep:false});
                                this.IMIVideoView&&this.IMIVideoView.prepare();
                            }

                        }
                    }
                }
            }
            /**
             * s首页无法监听滑动到底
             * this.isForegroundPage防止在后台时，监听到改变，导致出现提示
             */
            if (data.MotorPositon!=undefined&&data.MotorPositon!="undefined"&&this.isForegroundPage){
                console.log("data.MotorPositon home",data.MotorPositon,this.isForegroundPage)
                let arrData = data.MotorPositon.split(",");
                if (arrData.length>2){
                    let isMoveError  = Number(arrData[0]);
                    if (isMoveError<0){
                        //不在前台不管是ios还是Android手机，都不要提示转不动了
                        if (this.isForegroundPage){
                            let toast = Toast.show(stringsTo('direction_end_009'),{
                                duration: Toast.durations.LONG, // toast显示时长
                                    position: Toast.positions.CENTER, // toast位置
                                    shadow: false, // toast是否出现阴影
                                    animation: true, // toast显示/隐藏的时候是否需要使用动画过渡
                                    hideOnPress: true, // 是否可以通过点击事件对toast进行隐藏
                                    delay: 0, // toast显示的延时
                                    onShow: () => {
                                    // toast出现回调（动画开始时）
                                },
                                    onShown: () => {
                                    // toast出现回调（动画结束时）
                                },
                                    onHide: () => {
                                    // toast隐藏回调（动画开始时）
                                },
                                    onHidden: () => {
                                    // toast隐藏回调（动画结束时）
                                }})
                            // showToast(stringsTo('direction_end_009'));
                        }
                    }
                }
            }

            //监听激活位置信息
            if (data.FavAreaActive != undefined){
                console.log("当前激活位置：",data.FavAreaActive)
                if (this.isForegroundPage){
                    console.log("当前激活位置：在前台");
                    //在前台的时候执行，防止不在前台弹出，看护区域失效提示
                    if (data.FavAreaActive == 0){
                        //区域失效
                        if (this.state.areaSelectIndex != -1){
                            this.setState({areaSelectIndex:-1,showMsgDialog:true,msgTitle:stringsTo("active_fail_tip")});
                        }
                    }else {
                        this.setState({areaSelectIndex:data.FavAreaActive-1});
                    }
                }
            }

            if (data.NeedFormatStorageMedia != undefined){
                // if (this.isForegroundPage){
                //     if (data.NeedFormatStorageMedia != NeedFormatType.NORMAL){
                //         //需要格式化的状态，需要去获取一下storageStatus的状态，是否处于格式化中，如果处于格式化中，不提示
                //         this.getStorageStatusInfo2(data.NeedFormatStorageMedia);
                //     }
                // }
                //状态变更，重新处理下
                this.getSDCardStatus(false);
            }
            //监听告警状态，暂时不监听这个状态了
            // if (data.OneKeyAttr){
            //     //只监听告警结束标记位
            //     if (data.OneKeyAttr.sound_alarm){
            //         let oneKey = typeof (data.OneKeyAttr.sound_alarm) === "string"?JSON.parse(data.OneKeyAttr.sound_alarm):data.OneKeyAttr.sound_alarm;
            //         if (oneKey.switch ==1 && !this.state.isWarning){
            //             //处于报警中
            //             this.setState({isWarning:true})
            //         }else {
            //             //未处于报警中
            //             this.setState({isWarning:false})
            //         }
            //     }
            // }
        });
        this.deviceInfoListener = LetDevice.addInfoChangeListener((info)=>{
            let data = typeof(info)=='object'?info:JSON.parse(info);
            console.log('info----------'+JSON.stringify(data));
            if (data.key == 'isOnline'){
                this.setState({isOnline:data.value});
                if (this.isForegroundPage){
                    //只有在前台的时候，我们才去操作播放器，
                    //后台时即使离线，我们也不能去初始化播放器
                    if (data.value == false){
                        this.IMIVideoView&&this.IMIVideoView.stop();
                    }else {
                        this.IMIVideoView&&this.IMIVideoView.prepare();
                    }
                }
            }
            if (data.iotId == LetDevice.deviceID) {
                // if (data.thingType == 'DEVICE'){
                //     if (data.status){
                //         this.setState({isOnline:data.status.value==1?true:false});
                //         if (data.status.value == false){
                //             this.IMIVideoView&&this.IMIVideoView.stop();
                //         }else {
                //             this.IMIVideoView&&this.IMIVideoView.prepare();
                //         }
                //     }
                // }
            }

        });
        /**
         * 监听是否再云台校准中
         */
        this.isCalibration = DeviceEventEmitter.addListener( 'isCalibration', ( isCalibration) =>{
            //  showToast(stringsTo('is_the_calibration'));
            let calibrationTimes=25000;
            if (IMIHost.serverCode==0){
                calibrationTimes=32000;
            }

            this.calibrationTimer&&clearTimeout(this.calibrationTimer);
            this.calibrationTimer = setTimeout(()=>{
                showLoading(stringsTo('is_the_calibration'),false);
                showToast(I18n.t('calibration_completed'))
            },calibrationTimes);
        });
        /**
         * 每次进入页面弹出
         */
        this.getSDCardStatus(true);
        GotoPageInNative.addStarNativeGoToPage(this.props.navigation);

        //showToast('version  '+configCameraProjectDefault.versionCode);
    }

    getPanoramaProperty(){
        LetDevice.updateAllPropertyCloud().then((data)=>{
            let dataObject = JSON.parse(data);
            // let dataObject = data;
            let stateProps = {};
            //侦测时间
            if (dataObject.PanoramStartMotorPositon) {
                let ary = dataObject.PanoramStartMotorPositon.value.split(',');
                stateProps.leftValue = parseInt(ary[0].substr(1));
            }
            if (dataObject.PanoramEndMotorPositon) {
                let ary = dataObject.PanoramEndMotorPositon.value.split(',');
                stateProps.rightValue = parseInt(ary[0].substr(1));
            }
            //时间水印
            if (dataObject.OSDSwitch){
                console.log("王 oSDSwitch:",dataObject.OSDSwitch.value)
                stateProps.oSDSwitch = dataObject.OSDSwitch.value;
            }

            if(dataObject.oneKeyLighting){
                stateProps.spotlightSwitch = dataObject.oneKeyLighting.value.switch == 1;
                stateProps.spotlightBrightness = dataObject.oneKeyLighting.value.brightness;
                stateProps.spotlightSliderVisible = stateProps.spotlightSwitch;

            }

            this.setState(stateProps);

            //获取看家总开关的值，看家总开关如何是关闭的情况下
            if (dataObject.AlarmSwitch){
                if (dataObject.AlarmSwitch.value != 1){
                    //看家开关未打开----开闭所有的消息推送
                    this.closeAllPushNotice();
                }
            }

        }).catch(error=>{

        });
    }

    closeAllPushNotice(){
        //不同的项目可能有不需要的推送通知
        let {showMoveSwitch,showPeopleSwitch,showLoudSwitch,showFencesSwitch,
            showArea,showNoHuman,showMotorVehicle,showNonMotorVehicle} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let params = {};

        //关闭看家
        if (showMoveSwitch){
            params['MOVE'] = false;
        }
        if (showPeopleSwitch){
            params['PEOPLE'] = false;
        }
        if (showLoudSwitch){
            params['SOUND'] = false;
        }
        if (showFencesSwitch){
            params['OVER_AREA'] = false;
        }
        if (showArea){
            params['KEY_AREA'] = false;
        }
        if (showNoHuman){
            params['NOBODY'] = false;
        }
        if (showMotorVehicle){
            params['VEHICLE'] = false;
        }
        if (showNonMotorVehicle) {
            params['BIKE'] = false;
        }

        //设置本项目事件推送
        imiAlarmEventCloudApi.setEventNoticePushByList(LetDevice.deviceID, LetDevice.model, params).then((res)=>{
            //成功就成功了，不做其他的处理了
            console.log("close push success");
        }).catch(error=>{
            //失败了怎么办
            console.log("close push fail");
            IMILogUtil.uploadClickEventForCount("OpenCloseHouseError")
        });
    }

    getSameData(){
        LetDevice.getPropertyCloud('SleepStatus').then((data) =>{ //0休眠 1关闭
            console.log('设备休眠--------SleepStatus' + data,typeof(data));
            if(data=="0"||data==0){
                this.setState({isSleep:true});
            }else {
                this.setState({isSleep:false});
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
        imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
            console.log("_getVipState"+res)
            let data = JSON.parse(res);
            let storageType=0;
            if (data.state==1){
                storageType=data.storageType;
            }
            this.setState({vipState:data.state,storageType:storageType});
        }).catch(error=>{
            console.log("_getVipState -1")
            this.setState({vipState:-1});
        });

        let {supportLensCorrect} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        if (!supportLensCorrect){
           //固件不支持畸变纠正，交由APP自己做
            IMIStorage.load({
                key: LetDevice.deviceID+'DistortionCorrection',
                autoSync: true,
                syncInBackground: true,
            }).then(res => {
                this.setState({lensCorrect: res.lensCorrect});
            }).catch(_=> {
                this.setState({lensCorrect: false});
            });
        }

        IMIStorage.load({
            key: LetDevice.deviceID+'cameraGuideForZoomed',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({step: res.cameraGuideForZoomed});
        }).catch(_=> {
            this.setState({step: 1});
        });
        //缩放比例
        IMIStorage.load({
            key: LetDevice.deviceID+'zoomScale',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({playerZoomScale:res.zoomScale});
        }).catch(_=> {
            this.setState({playerZoomScale:1})
        });

        //时间水印
        LetDevice.updateAllPropertyCloud().then((data)=>{
            let dataObject = JSON.parse(data);
            // let dataObject = data;
            let stateProps = {};
            if (dataObject.OSDSwitch){
                stateProps.oSDSwitch = dataObject.OSDSwitch.value;
            }
            if (dataObject.StorageStatus){
                stateProps.sdCardStatus = dataObject.StorageStatus.value;
            }
            //这个逻辑可能后面会用到，暂时不理想
            // if (dataObject.OneKeyAttr && dataObject.OneKeyAttr.value){
            //     console.log("all OneKeyAttr",dataObject.OneKeyAttr.value);
            //     let value = dataObject.OneKeyAttr.value;
            //     let onKeyData = typeof (value) === "string"?JSON.parse(value):value;
            //     let onKey = typeof (onKeyData.sound_alarm) === "string"?JSON.parse(onKeyData.sound_alarm):onKeyData.sound_alarm;
            //     stateProps.isWarning  = onKey.switch == 1;
            // }
            console.log("all stateProps",stateProps);
            this.setState(stateProps);
        }).catch(error=>{

        });
    }

    getNetWork(connected=true){
        if(!this.isForegroundPage){ //处于后台则不作处理，防止直播流在后台启动
            return;
        }
        if (this.isForm){
            return;
        }
        if (!connected){
            //已经断网了，再去加载也没用，直接显示暂停吧
            this.homePageNewLivePlayerComponent.setPauseView(true);
            return;
        }
        IMIStorage.load({
            key: LetDevice.deviceID+'isDataUsageWarning',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({isDataUsage: res.isDataUsage});
            if (res.isDataUsage){
                NetInfo.fetch().then(state => {
                    console.log("王 NetInfo.fetch",state.type)
                    if (state.type=='wifi'){
                        this.IMIVideoView && this.IMIVideoView.prepare();
                        this.homePageNewLivePlayerComponent.setLoadingView(true);
                    } else {
                        this.IMIVideoView && this.IMIVideoView.stop();
                        //如果设备已经休眠了，这个提示就不要提示出来
                        //由于休眠与网络的变化是不存在先后关系的，刚进入插件的时候，不能保证哪个先执行
                        //稍微加个延迟
                        this.networkProtectTimeout && clearTimeout(this.networkProtectTimeout);
                        this.networkProtectTimeout = setTimeout(()=>{
                            if (state.type == 'cellular' && !this.state.isSleep){
                                showToast(stringsTo('isDataUsageTip'));
                            }
                        },300);

                        this.homePageNewLivePlayerComponent.setPauseView(true);
                        this.setState({currentStatus: HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE});
                    }
                }).catch(error => {
                    console.log("getNetWork NetInfo error",error);
                });
            }else {
                this.IMIVideoView && this.IMIVideoView.prepare();
                this.homePageNewLivePlayerComponent.setLoadingView(true);
            }
        }).catch(_=> {
            this.setState({isDataUsage: false});
            if (connected){ //断网状态会再次加载
                this.homePageNewLivePlayerComponent.setLoadingView(true);
                this.IMIVideoView && this.IMIVideoView.prepare();
            }
        });
    }
    // 获取常用角度数据
    initAreaData(shouldFetch = false) {
        let tempData = AngleDataManager.getInstance().getAngleData();
        if (tempData){
            console.log('单例有数据',tempData);
            this.setState({areaArrData: tempData});
            if(!shouldFetch){
                this.getActiveIndex();
                return;
            }
        }
        console.log('单例无常用角度数据');
        if (shouldFetch){
            console.log('常用角度数据刷新');
            this.getAreaData();
            return;
        }
        let angleData = [];
        for (let i = 0; i < 3; i++) {
            angleData.push({
                index: i,
                monPosition:'[0,0]',
                areaSwitch: false,
                start_x: 0,
                start_y: 0,
                end_x: 0,
                end_y: 0,
                active_time: '[0,0,0,0,0,0,0]',
                isSelected: false,
                hasImg: false,
                hasData: false,
                fileName: '',
                filePath: '',
                isNameDefault: true,
                areaName: stringsTo("angelCommon") + (i + 1),
                startTime:"0000",
                endTime:"2400",
                repeatTime:'1,1,1,1,1,1,1', // 周一-周日
                repeatTimeTit: stringsTo("angel_no_set_time"),
                imageExist:false,//是否存在本地图片
                imgKey:'',//图片加密key值（新增）
                pushFlag:true,//默认打开
                active_time_switch:false,//默认关闭
            })
        }
        this.setState({areaArrData: angleData,}, () => {
            this.getAreaData();
        });
    }
     // 获取激活角度
    getActiveIndex(){
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.getPropertyCloud('FavAreaActive').then((data) => {
            console.log("激活角度位置=" + data);
            showLoading(false);
            this.setState({areaSelectIndex: data-1})
        }).catch(error => {
            console.log(JSON.stringify(error))
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    // 获取常用角度数据
    getAreaData() {
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.updateAllPropertyCloud().then((data) => {
            let dataObject = JSON.parse(data);
            console.log('获取常用角度数据',dataObject);
            // 激活角度
            if (dataObject.FavAreaActive){
                this.setState({areaSelectIndex:dataObject.FavAreaActive.value-1});
            }
            // 常用角度
            if(dataObject.FavAreaAttr){
                if (dataObject.FavAreaAttr.value.length>0) {
                    //有值
                    let favArr = dataObject.FavAreaAttr.value;
                    let angleData = this.favAreaArr(favArr);
                    //去处理重点区域
                    let afterKeyAreaAngleData = this.keyAreaArr(dataObject,angleData);
                    //最后处理下，图片，名称等数据
                    this.customContent(dataObject,afterKeyAreaAngleData);
                }else {
                    showLoading(false);
                    this.deleteAllImg();
                }
            }

        }).catch(error => {
            showLoading(false);
            console.log(JSON.stringify(error))
        });
    }
    /**
     * 常用角度数据处理
     * 返回处理后的常用角度
     */
    favAreaArr(favArr){
        //深拷贝出来常用角度的值
        let tempAllData = JSON.parse(JSON.stringify(this.state.areaArrData));
        for (let i = 0; i < favArr.length; i++) {
            let item = favArr[i];
            let startTime = 0;
            let endTime = 0;
            if (item.repeat_time){
                // 已编辑
                let timeStr = item.repeat_time;
                let timeArr = timeStr.split(',');
                startTime = timeArr[0];
                endTime = timeArr[1];
            }
            for (let j = 0;j<tempAllData.length;j++){
                let normalArea = tempAllData[j];
                if (item.idx == j+1){
                    // let tempTime = JSON.parse();
                    normalArea.active_time = item.active_time;
                    normalArea.startTime = startTime;
                    normalArea.endTime = endTime;
                    normalArea.repeatTime = item.repeat_week;
                    normalArea.active_time_switch = item.active_time_switch == 1;
                    normalArea.hasImg = true;
                    normalArea.monPosition = item.pos;
                    normalArea.repeatTimeTit = this.getTimeTit(startTime, endTime, item.repeat_week);
                    console.log('更新数据时间',normalArea.index,normalArea.active_time,item.active_time);
                    console.log('当前norm--',normalArea.index,normalArea,normalArea.startTime,normalArea.active_time,);
                }
            }
        }
        //返回添加后的角度值
        return tempAllData;
    }

    /**
     * 重点区域数据处理
     * @param dataObject 获取的物模型数据
     * @param angleData 添加常用角度后的数据
     */
    keyAreaArr(dataObject,angleData){
        // 重点区域
        if(dataObject.KeyAreaAttr){
            if (dataObject.KeyAreaAttr.value.length>0) {
                //有值
                console.log('重点区域有值');
                let areaArr = dataObject.KeyAreaAttr.value;
                if (areaArr.length>0){
                    for (let j = 0; j < areaArr.length; j++) {
                        let singleArea = areaArr[j];
                        this.updateAreaData(singleArea,angleData);
                    }
                }
            }else {
                console.log('重点区域无值');
                for (let i = 0; i < angleData.length; i++) {
                    let normalArea = angleData[i];
                    normalArea.hasData = false;
                    normalArea.areaSwitch = false;
                }
            }
        }
        return angleData;
    }

    /**
     * 处理图片，名称等
     */
    customContent(dataObject,angleData){
        //名称自定义物模型
        if (dataObject.AreaCustomContentList) {
            if (dataObject.AreaCustomContentList.value.length > 0) {
                // 重点区域名称
                let nameArr = dataObject.AreaCustomContentList.value;
                console.log('当前名称数组', nameArr, nameArr.length);
                this.fetchAndDealData(0,nameArr,angleData);
                return;
            }
        }
        // 无名称数组
        console.log('无名称数组');
        for (let i = 0; i < angleData.length; i++) {
            let normalArea = angleData[i];
            normalArea.isNameDefault = true;
            normalArea.areaName = stringsTo("angelCommon") + (i + 1);
        }
        showLoading(false);
        //最后去刷新
        this.setState({areaArrData:angleData});
        //设置单例数据
        AngleDataManager.getInstance().setAngleData(angleData);
    }
    //删除目录下所有图片数据
    deleteAllImg() {
        let params = {
            Path: 'api/app_file/device/delete_folder_file',
            Method: 'POST',
            ParamMap: {
                deviceId: LetDevice.deviceID,
                productKey:LetDevice.model,
                fileType:"preset",
            }
        };
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            console.log('删除所有图片成功',JSON.parse(data));
        }).catch((error) => {
            console.log('删除所有图片失败',JSON.stringify(error));
            // showToast(I18n.t('delete_failed'));
            // showLoading(false);
        });
    }
    // 更新常用角度数据
    getNewData(singleArea) {
        console.log('从物模型获取的angel',singleArea);
        let startTime = 0;
        let endTime = 0;
        if (singleArea.active_time != '[0,0,0,0,0,0,0]'){
            // 已编辑
            let timeStr = singleArea.repeat_time;
            let timeArr = timeStr.split(',');
            startTime = timeArr[0];
            endTime = timeArr[1];
        }
        let tempAllData = JSON.parse(JSON.stringify(this.state.areaArrData));
        for (let j = 0;j<tempAllData.length;j++){
            let normalArea = tempAllData[j];
            if (singleArea.idx == j+1){
                normalArea.active_time = JSON.stringify(JSON.parse(singleArea.active_time));
                normalArea.startTime = startTime;
                normalArea.endTime = endTime;
                normalArea.repeatTime = singleArea.repeat_week;
                normalArea.active_time_switch = singleArea.active_time_switch == 1?true:false;
                normalArea.hasImg = true;
                normalArea.monPosition = singleArea.pos;
                normalArea.repeatTimeTit = this.getTimeTit(startTime, endTime, singleArea.repeat_week);
                console.log('当前norm--',normalArea.index,normalArea,normalArea.startTime,normalArea.active_time);
            }
        }
        this.setState({areaArrData:tempAllData},callback=>{
            console.log('更新生效时间数据',this.state.areaArrData);
            // AngleDataManager.getInstance().setAngleData(this.state.areaArrData);
            // this.setState({});
        });
    }
    // 更新重点区域数据
    updateAreaData(singleArea,angleData) {
        console.log('从物模型获取的area',singleArea);
        let tempAreaArr = JSON.parse(singleArea.area);
        console.log('重点区域值---',tempAreaArr);
        for (let j = 0;j<angleData.length;j++){
            let normalArea = angleData[j];
            if (singleArea.idx == j+1){
                normalArea.areaSwitch = singleArea.switch == 1? true:false;
                normalArea.pushFlag = singleArea.push_flag == 1? true:false;
                normalArea.start_x = tempAreaArr[0];
                normalArea.start_y = tempAreaArr[1];
                normalArea.end_x = tempAreaArr[2];
                normalArea.end_y = tempAreaArr[3];
                if (singleArea.start_x  === singleArea.start_y  === singleArea.end_x === singleArea.end_y === 0){
                    normalArea.hasData = false;
                }else {
                    normalArea.hasData  = true;
                }
                console.log('当前重点区域值---',normalArea,normalArea.index);
            }
        }
        console.log('更新重点区域数据',this.state.areaArrData);

        // this.setState({areaArrData: this.state.areaArrData},callback=>{
        //     console.log('更新重点区域数据',this.state.areaArrData);
        //     AngleDataManager.getInstance().setAngleData(this.state.areaArrData);
        //     this.setState({});
        // });
        // this.setState({areaArrData:tempAllData});
    }

    fetchAndDealData(index,arrData,angleData){
        this.updateNameStr(index,arrData,angleData).then(res=>{
            if (index == arrData.length-1){
                showLoading(stringsTo('commWaitText'), false);
                console.log("---------some success")
                //保存本次所有的请求数据
                this.setState({areaArrData:angleData});
                AngleDataManager.getInstance().setAngleData(angleData);
            } else {
                console.log('我执行单个常用角度');
                this.fetchAndDealData(index+1,arrData,angleData);
            }
        }).catch(err=>{
            if (index == arrData.length-1){
                showLoading(stringsTo('commWaitText'), false);
                console.log("---------some error");
                this.setState({areaArrData:angleData});
                AngleDataManager.getInstance().setAngleData(angleData);
            } else {
                console.log('失败了走这里下载其他的');
                this.fetchAndDealData(index+1,arrData,angleData);
            }
        });
    }

    // 更新常用角度名称，图片url
    updateNameStr(i,arr,angleData){
        let nameStr = arr[i];
        nameStr = typeof(nameStr) == 'string'?JSON.parse(nameStr):nameStr;
        return new Promise((resolve, reject)=>{
            // let tempAllData = JSON.parse(JSON.stringify(this.state.areaArrData));
            for (let j = 0;j<angleData.length;j++){
                let normalArea = angleData[j];
                if (nameStr.nameLocation == j+1){
                    console.log('当前名称字符串--',nameStr);
                    normalArea.isNameDefault = nameStr.nameDefault;
                    if (nameStr.nameDefault){
                        normalArea.areaName = `${stringsTo("angelCommon")}${ j + 1  }`;
                        console.log('名字默认');
                    }else {
                        normalArea.areaName  = nameStr.areaName;
                        console.log('m名字修改');
                    }
                    normalArea.fileName = nameStr.imgCloudPath;
                    let tempFileName = nameStr.imgLocalPath;
                    let imgPath = `${AREA_PATH_PREFIX}${tempFileName}`;
                    normalArea.filePath = imgPath;
                    console.log('获取图片临时路径',tempFileName);
                    console.log('图片完整路径',imgPath);
                    IMIFile.fileExists(imgPath).then((result)=>{
                        console.log('判断图片是否存在',normalArea.index,result);
                        if (result) {
                            normalArea.imageExist = true;
                            console.log('图片存在',normalArea.index);
                            resolve();
                        } else {
                            //先下载，后播放，然后设置
                            console.log('图片下载',normalArea.index,normalArea.hasImg);
                            this.downAreaImg(normalArea,tempFileName,nameStr.imgKey,resolve,reject);
                        }
                    }).catch((error) => {
                        reject();
                        console.log('获取本地存在失败',JSON.stringify(error));
                    });
                }
            }
        })
    }
    // 下载图片  获取图片下载路径
    downAreaImg(normalArea,fileName,key,resolve,reject){
        console.log('当前单个的Area',normalArea,fileName);
        let params = {
            Path: 'api/app_file/device/download_by_user',
            Method: 'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path:normalArea.fileName,
                timer:600000
            }
        };
        console.log('获取图片下载路径的params--',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            let tempStr = JSON.parse(data);
            console.log('获取下载文件数据--', tempStr); //TODO测试每个服务器下，path前拼接的前缀
            //临时保存下需要下载的数据
            // this.downloadArr.push({tempStr:tempStr,normalArea:normalArea,fileName:fileName,key:key})
            this.getDownImgPath(tempStr,normalArea,fileName,key,resolve,reject);
        }).catch((error) => {
            console.log('获取图片下载路径失败',JSON.stringify(error));
            // showToast(I18n.t('waitFailedTip'));
            // showLoading(false);
            reject();
        });
    }

    getDownImgPath(dataStr,singleArea,fileName,key,resolve,reject) {
        console.log('下载data', dataStr,IMIFile.storageBasePath,fileName,singleArea);
        IMIDownload.downloadToPath(EVENT_NAME, dataStr.downloadUrl, AREA_PATH_PREFIX, fileName);
        this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
            if (event.status === IMIDownload.STATUS_START) {
                console.log('开始下载');
            }
            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                console.log('正在下载');
            }

            if (event.status === IMIDownload.STATUS_ERROR) {
                // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                // console.log(EVENT_NAME + " download error mataInfo : " + mataInfo)
                console.log('下载图片失败',singleArea.index);
                //用过一次必须释放
                reject();
                this.listener && this.listener.remove();
                this.listener = null;
            }
            if (event.status === IMIDownload.STATUS_CANCEL) {
                // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                //用过一次必须释放
                reject();
                this.listener && this.listener.remove();
                this.listener = null;
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {
                console.log('下载成功');
                console.log('下载成功后文件路径-常量---',fileName);
                console.log('下载路径',event.downloadPath);
                let areaImgPath = `${AREA_PATH_PREFIX}${fileName}`;
                console.log('areaImgPath',areaImgPath);
                //下载完成后，需要去解密
                //这块是解密读本地文件
                RNFS.readFile(areaImgPath, 'base64')
                    .then((content) => {
                        let decrypted = AngleDataManager.getInstance().decryptAes(content,key);
                        let writePath = `${IMIFile.storageBasePath}/area/IMG_666666.jpg`;
                        RNFS.writeFile(areaImgPath,decrypted,'base64')
                            .then((result) => {
                                //写入文件成功
                                console.log("result 写入成功",result);
                                singleArea.filePath = areaImgPath;
                                singleArea.imageExist = true;
                                console.log('下载图片数据',this.state.areaArrData);
                                resolve();
                            })
                            .catch((err) => {
                                reject();
                                console.log("图片写失败");
                            });
                    })
                    .catch((err) => {
                        console.log("错误1：",err);
                        reject();
                    });

                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
        });

    }

    getTimeTit(timeStart, timeEnd, timeRepeat) {
        // console.log('当前设置时间标题', item);
        let startTime = timeStart;
        let endTime = timeEnd;
        let repeatStr = timeRepeat;
        let repeatArr = repeatStr.split(',');
        console.log('当前获取重复标题', startTime, endTime, repeatStr)
        let startTimeStr;
        let endTimeStr;
        if (startTime.length==4){
            //新的定时生效数据1001-》10：01
            startTimeStr = startTime.substring(0,2) + ':'+ startTime.substring(2,4);
        }else {
            //老数据
            startTimeStr = startTime + ':00';
        }
        if (endTime.length==4){
            //新的定时生效数据1001-》10：01
            endTimeStr = endTime.substring(0,2) + ':'+ endTime.substring(2,4);
        }else {
            //老数据
            endTimeStr = endTime + ':00';
        }
        console.log('重复周期数组---', repeatArr);
        let tempWeekArr = [];
        let tempSelectWeek = [];
        for (let i = 0; i < repeatArr.length; i++) {
            let tempValue = parseInt(repeatArr[i]);
            // console.log('当前重复周期---', tempValue);
            if (tempValue == 1) {
                tempWeekArr.push(i);
                tempSelectWeek.push(1);
            } else {
                tempSelectWeek.push(0);
            }
        }

        if (tempWeekArr.length > 0) {
            tempWeekArr.sort(((a, b) => {
                return a - b
            }))
        }

        console.log('当前设置的重复周期', tempWeekArr, tempWeekArr.length);
        if ((tempWeekArr.length == 2) && (tempWeekArr[0] == 5) && (tempWeekArr[1] == 6)) {
            // 周末
            let tempStr = stringsTo("do_weekend") + startTimeStr + '-' + endTimeStr;
            // item.repeatTimeTit = tempStr;
            return tempStr;
        } else if ((tempWeekArr.length == 5) && (tempWeekArr[0] == 0) && (tempWeekArr[1] == 1) && (tempWeekArr[2] == 2) && (tempWeekArr[3] == 3) && (tempWeekArr[4] == 4)) {
            // 周一到周五
            let tempStr = stringsTo("do_weekday") + startTimeStr + '-' + endTimeStr;
            return tempStr;
            // item.repeatTimeTit = tempStr;

        } else if ((tempWeekArr.length == 7) && (tempWeekArr[0] == 0) && (tempWeekArr[1] == 1) && (tempWeekArr[2] == 2) && (tempWeekArr[3] == 3) && (tempWeekArr[4] == 4) && (tempWeekArr[5] == 5) && (tempWeekArr[6] == 6)) {
            // 每天
            let tempStr = stringsTo("do_everyday") + startTimeStr + '-' + endTimeStr;
            return tempStr;
            // item.repeatTimeTit = tempStr;
        } else {
            // 自定义
            console.log('走自定义', tempWeekArr, repeatArr);
            let tempSelectWeek = [];
            for (let j = 0; j < repeatArr.length; j++) {
                let tempValue = parseInt(repeatArr[j]);
                if (tempValue == 1) {
                    tempSelectWeek.push(1);
                } else {
                    tempSelectWeek.push(0);
                }
            }
            console.log('临时选中的selectWeek', tempSelectWeek);
            let weekTxt = '';
            if (tempWeekArr.includes(0)) {
                weekTxt += " " + stringsTo("week_new_1");
            }
            if (tempWeekArr.includes(1)) {
                weekTxt += " " + stringsTo("week_new_2");
            }
            if (tempWeekArr.includes(2)) {
                weekTxt += " " + stringsTo("week_new_3");

            }
            if (tempWeekArr.includes(3)) {
                weekTxt += " " + stringsTo("week_new_4");
            }
            if (tempWeekArr.includes(4)) {
                weekTxt += " " + stringsTo("week_new_5");
            }
            if (tempWeekArr.includes(5)) {
                weekTxt += " " + stringsTo("week_new_6");
            }
            if (tempWeekArr.includes(6)) {
                weekTxt += " " + stringsTo("week_new_7");
            }
            let tempStr = weekTxt + startTimeStr + '-' + endTimeStr;
            console.log('自定义str', tempStr);
            return tempStr;
            // item.repeatTimeTit = tempStr;
        }
    }

    componentWillUnmount() {
        // if (isAndroid()) {
        //     this.IMIVideoView&&this.IMIVideoView.destroy();
        // }
        this.homePageNewLivePlayerComponent&&this.homePageNewLivePlayerComponent.setMute(true);//设置关闭声音
        LetDevice.removeDeviceEventChangeListener();
        this._subscribe_focus && this._subscribe_focus();
        this._subscribe_blur && this._subscribe_blur();
        this._enterBackground&&this._enterBackground.remove();
        this._enterForeground&&this._enterForeground.remove();
        this._onPauseListener&&this._onPauseListener.remove();
        this._onResumeListener&&this._onResumeListener.remove();
        this.devicePropertyListener&&this.devicePropertyListener.remove();
        this.deviceInfoListener&&this.deviceInfoListener.remove();
        this.timer&&clearTimeout(this.timer);
        this.timerCallingStatue&&clearTimeout(this.timerCallingStatue);
        this.calibrationTimer&&clearTimeout(this.calibrationTimer);
        this.isCalibration&&this.isCalibration.remove();
        GotoPageInNative.removeStarNativeGoToPage();
        this.uploadImgTime && clearTimeout(this.uploadImgTime);
        this.networkProtectTimeout && clearTimeout(this.networkProtectTimeout);
        this.warningTimer && clearTimeout(this.warningTimer);
        this.angleChangeListener && this.angleChangeListener.remove();
        this.intervalQuerySD && clearInterval(this.intervalQuerySD);
        this.toTransparentTimer && clearTimeout(this.toTransparentTimer);
        // Unsubscribe
        this.unsubscribe();
        if (this.state.isFullScreen) {
            Orientation.lockToPortrait();
        }

        this.setState = (state,callback)=>{
            return;
        };
    }


    _onPressBack = (isFullScreen) => {
        if (isFullScreen) {
            Orientation.lockToPortrait();
            this.props.navigation.setOptions({tabBarVisible: true});
        } else {
            if (!this._canStepInCall())return;
            console.log('保存竖屏缩放比例',this.state.playerZoomScale);
            IMIStorage.save({
                key: LetDevice.deviceID+'zoomScale',
                data: {
                    zoomScale: this.state.playerZoomScale
                },
                expires: null,
            });
            //计算实时页播放时长
            let homeLiveStartTime = this.homePageNewLivePlayerComponent && this.homePageNewLivePlayerComponent.liveVideoStartTime;
            if (homeLiveStartTime > 0){
                GlobalUtil.liveVideoTime = GlobalUtil.liveVideoTime+new Date().getTime()-homeLiveStartTime;
                if (this.homePageNewLivePlayerComponent){
                    this.homePageNewLivePlayerComponent.liveVideoStartTime = 0;
                }
            }
            IMIGotoPage.exit();
        }
    };

    _onClickSleepButton = () =>{ //注意：0是休眠 1是关闭休眠
        let params = {
            SleepStatus: this.state.isSleep ? 1 : 0,
        };
        if (!this.state.isOnline){
            showToast(stringsTo('device_offline'));
            return ;
        }
        if (!this.state.netConnected){
            showToast(stringsTo('onlyDoInLive'));
            return ;
        }
        if (this._isShareUser()) return;
        if(!this._canStepInCall())  return;
        // if (this.state.panoramicType==1||this.state.panoramicType==0){
        //     showToast(stringsTo('Panoramic_loading'));
        //     return ;
        // }
        IMILogUtil.uploadClickEventForCount("SleepFuc");
        console.log('params---',params);
        IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params)).then((data) => {
            // console.log("设置休眠成功-------",data);
            this.setState({isSleep: !this.state.isSleep},()=>{
                if(this.state.isSleep){
                    this.homePageNewLivePlayerComponent && this.homePageNewLivePlayerComponent.setMute(true);//设置关闭声音
                    this.setState({isPanoramic:false});
                    this.intervalID&&clearInterval(this.intervalID);//清除
                    this.IMIVideoView.stop();
                }else{
                    //loading
                    this.homePageNewLivePlayerComponent && this.homePageNewLivePlayerComponent.setLoadingView(true);
                    setTimeout(()=>{
                        this.IMIVideoView.prepare();
                        console.log('关闭休眠');
                    },200);
                }
            });


        }).catch((err) => {
            console.log("设置休眠失败-------",err);
        });

    };

    //判断当前是否可以操作
    _canStepIn(){

        if(this.state.currentStatus == HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            return true;
        }
        if (this.state.isSleep){
            showToast(stringsTo('power_off'));
            return false;
        }
        if (!this.state.isOnline){
            showToast(stringsTo('device_offline'));
            return false;
        }
        showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
        return false;
    }

    /**
     * 判断是否可通话
     * @returns {boolean}
     * @private
     */
    _canHang(){
        if(this.state.isCalling){
            //如果是在通话中，允许用户操作挂断
            return true;
        }
        if(this.state.currentStatus == HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            return true;
        }
        if (this.state.isSleep){
            showToast(stringsTo('power_off'));
            return false;
        }
        if (!this.state.isOnline){
            showToast(stringsTo('device_offline'));
            return false;
        }
        showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
        return false;
    }

    //判断是否通话中、录像中
    _canStepInCall(){

        if (this.state.isCalling)
        {
            showToast(stringsTo('imi_speaking_block'));
            return false;
        }
        if (this.homePageNewLivePlayerComponent.getRecordStatus())
        {
            showToast(stringsTo('screen_recording'));
            return false;
        }
        return true;
    }

    _doCall() {
        IMILogUtil.uploadClickEventForCount("VoiceCall"); //统计点击通话的情况
        if (this.state.isCalling) {
            this.IMIVideoView && this.IMIVideoView.stopSpeak();
            if (audioNeedRecovery){
                this.homePageNewLivePlayerComponent.setMute(true)
            }

        } else {
            if(this.homePageNewLivePlayerComponent.getMute()){ //通话前监听是关闭的，则打开监听，并且结束通话后需要再次恢复到静音状态
                audioNeedRecovery = true;
                this.homePageNewLivePlayerComponent.setMute(false);
            }else {
                audioNeedRecovery = false;
            }
            this.IMIVideoView && this.IMIVideoView.startSpeak();
        }

        GlobalUtil.isCalling = !this.state.isCalling;
        this.setState({isCalling: !this.state.isCalling,
            isCallingStatue:true,
        });

        this.timerCallingStatue = setTimeout(() => {
                this.setState({
                    isCallingStatue:false,
                })
            },
            1000
        );
    }

    _doCallClose(isNetConnected=true){
        this.IMIVideoView.stopSpeak();
        audioNeedRecovery&&this.homePageNewLivePlayerComponent.setMute(true)
        GlobalUtil.isCalling = false;
        if (isNetConnected==true){
            this.setState({isCalling: false,
                isCallingStatue:false,
                netConnected:false,
            });
        }else {
            this.setState({isCalling: false,
                isCallingStatue:false,
            });
            console.log('通话报错---未连接--');
        }
    }

    _renderNavigationBar(bps, isFullScreen) {
        let {
            showSynchronous056,
            showSDError,
            showCrySwitch
        } = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);

        let containerHeight = 50;
        // let statusBarHeight = this.getStatusBarHeight();

        // let bgColor;
        let txtColor;
        let gradientColors;
        let iconBack;
        let iconMore;
        let showGradient = false;
        gradientColors = ['#00000099', '#00000000'];
        // gradientColors = ['#ff0000', '#0000ff'];

        // let videoCovered = false;
        // if (this.state.showErrorView || this.state.showPauseView || this.state.isSleep) {
        //     videoCovered = true;
        // }

        // bgColor = "transparent";
        let titleColor;
        if (this.state.playerZoomScale <= 1.03 || this.state.isSleep) {
            showGradient = false;
            bgColor = "#ffffff";
            txtColor = "#333333";
            titleColor = { color: "#333333" }
            iconBack = require("../../resources/images/newLive/icon_back_nor.png");
            iconMore = require("../../resources/images/newLive/icon_set_nor.png");// this.setState({});
            if (this.isForegroundPage) {
                StatusBar.setBarStyle('dark-content');
            }
        } else {
            showGradient = true;
            bgColor = "transparent";
            txtColor = "#ffffff";
            titleColor = { color: "#FFFFFF" }
            iconBack = require("../../resources/images/newLive/icon_back_wht.png");
            iconMore = require("../../resources/images/newLive/icon_set_wht.png");
            if (this.isForegroundPage) {//去往其他页面后 直播页面还有偶现的setState导致调用刷新了状态栏颜色, 未知原因
                StatusBar.setBarStyle('light-content');
            }
        }

        let titleBarStyle = {
            display: "flex",
            position: "absolute",
            top: 0,
            height: containerHeight+StatusBarHeight,
            // height:70,
            width: "100%",
            flexDirection: "column",
            backgroundColor: bgColor,
            zIndex: 1,
            alignItems: "center"
        };

        const containerStyle = {
            zIndex: 1,
            position: "relative",
            paddingTop: StatusBarHeight,
            height: 50,
            flex: 1,
            flexDirection: "row",
            alignItems: "center",
            paddingLeft: 12,
            paddingRight: 12,
            // backgroundColor:'orange'
        };

        const gradientStyle = {
            position: "absolute",
            top: 0,
            width: "100%",
            height: "100%"
        };

        const textContainerStyle = {
            display: "flex",
            flexDirection: "column",
            height: "100%",
            width: SCREEN_WIDTH - (30 * 3),
            paddingHorizontal: 20,
            flexGrow: 1,
            position: "relative",
            alignSelf: 'stretch',
            justifyContent: 'center',
            // backgroundColor:'red'

        };

        const titleTextStyle = {
            // fontSize: kIsCN ? 16 : 14,
            fontSize:17,
            // marginTop:5,
            textAlignVertical: 'center',
            textAlign: 'center',
            // backgroundColor:'blue',
        };

        const subTitleTextStyle = {
            // fontSize: kIsCN ? 12 : 10,
            fontSize: 12,
            lineHeight: 17,
            color:"#7F7F7F",
            textAlignVertical: 'center',
            textAlign: 'center',
        };


        let imageBtnStyle = {
            width: 30,
            height: 30,
        };
        let toastString = 'version  '+DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model).versionCode;
        if (this.homePageNewLivePlayerComponent && this.homePageNewLivePlayerComponent.connectType){
            toastString = `${toastString}  connectType：${this.homePageNewLivePlayerComponent.connectType}`;
        }
        return (
            <View style={titleBarStyle}>
                {/*<LinearGradient colors={gradientColors} style={gradientStyle} />*/}
                {showGradient ? <LinearGradient colors={gradientColors} style={gradientStyle} /> : null}
                <TouchableWithoutFeedback onLongPress={()=>{
                    showToast(toastString);
                }}>
                    <View
                        style={containerStyle}>
                        <View
                            style={{ width: 30, height: 30,}}>
                            <ImageButton
                                style={imageBtnStyle}
                                source={iconBack}
                                accessibilityLabel={"live_back"}
                                onPress={() => {
                                    this._onPressBack(isFullScreen)
                                }}
                            />
                        </View>

                        <View style={textContainerStyle}>
                            <Text
                                accessibilityLabel={"name"}
                                numberOfLines={1}
                                ellipsizeMode={"tail"}
                                style={[titleTextStyle, titleColor]}

                            >
                                {LetDevice.devNickName}
                            </Text>

                            <Text
                                accessibilityLabel={"sub_bps"}
                                numberOfLines={1}
                                ellipsizeMode={"tail"}
                                style={[subTitleTextStyle]}
                            >
                                {
                                    bps >= 0 ? `${bps}KB/S`:undefined
                                }
                            </Text>

                        </View>

                        <View style={{ width: 30, height: 30,position: "relative" }}>
                            <ImageButton
                                style={imageBtnStyle}
                                source={iconMore}
                                accessibilityLabel={"icon_more"}
                                onPress={() => {
                                    if (!this.state.isOnline){
                                        showToast(stringsTo('device_offline'));
                                        return true;
                                    }
                                    if (!this._canStepInCall())return;
                                    if (isWait()){
                                        return;
                                    }
                                    navigation.push("CommonSettingPage",{isSleep:this.state.isSleep});
                                }}
                            />
                        </View>
                    </View>
                </TouchableWithoutFeedback>
            </View>
        );
    }

    renderVideoSubView(isFullScreen,showFullScreenTools){  //竖屏时没有要显示的控件，竖屏显示拨号键、云台 是否显示云台
        let {showPTZ} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model); //是否显示圆盘
        return (<View style={{width: "100%",
            height: "100%",
            alignItems: "center",
            justifyContent: "center",
            position: "absolute"}} pointerEvents={'box-none'}>
            {this._renderCallViewFullScreen(isFullScreen,showFullScreenTools)}
            {showPTZ?this._rendLandscapeDirectionView(isFullScreen,showFullScreenTools):null}
            {this._sleepView(isFullScreen)}
            {/*{this._deviceOffLineView()}*/}
            {this._renderTargetPushView(isFullScreen)}
            {this._renderSliderView(isFullScreen)}
        </View>);
    }

    //横屏时的拨号键
    _renderCallViewFullScreen(isFullScreen,showFullScreenTools) {
        if (!isFullScreen || !showFullScreenTools) return null;
        return (<View style={{position: 'absolute', right: 30}}>
            <ImageButton
                style={{width: 44, height: 44}}
                source={this.state.isCalling?this.state.isCallingStatue?require("../../resources/images/icon_hangup_big_p.png"):require("../../resources/images/icon_hangup_fullscreen.png"):
                    this.state.isCallingStatue?require("../../resources/images/icon_call_p.png"):require("../../resources/images/icon_call_fullscreen.png")}
                /*  source={this.state.isCalling ? require("../../resources/images/icon_hangup_fullscreen.png") : require("../../resources/images/icon_call_fullscreen.png")}*/
                highlightedSource={this.state.isCalling?this.state.isCallingStatue?require("../../resources/images/icon_hangup_big_p.png"):require("../../resources/images/icon_hangup_fullscreen.png"):
                    this.state.isCallingStatue?require("../../resources/images/icon_call_p.png"):require("../../resources/images/icon_call_fullscreen.png")}
                onPress={() => {
                    if (this.state.isCallingStatue){
                        return;
                    }
                    if(!this._canHang())  return;
                    isCheckingPermission = true;
                    checkPermissionForFullScreen = true;
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                        if (status === 0) {
                            this._doCall();
                            isCheckingPermission = false;
                        } else if (status === -1) {
                            isCheckingPermission = false;
                            showToast(stringsTo('audio_permission_denied'))
                        }
                    })

                }}
                accessibilityLabel = {this.state.isCalling?"home_page_phone_on_full_screen":"home_page_phone_off_full_screen"}
            />

        </View>);
    }

    /*横屏云台View*/
    _rendLandscapeDirectionView(isFullScreen,showFullScreenTools) {
        if(isFullScreen&&showFullScreenTools){
        return (<View
            style={{
                position: "absolute", left: 20, bottom: 20
            }}>

            <JoystickControlView
                onStart={() => {
                }}
                onMove={(type) => {
                    if(!this._canStepIn())  return;
                    ptz_pressed = false;
                    this.setState({isMoveStatus:true});
                    clearInterval(this.setPTZIntervalID);
                    this._doDirection(type);
                }}
                onLoosen={() => {
                    ptz_pressed = false;
                    this.setState({isMoveStatus:false});
                    this.homePageNewLivePlayerComponent._onPressFullScreenTools();
                    clearInterval(this.setPTZIntervalID);
                    // this._getMotorPositonStatus();
                }}
                isFullscreen={true}
                isNewBgImg={true}
                diameterPan={150}
                diameterMid={20}/>
        </View>);}
    }
    //通话按钮控件
    _renderCallView() {
        //获取屏幕高度
        let callWidthHeight = 100;
        if (SCREEN_HEIGHT < 700){
            callWidthHeight = 80;
        }
        if (SCREEN_HEIGHT < 600){
            callWidthHeight = 60;
        }

        return (
            <ScrollView style={{flex: 1}}
                        contentContainerStyle={{flexGrow: 1}}
                        showsVerticalScrollIndicator={false}>

            <View style={{
                flex: 1,
                alignItems: "center",
                justifyContent: "center",
                //   backgroundColor: "white"
            }}>
                {this._renderCallWaveView()}
                <ImageButton
                    style={{width: callWidthHeight, height: callWidthHeight}}
                    source={!this.state.isSleep?this.state.isCalling?this.state.isCallingStatue?require("../../resources/images/newLive/icon_call_new_press.png"):require("../../resources/images/newLive/icon_call_no_pan.png"):
                        this.state.isCallingStatue?require("../../resources/images/newLive/icon_hangup_press.png"):require("../../resources/images/newLive/icon_call_hangup_no_pan.png"):require("../../resources/images/newLive/icon_call_new_press.png")}
                    highlightedSource={this.state.isCalling?require("../../resources/images/newLive/icon_hangup_press.png"):require("../../resources/images/newLive/icon_call_new_press.png")}
                    onPress={() => {
                        if (this.state.isCallingStatue){
                            return;
                        }
                        if(!this._canStepIn())  return;
                        isCheckingPermission = true;
                        checkPermissionForFullScreen = true;
                        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                            if (status === 0) {
                                this._doCall();
                                isCheckingPermission = false;
                            } else if (status === -1) {
                                showToast(stringsTo('audio_permission_denied'));
                                isCheckingPermission = false;
                            }
                        })

                    }}
                />
                {
                    !this.state.isCalling?<Text style={styles.callWarning}>{stringsTo('click_to_start_call')}</Text>:<Text style={styles.callWarning}></Text>
                }

            </View>
            </ScrollView>
        );
    }
    // 常用角度页面
    _renderAngel() {
        if (!this.state.isShowAngle){
            return;
        }

        let itemWidth = (SCREEN_WIDTH - 14 * 4) / 3;
        let itemHeight = itemWidth * 5 / 9;
        let angleHeight = 170;
        let angleTitleHeight = 50;
        let settingMarginBottom = 10;
        if (SCREEN_HEIGHT < 700){
            angleHeight = 142;
            angleTitleHeight = 37;
            itemHeight = itemHeight - 10;
            settingMarginBottom = 5;
        }
        return(
            <View style={{backgroundColor:'#F7F7F7',flexDirection: "column",width:SCREEN_WIDTH,height:700}}>
                <View style={{backgroundColor:'#FFFFFF',width:SCREEN_WIDTH,height:angleHeight,borderBottomLeftRadius:20,borderBottomRightRadius:20}}>
                    <TouchableOpacity accessibilityLabel={"angle_hide"} onPress={() => {
                        this.setState({isShowAngle: false})
                    }}>
                        <View style={{
                            // marginTop: Utils.isAndroid() ? -8 : 0,
                            height: angleTitleHeight,
                            width: '100%',
                            flexDirection: 'row',
                            justifyContent: "center",
                            alignItems: "center",
                        }}>
                            <Text accessibilityLabel={"angle_hide"} style={{
                                color: '#333333',
                                width: '100%',
                                textAlignVertical: 'center',
                                textAlign: 'center',
                                fontSize: 16
                            }}>{stringsTo("angelCommon")}</Text>
                            <Image style={{position: "absolute", width: 30, height: 30, right: 14}}
                                   source={require("../../resources/images/newLive/icon_angel_del_new.png")}></Image>
                        </View>
                    </TouchableOpacity>
                    <TouchableOpacity accessibilityLabel={"angle_set"} onPress={() => {
                        if (!this._canStepIn()){
                            return;
                        }

                        if (this.homePageNewLivePlayerComponent.getRecordStatus()) {
                            showToast(stringsTo('screen_recording'));
                            return;
                        }

                        this.props.navigation.push("AngelAreaSetting");
                    }}>
                        <View style={{
                            height: 20,
                            width: '100%',
                            flexDirection: 'row',
                            justifyContent: "center",
                            alignItems: "center",
                            marginBottom:settingMarginBottom,
                        }}>
                            <Text accessibilityLabel={"angle_set_new"} style={{
                                color: '#4A70A5',
                                width: '100%',
                                textAlignVertical: 'center',
                                textAlign: 'center',
                                fontSize: 12
                            }}>{stringsTo("angelMoreSetting")}</Text>
                        </View>
                    </TouchableOpacity>

                    <View style={{
                        width: '100%',flexDirection: 'row',
                        flexWrap: "wrap", justifyContent: "center", alignItems: "center",
                        height:itemHeight + 20,
                    }}>
                        {
                            this.state.areaArrData.map((item, index) => {

                                let screenWidth = width;
                                let itemWidth = (SCREEN_WIDTH - 20 * 4) / 3;
                                let itemHeight = itemWidth * 5 / 9;
                                let isSelected;
                                let imgWidth;
                                let imgHeight;
                                if (this.state.areaSelectIndex == index) {
                                    isSelected = true;
                                    imgWidth = itemWidth - 6;
                                    imgHeight = itemHeight - 6;
                                } else {
                                    isSelected = false;
                                    imgWidth = itemWidth;
                                    imgHeight = itemHeight;
                                }

                                let location = item.index;
                                let hasAngleData = (item.hasData == true);
                                let hasImgValue = item.hasImg == true;

                                let showTime = false;
                                let timeStr;//时间字符串
                                //2022-09-30@byh item.active_time == '[0,0,0,0,0,0,0]'不能使用这个来作为展示与否
                                //因为设置过生效时间后，active_time就有值了
                                if (item.active_time_switch) {
                                    showTime = true;

                                    let startTimeStr;
                                    let endTimeStr;
                                    if (item.startTime.length==4){
                                        //新的定时生效数据1001-》10：01
                                        startTimeStr = item.startTime.substring(0,2) + ':'+ item.startTime.substring(2,4);
                                    }else {
                                        //老数据
                                        startTimeStr = item.startTime + ':00';
                                    }
                                    if (item.endTime.length==4){
                                        //新的定时生效数据1001-》10：01
                                        endTimeStr = item.endTime.substring(0,2) + ':'+ item.endTime.substring(2,4);
                                    }else {
                                        //老数据
                                        endTimeStr = item.endTime + ':00';
                                    }
                                    timeStr = startTimeStr + '-' + endTimeStr;
                                }

                                // 重点区域
                                let tempStartX = (item.start_x / 100);
                                let tempStartY = (item.start_y / 100);
                                let tempEndX = (item.end_x / 100);
                                let tempEndY = (item.end_y / 100);
                                // console.log('临时获取startX--',tempStartX,tempStartY,tempEndX,tempEndY);

                                let max_x = parseInt((tempEndX - tempStartX) * imgWidth);
                                let max_y = parseInt((tempEndY - tempStartY) * imgHeight);
                                let min_x = parseInt(tempStartX * imgWidth);
                                let min_y = parseInt(tempStartY * imgHeight);

                                let itemTotalStyle = {
                                    width: itemWidth,
                                    height: itemHeight + 20,
                                    flexDirection: 'column',
                                    // backgroundColor: '#F2F3F5',
                                    borderTopLeftRadius: 8,
                                    borderTopRightRadius: 8
                                };
                                let itemStyle = {
                                    width: itemWidth,
                                    height: itemHeight,
                                    flexDirection: 'row',
                                    borderWidth: isSelected ? 3 : 0,
                                    borderColor: isSelected ? '#4A70A5' : '#FFFFFF',
                                    borderRadius: 8,
                                };
                                let numViewStyle = {
                                    position: "absolute",
                                    zIndex: 999,
                                    left: -0.5,
                                    top: -0.5,
                                    borderTopLeftRadius: 8,
                                    borderBottomRightRadius: 8,
                                    width: 18,
                                    height: 18,
                                    backgroundColor: '#000000',
                                    opacity: 0.3,
                                };
                                let numStyle = {
                                    textAlign: 'center',
                                    color: 'white',
                                    fontSize: 12,
                                    textAlignVertical: 'center'
                                };
                                let delStyle = {
                                    position: "absolute",
                                    zIndex: 999,
                                    top: -11,
                                    right: -11,
                                    height: 30,
                                    width: 30
                                };
                                let delImgStyle = {height: 25, width: 25};
                                let addStyle = {
                                    height: 24,
                                    width: 24,
                                    marginLeft: isSelected?(itemWidth - 24-6) / 2:(itemWidth - 24) / 2,
                                    marginTop: isSelected?(itemHeight - 24-6) / 2:(itemHeight - 24) / 2
                                };
                                let imageStyle = {
                                    width: isSelected ? itemWidth - 6 : itemWidth,
                                    height: isSelected ? itemHeight - 6 : itemHeight,
                                    borderRadius: 8,
                                };
                                let showImgTimeStyle = {height: 18, width: 18};
                                let showImgStyle = {
                                    position: "absolute",
                                    zIndex: 999,
                                    bottom: 0,
                                    right: 0,
                                    height: 18,
                                    width: 18
                                };
                                let showAngleStyle = {
                                    position: "absolute",
                                    backgroundColor: '#4A70A5',
                                    opacity: 0.6,
                                    borderTopLeftRadius: 4,
                                    borderBottomLeftRadius: 4,
                                    borderTopRightRadius: 4,
                                    borderBottomRightRadius: 4,
                                    marginLeft: min_x,
                                    marginTop: min_y,
                                    width: max_x,
                                    height: max_y,
                                };

                                return (
                                    <TouchableOpacity accessibilityLabel={"set_favorite_area"} key={index}
                                                      onPress={() => {
                                                          // console.log('是否有设置图片--',hasAngleData,hasImgValue);
                                                          if (!this._canStepIn()){
                                                              return;
                                                          }
                                                          if (hasImgValue) {
                                                              // 有设置重点区域 点击设置激活位置
                                                              this.setActiveArea(index+1,false);
                                                          } else {
                                                              // 添加重点区域
                                                              if (this.homePageNewLivePlayerComponent.getRecordStatus()) {
                                                                  showToast(stringsTo('screen_recording'));
                                                                  return;
                                                              }
                                                              this.addArea(index);
                                                          }
                                                          // this.addArea(index);
                                                          //    this.saveArea();
                                                      }
                                                      }>
                                        <View
                                            style={[itemTotalStyle, (item.index) % 3 !== 0 ? {marginLeft: 20} : {}, (item.index) / 3 >= 1 ? {marginTop: 20} : {}, {marginBottom: -5}]}>
                                            <View style={[itemStyle, {backgroundColor: '#F7F7F7'}]}>
                                                {hasImgValue ? <View style={numViewStyle}>
                                                    <Text style={numStyle}>{item.index + 1}</Text>
                                                </View> : null}
                                                {
                                                    hasImgValue ?
                                                        <Image style={imageStyle}
                                                               resizeMode={"cover"}
                                                               source={hasImgValue && item.imageExist ? ({uri: `file://` + item.filePath}) : null}>
                                                               {/*source={require('../../resources/images/wdr_mode.png')}>*/}
                                                        </Image>
                                                        : <Image style={addStyle}
                                                                 source={require('../../resources/images/newLive/icon_area_add_new.png')}></Image>
                                                }

                                                {
                                                    hasAngleData && hasImgValue && item.areaSwitch ? <View style={showAngleStyle}>
                                                    </View> : null
                                                }

                                                {
                                                    hasImgValue ?
                                                        <TouchableOpacity style={delStyle}
                                                                          accessibilityLabel={"del_favorite_area"}
                                                                          onPress={() => {
                                                                              // this.deleteAreaNew(item);
                                                                              if (!this._canStepIn()){
                                                                                  return;
                                                                              }
                                                                              this.setState({showDeleteDialog:true,deleteIndex:item.index})
                                                                          }}>
                                                            <Image style={delImgStyle}
                                                                   source={require('../../resources/images/newLive/icon_area_del_new.png')}></Image>
                                                        </TouchableOpacity>
                                                        : null
                                                }

                                                {/*{showTime ? <View style={showImgStyle}>*/}
                                                {/*    <Image style={showImgTimeStyle}*/}
                                                {/*           source={require('../../resources/Images/icon_area_time_show.png')}></Image>*/}
                                                {/*</View> : null}*/}
                                            </View>

                                            {showTime?<View style={{
                                                // marginTop: 3,
                                                marginTop:2,
                                                height: 16,
                                                width: itemWidth,
                                                // backgroundColor: '#F2F3F5'
                                            }}>
                                                <Text style={{
                                                    width: itemWidth,
                                                    lineHeight: 16,
                                                    fontSize: 11,
                                                    color: '#4A70A5',
                                                    textAlign: 'center',
                                                }}>{timeStr}</Text>
                                            </View> : null}
                                        </View>
                                    </TouchableOpacity>
                                )
                            })
                        }
                    </View>
            </View>

                <View style={{
                    backgroundColor:'#F7F7F7',
                    height:270,
                }}>
                    {this.renderPtzControlView()}
                </View>

            </View>
            )
    }
    //删除常用角度
    deleteAreaNew(item) {
        console.log('删除常用角度',item);
        showLoading(stringsTo('commWaitText'), true);
        let delteValue = 0;
        switch (item.index) {
            case 0:
                delteValue = 1;
                break;
            case 1:
                delteValue = 2;
                break;
            case 2:
                delteValue = 4;
                break;
        }
        console.log('删除常用角度',delteValue);
        let params = {"FavAreaDelete": delteValue}
        console.log('删除常用角度params--',params);
        LetDevice.setPropertyCloud(params).then( ()=> {
            console.log('删除常用角度物模型成功,然后删除云端图片');
            this.deleteImg(item);
        }).catch((error) => {
            console.log('激活角度失败----',error);
            showToast(I18n.t('delete_failed'));
            showLoading(false);
        });
    }

    deleteImg(item){
        //20220916@byh 现在删除云端图片，可能有概率会失败
        //而且不能以删除云端图片的成功与否作为前置条件
        let params = {
            Path: 'api/app_file/device/delete_by_user',
            Method: 'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path:item.fileName,
                timer:600000
            }
        };
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            console.log('删除图片成功',JSON.parse(data));
            IMIFile.deleteFileByPath(item.filePath).then((data)=>{
                console.log('删除本地图片成功');
            }).catch((error)=>{
                console.log('删除本地图片失败',JSON.stringify(error))
            });
        }).catch((error) => {
            console.log('删除图片失败',JSON.stringify(error));
        });
        this.deleteName(item);
    }

    deleteName(item){
        LetDevice.updateAllPropertyCloud().then((data) => {
            console.log('获取重点区域所有名称--删除',JSON.parse(data));
            let dataObject = JSON.parse(data);
            if (dataObject.AreaCustomContentList) {
                if (dataObject.AreaCustomContentList.value.length > 0) {
                    // 重点区域名称
                    let nameArr = dataObject.AreaCustomContentList.value;
                    console.log('当前名称数组', nameArr, nameArr.length);
                    if (nameArr.length>0){
                        let index;
                        for (let i = 0; i < nameArr.length; i++) {
                            let singleNameStr = JSON.parse(nameArr[i]);
                            console.log('获取单个图片名称路径等',singleNameStr);
                             if (singleNameStr.nameLocation == item.index+1){
                                 index = i;
                             }
                        }
                        console.log('删除前nameArr',nameArr);
                        nameArr.splice(index,1);
                        console.log('删除后nameArr',nameArr);
                        let params = {AreaCustomContentList: nameArr};
                        console.log('删除-上传名称',params);
                        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
                            //名称成功--
                            const addImgTrue = setTimeout(() => {
                                //时间到了，开始执行
                                console.log('删除-上传名称成功');
                                this.setState({deleteIndex:-1});
                                this.updateDataArr(item.index);
                                if (this.state.areaSelectIndex == item.index) {
                                    this.setState({areaSelectIndex: -1}, callback => {
                                        showLoading(false)
                                    });
                                } else {
                                    showLoading(false)
                                }
                                showToast(I18n.t('delete_success'));
                                //清除
                                clearTimeout(addImgTrue);
                            }, 3000);
                        }).catch((error) => {
                            console.log('失败----', error);
                            console.log('失败----', error);
                            showToast(I18n.t('delete_failed'));
                            showLoading(false);
                        });
                        return;
                    }
                }
            }
            showToast(I18n.t('delete_failed'));
            showLoading(false);
        }).catch(error => {
            console.log('删除名称失败--', JSON.stringify(error));
            showToast(I18n.t('delete_failed'));
            showLoading(false);
        });
    }

    // 删除之后单例重新赋值
    updateDataArr(index){
        console.log('当前index',index);
        for (let i = 0;i<this.state.areaArrData.length;i++){
            let item = this.state.areaArrData[i];
            if (item.index == index){
                    item.monPosition = '[0,0]',
                    item.areaSwitch = false,
                    item.start_x = 0,
                    item.start_y = 0,
                    item.end_x = 0,
                    item.end_y = 0,
                    item.active_time = '[0,0,0,0,0,0,0]',
                    item.isSelected = false,
                    item.hasImg = false,
                    item.hasData = false,
                    item.fileName = '',
                    item.filePath = '',
                    item.isNameDefault = true,
                    item.areaName = stringsTo("angelCommon") + (item.index + 1);
                    item.startTime = "0000";
                    item.endTime = "2400";
                    item.repeatTime = '1,1,1,1,1,1,1';
                    item.repeatTimeTit=stringsTo("angel_no_set_time"),
                    item.imageExist = false;
                    item.imgKey = '';
                    item.pushFlag = true;
                    item.active_time_switch = false;
            }
        }
        AngleDataManager.getInstance().setAngleData(this.state.areaArrData);
        this.setState({});
    }

    // 常用角度切换分辨率截图
    snapAreaImg(value) {
        console.log('当前分辨吕---',this.state.qualityDataIndex,value);
        if (this.state.qualityDataIndex === 1) return;
        LetDevice.setPropertyCloud(JSON.stringify({"StreamVideoQuality": value})).then(data => {
            if (value == 1){
                change_snapArea_quality = true;
            }else {
                change_snapArea_quality = false;
            }
            console.log('切换分辨率成功',value);
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    // 添加重点区域
    addArea(index){
       console.log('添加常用角度index--',index);
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
            if (status2 === 0) {
                showLoading(stringsTo('commWaitText'), true);
                this.setState({addAreaIndex: index,},callback=>{
                    this.snapAreaImg(1);
                    this._getMotorPositionStatus();
                })
            } else if (status2 === -1) {
                showToast(stringsTo('storage_permission_denied'));
            }else {
                showToast(stringsTo('storage_permission_denied'));
            }
        });
    }

    //获取电机马达转到后的位置XY
    _getMotorPositionStatus() {
        LetDevice.getPropertyCloud('MotorPositon').then((data) => {
            console.log("MotorPositon = " + data);
            let arrData = data.split(",");
            if (arrData.length > 2) {
               let xValue = arrData[1];
               let yValue = arrData[2];
               console.log('xValue--yvalue',xValue,yValue)
                let positionStr = '['+arrData[1]+","+arrData[2]+']';
               console.log('马达位置--',positionStr);
                this.uploadAreaImg(positionStr);
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }
    /**
     * 获取上传图片的URL和path
     * @param currentSnapshotPath
     */
    uploadAreaImg(positionStr) {
        console.log('位置-',positionStr);
        let params = {
            Path: 'api/app_file/device/upload_by_user',
            Method: 'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                storeType:2,
                productKey:LetDevice.model,
                folder:"preset",
                suffix:"jpg",
                timer:600000
            }
        };
        console.log('上传图片请求',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            let tempStr = JSON.parse(data);
            console.log('获取当前云端返回上传图片的url',tempStr)
            this.uploadImgTime && clearTimeout(this.uploadImgTime);
            this.uploadImgTime = setTimeout(() => {
                let filename = `IMG_${new Date().getTime()}.jpg`;
                let currentSnapshotPath = `${AREA_PATH_PREFIX}${filename}`;


                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                    //如果需要切回原来的分辨率
                    if (change_snapArea_quality){
                        change_snapArea_quality = false;
                        this.snapAreaImg(2);
                    }
                    //对截图文件进行加密
                    AngleDataManager.getInstance().encryptFile(currentSnapshotPath).then(res=>{
                        //加密成功，返回加密后数据，包含加密后的文件全路径，加密时用的key
                        //加密后的文件全路径
                        let ePath = res.filename;
                        //加密后的key
                        let eKey = res.key;
                        //上传加密了的文件
                        this.uploadUrl(tempStr, filename,ePath,positionStr,eKey);


                        //this.uploadUrl(tempStr, filename,currentSnapshotPath,positionStr);
                    }).catch(error => {
                        showToast(I18n.t('waitFailedTip'));
                        showLoading(false);
                        console.log("错误2：",error);
                    })
                    // console.log("上传常用角度------截图成功", currentSnapshotPath);
                    // IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, LetDevice.deviceID).then(_ => {
                    //     console.log('截图上传到服务器');
                    //     // this.snapAreaImg(2);
                    //     this.uploadUrl(tempStr, filename,currentSnapshotPath,positionStr);
                    // });
                });
                },1000);
        }).catch((error) => {
            console.log('获取图片上传路径失败',JSON.stringify(error));
            console.log('获取图片上传路径失败',JSON.stringify(error));
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });

    }

    /**
     * 上传图片
     * @param dataStr
     * @param currentSnapshotPath 加密后的文件全路径
     */
    uploadUrl(dataStr,fileName, currentSnapshotPath,positionStr,key) {
        IMIIotRequest.uploadFileToIMICloud(dataStr.uploadUrl, currentSnapshotPath, dataStr.path, "")
            .then((data) => {
                this.uploadAreaNameDataCloud(dataStr,fileName,positionStr,key);
                console.log('重点区域上传图片成功',data);
            }).catch((error) => {
                console.log('上传图片失败',JSON.stringify(error));
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    /**
     * 保存名称等数据到服务器
     * @param path
     */
    uploadAreaNameDataCloud(dataStr,fileName,positionStr,key) {
        LetDevice.updateAllPropertyCloud().then((data) => {
            console.log('获取重点区域所有名称--',JSON.parse(data));
            let dataObject = JSON.parse(data);
            let nameArr = [];
            if (dataObject.AreaCustomContentList) {
                if (dataObject.AreaCustomContentList.value.length > 0) {
                    // 重点区域名称
                    nameArr = dataObject.AreaCustomContentList.value;
                    console.log('当前名称数组', nameArr, nameArr.length);
                }
            }
            this.updateNewName(dataStr,fileName,positionStr,nameArr,key);
        }).catch(error => {
            console.log('名称错误哈哈哈--', JSON.stringify(error));
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    // 上传名称
    updateNewName(dataStr,fileName,positionStr,nameArr,key){
        let tempNameArr = nameArr;
        let location = this.state.addAreaIndex + 1;
        let nameStr = stringsTo("angelCommon") + location;
        console.log('上传图片成功相关返回--',dataStr,fileName,positionStr,tempNameArr);
        let nameParamData = {
            "imgCloudPath": dataStr.path,
            "imgLocalPath":fileName,
            "nameLocation": location,
            "areaName":nameStr,
            "nameDefault": true,
            "imgKey":key
        }
        //遍历是否有重复的信息，有责替换，无则新增
        let hasLocation = false;
        let hasIndex = 0;
        for (let i = 0; i < tempNameArr.length; i++) {
            let item = tempNameArr[i];
            item = typeof(item) === 'string'?JSON.parse(item):item;
            if (location === item.nameLocation){
                hasLocation = true;
                hasIndex = i;
            }
        }
        if (hasLocation){
            tempNameArr.splice(hasIndex,1,JSON.stringify(nameParamData));
        }else {
            tempNameArr.push(JSON.stringify(nameParamData));
        }

        let params = {AreaCustomContentList: tempNameArr};
        console.log('上传名称',params);
        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            //上传名称成功--
            this.saveAreaPropertyCloud(positionStr);
        }).catch((error) => {
            console.log("set custom error",error)
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }
    // 上传常用角度物模型
    saveAreaPropertyCloud(positionStr) {
        let location = this.state.addAreaIndex + 1;
        let tempArr = [];
        let areaValue = {"idx":location,
            "pos":positionStr,
            "active_time":'[0,0,0,0,0,0,0]',
            "active_time_switch":0,
            "repeat_time":'0,0',
            "repeat_week":'1,1,1,1,1,1,1',
        };
        tempArr.push(areaValue);
        let params = {FavAreaAttr:tempArr};
        LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
            this.setActiveArea(location,true);
        }).catch((error) => {
            console.log('添加常用角度失败----',error);
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    setActiveArea(location,isAddActive){
       console.log('当前激活角度',location,isAddActive);
        let params = {"FavAreaActive": parseInt(location)}
        if (!isAddActive){
            showLoading(stringsTo('commWaitText'), true);
        }
        console.log('激活角度params--',params);
        LetDevice.setPropertyCloud(params).then( ()=> {
            if (isAddActive){
                const addImgTrue = setTimeout(() => {
                    //时间到了，开始执行
                    // Utils.hide();
                    this.initAreaData(true);
                    //清除
                    clearTimeout(addImgTrue);
                }, 5000)
            }else {
                // showToast(I18n.t('settings_set_success'));
                showLoading(false);
                this.setState({areaSelectIndex:location-1,showMsgDialog:true,msgTitle:stringsTo("active_use_tip")});
            }
        }).catch((error) => {
            console.log('激活角度失败----',error);
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    /**
     * 回调的竖屏页面
     * 支持云台与非云台
     * 功能不同，功能排序也不同
     * @returns {Element}
     * @private
     */
    _renderPortraitToolBarView() {
        //是否显示常用角度
        let {showAngle,showPTZ,showStreamingSpotlight,showStreamingAlarm} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let oneLineArr = [];
        let twoLineArr = []
        if (showAngle){
            oneLineArr = [
                this._renderAngelButton(),
                this._renderSnapButton(),
                this._renderCallButton(),
                this._renderRecordButton(),
                this._renderCloudButton()
            ];

            twoLineArr = [this._renderParamcButton()];
            if(showStreamingAlarm){
                twoLineArr.unshift(this._renderAlarmButton())
            }
            if(showStreamingSpotlight){
               twoLineArr.unshift(this._renderSpotlightButton(false))
            }

            let pushCount = 5 - twoLineArr.length;
            for (let i = 0; i < pushCount; i++) { //填充透明按钮占位来充满第二行，否则不会从左按照固定间隔排开
                twoLineArr.push(this._renderSpotlightButton(true))
            }

        }else {//IPC040
            oneLineArr =  [
                this._renderAlarmButton(),
                this._renderSnapButton(),
                this._renderRecordButton(),
                this._renderCloudButton()
            ];
        }
        if (this.state.isShowAngle){
            //显示常用角度区域时，不显示这块内容
            return;
        }

        let controlMarginTop = 20;
        let expendIconMarginTop = 28;
        let iconExpendHeight = 45;
        if (SCREEN_HEIGHT < 700){
            controlMarginTop = 10;
            expendIconMarginTop = 10;
            iconExpendHeight = 27;
        }

        let fixContolViewStyle = {
            marginTop:controlMarginTop,
            marginBottom:twoLineArr.length>0?0:controlMarginTop,
            height: 50,
            width: "100%",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            paddingRight: 16,
            paddingLeft: 16,
            // backgroundColor: "#ffffff",
        };

        return (

            <View style={{
                flex:1,
                backgroundColor:"#F7F7F7",
                flexDirection:"column",
                // height:700
            }}>
                <View style={{backgroundColor:"#FFFFFF",flexDirection:"column",width:SCREEN_WIDTH,borderBottomLeftRadius:20,borderBottomRightRadius:20}}>
                    <View style={fixContolViewStyle}>
                        {oneLineArr}
                        {/*{this._renderAngelButton()}*/}
                        {/*{this._renderSnapButton()}*/}
                        {/*{this._renderCallButton()}*/}
                        {/*{this._renderRecordButton()}*/}
                        {/*{this._renderCloudButton()}*/}
                    </View>
                    {
                        twoLineArr.length > 0 && this.isShowSecLandToolBar ?
                            <Animated.View style={{
                                height: this.state.dynamicHeight,
                            }}>
                                {this._renderSecLandToolBar(twoLineArr)}
                            </Animated.View> :
                            <Animated.View style={{
                                height: this.state.dynamicHeight,
                            }}></Animated.View>
                    }
                    {
                        twoLineArr.length>0?
                            <TouchableWithoutFeedback
                                accessibilityLabel={"home_page_expand"}
                                onPress={()=>{
                                    if (this.isShowSecLandToolBar){
                                        this.startViewAnimation(false);
                                    }else {
                                        this.startViewAnimation(true);
                                    }
                                }}
                            >
                                <View style={{backgroundColor:'#FFFFFF',width:SCREEN_WIDTH,height:iconExpendHeight,borderBottomLeftRadius: 20,
                                    borderBottomRightRadius: 20,}}>
                                    <Image style={[{width: 23,height: 8,marginTop:expendIconMarginTop,left:(SCREEN_WIDTH-23)/2,alignItems:'center',justifyContent:'center'}]}
                                           source={this.isShowSecLandToolBar?require('../../resources/images/newLive/icon_liveUp.png'):require('../../resources/images/newLive/icon_liveDown.png')}/>
                                </View>
                            </TouchableWithoutFeedback>
                            :null
                    }


                </View>
                {/*<ScrollView showsVerticalScrollIndicator={false}>*/}
                    <View style={{
                    backgroundColor:'#F7F7F7',
                        flex:1,
                    // height:270,
                    opacity:this.state.isSleep?0.3:1
                }}>
                    {showPTZ?this.renderPtzControlView():this._renderCallView()}
                </View>
            {/*</ScrollView>*/}
            </View>

        );
    }
    _renderSecLandToolBar(twoLineArr){
        let secondLineMarginTop = 15;
        if (SCREEN_HEIGHT <700){
            secondLineMarginTop = 10;
        }
        let fixContolViewStyle = {
            marginTop:secondLineMarginTop,
            width: "100%",
            flexDirection: "row",
            alignItems: "center",
            justifyContent: "space-between",
            paddingRight: 16,
            paddingLeft: 16,
            // backgroundColor:'#ff00ff'
        };
        return(
                <View style={fixContolViewStyle}>
                    {twoLineArr}
                    {/*{this._renderParamcButton()}*/}
                </View>
        )
    }

    _renderParamcButton() {
        return (
            <View key={"panoramic_button"}>
                <TouchableOpacity
                    disabled={this.state.isSleep?true:false}
                    accessibilityLabel={"home_page_panoramic"}
                    onPress={()=>{
                        if (this._isShareUser()) return;
                        if(!this._canStepIn())  return;
                        let isPanoramic = !this.state.isPanoramic;
                        if (isPanoramic){
                            showLoading(stringsTo('commLoadingText'),true);
                            imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
                                showLoading(false);
                                console.log('-------------'+res.url);
                                // parseInt(res.status)==1||parseInt(res.status)==0?3:
                                let  isEnabledOpen = parseInt(res.status)==2?true:false;
                                this.setState({panoramicType:(!isEnabledOpen&&res.timeOutFlag)?3:parseInt(res.status),overAllImgStoreUrl:res.url,isPanoramic:isPanoramic});
                            }).catch(error=>{
                                showLoading(false);
                                this.setState({isPanoramic:isPanoramic});
                            });
                        }else {
                            if (this.state.panoramicType == 1){
                                showToast(stringsTo('panoramicing_tip'));
                                return;
                            }
                            this.setState({isPanoramic:isPanoramic});
                        }
                    }}
                >
                    <Image style={[{width: 40, height: 40,opacity:this.state.isSleep?0.3:1}]}
                           source={require('../../resources/images/newLive/icon_liveParamc.png')}/>
                </TouchableOpacity>

            </View>
        )
    }

    _renderAngelButton() {

        return (
            <View key={"angle_button"}>
                <TouchableOpacity
                    disabled={this.state.isSleep?true:false}
                    accessibilityLabel={"home_page_angle"}
                    onPress={()=>{
                        if (LetDevice.isShareUser){
                            showToast(stringsTo('shareUser_tip'));
                            return;
                        }
                        if (!this._canStepIn()){
                            //设备状态错误时，不可点击
                            return;
                        }

                        if (!this.state.isShowAngle) {//xy@20210830 显示时刷新区域
                            this.initAreaData(false);
                        }
                        this.setState({isShowAngle:!this.state.isShowAngle})
                    }}
                >
                    <Image style={[{width: 40, height: 40,opacity:this.state.isSleep?0.3:1}]}
                           source={require('../../resources/images/newLive/icon_liveAngel.png')}/>
                </TouchableOpacity>

            </View>
        )
    }

    /**
     * 报警功能
     * 1、点击开始报警
     * 2、默认报警10s,未操作10秒后结束报警
     * 3、报警中用户再次点击，取消报警
     * 4、报警需要个报警弹框
     */
    _renderAlarmButton() {
        return (
            <View key={"alarm_button"}>
                <TouchableOpacity
                    disabled={this.state.isSleep}
                    accessibilityLabel={"home_page_alarm"}
                    onPress={()=>{
                        if (!this._canStepIn()){
                            //设备状态错误时，不可点击
                            return;
                        }
                        //统计点击报警按钮的情况
                        IMILogUtil.uploadClickEventForCount("StreamingAlarm");
                        // if (this.state.isWarning){
                        //     this._doWarning();
                        // }else {
                        //     this.setState({showWarningDialog:true});
                        // }

                        if (!this.state.isWarning){
                            this.setState({showWarningDialog:true});
                        }
                    }}
                >
                    <Image style={[{width: 40, height: 40,opacity:this.state.isSleep?0.3:1}]}
                           source={this.state.isWarning?require('../../resources/images/newLive/icon_alarm_warning.png'):require('../../resources/images/newLive/icon_alarm_normal.png')}/>
                </TouchableOpacity>

            </View>
        )
    }

    /**
     * 开始、结束报警
     * 固件暂时不支持报警中关闭报警
     * 所以这里只有开启
     */
    _doWarning(){

        this.warningTimer && clearTimeout(this.warningTimer);
        let sound_alarm =  {switch: 1,name:"1-alarm"};
        let paramLight = [0,10,50,500,500];
        let light_alarm =  {switch: 1,param:JSON.stringify(paramLight)};
        let oneAlarm = {
            sound_alarm:JSON.stringify(sound_alarm),
            light_alarm:JSON.stringify(light_alarm),
        }
        let param = {OneKeyAttr: oneAlarm}
        let paramStr = JSON.stringify(param);
        IMILogUtil.setPropertyCloudWithUploadLog(paramStr).then(res => {
            this.warningTimer = setTimeout(() => {
                if (this.state.isWarning){
                    this.setState({isWarning: false});
                }
            }, 12000);

        }).catch(err => {
            console.log("开启报警失败",err,JSON.stringify(err));
            this.setState({isWarning: false});
        });
    }

    //点击控制聚光灯开关(竖屏下可以控制亮度滑动条的隐现)
    _onClickSpotlight(){
        if(!this._canStepIn())  return;

        this.homePageNewLivePlayerComponent._onClosePlayToolBar();

        let params = {
            oneKeyLighting: {
                switch: this.state.spotlightSwitch ? 0 : 1,
                brightness: this.state.spotlightBrightness
            }
        };

        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            this.setState({
                spotlightSwitch: !this.state.spotlightSwitch,
                spotlightSliderVisible: !this.state.spotlightSwitch
            },()=>{
                if (this.state.spotlightSwitch) {
                    //亮度调节组件出现5秒后无操作，自动变为半透明
                    this.toTransparentTimer && clearTimeout(this.toTransparentTimer);
                    this.toTransparentTimer = setTimeout(() => {
                        this.verticalSlider && this.verticalSlider.setTransparent();
                    }, 5000);
                }else{
                    this.toTransparentTimer && clearTimeout(this.toTransparentTimer);
                }
            });
        }).catch((err) => {
            showToast(stringsTo('operationFailed'));
        });
    };

    /*调节聚光灯亮度*/
    _adjustSpotlightBrightness(brightness){
        if(!this._canStepIn())  return;

        let params = {
            oneKeyLighting: {
                switch: this.state.spotlightSwitch ? 1 : 0,
                brightness: brightness
            }
        };

        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            this.setState({
                spotlightBrightness:brightness
            });
            //亮度调节组件5秒无操作，自动变为半透明
            this.toTransparentTimer && clearTimeout(this.toTransparentTimer);
            this.toTransparentTimer = setTimeout(()=>{
                this.verticalSlider && this.verticalSlider.setTransparent();
            },5000);
        }).catch((err) => {
            showToast(stringsTo('operationFailed'));
        });
    };

    /** 一键照明按钮
     * @params hide 隐藏按钮来到占位的作用
     */
    _renderSpotlightButton(hide){
        return (
            <View key={"spotlight_button"}>
                <TouchableOpacity
                    disabled={this.state.isSleep || hide}
                    accessibilityLabel={"home_page_spotlight"}
                    onPress={()=>{
                        if (isWait()){
                            return;
                        }
                        this._onClickSpotlight();
                    }}
                >
                    <Image style={[{width: 40, height: 40,opacity:this.state.isSleep?0.3:1,tintColor:hide?"transparent":null}]}
                           source={this.state.spotlightSwitch?require('../../resources/images/newLive/icon_liveSpotlight_on.png')
                               :require('../../resources/images/newLive/icon_liveSpotlight_off.png')}/>
                </TouchableOpacity>

            </View>
        )
    }


    _renderSliderView(isFullScreen) {
        if(!this.state.spotlightSliderVisible){
            return ;
        }

        return (
            <View style={{
                position: "absolute",
                right: isFullScreen?100:30,
                top:isFullScreen? 0:StatusBarHeight+40,
                height:isFullScreen? SCREEN_WIDTH :SCREEN_WIDTH*9/16,
                justifyContent:'center',
                zIndex: 999,
            }}>
                <VerticalSlider
                    ref={component => (this.verticalSlider = component)}
                    height={isFullScreen?200:140}
                    width={30}
                    min={5}
                    max={100}
                    step={1}
                    minimumTrackTintColor={"#1E5BA9"}
                    maximumTrackTintColor={'#FFFFFF'}
                    borderRadius={50}
                    value={this.state.spotlightBrightness}
                    onComplete={value => {
                        this.homePageNewLivePlayerComponent._onCloseFullScreenTools();
                        this._adjustSpotlightBrightness(value);
                    }}
                />
            </View>
        )
    }

    _renderSnapButton() {
        return (
            <View key={"snap_button"}>
                <TouchableOpacity
                    disabled={this.state.isSleep}
                    accessibilityLabel={"home_page_snap"}
                    onPress={()=>{
                        // console.log('截图点击')
                        // this.setState({isOnLine:false});
                        // return;
                        if (isWait()){
                            console.log("wait wait wait");
                            return;
                        }
                        // fastForWait(this.homePageNewLivePlayerComponent._onPressScreenShot())
                        this.homePageNewLivePlayerComponent._onPressScreenShot();
                    }}
                >
                    <Image style={[{width: 40, height: 40,opacity:this.state.isSleep?0.3:1}]}
                           source={require('../../resources/images/newLive/icon_liveScreen.png')}/>
                </TouchableOpacity>

            </View>
        )
    }

    _renderCallButton() {
        return (
            <View key={"call_button"}>
                <TouchableOpacity
                    disabled={this.state.isSleep?true:false}
                    accessibilityLabel={this.state.isCalling?"home_page_phone_off":"home_page_phone_on"}
                    onPress={()=>{
                        console.log('通话点击');
                        if (this.state.isCallingStatue){
                            return;
                        }
                        if(!this._canHang())  return;
                        IMILogUtil.uploadClickEventForCount("VoiceCall");
                        isCheckingPermission = true;
                        checkPermissionForFullScreen = true;
                        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                            if (status === 0) {
                                this._doCall();
                                isCheckingPermission = false;
                            } else if (status === -1) {
                                isCheckingPermission = false;
                                showToast(stringsTo('audio_permission_denied'))
                            }
                        })
                    }}
                >
                    <Image style={[{width: 50, height: 50,opacity:this.state.isSleep?0.3:1}]}
                           source={this.state.isCalling?require('../../resources/images/newLive/icon_calling_new.png'):require('../../resources/images/newLive/icon_liveCall.png')}/>
                </TouchableOpacity>

            </View>
        )
    }

    _renderRecordButton() {
        return (
            <View key={"record_button"}>
                <TouchableOpacity
                    disabled={this.state.isSleep?true:false}
                    accessibilityLabel={this.state.recording?"home_page_record_off":"home_page_record_on"}
                    onPress={()=>{
                        console.log(' 录像点击')
                        this.homePageNewLivePlayerComponent._onPressRecord();
                    }}
                >
                    <Image style={[{width: 40, height: 40,opacity:this.state.isSleep?0.3:1}]}
                           source={this.state.recording?require('../../resources/images/newLive/icon_liveRecording.png'):require('../../resources/images/newLive/icon_liveRecord.png')}/>
                </TouchableOpacity>

            </View>
        )
    }

    _renderCloudButton() {
        return (
            <View key={"cloud_button"}>
                <TouchableOpacity
                    accessibilityLabel={"home_page_cloud"}
                    onPress={()=>{
                        if (this._isShareUser()) return;
                        if(!this._canStepInCall())  return;
                        IMILogUtil.uploadClickEventForCount("CloudBuy");
                        if (this.state.vipState == -1){
                            this.getVipState();
                        } else{
                            this.goToClound();
                        }
                    }}
                >
                    <Image style={[{width: 40, height: 40,}]}
                           source={require('../../resources/images/newLive/icon_liveCloud.png')}/>
                </TouchableOpacity>

            </View>
        )
    }


    /*绘制摇杆式云台View*/
    renderPtzControlView(){
        let {showAllPanoramara} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model); //是否显示圆盘
        console.log('是否显示全景绘图---panoramic',showAllPanoramara,this.state.isPanoramic);
        //获取屏幕高度
        let marginTopValue = 30;
        if (SCREEN_HEIGHT < 700){
            marginTopValue = 15;
        }
        let diameterPan = 206;
        let diameterMid = 30;
        if (SCREEN_HEIGHT < 600){
            diameterPan = 150;
            diameterMid = 30;
        }
        return (
            // <View style={{backgroundColor:'red',top:30}}>
            <View
            style={{
                // flexGrow: 1,
                width: "100%",
                flexDirection: "column",
                display: "flex",
                marginTop: marginTopValue,
                paddingRight: 14,
                paddingLeft: 14,
                alignItems: "center",
                // justifyContent: "center",
                backgroundColor:"#F7F7F7",
        }}>
            {this.state.isPanoramic?<PanoramicView
                Type={this.state.panoramicType}
                isShowNewPanoramic={true}
                accessibilityLabel={"show_all_panorama"}
                onTouch={(pointX) => {
                    this.selectPositionX = pointX;
                    ptz_pressedPanoramic = false;
                    // console.log(" _setPanoramaRotateAngle h=",this.selectPositionX,",v=",this.selectPositionY);
                    this._doDirectionPanorama(this.selectPositionX,50)
                }} onLoosen={() => {}}
                startPanoramic={()=>{
                    if(!this._canStepIn())  return;
                    LetDevice.sendDeviceServerRequest("PanoramaEnableComposition",{}).then((data) => {
                        this.setState({panoramicType:1});
                        this.timer&&clearTimeout(this.timer);
                        this.intervalID&&clearInterval(this.intervalID);//清除
                        this._panoramicTimeout();
                    }).catch((error) => {
                    });
                }}
                endPanoramic={()=>{
                    // if (this.state.panoramicType == 1){
                    //     showToast(stringsTo('panoramicing_tip'));
                    //     return;
                    // }
                    this.setState({isPanoramic:false});}}
                selectPositionX={this.selectPositionX} minSelectPositionLeft={0} maxSelectPositionRight={SCREEN_WIDTH-28} leftValue={this.state.leftValue} rightValue={this.state.rightValue} imgStoreUrl={this.state.overAllImgStoreUrl}/>:
                <JoystickControlView
                onMove={ (type) => {
                    if(!this._canStepIn())  return;
                    ptz_pressed = false;
                    clearInterval(this.setPTZIntervalID);
                    this._doDirection(type);
                } }
                onLoosen={ () => {
                    ptz_pressed = false;
                    clearInterval(this.setPTZIntervalID);
                    // this._getMotorPositonStatus();
                } }
                isFullscreen={ false}
                isNewBgImg={true}
                diameterPan={ diameterPan }
                diameterMid={ diameterMid }/>}
            </View>);
    }

    //
    _panoramicTimeout(){
        this.timer = setTimeout(() => {
                let times = 0;
                this.intervalID = setInterval(() => {
                    times = times + 1;
                    if(times > 7){//rcp20秒后才开始拉取图片，每10秒拉一次6次即1分钟+20秒后还拉不到图片，认为失败了

                        this.intervalID&&clearInterval(this.intervalID);//清除
                        this._getMergePhotoMeta(true);
                        return;
                    }
                    //获取全景图信息
                    this._getMergePhotoMeta(false);
                }, 10000);
            },
            20000
        );
    }

    _getMergePhotoMeta(isLast){
        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
            console.log('-------------'+res.url);
            if (!isLast&&parseInt(res.status)==2){
                this.setState({panoramicType:parseInt(res.status),tempPanoramicType:parseInt(res.status),overAllImgStoreUrl:res.url});
            }
            if (isLast){
                if (parseInt(res.status)!=2) {
                    showToast(stringsTo('operate_time_out'));
                }
                let  isEnabledOpen = parseInt(res.status)==2?true:false;
                //parseInt(res.status)==1||parseInt(res.status)==0?3: 根据后台状态显示
                // this.setState({panoramicType:3});
                this.setState({panoramicType:(!isEnabledOpen&&res.timeOutFlag)?3:parseInt(res.status),tempPanoramicType:(!isEnabledOpen&&res.timeOutFlag)?3:parseInt(res.status),overAllImgStoreUrl:res.url});
            }
        }).catch(error=>{
            if (isLast){
                this.setState({panoramicType:3,tempPanoramicType:3});
            }
        });
    }


    /*控制云台转动*/
    _doDirection(m_direction){
        let type= !this.state.isPanoramic&&this.state.panoramicType==0||this.state.panoramicType==1;
        if (type){
            showToast(stringsTo('Panoramic_loading'));
        }
        ptz_pressed = true;

        this._checkDirection(m_direction);
        if(m_direction !== 5) {
            // 每100毫秒一次操作
            clearInterval(this.setPTZIntervalID);
            this.setPTZIntervalID = setInterval(() => {
                if (!this._checkDirection(m_direction)) {
                    clearInterval(this.setPTZIntervalID);
                }
            }, 100);
        }else{
            ptz_pressed = false;
        }

    }

    /*判断当前是否可操作*/
    _checkDirection(m_direction){

        console.log("_checkDirection m_direction="+m_direction+",ptz_pressed="+ptz_pressed);

        if(ptz_pressed){
            this.sendDirectionCmd(m_direction);
        }

        return ptz_pressed;
    }

    /*发送控制云台的指令*/
    sendDirectionCmd(m_direction) {
        let paramsJSON = {ActionType:m_direction,Step:0};
        LetDevice.sendDeviceServerRequest("PTZActionControl", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------' +m_direction+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    /*控制云台转动*/
    _doDirectionPanorama(h,v){
        if (!this._checkDirectionPanorama(h,v)) {
            ptz_pressedPanoramic = true;
            this._checkDirectionPanorama(h,v);
        }
    }

    /*判断当前是否可操作*/
    _checkDirectionPanorama(h,v){

        console.log("_checkDirection m_direction="+h+",ptz_pressed="+ptz_pressed);

        if(ptz_pressedPanoramic){
            this._setPanoramaRotateAngle(h,v);
        }

        return ptz_pressedPanoramic;
    }
    _setPanoramaRotateAngle(h,v){

        let paramsJSON = {position:"["+h+','+v+"]"};

        LetDevice.sendDeviceServerRequest("PanoramSlide", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------'+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    // 获取转动状态
    _getMotorPositonStatus(){
        LetDevice.getPropertyCloud('MotorPositon').then((data) =>{ //0休眠 1关闭
            console.log('MotorPositon--------' + data,typeof(data));
            if (data.substr(0,3)=='-1,'){
                showToast(stringsTo('direction_end_009'));
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    /*监听直播流播放状态*/
    _onLivePlayerStatusChangeListener(status){
        // console.log('直播流状态---',status);
        if (status == HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
            this.IMIVideoView.start();
            console.log("sdstatus",this.state.sdCardStatus);
            if (this.state.sdCardStatus == 1){
                //开始优先请求回看
                DateHasManager.fetchSDDateDataV2(null);
            }
        }else if(status == HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE||status==HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR){
           this.setState({showNormalNav:true});
            //ios在海外服务器，实时页切到其他页面，再返回实时页，直播会卡住
            //排查发现：直播页在切换其他页面，播放器调用stop方法后，存在无法正常停止播放，或者说停止播放的响应时间很长
            //这时切回到实时页，prepare播放实时流，无法正常开启实时流播放
            //在start一断时间后，eventChange会收到99007也就是stop的停止回调，此时我们再次启用播放流程，可正常加载实时流
            //所以添加这块内容
            if (isIos() && status == HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE
                && this.isForegroundPage && this.iosRetryCount < 1
                && !this.homePageNewLivePlayerComponent.getErrorStatus()){
                this.iosRetryCount = 1;
                this._iosRetry()
            }else {
                this._dealWithErrorOrPause();
            }
        }else if (status == HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            this.setState({isOnline:true});
        }
        if (status != HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) this.setState({currentStatus: status});

    }
    _iosRetry(){
        if (this.state.isCalling && this.IMIVideoView) {
            GlobalUtil.isCalling = false;
            this.setState({isCalling: false,
                isCallingStatue:false,});
            this.IMIVideoView.stopSpeak();
            audioNeedRecovery&&this.homePageNewLivePlayerComponent.setMute(true)
        }
        this.getNetWork();
    }
    _dealWithErrorOrPause(){

        //直播流出错或暂停时，停止通话
        if (this.state.isCalling && this.IMIVideoView) {
            GlobalUtil.isCalling = false;
            this.setState({isCalling: false,
                isCallingStatue:false,});
            this.IMIVideoView.stopSpeak();
            audioNeedRecovery&&this.homePageNewLivePlayerComponent.setMute(true)
        }
        !isCheckingPermission&&this.IMIVideoView && this.IMIVideoView.stop();

    }

    //通话时的波纹动画
    _renderCallWaveView(){
        if (this.state.fullScreen) {
            return null;
        }

        if (!this.state.isCalling) {
            return null;
        }
        //获取屏幕高度
        let topValue = 30;
        let waveHeight = 36;
        if (SCREEN_HEIGHT < 700){
            topValue = 20;
            waveHeight = 30;
        }

        if (SCREEN_HEIGHT < 600){
            topValue = 10;
            waveHeight = 25;
        }
        return (<View style={{
                width: SCREEN_WIDTH,
                top: topValue,
                position:'absolute'
            }}>
                <WaveView
                    waveHeight={waveHeight}
                    waveWidth={SCREEN_WIDTH}/>
            </View>
        );

    }
    //通话时的波纹动画
    _renderCallWaveNoPanoramicView(){
        if (this.state.fullScreen) {
            return null;
        }

        if (!this.state.isCalling) {
            return null;
        }

        return (<View style={{
                width: SCREEN_WIDTH,
                top:90,
                // bottom: 83,
                position: "absolute",
            }}>
                <WaveView
                    waveHeight={36}
                    waveWidth={SCREEN_WIDTH}/>
            </View>
        );

    }
    //休眠提示
    _sleepView(isFullScreen) {
        if (!this.state.isOnline)return null;
        if (!this.state.isSleep) return null;
        return (
            <TouchableWithoutFeedback onPress={_=>{
                this.homePageNewLivePlayerComponent && this.homePageNewLivePlayerComponent._doToolsShowHide();
            }}>

            <View
                   style={{
                       position: "absolute",
                       width: "100%",
                       // height: "100%",
                       top:isFullScreen? 0:StatusBarHeight+50,
                       height:isFullScreen? "100%":SCREEN_WIDTH*9/16,
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                       backgroundColor: "#000",
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={stringsTo('power_off')}
                />

                <RoundedButtonView buttonText={stringsTo('wake_up')}
                                   buttonStyle={{
                                       margin: 14,
                                       width: 100,
                                       height: 40,
                                       borderRadius:7,
                                   }}
                                   onPress={() => {
                                       if (this._isShareUser()) return;
                                       LetDevice.propertyOn("SleepStatus").then(()=>{
                                           this.setState({isSleep: false},()=>{
                                               //解决休眠后，展示播放暂停按钮，再去显示loading
                                               this.homePageNewLivePlayerComponent && this.homePageNewLivePlayerComponent.setLoadingView(true);
                                               this.IMIVideoView && this.IMIVideoView.prepare();
                                           });
                                       }).catch(err=>{
                                           this.setState({isSleep:true});
                                           showToast(I18n.t('operationFailed'));
                                       });
                                   }}
                                   accessibilityLabel={"home_page_video_dormancy"}
                />
            </View>
            </TouchableWithoutFeedback>
        );
    }

    //离线提示
    _deviceOffLineView() {
        if (this.state.isOnline) return null;
        if (HomePageNewLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING==this.state.currentStatus) return null;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       zIndex:2,
                       width: "100%",
                       // height:"100%",
                       top:StatusBarHeight+50,
                       height: SCREEN_WIDTH*9/16,
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                       flexDirection:"column",
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: "#FFFFFF",
                    fontSize: 15,
                }}
                       text={stringsTo("device_offline")}
                />
            </View>
        );
    }

    _isShareUser(){
        if (LetDevice.isShareUser){
            showToast(stringsTo('shareUser_tip'));
            return true;
        }
        return false;
    }

    /**
     * 这个方法是在T卡状态为未插卡状态时
     * 再去获取一次T卡状态，只能减少T卡状态获取失败
     * 不能规避
     */
    confirmSDCardState(){
        console.log("---------")
        showLoading(stringsTo('commLoadingText'),true);
        LetDevice.getPropertyCloud('StorageStatus').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
            console.log("---------"+value)
            showLoading(false);
            value = parseInt(value);

            this.setState({sdCardStatus: value});
            this.sdcardAction(value);
        }).catch(error => {
            console.log("---------"+error)
            showLoading(false);
            this.sdcardAction(this.state.sdCardStatus);
        });
    }
    //点击sdcard后的动作
    sdcardAction(value){
        if (value == 0) { //未插卡
            this.props.navigation.push('PlayBackNoSDCardSettingPage');
        } else if (value == 4) { //存储卡已弹出\n请重新插拔存储卡!
            showToast(stringsTo('sdcard_error_out'));
        } else   if (value == 2)  { //正在格式化
            showToast(stringsTo('sdcard_status_abnormal_'));
        }else if (value == 1){
            if(!this._canStepInCall())  return;
            if (this.state.isSleep){
                showToast(stringsTo('power_off'));
                return ;
            }
            if (!this.state.isOnline){
                showToast(stringsTo('device_offline'));
                return;
            }
            this.props.navigation.push('PlayBackPage');
        }
    }
    getVipState(){
        showLoading(stringsTo('commLoadingText'),true);
        imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
            console.log("_getVipState"+res)
            let data = JSON.parse(res);
            let storageType=0;
            if (data.state==1){
                storageType=data.storageType;
            }
            showLoading(false);
            this.setState({vipState:data.state,storageType:storageType},()=>{
                this.goToClound();
            });
        }).catch(error=>{
            showLoading(false);
            console.log("_getVipState -2")
            this.setState({vipState:0},()=>{
                this.goToClound();
            });
        });

    }
    goToClound(){
        //VIP跳转到我的套餐页面  非VIP跳到购买页，需要提供跳个人套餐页方法
        if (this.state.vipState != 1){
            IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID,CLOUD_BUG,{page:"BuyPage"});
        }else {
            IMIGotoPage.startNativePageWithRouterCommon(LetDevice.deviceID,CLOUD_BUG,{page:"MainPage"});
        }
    }

    //进入相册
    goToAlbum() {
        let {showRNAlbum} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        if (showRNAlbum) {
            this.checkIMIPermission();
        } else {
            IMIGotoPage.startAlbumPage(LetDevice.deviceID);
        }
    }

    //  检查进入相册权限
    checkIMIPermission() {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
            if (status2 === 0) {
                // if (isAndroid() && IMIPackage.minApiLevel>=10006){
                //     this.props.navigation.push('CameraListPage');
                // }else if (isIos() &&IMIPackage.minApiLevel>=10005){
                //     this.props.navigation.push('CameraListPage');
                // }else {
                //     IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                // }
                if (IMIPackage.minApiLevel<10007){
                    IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                }else{
                    this.props.navigation.push('CameraListPage');
                }
            } else if (status2 === -1) {
                showToast(stringsTo('storage_permission_denied'));
            }
        });
    }

    render() {

        let {showPTZ,showCustomQuality,audioParams,showSDError,supportLensCorrect,productNotSupportLensCorrect} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model); //是否显示圆盘
        let sleepModeIndex = this.state.isSleep ? 1 : 0;
        let sleepBtn = {
            isText: false,
            data: [require("../../../../imi-rn-commonView/PlayerToolBarView/res/icon_live_sleep.png"),require("../../../../imi-rn-commonView/PlayerToolBarView/res/icon_live_sleep.png")],
            onPress: this._onClickSleepButton,
            disabled: showPTZ?this.state.isCallingStatue:false,
            dataIndex: sleepModeIndex,
            accessibilityLabel: ["home_page_dormancy_on", "home_page_dormancy_off"]
        };
        //摄像机默认清晰度：流畅、标清、高清，如有自定义，请在camera/configProject中配置
        let qualityDataTitle=[{title:stringsTo("quality_low"),index:0,accessibilityLabel:"home_page_clarity_show_low"},
            {title:stringsTo("quality_sd"),index:1,accessibilityLabel:"home_page_clarity_show_sd"},
            {title:stringsTo("quality_fhd"),index:2,accessibilityLabel:"home_page_clarity_show_fhd"}];
        if (showCustomQuality){
            qualityDataTitle = showCustomQuality
        }
        //固件不支持 自动 0 {title:'高清',index:2},
        let dataSource = {
            playerClass: IMICameraVideoView.PlayerClass.LIVE,
            did: LetDevice.deviceID
        };
        if (audioParams){
            dataSource['audioParams'] = audioParams;
        }
        return (
        <View style={{flex:1}}>
                <HomePageNewLivePlayerComponent {...this.props}
                                             ref={component => (this.homePageNewLivePlayerComponent = component)}
                                             videoRef={ref => this.IMIVideoView = ref}
                                             qualityData={qualityDataTitle}
                                             navBar={(bps, isFullScreen) => this._renderNavigationBar(bps, isFullScreen)}
                                             toolBarMoreItems={[{item: sleepBtn, insertIndex: 0}]}
                                             videoSubView={(isFullScreen,showFullScreenTools) => this.renderVideoSubView(isFullScreen,showFullScreenTools)}
                                             onLivePlayerStatusChange={(status)=>this._onLivePlayerStatusChangeListener(status)}
                                             lensCorrect={{use: ((supportLensCorrect || productNotSupportLensCorrect)?false:this.state.lensCorrect), x:this.state.oSDSwitch? lensCorrect_x:0, y: this.state.oSDSwitch?lensCorrect_y:0}}
                                             dataSource={dataSource}
                                             isSleepStatus={this.state.isSleep}
                                             isMove={this.state.isMoveStatus}
                                             albumName={LetDevice.deviceID}
                                             isOnLine={this.state.isOnline}
                                             isCalling={this.state.isCalling}
                                             isShowPanoramicView={showPTZ}
                                             netConnected={this.state.netConnected}//网络连接
                                             isDataUsage={this.state.isDataUsage}
                                             isCallingStatue={this.state.isCallingStatue}
                                             onHideFullScreenTool={(value)=>{
                                                 //全屏的时候，在转动云台的时候，这时候点击使云台消失
                                                 //这个时候云台的定时触发没有销毁,会一直下发电机转动请求
                                                 this.setPTZIntervalID && clearInterval(this.setPTZIntervalID);
                                             }}
                                             onGetQualityIndex={(value)=>{
                                                 console.log('当前清晰度选择',value);
                                                 this.setState({qualityDataIndex:value});
                                             }}
                                             onGetRecordStatus={(value)=>{
                                                 console.log('录像状态--',value);
                                                 this.setState({recording:value});
                                             }}
                                             onGetZoomScale={(value)=>{
                                                 console.log('实时页zoomScale--',value);
                                                 this.setState({playerZoomScale:value})
                                             }}

                                             onCheckPermissionStatusChange={(value)=> {
                                                 isCheckingPermission = value
                                                 if (value){
                                                     checkPermissionForFullScreen = true;
                                                 }
                                             }}
                                             onVoiceCallErrorOccurred={(code) =>{
                                                 //两部手机通话互相占用
                                                 // if (code==12||code==-88002){
                                                 //
                                                 // }
                                                 this._doCallClose(false);
                                             }}
                                                onLiveStatusErrorChange={()=>{
                                                    //实时流播放中出现错误，这个回调主要是用来处理，如果在绘制全景图的过程中出现错误
                                                    //这里直接做不太合理，设备和云端可能还在绘制，这里提示绘制失败，有可能云端与设备实际会绘制成功
                                                    //不能以播放器的播放状态，代表绘制状态
                                                    // if (this.state.panoramicType == 1){
                                                    //     //处于正在绘制中
                                                    //     showToast(stringsTo("fail_panoramic_try_later"));
                                                    //     this.intervalID&&clearInterval(this.intervalID);//清除
                                                    //     this.setState({panoramicType:this.state.tempPanoramicType})
                                                    // }

                                                }}
                >
                    {this.state.isShowAngle ? this._renderAngel():this._renderPortraitToolBarView()}
                </HomePageNewLivePlayerComponent>

                {this._renderExpiredCountdownHintDialog()}
                {/*{this._renderGiftDialog()}*/}
                {this._renderNewGiftDialog()}
                {this.deleteAreaDialog()}
                {this._renderMsgDialog()}
                {this._renderAlarmDialog()}
                {showSDError ?this._renderMicroSDCardAbnormalDialog():null}
            </View>


        );
    }
     // 显示激活角度弹窗/失效弹窗
    _renderMsgDialog() {
        return (
            <MessageDialog
                showTitle={false}
                visible={this.state.showMsgDialog}
                containerStyle={{marginBottom:15}}
                message={this.state.msgTitle}
                messageStyle={{textAlign: 'center'}}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            this.setState({
                                showMsgDialog: false,
                            });
                        }
                    }
                ]}
            >
            </MessageDialog>
        );
    }

    // 删除重点区域弹窗
    deleteAreaDialog(){
        return(<AlertDialog
            showTitle={false}
            visible={this.state.showDeleteDialog}
            message={stringsTo('deleteAreaTit')}
            messageStyle={{
                marginTop: 20,
                marginBottom: 26,
                fontSize: 17,
                fontWeight: '700',
                color: "#333333"
            }}
            showNewDialogStyle={true}
            ignoreHint={this.state.isNeverRemind}
            onCheckValueChange={(checked) => {
                this.setState({isNeverRemind: checked});
            }}
            canDismiss={false}
            onDismiss={() => {
                this.setState({showDeleteDialog: false});
            }}
            buttons={[
                {
                    text: stringsTo('cancel'),
                    callback: _ => {
                        this.setState({showDeleteDialog: false,deleteIndex:-1});
                    }
                },
                {
                    text: stringsTo('ok_button'),
                    callback: _ => {
                        this.setState({showDeleteDialog:false,},()=>{
                            for (let i=0;i<this.state.areaArrData.length;i++){
                                let item = this.state.areaArrData[i];
                                if (item.index == this.state.deleteIndex){
                                    this.deleteAreaNew(item);
                                }
                            }
                        });
                    }
                },
            ]}
        />);
    }
    _renderAlarmDialog(){
        return(<AlertDialog
            showTitle={false}
            visible={this.state.showWarningDialog}
            message={stringsTo('start_light_sound_alarm_tx')}
            messageStyle={{
                marginTop: 20,
                marginBottom: 26,
                fontSize: 14,
                fontWeight: '400',
                color: "#333333"
            }}
            showNewDialogStyle={true}
            ignoreHint={this.state.isNeverRemind}
            onCheckValueChange={(checked) => {
                this.setState({isNeverRemind: checked});
            }}
            canDismiss={false}
            onDismiss={() => {
                this.setState({showWarningDialog: false});
            }}
            buttons={[
                {
                    text: stringsTo('cancel'),
                    callback: _ => {
                        this.setState({showWarningDialog: false});
                    }
                },
                {
                    text: stringsTo('ok_button'),
                    callback: _ => {
                        this.setState({showWarningDialog:false,isWarning:true},()=>{
                            this._doWarning()
                        });
                    }
                },
            ]}
        />);
    }

    /*查询和计算云存会员还有几天到期*/
    _getCloudExpiredRemainDays(){
        if(LetDevice.isShareUser){
            return;
        }
        let params = {
            Path: 'api/app/storage/expireTime',
            ParamMap: {
                model:LetDevice.model,
                deviceId: LetDevice.deviceID,
            }
        };
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {

            let cloudExpireDate = JSON.parse(data).expireTime;
            let remainDays = null;

            let currentStamp = moment().unix()*1000;


            if (cloudExpireDate != 0) { //为0则是没开通云存获者是重绑机器了

                cloudExpireDate = moment(parseInt(cloudExpireDate));
                remainDays = cloudExpireDate.diff(moment(), 'day');
                if (cloudExpireDate <= currentStamp) //过期了则剩余天数就是负数
                    remainDays = -100;
                this._getLocalStorage(remainDays);
                // this.setState({
                //     cloudExpireRemainDays: remainDays,
                //     expiredDialog: remainDays != null && remainDays >= 0 && remainDays < 7,
                // }, () => {
                //     console.log("ggggggggg-----"+this.state.cloudExpireRemainDays);
                // });
            } else { //如果为0可能是0从未购买、1使用中、2已过期
                // this.getCloundStorageStatus()
            }


        }).catch((error) => {
            console.log('查询云存过期时间-- error ',error)
        });

    }

    // 获取云存储相关信息
    //获取是否可以领取免费云存
    getFreeCloudStorageInfo() {

        if(LetDevice.isShareUser){
            return;
        }

        let params = {
            Path: 'api/app/storage/free',
            ParamMap: {
                deviceId: LetDevice.deviceID,
                model: LetDevice.model
            }
        };

        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // alert('免费云存数据--'+JSON.stringify(data));
            console.log(JSON.stringify(data));
            let result = JSON.parse(data);
            if(result.able){
                this.setState({
                    giftDialog:true,
                    giftName:result.plan.planName,
                    giftPrice:result.plan.marketPrice,
                    giftId:result.plan.id,
                    deviceName:result.deviceName,
                    currency: result.currency,//区分人民币和美元
                });
            }

        }).catch((error) => {
            console.log('推送数据获取错误--',error);
            console.log('sendUserServerRequest error ' + error)
        });
    }

    getSDCardStatus(isEvery=false){

        LetDevice.updateAllPropertyCloud().then((data)=>{
            console.log("all data",data);
            let dataObject = JSON.parse(data);
            //获取下卡状态
            //一般情况下默认卡状态正常
            let value = 1;
            if (dataObject.StorageStatus){
                value = parseInt(dataObject.StorageStatus.value);
            }
            if (value == 0){
                //无卡，后续不需要操作
                if (this.state.SDCardAbnormalDialog || this.state.showTargetPushView){
                    this.setState({SDCardAbnormalDialog:false,showTargetPushView:false});
                }
                return;
            }
            //新SD卡录制方案
            //考虑到没有导入此方案的其他项目，NeedFormatStorageMedia默认为0
            //目前需要导入的项目040A03 060A01
            //1、自动弹框提示，只弹出一次，后续不再弹出
            //2、实时流上面未格式化、异常情况时一直显示
            let needFormatValue = NeedFormatType.NORMAL;
            if (dataObject.NeedFormatStorageMedia){
                needFormatValue = dataObject.NeedFormatStorageMedia.value;
                //needFormatValue 等于1或者2的时候
            }
            this.dealWithSDData(needFormatValue,value,isEvery);

        }).catch(error=>{

        });


        // LetDevice.getPropertyCloud('StorageStatus').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
        //     console.log('SD卡getPropertyCloud---------' + value + typeof (value));
        //     value = parseInt(value);
        //     this.setState({sdCardStatus: value});
        //     if (value == 2 || value == 5)  { //正在格式化
        //         if (isEvery){
        //             this.setState({
        //                 SDCardAbnormalDialog:true,
        //             });
        //         }
        //     }
        // }).catch(error => {
        //     console.log(JSON.stringify(error))
        // });
    }
    async dealWithSDData(needFormatValue,value,isEvery){
        console.log("deaWithSDData",needFormatValue,value,isEvery);
        //标记是否已经显示过异常SD卡的弹框，显示过了，每次进来不在弹框
        //实时流上的样式需要显示showTargetPushView
        let isShowSdDialog = await this.dealSDAndShow();
        if (needFormatValue == NeedFormatType.NORMAL){
            //走老的逻辑，看看卡状态是否异常就行
            if (value == 5)  {
                //异常卡，弹提醒
                this.targetpushItem = "error";
                this.setState({
                    sdMessage:stringsTo('sd_card_abnormal_massage'),
                    SDCardAbnormalDialog:!isShowSdDialog,
                    showTargetPushView:isShowSdDialog,
                });
            }else {
                this.setState({
                    SDCardAbnormalDialog:false,
                    showTargetPushView:false,
                });
            }
        }else {
            if (needFormatValue == NeedFormatType.NO_INDEX && value != 3){
                //没有索引
                this.targetpushItem = "noIndex";
                this.setState({
                    sdMessage:stringsTo('sd_need_format_no_index_massage'),
                    SDCardAbnormalDialog:!isShowSdDialog,
                    showTargetPushView:isShowSdDialog,
                });
            }else if (needFormatValue == NeedFormatType.HAS_OLD_INDEX && value != 3){
                //有老索引
                this.targetpushItem = "oldIndex";
                this.setState({
                    sdMessage:stringsTo('sd_need_format_has_old_index_massage'),
                    SDCardAbnormalDialog:!isShowSdDialog,
                    showTargetPushView:isShowSdDialog,
                });
            }
        }
    }
    /**
     * 根据SD卡的状态及显示情况去具体展示对应数据
     *
     */
    async dealSDAndShow(){
        return new Promise((resolve, reject) => {
            IMIStorage.load({
                key: LetDevice.deviceID+'showSDAbnormalDialog',
                autoSync: true,
                syncInBackground: true,
            }).then(res => {
                //布尔类型
                console.log("dealSDAndShow",res.showSdDialog);
                resolve(res.showSdDialog);
            }).catch(_=> {
                resolve(false)
            });
        });

    }
    getStorageStatusInfo2(needFormatValue){
        LetDevice.getPropertyCloud('StorageStatus').then((value) => {
            value = parseInt(value);
            this.setState({sdCardStatus: value,needFormatValue:needFormatValue});
            if (value != 3)  {
                //非正在格式化
                if (needFormatValue == NeedFormatType.NO_INDEX){
                    //没有索引
                    this.targetpushItem = "noIndex";
                    this.setState({
                        sdMessage:stringsTo('sd_need_format_no_index_massage'),
                        SDCardAbnormalDialog:true
                    });
                }else if (needFormatValue == NeedFormatType.HAS_OLD_INDEX){
                    //有老索引
                    this.targetpushItem = "oldIndex";
                    this.setState({
                        sdMessage:stringsTo('sd_need_format_has_old_index_massage'),
                        SDCardAbnormalDialog:true
                    });
                }
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    getStorageStatusInfo(){

        LetDevice.getPropertyCloud('StorageStatus').then((value) => {
            console.log('getPropertyCloud---------' + value + typeof (value));

            value = parseInt(value);

            if (value == 0) { //未插卡
                this.intervalQuerySD && clearInterval(this.intervalQuerySD);
                this.setState({sdCardStatus: value});

                if (this.isStartFormat){
                    showLoading(stringsTo('storageCardFormating'), false);
                    this.isStartFormat = false;
                    showToast(stringsTo('sdcard_format_fail'));
                }
                //无SD卡
                if (this.state.SDCardAbnormalDialog){
                    //监听到无卡，并且首页弹出了异常弹框，可能是SD卡异常、可能是SD卡未格式化弹框
                    this.setState({SDCardAbnormalDialog:false})
                }

            } else if (value == 1 || value == 2) { //正常使用中 和 未格式化(格式化失败为此状态)
                showLoading(stringsTo('storageCardFormating'), false);

                if (value == 1 && this.isStartFormat ) { //发送格式化指令后，轮询出卡状态正常
                    showToast(I18n.t("sdcard_format_success"));
                }

                if (value == 2 && this.isStartFormat ) { //发送格式化指令后，轮询出状态为未格式化，则格式化失败
                    showToast(I18n.t("sdcard_format_fail"));
                }

                this.intervalQuerySD && clearInterval(this.intervalQuerySD);
                this.isStartFormat = false;
                IMIStorage.save({
                    key: LetDevice.deviceID + 'sdCardStatus',
                    data: {
                        sdCardStatus: value
                    },
                    expires: null,
                })
            } else if (value == 3) { //正在格式化
                this.isStartFormat = true;
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    // 未领取云存 记录当前时间
    saveAlertDayTime() {
        let nowTime = Date.parse(new Date());


        // let endDate = new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 23, 59, 59);
        // let endTime = parseInt(endDate.getTime());
        // console.log('今天的日期最后--',endDate,endTime);

        let newDate = new Date(( new Date()).getTime()+1000*60*60*24);

        let tempNewDate = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 0, 0, 0);
        let startTime = parseInt(tempNewDate.getTime());

        IMIStorage.save({
            key: LetDevice.deviceID+'freeDay',
            data: {
                nextStartTime:startTime
            },
            expires: null,
        });
    }
    // 云存储提示
    getCloudInfo() {

        this._getCloudExpiredRemainDays();
        // this._getLocalStorage();
        this.getFreeDaySave();
        // 获取云存储相关信息
        this.getFreeCloudStorageInfo();
    }


    getFreeDaySave(){

        IMIStorage.load({
            key: LetDevice.deviceID+'freeDay',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {

            let oldTime = res.nextStartTime;
            let newTime = Date.parse(new Date());
            // let tempTime = newTime - oldTime;
            // let oneDayTime = 24*60*60*1000

            // console.log('当前剩余时间',tempTime,oneDayTime);

            if (newTime >= oldTime){
                this.setState({
                    showFreeCloud:true
                },callback=>{
                    // console.log('显示',this.state.showFreeCloud);
                })
            }else {
                this.setState({
                    showFreeCloud:false
                },callback=>{
                    // console.log('不显示',this.state.showFreeCloud);
                })
            }
        }).catch(_=> {
            this.setState({
                showFreeCloud:true
            },callback=>{
                // console.log('报错这里zou',this.state.showFreeCloud);
            })
        });
    }
    /*领取免费云存提示对话框*/
    _renderGiftDialog(){

        if (!this.state.showFreeCloud){
            return null;
        }
        return (<GiftDialog
                visible={this.state.giftDialog}
                resource={require("../../resources/images/pic_gift_box.png")}
                giftName={this.state.giftName}
                giftPrice={this.state.giftPrice}
                currency={this.state.currency}
                canDismiss={false}
                onDismiss={()=>{
                    this.setState({giftDialog:false});
                    this.saveAlertDayTime();
                }}
                onPress={()=>{
                    //快速点击，会出现多次领用云存的问题，
                    //改为giftDialog状态成功后再去更新云端值,降低出错
                    //this.gainFreeCloudStorage();
                    this.setState({giftDialog:false},()=>{this.gainFreeCloudStorage()});
                }}
                onPressCloseDialog={()=>{
                    this.setState({giftDialog:false});
                    this.saveAlertDayTime();
                }}


            />
        );
    }

    /*领取免费云存提示对话框 --- 新版*/
    _renderNewGiftDialog(){
        if (!this.state.showFreeCloud){
            return null;
        }
        return (<NewGiftDialog
                visible={this.state.giftDialog}
                resource={require("../../resources/images/newFreeCloudBg.png")}
                giftName={this.state.giftName}
                giftPrice={this.state.giftPrice}
                currency={this.state.currency}
                canDismiss={false}
                onDismiss={()=>{
                    this.setState({giftDialog:false});
                    this.saveAlertDayTime();
                }}
                onPress={()=>{
                    //快速点击，会出现多次领用云存的问题，
                    //改为giftDialog状态成功后再去更新云端值,降低出错
                    //this.gainFreeCloudStorage();
                    this.setState({giftDialog:false},()=>{this.gainFreeCloudStorage()});
                }}
                onPressCloseDialog={()=>{
                    this.setState({giftDialog:false});
                    this.saveAlertDayTime();
                }}


            />
        );
    }

    //获取本地存储
    _getLocalStorage(remainDays) {
        //获取本地存储信息--浏览节点提醒是否不再弹窗提醒
        IMIStorage.load({
            key: LetDevice.deviceID+'cloudExpireRemainDaysLocal',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {

            // this.setState({cloudExpireRemainDaysLocal: res.cloudExpireRemainDaysLocal})
            this.setState({cloudExpireRemainDaysLocal: res.cloudExpireRemainDaysLocal,cloudExpireRemainDays: remainDays,
                expiredDialog: remainDays != null && remainDays >= 0 && remainDays < 7})
        }).catch(_=> {
            this.setState({cloudExpireRemainDaysLocal: null,cloudExpireRemainDays: remainDays,
                expiredDialog: remainDays != null && remainDays >= 0 && remainDays < 7})
        });
    }
    /*云存储即将过期的弹窗提醒*/
    _renderExpiredCountdownHintDialog(){
        //弹窗提示每天只提醒一次
        //条件不是同步设置，会导致弹框先出现后自动消失
        if(this.state.cloudExpireRemainDays == null || this.state.cloudExpireRemainDaysLocal!=null&&this.state.cloudExpireRemainDaysLocal==this.state.cloudExpireRemainDays) return null;
        return (<AlertDialog
                title={stringsTo('cloud_will_time_out_str')}
                visible={this.state.expiredDialog}
                message={I18n.t('cloud_time_out_effect_str')}
                canDismiss={false}

                buttons={[
                    {
                        text: I18n.t("know_button"),
                        callback: _ => {
                            this.setState({expiredDialog:false});
                            IMIStorage.save({
                                key: LetDevice.deviceID+'cloudExpireRemainDaysLocal',
                                data: {
                                    cloudExpireRemainDaysLocal: this.state.cloudExpireRemainDays
                                },
                                expires: null,
                            });
                        }
                    },
                    {
                        text: I18n.t('buy_again'),
                        callback: _ => {
                            this.setState({expiredDialog:false},()=>{
                                IMIGotoPage.starCloudBuyPage(LetDevice.deviceID)
                            });
                            IMIStorage.save({
                                key: LetDevice.deviceID+'cloudExpireRemainDaysLocal',
                                data: {
                                    cloudExpireRemainDaysLocal: this.state.cloudExpireRemainDays
                                },
                                expires: null,
                            });
                        }
                    },
                ]}
            />
        );
    }
    /*SD卡异常提示*/
    _renderMicroSDCardAbnormalDialog(){
        //弹窗提示每次进入都提示只提醒一次

        return (<AlertDialog
                showTitle={false}
                visible={this.state.SDCardAbnormalDialog && this.isForegroundPage}
                message={this.state.sdMessage}
                messageStyle={{marginVertical:10,marginHorizontal:15,fontSize:17,fontWeight:'500',color:"#333333"}}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("temporarily_not"),
                        callback: _ => {
                            IMIStorage.save({
                                key: LetDevice.deviceID+'showSDAbnormalDialog',
                                data: {
                                    showSdDialog: true
                                },
                                expires: null,
                            });
                            this.setState({SDCardAbnormalDialog:false,showTargetPushView:true});
                        }
                    },
                    {
                        text: I18n.t('formatting_btn'),
                        callback: _ => {
                            this.setState({SDCardAbnormalDialog:false},()=>{

                                LetDevice.sendDeviceServerRequest("FormatStorageMedium", JSON.stringify({})).then((data) => {
                                    //是否处于格式化中的标记位
                                    showLoading(stringsTo('storageCardFormating'), true,true);
                                    this.isStartFormat = true;
                                    //SD卡数据需要重新拉取
                                    GlobalUtil.needClearSDData = true;
                                    this.intervalQuerySD = setInterval(() => this.getStorageStatusInfo(), 3500);
                                }).catch((error) => {
                                    showToast(I18n.t("sdcard_format_fail"));
                                    showLoading(stringsTo('storageCardFormating'), false);
                                });
                                // this.props.navigation.push('SdCardNewPage');
                            });
                        }
                    },
                ]}
            />
        );
    }

    /**
     * SD卡点击暂不后，实时流上提示用户
     */
    _renderTargetPushView(isFullScreen = false) {
        if (!this.state.showTargetPushView) {
            return null;
        }
        let ViewStyle = {
            position: "absolute",
            width: "100%",
            // top: this._getTitleBarPortraitHeight() + 13,
            top: !isFullScreen ? this._getTitleBarPortraitHeight() + 13 : 13,
            alignItems: "center",
            justifyContent: "center"
        };
        let containerStyle = {
            position: "absolute",
            top: 0,
            alignItems: "center",
            justifyContent: "center",
            borderRadius: 10,
            borderWidth: 0.5,
            borderColor: "rgba(255,255,255,0.1)",
            flexDirection: "row",
            backgroundColor: "#ffffff",
            shadowRadius: 8,
            marginHorizontal: 20,
            shadowOpacity: 0.15,
            shadowOffset: { width: 0, height: 2 },
            shadowColor: "#ffffff"
        };
        let title = null;
        let titleh5 = null;
        let subtitle = null;
        switch (this.targetpushItem) {
            case "noIndex":
                title = stringsTo("targetPush_sdcard_format");
                titleh5 = `Native_SDCardFormatSetting`;
                subtitle = stringsTo("targetPushTitle_subtitle");
                break;
            case "oldIndex":
                title = stringsTo("sdcard_format_title_tips");
                titleh5 = `Native_SDCardFormatSetting`;
                subtitle = stringsTo("targetPushTitle_subtitle");
                break;
            case "error":
                //SD卡异常
                title = stringsTo("sdcard_status_error");
                titleh5 = `Native_SDCardFormatSetting`;
                subtitle = stringsTo("targetPushTitle_subtitle");
                break;
            default:
                console.log(`TargetPushError`);
        }
        title = `${ title } `;
        return (
            <View style={ViewStyle}>
                <View style={containerStyle}>
                    <Text style={{ fontSize: locales[0]?.languageCode.indexOf("zh") !==-1?12:10, color: "#4c4c4c", marginLeft: 5 }}> {`${title}`}
                        <Text style={{ fontSize: locales[0]?.languageCode.indexOf("zh") !==-1?12:10, color: "#4A70A5" }}
                              onPress={() => this._showTargetPush(titleh5)} >{subtitle}</Text>
                    </Text>
                    <TouchableHighlight onPress={() => {
                        this.setState({
                            showTargetPushView: false
                        });
                    }}>
                        <Image style={{ width: 22, height: 22, marginLeft: 8, marginRight: 9 }} source={require('../../resources/images/close.png')}></Image>
                    </TouchableHighlight>
                </View>
            </View>
        );
    }

    _showTargetPush(h5) {
        this.setState({ SDCardAbnormalDialog: true ,showTargetPushView: false});
    }
    _getTitleBarPortraitHeight() {
        let titleBarHeight = 53;
        titleBarHeight += StatusBarHeight;
        return titleBarHeight;
    }
    gainFreeCloudStorage() {
        this.paramMap = {
            deviceId: LetDevice.deviceID,
            model: LetDevice.model,
            planId: this.state.giftId + "",
            deviceName: this.state.deviceName
        };
        let params = {
            Path: 'api/app/storage/gain',
            ParamMap: this.paramMap
        };

        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {

            showToast(stringsTo("get_success"))
        }).catch((error) => {

            console.log('领取免费云存数据-- error ' , error)
        });
    }
}


const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex",
        width: "100%",
        height: SCREEN_WIDTH*60/360.0+IphoneXSeries,//IphoneX底部遮挡上移
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center",
    },
    bottomLayoutItem: {
        flex: 1,
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        marginTop: 15,
    },
    bottomLayoutItemNoCall: {
        flex: 1,
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
    callWarning:{
        fontSize:15,
        color:'#333333',
        marginTop:24
    }
});
