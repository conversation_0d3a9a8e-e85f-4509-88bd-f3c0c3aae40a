import React, {Component} from 'react';
import {
    Image,
    View,
    Dimensions,
    TouchableWithoutFeedback,
    ImageBackground,
    TouchableOpacity,
    Text,
    StyleSheet, DeviceEventEmitter
} from 'react-native';

const {width} = Dimensions.get('window');
let SCREEN_WIDTH = width;
let cur_direction = -1;//当前云台转动方位
let ptz_pressed = false; //当前是否在操作云台
let ptz_pressedPanoramic = false; //当前是否在操作全景云台

import {colors, RNLine, showLoading, showToast,RoundedButtonView} from '../../../../imilab-design-ui';
import I18n, {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../com.chuangmi.door/src/CommonView/NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';
import HomePageLivePlayerComponent
    from "../../../com.chuangmi.door/src/CommonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent";
import ImageButton from "../../../com.chuangmi.door/src/CommonView/ImageButton/ImageButton"
import WaveView from '../../../../imilab-design-ui/src/widgets/WaveView'
import {
    LetDevice,
    IMIGotoPage,
    BaseDeviceComponent,
    imiAlarmEventCloudApi, aliAlarmEventCloudApi,
} from '../../../../imilab-rn-sdk';
import JoystickControlView from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/ui/JoystickControlView/JoystickControlView";
import Toast from "react-native-root-toast";
import PanoramicView from "./PanoramicView";
import AlarmListPage from "../alarmList/AlarmListPage";
import { XImage,XText,XView} from 'react-native-easy-app';
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {isAndroid} from "../../../../imilab-rn-sdk/utils/Utils";
import {GotoPageInNative, IMIStorage, LetIProperties,AlarmType,IMIPackage} from "../../../../imilab-rn-sdk";
import NetInfo from "@react-native-community/netinfo";
import {IMINativeLifeCycleEvent} from "../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import AlertDialog from "../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";
import GiftDialog from "../../../../imilab-design-ui/src/widgets/settingUI/GiftDialog";
import {LetIMIIotRequest} from "../../../../imilab-rn-sdk";
import project from '../../../../jenkinsBuild/052/project';
let moment = require("moment");


const EventName = "EventName-PanoramaCompoundFinishE";
const lensCorrect_x = 880.0 / 2560.0;
const lensCorrect_y = 64.0 / 1440.0;
let audioNeedRecovery = false; //通话结束是否需要恢复监听状态
let isCheckingPermission = false;

export default class CameraPlayerBasePage extends BaseDeviceComponent {
    constructor(props, context) {
        super(props, context);
        this.state = {
            currentStatus:HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED,
            isFullScreen: false,
            isCalling: false,//是否正在通话
            isSleep: false,//设备是否休眠
            spotlightSliderVisible: false, //控制聚光灯亮度滑动条的隐现
            spotlightBrightness: 0,
            isPanoramic:false,
            sdCardStatus:0,
            isDataUsage:false,
            vipState:-1,
            panoramicType:3,
            overAllImgStoreUrl:null,
            leftValue:1,
            rightValue:90,
            lensCorrect:false,
            isMoveStatus:false,//全屏长按触摸判断
            step:0,
            isOnline:LetDevice.isOnline,
            alarmSwitch: false, //报警总开关
            oneKeySwitchValue:false,//一键警告
            spotlightMode:1,//一键警告模式
            spotTime:10,//一键警告持续时间
            countTime:10,//一键警告递减时间
            showSpotlightTimer:false,//是否显示倒计时
            showOneKeyAlert:false,//未设置一键报警弹窗显示
            stateType:undefined,
            waterSwitch:false,// 时间水印
            // 云存相关
            expiredDialog:false, //云存储即将过期的弹窗提醒
            giftDialog:false, //免费云存领取提示
            giftName:"",
            giftPrice:null,
            giftId:null,
            deviceName:"",
            cloudExpireRemainDays:null,
            cloudExpireRemainDaysLocal:null, //本地存储的云存到期天数
            cloudExpireDate:null,
            showFreeCloud:false,//
        }
        this.isForegroundPage = true;
    }

    componentDidMount() {
        // this.IMIVideoView.prepare();
        console.log('设备状态did--',LetDevice.deviceID,LetDevice.devNickName);
        this._subscribe_focus = this.props.navigation.addListener('focus', () => {
            this.isForegroundPage = true;
            if (this.state.isOnline){
                this.getNetWork();
            }

            this.getSameData();
            this.getSDCardStatus();
             if (this.openMute){
                 // 进入其他页面前打开了监听，返回当前页面之后要重新打开监听
                 this.homePageLivePlayerComponent.setMute(false);
             }

        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            // this.IMIVideoView && this.state.currentStatus===HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING && this.IMIVideoView.stop();
            this.isForegroundPage = false;
            if(!this.homePageLivePlayerComponent.getMute()){
                // 进入其他页面前打开了监听
                console.log('监听打开');
                this.openMute = true;
                this.homePageLivePlayerComponent.setMute(true);
            }
            this.IMIVideoView && this.IMIVideoView.stop();
            if (this.state.showSpotlightTimer){
                this.levaveTime = Date.parse(new Date());
                this.lastTime = this.state.countTime;
                console.log('当前离开时间戳',this.levaveTime,this.state.showSpotlightTimer,this.lastTime);
            }
        });

        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            this.isForegroundPage = false;
            this._dealWithErrorOrPause();
        });

        this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(()=>{
            this.isForegroundPage = true;
            //if (!this.isForegroundPage)return;
            this.getSameData();
            if (this.state.isOnline){
                this.getNetWork();
            }
            //如果显示云存过期提醒，则回到此页面刷新一下，防止购买云存回来没有移除提示页面
            if(this.state.cloudExpireRemainDays!=null&&this.state.cloudExpireRemainDays<0){
                this._getCloudExpiredRemainDays();
            }
        });

        this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(()=>{
            this.isForegroundPage = false;
            this._dealWithErrorOrPause();
        });

        this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(()=>{
            //app页面回到插件
            NavigationBar.setBarStyle('light-content'); // 修改从云存购买界面返回状态栏显示黑色字体问题

            this.isForegroundPage = true;
            this.getSameData();
            if (this.state.isOnline){
                this.getNetWork();
            }
        });

        // Subscribe
        this.unsubscribe = NetInfo.addEventListener(state => {
            // this.getNetWork();
            if (this.state.stateType !== state.type){
                this.setState({stateType:state.type});
                this.getNetWork();
            }
            // console.log("是否链接--Is connected?", state.isConnected);
        });

        // 云存储相关信息
        this.getCloudInfo();




        // this.getPanoramaProperty();
        // this.getOneKeyValue()

        LetDevice.registerDeviceEventChangeListener((data)=>{
            console.log('registerDeviceEventChangeListener-----',data);
            let {iotId,identifier,value} = JSON.parse(data);
            if (iotId == LetDevice.deviceID){
                if (identifier=='PanoramaCompoundFinishE'){
                    let {isSuccess} = value;
                    this.intervalID&&clearInterval(this.intervalID);//清除
                    DeviceEventEmitter.emit('PanoramaCompoundFinishE', isSuccess);
                    if (parseInt(isSuccess)){
                        this.getPanoramaProperty();
                        showToast(stringsTo('panoramicSuccess'));
                        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
                            showLoading(false);
                            this.setState({panoramicType:parseInt(res.status),overAllImgStoreUrl:res.url});
                        }).catch(error=>{
                            showLoading(false);
                        });
                    }else {
                        showToast(stringsTo('panoramicError'));
                        this.setState({panoramicType:3});
                    }
                }
                // if (identifier == 'PTZUpdateCoordinates'){
                //     let {coordinates} = value;
                //     if (coordinates.substr(0,3)=='-1,'){
                //         showToast(stringsTo('direction_end_009'));
                //     }
                //     // alert(coordinates);
                //
                // }
            }
        });
        this.devicePropertyListener = LetIProperties.addPropertyChangeListener((event)=>{
            console.log('event-----------------',event);
            let data = typeof(event)=='object'?event:JSON.parse(event);

            if (data.SleepStatus!=undefined){
                if (data.SleepStatus == "0"|| data.SleepStatus==0){
                    this.setState({isSleep:true});
                    this.IMIVideoView&&this.IMIVideoView.stop();
                }else {
                    this.setState({isSleep:false});
                    this.IMIVideoView&&this.IMIVideoView.prepare();
                }
            }
            if (data.iotId == LetDevice.deviceID){
                if (data.items){
                    if (data.items.SleepStatus){
                        if (data.items.SleepStatus.value == "0"|| data.items.SleepStatus.value==0){
                            this.setState({isSleep:true});
                            this.IMIVideoView&&this.IMIVideoView.stop();
                        }else {
                            this.setState({isSleep:false});
                            this.IMIVideoView&&this.IMIVideoView.prepare();
                        }
                    }
                }
            }

        });
        this.deviceInfoListener = LetDevice.addInfoChangeListener((info)=>{
            let data = typeof(info)=='object'?info:JSON.parse(info);
            console.log('info----------'+JSON.stringify(data));
            let nowTime = Date.parse(new Date());
            let nowDate = new Date();
            console.log('设备状态did--',LetDevice.deviceID);
            console.log('当前设备状态----及出现时间',JSON.stringify(data),nowTime,nowDate);
            if (data.key == 'isOnline'){
                this.setState({isOnline:data.value});
                if (data.value == false){
                    this.IMIVideoView&&this.IMIVideoView.stop();
                }else {
                    this.IMIVideoView&&this.IMIVideoView.prepare();
                }
            }
            // if (data.iotId == LetDevice.deviceID) {
            //     if (data.thingType == 'DEVICE'){
            //         if (data.status){
            //             this.setState({isOnline:data.status.value==1?true:false});
            //             if (data.status.value == false){
            //                 this.IMIVideoView&&this.IMIVideoView.stop();
            //             }else {
            //                 this.IMIVideoView&&this.IMIVideoView.prepare();
            //             }
            //         }
            //     }
            // }
        });
        GotoPageInNative.addStarNativeGoToPage(this.props.navigation);
        // showToast('version  6');
    }

    // 云存储提示
    getCloudInfo() {

        this._getCloudExpiredRemainDays();
        this._getLocalStorage();
        this.getFreeDaySave();
        // 获取云存储相关信息
        this.getFreeCloudStorageInfo();
    }

    getFreeDaySave(){

        IMIStorage.load({
            key: LetDevice.deviceID+'freeDay',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            console.log('获取免费本地存储--',res);
            let oldTime = res.nextStartTime;
            let newTime = Date.parse(new Date());
            // let tempTime = newTime - oldTime;
            // let oneDayTime = 24*60*60*1000

            // console.log('当前剩余时间',tempTime,oneDayTime);
            console.log('老时间---',oldTime);
            console.log('新时间---',newTime);
            if (newTime >= oldTime){
                this.setState({
                    showFreeCloud:true
                },callback=>{
                    // console.log('显示',this.state.showFreeCloud);
                })
            }else {
                this.setState({
                    showFreeCloud:false
                },callback=>{
                    // console.log('不显示',this.state.showFreeCloud);
                })
            }
        }).catch(_=> {
            this.setState({
                showFreeCloud:true
            },callback=>{
                // console.log('报错这里zou',this.state.showFreeCloud);
            })
        });
    }

   // 获取云存储相关信息
    //获取是否可以领取免费云存
    getFreeCloudStorageInfo() {
        if(LetDevice.isShareUser){
            return;
        }

        let params = {
            Path: 'api/app/storage/free',
            ParamMap: {
                deviceId: LetDevice.deviceID,
                model: LetDevice.model
            }
        };

        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // alert('免费云存数据--'+JSON.stringify(data));
            console.log('免费云存数据---'+JSON.stringify(data));
            let result = JSON.parse(data);
            if(result.able){
                this.setState({
                    giftDialog:true,
                    giftName:result.plan.planName,
                    giftPrice:result.plan.marketPrice,
                    giftId:result.plan.id,
                    deviceName:result.deviceName,
                    currency:result.currency
                });
            }

        }).catch((error) => {
            console.log('推送数据获取错误--',error);
            console.log('sendUserServerRequest error ' + error)
        });
    }

    gainFreeCloudStorage() {
        let params = {
            Path: 'api/app/storage/gain',
            ParamMap: {
                deviceId: LetDevice.deviceID,
                model: LetDevice.model,
                planId:this.state.giftId+"",
                deviceName:this.state.deviceName
            }
        };
        console.log('领取免费云存数据--',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            console.log('领取免费云存数据--结果',JSON.parse(data));



        }).catch((error) => {

            console.log('领取免费云存数据-- error ' , error)
        });
    }

    getCloundStorageStatus(){
        let params = {
            Path: 'api/app/storage/state',
            ParamMap: {
                deviceId: LetDevice.deviceID,
            }
        };
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => { ////如果为0可能是0从未购买、1使用中、已过期

            let remainDays = JSON.parse(data).state == 2 ? -100 : null;
            this.setState({
                cloudExpireRemainDays: remainDays,
                expiredDialog:remainDays!=null&&remainDays>=0&&remainDays<30,
            });

        }).catch((error) => {
            console.log('查询云存状态-- error ' +error)
        });
    }

    //获取本地存储
    _getLocalStorage() {
        //获取本地存储信息--浏览节点提醒是否不再弹窗提醒
        IMIStorage.load({
            key: LetDevice.deviceID+'cloudExpireRemainDaysLocal',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            console.log('获取本地存储--',res);
            this.setState({cloudExpireRemainDaysLocal: res.cloudExpireRemainDaysLocal})
        }).catch(_=> {
        });
    }


    /*查询和计算云存会员还有几天到期*/
    _getCloudExpiredRemainDays(){
        if(LetDevice.isShareUser){
            return;
        }
        let params = {
            Path: 'api/app/storage/expireTime',
            ParamMap: {
                model:LetDevice.model,
                deviceId: LetDevice.deviceID,
            }
        };
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            console.log('查询云存过期时间--结果'+JSON.parse(data).expireTime);
            let cloudExpireDate = JSON.parse(data).expireTime;
            let remainDays = null;

            let currentStamp = moment().unix()*1000;

            console.log("gggggggggggg-------",cloudExpireDate,typeof(cloudExpireDate),moment().unix());
            if (cloudExpireDate != 0) { //为0则是没开通云存获者是重绑机器了
                console.log('获取云存结果--',cloudExpireDate,currentStamp);
                cloudExpireDate = moment(parseInt(cloudExpireDate));
                remainDays = cloudExpireDate.diff(moment(), 'day');
                if (cloudExpireDate <= currentStamp) //过期了则剩余天数就是负数
                    remainDays = -100;
                this.setState({
                    cloudExpireRemainDays: remainDays,
                    expiredDialog: remainDays != null && remainDays >= 0 && remainDays < 7,
                }, () => {
                    console.log("ggggggggg-----"+this.state.cloudExpireRemainDays);
                });
            } else { //如果为0可能是0从未购买、1使用中、2已过期
                // this.getCloundStorageStatus()
            }


        }).catch((error) => {
            console.log('查询云存过期时间-- error ',error)
        });

    }


    // 获取一键警告相关信息
    getOneKeyValue() {
        LetDevice.updateAllPropertyCloud().then((data) => {
            showLoading(false);
            // console.log('052获取value值',JSON.parse(data));
            let dataObject = JSON.parse(data);

            // let dataObject = data;
            let stateProps = {};
            if (dataObject.AlarmSwitch) {
                //报警总开关
                stateProps.alarmSwitch = dataObject.AlarmSwitch.value;
            }
            if (dataObject.OneKeyAlarm){
                //一键警告
                // console.log('一键警告有值',dataObject.OneKeyAlarm,dataObject.OneKeyAlarm.value,dataObject.OneKeyAlarm.value.length);
                if (dataObject.OneKeyAlarm.value.length == 0){
                    //字符串为空  设备第一次绑定成功之后
                    // console.log('无数据')
                    stateProps.oneKeySwitchValue = false;
                }else {
                    let tempStr = JSON.parse(dataObject.OneKeyAlarm.value);
                    stateProps.oneKeySwitchValue = tempStr.one_key_alarm_switch;// 一键警告开关
                    stateProps.spotlightMode = tempStr.mode;// 一键警告模式
                    // stateProps.spotTime = 10;// 一键警告持续时间
                    // stateProps.countTime = 10;// 一键警告持续时间
                }

                if (dataObject.OSDSwitch){
                    console.log('时间水印---',dataObject.OSDSwitch);
                    stateProps.waterSwitch = dataObject.OSDSwitch.value == 1 ? true:false;
                }

            }else {
                stateProps.oneKeySwitchValue = false;
                // console.log('一键警告开关无值');
            }

            console.log('statprops--'+ JSON.stringify(data));
            // 统一设置从设备端获取的值
            this.setState(stateProps);
        }).catch(error => {
            // alert(JSON.stringify(error));
            console.log(JSON.stringify(error))
        });
    }

    getPanoramaProperty(){
        LetDevice.updateAllPropertyCloud().then((data)=>{
            let dataObject = JSON.parse(data);
            // let dataObject = data;
            let stateProps = {};
            //侦测时间
            if (dataObject.PanoramStartMotorPositon) {
                let ary = dataObject.PanoramStartMotorPositon.value.split(',');
                stateProps.leftValue = parseInt(ary[0].substr(1));
            }
            if (dataObject.PanoramEndMotorPositon) {
                let ary = dataObject.PanoramEndMotorPositon.value.split(',');
                stateProps.rightValue = parseInt(ary[0].substr(1));
            }
            this.setState(stateProps);
        }).catch(error=>{

        });
    }

    getSameData(){
        LetDevice.getPropertyCloud('SleepStatus').then((data) =>{ //0休眠 1关闭
            console.log('设备休眠--------SleepStatus' + data,typeof(data));
            if(data=="0"||data==0){
                this.setState({isSleep:true});
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
        imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
            let data = JSON.parse(res);
            this.setState({vipState:data.state});
        }).catch(error=>{
            this.setState({vipState:-1});
        });
        IMIStorage.load({
            key: LetDevice.deviceID+'DistortionCorrection',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            console.log('时间水印----',res);
            this.setState({lensCorrect: res.lensCorrect});
        }).catch(_=> {
            this.setState({lensCorrect: false});
        });
        IMIStorage.load({
            key: LetDevice.deviceID+'cameraGuideForZoomed',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            console.log('res----',res);
            this.setState({step: res.cameraGuideForZoomed});
        }).catch(_=> {
            this.setState({step: 1});
        });
    }

    getNetWork(){
        if(!this.isForegroundPage){ //处于后台则不作处理，防止直播流在后台启动
           return;
        }
        IMIStorage.load({
            key: LetDevice.deviceID+'isDataUsageWarning',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            console.log('流量保护---',res);
            this.setState({isDataUsage: res.isDataUsage});
            if (res.isDataUsage){
                NetInfo.fetch().then(state => {
                    // console.log('网络状态---',state);
                    if (state.type=='wifi'){
                        // console.log('当前状态---',this.state.currentStatus);
                        this.IMIVideoView.prepare();
                        // if(this.state.currentStatus != HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
                        //     this.IMIVideoView.prepare();
                        // }
                        // console.log('每次都走这');
                    } else {
                        this.IMIVideoView && this.IMIVideoView.stop();
                        if (state.type == 'cellular'){
                            showToast(stringsTo('isDataUsageTip'));
                        }
                        this.homePageLivePlayerComponent.setPauseView(true);
                        this.setState({currentStatus: HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE});
                    }
                });
            }else {
                this.IMIVideoView.prepare();
                // console.log('看看流量保护关闭走几次');
            }
        }).catch(_=> {
            this.setState({isDataUsage: false});
            this.IMIVideoView.prepare();
            // console.log('看看流量保护关闭走几次未开启');
        });
    }

    componentWillUnmount() {
        // if (isAndroid()) {
        //     this.IMIVideoView&&this.IMIVideoView.destroy();
        // }
        LetDevice.removeDeviceEventChangeListener();
        this._subscribe_focus && this._subscribe_focus();
        this._subscribe_blur && this._subscribe_blur();
        this._enterBackground&&this._enterBackground.remove();
        this._enterForeground&&this._enterForeground.remove();
        this._onPauseListener&&this._onPauseListener.remove();
        this._onResumeListener&&this._onResumeListener.remove();
        this.devicePropertyListener&&this.devicePropertyListener.remove();
        this.deviceInfoListener&&this.deviceInfoListener.remove();
        this.timer&&clearTimeout(this.timer);
        this.countdownTimer && clearInterval(this.countdownTimer);
        GotoPageInNative.removeStarNativeGoToPage();
        // Unsubscribe
        this.unsubscribe();
    }


    _onPressBack = (isFullScreen) => {
        if (isFullScreen) {
            Orientation.lockToPortrait();
            this.props.navigation.setOptions({tabBarVisible: true});
        } else {
            if (!this._canStepInCall())return;
            IMIGotoPage.exit();
        }
    };

    _onClickSleepButton = () =>{ //注意：0是休眠 1是关闭休眠
        let params = {
            SleepStatus: this.state.isSleep ? 1 : 0,
        };
        if (this._isShareUser()) return;
        if(!this._canStepInCall())  return;
        // if (this.state.panoramicType==1||this.state.panoramicType==0){
        //     showToast(stringsTo('Panoramic_loading'));
        //     return ;
        // }
        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            console.log("设置休眠成功-------",data);
            this.setState({isSleep: !this.state.isSleep},()=>{
                if(this.state.isSleep){
                    this.setState({isPanoramic:false});
                    this.intervalID&&clearInterval(this.intervalID);//清除
                    this.IMIVideoView.stop();
                }else{
                    this.IMIVideoView.prepare();
                }
            });


        }).catch((err) => {
            console.log("设置休眠失败-------",err);
        });

    };

    //判断当前是否可以操作
    _canStepIn(){
        console.log('通话打印当前直播流状态----',this.state.currentStatus);
        if(this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            return true;
        }
        if (this.state.isSleep){
            showToast(stringsTo('power_off'));
            return false;
        }
        if (!this.state.isOnline){
            showToast(stringsTo('device_offline'));
            return false;
        }
        showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
        return false;
    }

    //判断是否通话中、录像中
    _canStepInCall(){
        if (this.state.isCalling)
        {
            showToast(stringsTo('imi_speaking_block'));
            return false;
        }
        if (this.homePageLivePlayerComponent.getRecordStatus())
        {
            showToast(stringsTo('screen_recording'));
            return false;
        }
        return true;
    }

    _doCall() {
        if (this.state.isCalling) {
            this.IMIVideoView.stopSpeak();
            this.homePageLivePlayerComponent.setCalling(false);
            audioNeedRecovery&&this.homePageLivePlayerComponent.setMute(true)
        } else {
            if(this.homePageLivePlayerComponent.getMute()){ //通话前监听是关闭的，则打开监听，并且结束通话后需要再次恢复到静音状态
                audioNeedRecovery = true;
                this.homePageLivePlayerComponent.setMute(false);
            }
                this.homePageLivePlayerComponent.setCalling(true);
            this.IMIVideoView.startSpeak();
        }
        this.setState({isCalling: !this.state.isCalling});
    }

    //点击报警按钮
    _onClickWarnButton = () => {

        if (this.state.isCalling){
            showToast(stringsTo('imi_speaking_block'));
            return;
        }

        if(!this._canStepIn())  return;
        // console.log('离线是不是走这里了');


        if (this.state.alarmSwitch){
            if (this.state.oneKeySwitchValue){
                //看家助手打开,且一键报警打开
                // 1闪烁 2 不亮 0 常亮
                showLoading(stringsTo('commWaitText'), true);
                let timestamp = Date.parse(new Date());
                let params = {"click_time":timestamp.toString()};
                // let params = {"click_time":'1622719406'};
                // console.log('当前时间戳--',timestamp,JSON.stringify(params))
                LetDevice.sendDeviceServerRequest("TriggerOneKeyAlarm", JSON.stringify(params)).then((data) => {
                    // console.log('触发一键报警' + data);
                    showLoading(false);
                    this.setState({showSpotlightTimer:true})
                    this.countdownTimer = setInterval(() => { //从10秒开始倒计时
                        let time = this.state.countTime - 1;
                        if (time <= 0) { //倒计时结束，停止视频播放
                            this.countdownTimer && clearInterval(this.countdownTimer);
                            this.setState({
                                showSpotlightTimer: false,
                                countTime:this.state.spotTime
                            });
                        } else {
                            this.setState({
                                countTime: time,
                            });
                        }
                    }, 1000);
                }).catch((error) => {
                    showToast(I18n.t("operationFailed"));
                    showLoading(false);
                    // console.log('触发一键报警 error ' + error)
                });

            }else {
                //看家助手打开，一键报警未打开
                this.setState({showOneKeyAlert:true})
            }
        }else {
            //看家助手开关未打开
            this.setState({showOneKeyAlert:true})
        }

    };
    //一键警告dialog
    _renderOneKeySetDialog() {
        return (
            <AlertDialog
                title={I18n.t("set_onekey_tit")}
                visible={this.state.showOneKeyAlert}
                message={I18n.t("set_onekey_msg")}
                messageStyle={{
                    marginBottom: 14,
                    marginHorizontal:28,
                    fontSize: 12,
                    // fontWeight: '500',
                    color: "#7F7F7F"
                }}
                canDismiss={true}
                onDismiss={() => {
                    this.setState({showOneKeyAlert: false});
                }}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({showOneKeyAlert: false});
                        }
                    },
                    {
                        text: I18n.t("set_onekey_sure"),
                        callback: _ => {
                            this.setState({showOneKeyAlert: false});
                            if (this.state.alarmSwitch){
                                if (!this.state.oneKeySwitchValue){
                                    // 未打开一键警告  进入一键警告页面
                                    if (this.state.isFullScreen){
                                        this.homePageLivePlayerComponent.quitFullScreen()
                                        this.setState({isFullScreen:false},callback=>{
                                            this.props.navigation.push("ClickWarningSetPage",{enterType:'liveVideo'});
                                        })
                                        return;
                                    }
                                    this.props.navigation.push("ClickWarningSetPage",{enterType:'liveVideo'});

                                }
                            }else {
                                //未打开看家助手  进入看家助手界面
                                if (this.state.isFullScreen){
                                    this.homePageLivePlayerComponent.quitFullScreen()
                                    this.setState({isFullScreen:false},callback=>{
                                        this.props.navigation.push("HouseKeepSetting");
                                    })
                                    return;
                                }
                                this.props.navigation.push("HouseKeepSetting");
                            }
                        }
                    },
                ]}
            />
        );
    }

    //顶部导航栏
    _renderNavigationBar(bps, isFullScreen) {
        return (
            <NavigationBar
                type={NavigationBar.TYPE.DARK}
                backgroundColor={"transparent"}
                title={LetDevice.devNickName}
                onLongPress={()=>showToast('V'+project.rnVersion)}
                subtitle={bps >= 0 ? `${bps}KB/S` : undefined}
                left={[{
                    key: NavigationBar.ICON.BACK,
                    onPress: () => this._onPressBack(isFullScreen)
                }]}
                right={[
                    {
                        key: NavigationBar.ICON.SETTING,
                        onPress: _ => {
                            if (!this.state.isOnline){
                                showToast(stringsTo('device_offline'));
                                return true;
                            }
                            if (!this._canStepInCall())return;
                            navigation.push("IMICommSettingPage",
                                {
                                    "showDelDev": false,
                                    "defaultStyleRenderItemArray": [{
                                        title: [stringsTo('popo_setting_camera_text')],
                                        onPress:  ()=> {
                                            this.props.navigation.push("IMICameraSettingVC");
                                        }
                                    },
                                        {
                                            title: [stringsTo('alarmSettingText')],
                                            onPress: ()=> {
                                                if (this._isShareUser())return;
                                                this.props.navigation.push("HouseKeepSetting");
                                            }
                                        }, {
                                            title: [stringsTo('popo_setting_storage_text')],
                                            onPress: () => {
                                                if (this._isShareUser())return;
                                                if (this.state.sdCardStatus == 0){
                                                    showToast(stringsTo('storage_no_sdcard_please_buy'));
                                                } else if (this.state.sdCardStatus == 2){
                                                    showToast(stringsTo('sdCardDamaged'));
                                                } else if (this.state.sdCardStatus == 4){
                                                    showToast(stringsTo('sdcard_error_out'));
                                                } else {
                                                    // this.props.navigation.push('SdCardPage');
                                                    this.props.navigation.push('SdCardSettingPage');
                                                }
                                                // IMIGotoPage.starNativeFileManagerSettingPage(LetDevice.deviceID)
                                            }
                                        }]
                                });
                        }
                    }
                ]}/>
        );
    }
    renderVideoSubView(isFullScreen,showFullScreenTools){  //竖屏时没有要显示的控件，竖屏显示拨号键、云台
        return (<View style={{width: "100%",
            height: "100%",
            alignItems: "center",
            justifyContent: "center",
            position: "absolute"}} pointerEvents={'box-none'}>
            {/*{this._renderCallViewFullScreen(isFullScreen,showFullScreenTools)}*/}
            {/*{this._rendLandscapeDirectionView(isFullScreen,showFullScreenTools)}*/}
            {this._renderCallAndAlarmViewFullScreen(isFullScreen,showFullScreenTools)}
            {this._sleepView()}
            {this._deviceOffLineView()}
        </View>);
    }

    //横屏时的拨号键 包含报警
    _renderCallAndAlarmViewFullScreen(isFullScreen,showFullScreenTools) {
        if (!isFullScreen || !showFullScreenTools) return null;
        return (<View pointerEvents="auto" style={{position: 'absolute', right: 30,backgroundColor:"transparent",}}>
            <ImageButton
                style={{width: 44, height: 44}}
                source={this.state.isCalling ? require("../../resources/images/icon_hangup_big.png") : require("../../resources/images/icon_call_big.png")}
                highlightedSource={this.state.isCalling ? require("../../resources/images/icon_hangup_big_p.png") : require("../../resources/images/icon_call_big_p.png")}
                onPress={() => {
                    if(!this._canStepIn())  return;
                    isCheckingPermission = true;
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                        if (status === 0) {
                            this._doCall();
                            isCheckingPermission = false;
                        } else if (status === -1) {
                            isCheckingPermission = false;
                            showToast(stringsTo('audio_permission_denied'))
                        }
                    })

                }}
            />
            {this.state.showSpotlightTimer ?  <View style = {{width:44,height:44,marginTop: 40,borderRadius:22,backgroundColor:'red',alignItems:"center",justifyContent:"center"}}>
                <Text
                    style={{fontSize:14,color : "#FFFFFF",}}
                >
                    {this.state.countTime+"S"}
                </Text>
            </View> : <ImageButton
                style={{width: 44, height: 44, marginTop: 40}}
                source={require("../../resources/images/icon_alarm_off_white.png")}
                highlightedSource={require("../../resources/images/icon_alarm_off_white_p.png")}
                onPress={() => {
                    if (this._isShareUser())return;
                    this.setState({isFullScreen:true},callback=>{
                         this._onClickWarnButton()
                     })
                }}
                // onPress={this._onClickWarnButton}
            />}

        </View>);
    }

    //横屏时的拨号键
    _renderCallViewFullScreen(isFullScreen,showFullScreenTools) {
        if (!isFullScreen || !showFullScreenTools) return null;
        return (<View style={{position: 'absolute', right: 30}}>
            <ImageButton
                style={{width: 44, height: 44}}
                source={this.state.isCalling ? require("../../resources/images/icon_hangup_fullscreen.png") : require("../../resources/images/icon_call_fullscreen.png")}
                highlightedSource={this.state.isCalling ? require("../../resources/images/icon_hangup_fullscreen.png") : require("../../resources/images/icon_call_fullscreen.png")}
                onPress={() => {
                    if(!this._canStepIn())  return;
                    isCheckingPermission = true;
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                        if (status === 0) {
                            this._doCall();
                            isCheckingPermission = false;
                        } else if (status === -1) {
                            isCheckingPermission = false;
                            showToast(stringsTo('audio_permission_denied'))
                        }
                    })

                }}
            />

        </View>);
    }

    /*横屏云台View*/
    _rendLandscapeDirectionView(isFullScreen,showFullScreenTools) {
        if(isFullScreen&&showFullScreenTools){
            return (<View
                style={{
                    position: "absolute", left: 15, bottom: 15
                }}>

                <JoystickControlView
                    onStart={() => {
                    }}
                    onMove={(type) => {
                        if(!this._canStepIn())  return;
                        ptz_pressed = false;
                        this.setState({isMoveStatus:true});
                        clearInterval(this.setPTZIntervalID);
                        this._doDirection(type);
                    }}
                    onLoosen={() => {
                        ptz_pressed = false;
                        this.setState({isMoveStatus:false});
                        this.homePageLivePlayerComponent._onPressFullScreenTools();
                        clearInterval(this.setPTZIntervalID);
                        this._getMotorPositonStatus();
                    }}
                    isFullscreen={true}
                    diameterPan={160}
                    diameterMid={36}/>
            </View>);}
    }

    /*绘制摇杆式云台View*/
    renderPtzControlView(){
        return (<View
            style={{flexGrow: 1,
                width: "100%",
                flexDirection: "column",
                display: "flex",
                // marginTop: 20,
                paddingRight: 14,paddingLeft: 14,
                alignItems: "center",
                justifyContent: "center"}}>
            {this.state.isPanoramic?<PanoramicView
                Type={this.state.panoramicType}
                accessibilityLabel={"show_all_panorama"}
                onTouch={(pointX) => {
                    this.selectPositionX = pointX;
                    ptz_pressedPanoramic = false;
                    // console.log(" _setPanoramaRotateAngle h=",this.selectPositionX,",v=",this.selectPositionY);
                    this._doDirectionPanorama(this.selectPositionX,50)
                }} onLoosen={() => {}}
                startPanoramic={()=>{
                    if(!this._canStepIn())  return;
                    LetDevice.sendDeviceServerRequest("PanoramaEnableComposition",{}).then((data) => {
                        this.setState({panoramicType:1});
                        this._panoramicTimeout();
                    }).catch((error) => {
                    });
                }}
                endPanoramic={()=>{
                    // if (this.state.panoramicType == 1){
                    //     showToast(stringsTo('panoramicing_tip'));
                    //     return;
                    // }
                    this.setState({isPanoramic:false});}}
                selectPositionX={this.selectPositionX} minSelectPositionLeft={0} maxSelectPositionRight={SCREEN_WIDTH-28} leftValue={this.state.leftValue} rightValue={this.state.rightValue} imgStoreUrl={this.state.overAllImgStoreUrl}/>:<JoystickControlView
                onMove={ (type) => {
                    if(!this._canStepIn())  return;
                    ptz_pressed = false;
                    clearInterval(this.setPTZIntervalID);
                    this._doDirection(type);
                } }
                onLoosen={ () => {
                    ptz_pressed = false;
                    clearInterval(this.setPTZIntervalID);
                    this._getMotorPositonStatus();
                } }
                isFullscreen={ false}
                diameterPan={ 206 }
                diameterMid={ 40 }/>}
            {
                !this.state.isPanoramic?<XImage style={{
                    position: "absolute", right: 15, bottom: 15
                }} iconSize={50} icon={this.state.isPanoramic?require('../../resources/images/icon_holder.png'): require('../../resources/images/icon_panoramic.png')}
                                                onPress={()=>{
                                                    if (this._isShareUser()) return;
                                                    if(!this._canStepIn())  return;
                                                    let isPanoramic = !this.state.isPanoramic;
                                                    if (isPanoramic){
                                                        showLoading(stringsTo('commLoadingText'),true);
                                                        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
                                                            showLoading(false);
                                                            console.log('-------------'+res.url);
                                                            // parseInt(res.status)==1||parseInt(res.status)==0?3:
                                                            let  isEnabledOpen = parseInt(res.status)==2?true:false;
                                                            this.setState({panoramicType:(!isEnabledOpen&&res.timeOutFlag)?3:parseInt(res.status),overAllImgStoreUrl:res.url,isPanoramic:isPanoramic});
                                                        }).catch(error=>{
                                                            showLoading(false);
                                                            this.setState({isPanoramic:isPanoramic});
                                                        });
                                                    }else {
                                                        if (this.state.panoramicType == 1){
                                                            showToast(stringsTo('panoramicing_tip'));
                                                            return;
                                                        }
                                                        this.setState({isPanoramic:isPanoramic});
                                                    }

                                                }}
                />:null
            }

        </View>);
    }


    //通话和报警按钮控件
    _renderCallAndAlarmView() {
        return (
            <View style={{
                flex: 1,
                flexDirection: "row",
                alignItems: "center",
                justifyContent: "center",
                backgroundColor: "white"
            }}>
                <ImageButton
                    style={{width: 52, height: 52, marginRight: 60}}
                    source={this.state.isCalling ? require("../../resources/images/icon_hangup.png") : require("../../resources/images/icon_call.png")}
                    highlightedSource={this.state.isCalling ? require("../../resources/images/icon_hangup.png") : require("../../resources/images/icon_call_p.png")}
                    onPress={() => {
                        if(!this._canStepIn())  return;
                        isCheckingPermission = true;
                        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                            if (status === 0) {
                                this._doCall();
                                isCheckingPermission = false;
                            } else if (status === -1) {
                                showToast(stringsTo('audio_permission_denied'));
                                isCheckingPermission = false;
                            }
                        })

                    }}
                />
                {this.state.showSpotlightTimer ?  <View style = {{width:52,height:52,borderRadius:26,backgroundColor:'red',alignItems:"center",justifyContent:"center"}}>
                    <Text
                        style={{fontSize:14,color : "#FFFFFF",}}
                    >
                        {this.state.countTime+"S"}
                    </Text>
                </View> : <ImageButton
                    style={{width: 52, height: 52}}
                    source={require("../../resources/images/icon_alarm_off.png")}
                    highlightedSource={require("../../resources/images/icon_alarm_off_p.png")}
                    onPress={() => {
                        if (this._isShareUser())return;
                        this.setState({isFullScreen:false},callback=>{
                            this._onClickWarnButton()
                        })
                    }}
                    // onPress={this._onClickWarnButton}
                />}


            </View>
        );
    }


    //
    _panoramicTimeout(){
        this.timer = setTimeout(() => {
                let times = 0;
                this.intervalID = setInterval(() => {
                    times = times + 1;
                    if(times > 6){//rcp20秒后才开始拉取图片，每10秒拉一次6次即1分钟+20秒后还拉不到图片，认为失败了
                        this.intervalID&&clearInterval(this.intervalID);//清除
                        this._getMergePhotoMeta(true);
                        return;
                    }
                    //获取全景图信息
                    this._getMergePhotoMeta(false);
                }, 10000);
            },
            20000
        );
    }

    _getMergePhotoMeta(isLast){
        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{
            console.log('-------------'+res.url);
            if (!isLast&&parseInt(res.status)==2){
                this.setState({panoramicType:parseInt(res.status),overAllImgStoreUrl:res.url});
            }
            if (isLast){
                if (parseInt(res.status)!=2) {
                    showToast(stringsTo('panoramicError'));
                }
                let  isEnabledOpen = parseInt(res.status)==2?true:false;
                //parseInt(res.status)==1||parseInt(res.status)==0?3: 根据后台状态显示
                this.setState({panoramicType:(!isEnabledOpen&&res.timeOutFlag)?3:parseInt(res.status),overAllImgStoreUrl:res.url});
            }
        }).catch(error=>{
            if (isLast){
                this.setState({panoramicType:3});
            }
        });
    }


    /*控制云台转动*/
    _doDirection(m_direction){

        ptz_pressed = true;

        this._checkDirection(m_direction);
        if(m_direction !== 5) {
            // 每100毫秒一次操作
            clearInterval(this.setPTZIntervalID);
            this.setPTZIntervalID = setInterval(() => {
                if (!this._checkDirection(m_direction)) {
                    clearInterval(this.setPTZIntervalID);
                }
            }, 100);
        }else{
            ptz_pressed = false;
        }

    }

    /*判断当前是否可操作*/
    _checkDirection(m_direction){

        console.log("_checkDirection m_direction="+m_direction+",ptz_pressed="+ptz_pressed);

        if(ptz_pressed){
            this.sendDirectionCmd(m_direction);
        }

        return ptz_pressed;
    }

    /*发送控制云台的指令*/
    sendDirectionCmd(m_direction) {
        let paramsJSON = {ActionType:m_direction,Step:0};
        LetDevice.sendDeviceServerRequest("PTZActionControl", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------' +m_direction+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    /*控制云台转动*/
    _doDirectionPanorama(h,v){
        if (!this._checkDirectionPanorama(h,v)) {
            ptz_pressedPanoramic = true;
            this._checkDirectionPanorama(h,v);
        }
    }

    /*判断当前是否可操作*/
    _checkDirectionPanorama(h,v){

        console.log("_checkDirection m_direction="+h+",ptz_pressed="+ptz_pressed);

        if(ptz_pressedPanoramic){
            this._setPanoramaRotateAngle(h,v);
        }

        return ptz_pressedPanoramic;
    }
    _setPanoramaRotateAngle(h,v){

        let paramsJSON = {position:"["+h+','+v+"]"};

        LetDevice.sendDeviceServerRequest("PanoramSlide", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------'+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    // 获取转动状态
    _getMotorPositonStatus(){
        LetDevice.getPropertyCloud('MotorPositon').then((data) =>{ //0休眠 1关闭
            console.log('MotorPositon--------' + data,typeof(data));
            // alert(data);
            if (data.substr(0,3)=='-1,'){
                showToast(stringsTo('direction_end_009'));
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    /*监听直播流播放状态*/
    _onLivePlayerStatusChangeListener(status){
        // console.log('监听直播流状态---',status);
        if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
            this.IMIVideoView.start();
        }else if(status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE||status==HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR){
            this._dealWithErrorOrPause();
        }else if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            this.setState({isOnline:true});
        }
        if (status != HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) this.setState({currentStatus: status});

    }

    _dealWithErrorOrPause(){
        // this.IMIVideoView && this.state.currentStatus===HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING && this.IMIVideoView.stop();

        //直播流出错或暂停时，停止通话
        if (this.state.isCalling && this.IMIVideoView) {
            this.setState({isCalling: false});
            this.IMIVideoView.stopSpeak();
            this.homePageLivePlayerComponent.setCalling(false);
            audioNeedRecovery&&this.homePageLivePlayerComponent.setMute(true);
        }
        !isCheckingPermission&&this.IMIVideoView && this.IMIVideoView.stop();
    }

    //通话时的波纹动画
    _renderCallWaveView(){
        if (this.state.fullScreen) {
            return null;
        }

        if (!this.state.isCalling) {
            return null;
        }

        return (<View style={{
                width: SCREEN_WIDTH,
                top:90,
                // bottom: 83,
                position: "absolute",
            }}>
                <WaveView
                    waveHeight={36}
                    waveWidth={SCREEN_WIDTH}/>
            </View>
        );

    }
    //休眠提示
    _sleepView() {
        if (!this.state.isOnline)return;
        if (!this.state.isSleep) return null;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       width: "100%",
                       height: "100%",
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                       backgroundColor: "#000",
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={stringsTo('power_off')}
                />

                <RoundedButtonView buttonText={stringsTo('wake_up')}
                                   buttonStyle={{
                                       margin: 14,
                                       width: 110,
                                       height: 40
                                   }}
                                   onPress={() => {
                                       LetDevice.propertyOn("SleepStatus").then(()=>{
                                           this.setState({isSleep: 0},()=>{
                                               this.IMIVideoView.prepare();
                                           });
                                       }).catch(err=>{
                                           this.setState({isSleep:1});
                                           showToast(I18n.t('operationFailed'));
                                       });
                                   }}/>
            </View>
        );
    }

    //离线提示
    _deviceOffLineView() {
        if (this.state.isOnline) return null;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       width: "100%",
                       height: "100%",
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center",
                       backgroundColor: "#000",
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={stringsTo('device_offline')}
                />
            </View>
        );
    }

    _isShareUser(){
        if (LetDevice.isShareUser){
            showToast(stringsTo('shareUser_tip'));
            return true;
        }
        return false;
    }

    /*底部tab功能栏无通话按钮*/
    _renderNormalBottomLayout(){
        return (

            <View style={[styles.bottomLayout,{backgroundColor:"white"}]}>
                {/*看家*/}
                <TouchableOpacity
                    style={styles.bottomLayoutNormalItem}
                    onPress={()=>{
                        if (this._isShareUser()) return;
                        if(!this._canStepInCall())  return;
                        this.props.navigation.push('AlarmListPage');}}
                >
                    <Image
                        style={{ width: 30,height: 30 }}
                        source={require('../../resources/images/icon_alarm.png')}
                    />
                    <XText
                        style={{ margin: 2, textAlign:'center',fontSize: 10,color: this.state.isRecording ? "#C0C0C0C0" : "#333333",width:SCREEN_WIDTH/5}}//设计稿改动
                        ellipsizeMode="tail"
                        numberOfLines={1}

                        text={stringsTo('bottom_house_keeping')}
                    />
                </TouchableOpacity>

                {/*回看*/}
                <TouchableOpacity
                    style={styles.bottomLayoutNormalItem}
                    onPress={()=>{
                        if (this.state.sdCardStatus == 0){
                            showToast(stringsTo('storage_no_sdcard_please_buy'));
                            return;
                        } else if (this.state.sdCardStatus == 2){
                            showToast(stringsTo('sdCardDamaged'));
                            return;
                        } else if (this.state.sdCardStatus == 4){
                            showToast(stringsTo('sdcard_error_out'));
                            return;
                        }
                        if(!this._canStepInCall())  return;
                        if (this.state.isSleep){
                            showToast(stringsTo('power_off'));
                            return ;
                        }
                        if (!this.state.isOnline){
                            showToast(stringsTo('device_offline'));
                            return ;
                        }
                        this.props.navigation.push('PlayBackPage');
                    }}
                >
                    <Image
                        style={{ width: 30,height: 30 }}
                        source={require('../../resources/images/icon_playback.png')}
                    />
                    <Text
                        style={{ margin: 2,fontSize: 10,color : "#333333"}}
                        ellipsizeMode="tail"
                        numberOfLines={2}
                    >
                        {stringsTo('playBackText')}
                    </Text>
                </TouchableOpacity>

                {/*云存*/}
                {
                    <TouchableOpacity
                        style={styles.bottomLayoutNormalItem}
                        onPress={() => {
                            if (this._isShareUser()) return;
                            if(!this._canStepInCall())  return;
                            if (this.state.vipState == -1){
                                showLoading(stringsTo('commLoadingText'),true);
                                imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
                                    let data = JSON.parse(res);
                                    showLoading(false);
                                    this.setState({vipState:data.state});
                                    this.goToClound();
                                }).catch(error=>{
                                    showLoading(false);
                                    this.setState({vipState:0});
                                    this.goToClound();
                                });
                            } else this.goToClound();
                        }}
                    >
                        <Image
                            resizeMode='contain'
                            style={{width: 30, height: 30}}
                            source={require('../../resources/images/icon_cloud.png')}
                        />
                        <Text
                            style={{margin: 2, fontSize: 10, color: "#333333"}}
                            ellipsizeMode="tail"
                            numberOfLines={1}
                        >
                            {stringsTo('bottom_cloud_storage')}
                        </Text>
                    </TouchableOpacity>
                }

                {/*快捷*/}
                {
                    <TouchableOpacity
                        style={styles.bottomLayoutNormalItem}
                        onPress={()=>{
                            if(!this._canStepInCall())  return;
                            IMIGotoPage.startAlbumPage(LetDevice.deviceID)
                        }}
                    >
                        <Image
                            style={{width: 30, height: 30}}
                            source={require('../../resources/images/icon_album_new.png')}
                        />
                        <Text
                            style={{margin: 2, fontSize: 10, color: "#333333"}}
                            ellipsizeMode="tail"
                            numberOfLines={2}
                        >
                            {stringsTo('bottom_video_album')}
                        </Text>
                    </TouchableOpacity>
                }
            </View>

        )
    }

    /*底部tab功能栏*/
    _renderBottomLayout(){
        return (

            <View style={styles.bottomLayout}>
                <ImageBackground style={{display: "flex", width: "100%", height: SCREEN_WIDTH*76/360.0, flexDirection: "row", flexWrap: 'nowrap', alignItems:"center"}}
                                 source={require('../../resources/images/bottom_tab.png')} >
                    {/*看家*/}
                    <TouchableOpacity
                        style={styles.bottomLayoutItem}
                        onPress={()=>{
                            if (this._isShareUser()) return;
                            if(!this._canStepInCall())  return;
                            this.props.navigation.push('AlarmListPage');}}
                    >
                        <Image
                            style={{ width: 26,height: 26 }}
                            source={require('../../resources/images/icon_alarm.png')}
                        />
                        <XText
                            style={{ margin: 2, textAlign:'center',fontSize: 9,color: this.state.isRecording ? "#C0C0C0C0" : "#333333" }}//设计稿改动
                            ellipsizeMode="tail"
                            numberOfLines={2}

                            text={stringsTo('bottom_house_keeping')}
                        />
                    </TouchableOpacity>

                    {/*回看*/}
                    <TouchableOpacity
                        style={styles.bottomLayoutItem}
                        onPress={()=>{
                            if (this.state.sdCardStatus == 0){
                                showToast(stringsTo('storage_no_sdcard_please_buy'));
                                return;
                            } else if (this.state.sdCardStatus == 2){
                                showToast(stringsTo('sdCardDamaged'));
                                return;
                            }else if (this.state.sdCardStatus == 4){
                                showToast(stringsTo('sdcard_error_out'));
                                return;
                            }
                            if(!this._canStepInCall())  return;
                            if (this.state.isSleep){
                                showToast(stringsTo('power_off'));
                                return ;
                            }
                            if (!this.state.isOnline){
                                showToast(stringsTo('device_offline'));
                                return ;
                            }
                            this.props.navigation.push('PlayBackPage');
                        }}
                    >
                        <Image
                            style={{ width: 30,height: 30 }}
                            source={require('../../resources/images/icon_playback.png')}
                        />
                        <Text
                            style={{ margin: 5,fontSize: 10,color : "#333333" }}
                            ellipsizeMode="tail"
                            numberOfLines={2}
                        >
                            {stringsTo('playBackText')}
                        </Text>
                    </TouchableOpacity>

                    {/*通话*/}
                    <TouchableOpacity
                        style={[styles.bottomLayoutItem, {marginTop: 5}]}
                        onPress={() => {
                            if(!this._canStepIn())  return;
                            isCheckingPermission = true;
                            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
                                if (status === 0) {
                                    this._doCall();
                                    isCheckingPermission = false;
                                } else if (status === -1) {
                                    isCheckingPermission = false;
                                    showToast(stringsTo('audio_permission_denied'))
                                }
                            })
                        }}
                    >
                        <Image
                            style={{width: 56, height: 56}}
                            source={this.state.isCalling?require("../../resources/images/icon_hangup.png"):require("../../resources/images/icon_call.png")}
                        />

                    </TouchableOpacity>

                    {/*云存*/}
                    {
                        <TouchableOpacity
                            style={styles.bottomLayoutItem}
                            onPress={() => {
                                if (this._isShareUser()) return;
                                if(!this._canStepInCall())  return;
                                if (this.state.vipState == -1){
                                    showLoading(stringsTo('commLoadingText'),true);
                                    imiAlarmEventCloudApi.getVipState(LetDevice.deviceID).then(res=>{
                                        let data = JSON.parse(res);
                                        showLoading(false);
                                        this.setState({vipState:data.state});
                                        this.goToClound();
                                    }).catch(error=>{
                                        showLoading(false);
                                        this.setState({vipState:0});
                                        this.goToClound();
                                    });
                                } else this.goToClound();
                            }}
                        >
                            <Image
                                resizeMode='contain'
                                style={{width: 30, height: 30}}
                                source={require('../../resources/images/icon_cloud.png')}
                            />
                            <Text
                                style={{margin: 5, fontSize: 10, color: "#333333"}}
                                ellipsizeMode="tail"
                                numberOfLines={1}
                            >
                                {stringsTo('bottom_cloud_storage')}
                            </Text>
                        </TouchableOpacity>
                    }

                    {/*快捷*/}
                    {
                        <TouchableOpacity
                            style={styles.bottomLayoutItem}
                            onPress={()=>{
                                if(!this._canStepInCall())  return;
                                IMIGotoPage.startAlbumPage(LetDevice.deviceID)
                            }}
                        >
                            <Image
                                style={{width: 30, height: 30}}
                                source={require('../../resources/images/icon_alarm.png')}
                            />
                            <Text
                                style={{margin: 5, fontSize: 10, color: "#333333"}}
                                ellipsizeMode="tail"
                                numberOfLines={2}
                            >
                                {stringsTo('bottom_video_album')}
                            </Text>
                        </TouchableOpacity>
                    }

                </ImageBackground>
            </View>

        )
    }

    goToClound(){

        if (this.state.vipState==0){
            IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
        } else {
            if (IMIPackage.minApiLevel>10002){
                //支持云存储筛选事件最低版本为10003
                let eventStr = {
                    "eventList":[AlarmType.MOVE,AlarmType.PEOPLE]
                };
                IMIGotoPage.startAliMoveDetectionPageConfig(null,LetDevice.deviceID,JSON.stringify(eventStr));
            }else {
                IMIGotoPage.startAliMoveDetectionPageV2(null,LetDevice.deviceID);
            }
            // IMIGotoPage.startAliMoveDetectionPage();
        }
    }

    // 通话异常
    _dealWithVoiceCallErrorOccurred(value){
        // console.log('当前错误码---',value);
        if (value == 1){
            showToast(stringsTo('call_connect_error'));
        }else if (value == 12){
            showToast(stringsTo('call_busy_tips'));
        }else {
            showToast(stringsTo("action_fail"));
        }
        if (this.state.isCalling) {
            this.IMIVideoView.stopSpeak();
            this.setState({isCalling: false});
        }
    }

    // 新版View
    render() {
        let sleepModeIndex = this.state.isSleep ? 1 : 0;
        let sleepBtn = {
            isText: false,
            data: [require("../../resources/images/icon_sleep_off.png"),require("../../resources/images/icon_sleep_on.png")],
            onPress: this._onClickSleepButton,
            disabled: false,
            dataIndex: sleepModeIndex
        };

        //固件不支持 自动 0 {title:'高清',index:2},
        return (
            <XView style={{flex:1}}>
                <HomePageLivePlayerComponent {...this.props}
                                             ref={component => (this.homePageLivePlayerComponent = component)}
                                             videoRef={ref => this.IMIVideoView = ref}
                                             qualityData={[{title:stringsTo('quality_sd'),index:1},{title: stringsTo('quality_fhd'),index:2}]}
                                             navBar={(bps, isFullScreen) => this._renderNavigationBar(bps, isFullScreen)}
                                             toolBarMoreItems={[]}
                                             videoSubView={(isFullScreen,showFullScreenTools) => this.renderVideoSubView(isFullScreen,showFullScreenTools)}
                                             onLivePlayerStatusChange={(status)=>this._onLivePlayerStatusChangeListener(status)}
                                             lensCorrect={this.state.waterSwitch ? {use: this.state.lensCorrect, x: lensCorrect_x, y: lensCorrect_y}:{use: false, x: 0, y: 0}}
                                             isSleepStatus={this.state.isSleep}
                                             isMove={this.state.isMoveStatus}
                                             albumName={LetDevice.deviceID}
                                             isOnLine={this.state.isOnline}
                                             onCheckPermissionStatusChange={(value)=> isCheckingPermission = value}
                                             onDeviceStatusChange={(value)=>{
                                                 console.log('设备当前--当前value值',value);
                                                 // 设备离线
                                                 this.setState({isOnline:false});
                                                 this.getNettimeOut && clearTimeout(this.getNettimeOut);
                                                 this.getNettimeOut = setTimeout(() => {
                                                     if(this.state.currentStatus != HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
                                                         this.IMIVideoView.prepare();
                                                     }
                                                 }, 60000);
                                             }}
                                             onVoiceCallErrorOccurred={(value)=>this._dealWithVoiceCallErrorOccurred(value)}

                >
                    {/*{this.renderPtzControlView()}*/}
                    {this._renderCallAndAlarmView()}
                    {this._renderCallWaveView()}
                    {this._renderNormalBottomLayout()}
                    {/*{this._renderBottomLayout()}*/}
                    {!this.state.fullScreen ? <RNLine style={{height: 1}}/> : null}

                </HomePageLivePlayerComponent>
                {this.state.step?<XView style={{position: "absolute",top:0,left:0,right:0,bottom:0,backgroundColor:'rgba(0, 0, 0, 0.6)'}} onPress={()=>{
                    if (this.state.step==1){
                        // this.setState({step:2})
                        this.setState({step:0})
                        IMIStorage.save({
                            key: LetDevice.deviceID+'cameraGuideForZoomed',
                            data: {
                                cameraGuideForZoomed: 0
                            },
                            expires: null,
                        });
                    }
                    // if (this.state.step==2){
                    //     this.setState({step:0})
                    //     IMIStorage.save({
                    //         key: LetDevice.deviceID+'cameraGuideForZoomed',
                    //         data: {
                    //             cameraGuideForZoomed: 0
                    //         },
                    //         expires: null,
                    //     });
                    // }
                }}>
                    {this.state.step==1?<XView style={{height:"50%",justifyContent:'center',alignItems:'center'}}>
                        <XImage iconSize={50} icon={require('../../resources/images/icon_zoom.png')}/>
                        <XText style={{marginTop:35,color:colors.white,textAlign:'center',height:50,}} text={stringsTo('camera_guide_for_zoomed_str')}/>
                    </XView>:null}
                    {/*{this.state.step==2?*/}
                    {/*    <XView style={{position: "absolute", right: 15, bottom: SCREEN_WIDTH*70/360.0,left:20,height:80,alignItems:'flex-end'}}>*/}
                    {/*        <XText style={{color:colors.white,textAlign:'center'}} text={stringsTo('camera_guide_for_panoramic_str')}/>*/}
                    {/*        <XImage style={{marginTop:14}} iconSize={50} icon={require('../../resources/images/icon_panoramic.png')}*/}
                    {/*        />*/}
                    {/*    </XView>:null}*/}
                </XView>:null}
                {this._renderOneKeySetDialog()}
                {this._renderExpiredCountdownHintDialog()}
                {this._renderGiftDialog()}
            </XView>
        );
    }

    /*云存储即将过期的弹窗提醒*/
    _renderExpiredCountdownHintDialog(){
        //弹窗提示每天只提醒一次
        if(this.state.cloudExpireRemainDays == null ||this.state.cloudExpireRemainDaysLocal==this.state.cloudExpireRemainDays) return null;
        return (<AlertDialog
                showTitle={false}
                visible={this.state.expiredDialog}
                message={this.state.cloudExpireRemainDays==0?I18n.t('expiredHint'):I18n.t('expiredCountdownHint', {code: this.state.cloudExpireRemainDays})}
                messageStyle={{marginVertical:10,marginHorizontal:15,fontSize:17,fontWeight:'500',color:"#333333"}}
                canDismiss={false}

                buttons={[
                    {
                        text: I18n.t("sdcard_tip_cancel"),
                        callback: _ => {
                            this.setState({expiredDialog:false});
                            IMIStorage.save({
                                key: LetDevice.deviceID+'cloudExpireRemainDaysLocal',
                                data: {
                                    cloudExpireRemainDaysLocal: this.state.cloudExpireRemainDays
                                },
                                expires: null,
                            });
                        }
                    },
                    {
                        text: I18n.t('fees_for_renewal'),
                        callback: _ => {
                            this.setState({expiredDialog:false},()=>{
                                IMIGotoPage.starCloudBuyPage(LetDevice.deviceID)
                            });
                            IMIStorage.save({
                                key: LetDevice.deviceID+'cloudExpireRemainDaysLocal',
                                data: {
                                    cloudExpireRemainDaysLocal: this.state.cloudExpireRemainDays
                                },
                                expires: null,
                            });
                        }
                    },
                ]}
            />
        );
    }


    /*领取免费云存提示对话框*/
    _renderGiftDialog(){
        // console.log("渲染----------",this.state.giftDialog);
        if (!this.state.showFreeCloud){
            return null;
        }
        return (<GiftDialog
                visible={this.state.giftDialog}
                resource={require("../../resources/images/pic_gift_box.png")}
                giftName={this.state.giftName}
                giftPrice={this.state.giftPrice}
                currency={this.state.currency}
                canDismiss={false}
                onDismiss={()=>{
                    this.setState({giftDialog:false});
                    this.saveAlertDayTime();
                }}
                onPress={()=>{
                    this.gainFreeCloudStorage();
                    this.setState({giftDialog:false});
                }}
                onPressCloseDialog={()=>{
                    this.setState({giftDialog:false});
                    this.saveAlertDayTime();
                }}


            />
        );
    }
    // 未领取云存 记录当前时间
    saveAlertDayTime() {
        let nowTime = Date.parse(new Date());
        console.log('现在时间',nowTime);

        // let endDate = new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 23, 59, 59);
        // let endTime = parseInt(endDate.getTime());
        // console.log('今天的日期最后--',endDate,endTime);

        let newDate = new Date(( new Date()).getTime()+1000*60*60*24);
        console.log('明天日期---',newDate);
        let tempNewDate = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 0, 0, 0);
        let startTime = parseInt(tempNewDate.getTime());
        console.log('明天0点日期---',tempNewDate,startTime);
        IMIStorage.save({
            key: LetDevice.deviceID+'freeDay',
            data: {
                nextStartTime:startTime
            },
            expires: null,
        });
    }

    getSDCardStatus(){
        LetDevice.getPropertyCloud('StorageStatus').then((value) => { //取得的值是字符串'0'，'-1'，'5'...
            console.log('SD卡getPropertyCloud---------' + value + typeof (value));
            value = parseInt(value);
            this.setState({sdCardStatus: value},callback=>{
                console.log('当前存储卡状态',this.state.sdCardStatus);
            });
            if (value == 0) { //未插卡
            } else if (value == 4) { //存储卡已弹出\n请重新插拔存储卡!
            } else  { //正在格式化
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

}


const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex",
        width: "100%",
        height: SCREEN_WIDTH*60/360.0,
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center",
    },
    bottomLayoutItem: {
        flex: 1,
        height: "100%",
        marginTop: 15,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
    bottomLayoutNormalItem: {
        flex: 1,
        height: "100%",
        marginTop: 8,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
});
