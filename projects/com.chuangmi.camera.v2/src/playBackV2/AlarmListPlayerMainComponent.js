import React from 'react';

import {
    StyleSheet,
    View,
    Dimensions,
    DeviceEventEmitter,
    ScrollView,
    ActivityIndicator,
    Text,
    Image, TouchableOpacity, BackHandler,ImageBackground
} from "react-native";

import {XFlatList, XImage, XText, XView} from 'react-native-easy-app';

import {
    CheckBoxButton,
    colors,
    IMIDesignEmptyView,
    IMIDesignEmptyViewNew,
    IMIImageView,
    RNLine, showLoading, showToast, MessageDialog, imiThemeManager, CONST, RoundedButtonView
} from '../../../../imilab-design-ui';

import {
    AlarmType,
    BaseDeviceComponent,
    DateUtils,
    aliAlarmEventCloudApi,
    LetDevice, IMIVideoView, LetIProperties, IMILog
} from '../../../../imilab-rn-sdk';

import AlarmTopMainSelectBar from "../alarmList/AlarmTopMainSelectBar";
import iconPlay from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_play.png';
import iconPause from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_pause.png';
import iconVoiceClose
    from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_voice_close.png';
import iconVoiceOpen from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_voice_open.png';
import iconSave from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_saveforever.png';
import iconFull from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/icon_full.png';
import iconEmpty from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/police_pic_empty.png';
import iconEmptyNew from '../../resources/images/police_pic_empty_new.png';
import Orientation from "react-native-orientation";
import PropTypes from 'prop-types';
import {getScreenWidth, isAndroid, isEmpty, isIos, isPhoneX, objHasKey} from "../../../../imilab-rn-sdk/utils/Utils";
import I18n, {stringsTo} from "../../../../globalization/Localize";
import {IMIDownload, IMIPackage,} from '../../../../imilab-rn-sdk';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import {PLAYER_EVENT_CODE} from "../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import IMIGotoPage from "../../../../imilab-rn-sdk/native/local-kit/IMIGotoPage";
import moment from "moment";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import AlarmListPlayerComponent from "../alarmList/AlarmListPlayerComponent";
import {
    CoveLoading,
    CoverVideoToolProgress,
    IMICloudVideo,
    VideoPlayerContainer
} from "../../../../imilab-rn-sdk/components";
import CoverJSCloudImage
    from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/player/cover/CoverJSCloudImage";
import CoveError from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/player/cover/CoveError";
import CoveRetry from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/player/cover/CoveRetry";
import IMIPlayerViewBase from "../../../../imilab-rn-sdk/components/av/player/IMIPlayerViewBase";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";
import IMICameraVideoView from "../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import ImageButton from "../../../com.chuangmi.door/src/CommonView/ImageButton/ImageButton";
import NavigationBar from "../../../com.chuangmi.door/src/CommonView/NavigationBar/NavigationBar";
import VideoSubProgressView from "../alarmList/VideoSubProgressView";
import {dateFormat, timeFilter} from "../../../../imilab-rn-sdk/utils/DateUtils";
import PlayCloudToolBarView from "../alarmList/PlayCloudToolBarView";
import TabType, {CLOUD_VIDEO_STATUS} from "../alarmList/TabType";
import VideoStateType from "../comm/VideoStateType";
import Toast from "react-native-root-toast";
import PlayCloudFullScreenToolBarView from "../alarmList/PlayCloudFullScreenToolBarView";
import DowaloadType from "../utils/DownloadType";
import CloudVideoUtil from "../utils/CloudVideoUtil";
import SDVideoUtil from "../utils/SDVideoUtil";
import TimeScaleView2 from "../ui/TimeScaleView2";
import TextImageButton from "../../../../imi-rn-commonView/TextImageButton/TextImageButton";
import ModalView from "../../../../imi-rn-commonView/ModalView/ModalView";
import {Calendar} from "react-native-calendars";
import CenterTimeView from "../ui/CenterTImeView";
import DateHasManager from "../utils/DateHasManager";
import IMIToast from "../../../../imilab-design-ui/src/widgets/IMIToast";
import {IMINativeLifeCycleEvent} from "../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import RecordTimeView from "../ui/RecordTimeView";
import {EVENT_TYPE} from "../live/EventTypeConfig";
import {
    ALL, CRY, CRY_LINE,
    FENCE,
    FENCE_LINE, IAREA_LINE,
    KEY_AREA,
    INTRUSION,
    MOVE,
    MOVE_LINE, NOBODY, NOBODY_LINE,
    People,
    INTRUSION_LINE,
    PEOPLE_LINE, SD_IAREA_LINE, SD_MOVE_LINE, SD_NOBODY_LINE, SD_PEOPLE_LINE, SD_SOUND_LINE,
    SOUND,
    SOUND_LINE, SD_INTRUSION_LINE
} from "../utils/AlarmConstants";
import {isWait} from "../utils/FastClickUtils";
import GlobalUtil from "../utils/GlobalUtil";


/**
 * @Description: 公版摄像看家模块，主模块功能
 * 自上而下模块包含；
 * 1、顶部tab
 * 2、网格视频播放器、时间轴视频播放器、图片展示区域
 * 3、中部功能标签切换tab
 *    a、日期切换
 *    b、事件切换
 *    c、网格与时间轴切换
 * 4、数据展示区域
 *    a、网格视频分组列表展示
 *    b、网格图片列表展示
 *    c、时间轴展示
 * @Author:   byh
 * @CreateDate: 2022
 * @UpdateUser:     更新者
 * @UpdateRemark:   更新说明
 */
const defaultEventCurrentTitles = [
    [stringsTo('all_events_str'), AlarmType.ALL],
    [stringsTo('people_event'), AlarmType.PEOPLE],
];
const defaultAlarmTitles = [
    [stringsTo('bottom_cloud_storage'), TabType.CLOUD],
    [stringsTo('alarm_pic'), TabType.PIC]
];

let windowWidth = Dimensions.get('window').width;
let isGoBack = false;//判断数组是否返回父视图
const queryAnimations = 'https://api.jikan.moe/v3/search/anime?q=Fate/Zero';//模拟測試接口

const DemoUrl = ["https://vfx.mtime.cn/Video/2017/01/05/mp4/170105105137886980_480.mp4", "http://vfx.mtime.cn/Video/2019/07/11/mp4/190711113255914537.mp4"];

const LIMIT = 50;
const EVENT_NAME = "IMIDownloadImageScheduler - ";

const TAG = " AlarmListPlayerMainComponent- ";

const VOD_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading',
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};
let lastClickSnapPhoto = 0; //上一次点击截图的时间
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
let time;//相册名字
const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
let backupIsPlay = false;//标记退到后台，视频是否在播放

export default class AlarmListPlayerMainComponent extends BaseDeviceComponent {

    static propTypes = {
        /* 事件选择title 数组 */
        eventCurrentTitles: PropTypes.array,
        currentTitles: PropTypes.array,
        onClickCancel: PropTypes.func,
        isEdit: PropTypes.bool,
        onClickSelect: PropTypes.func,
        isSelectedAll: PropTypes.bool,
        dataArray: PropTypes.array,
        vipState: PropTypes.number,
        storageType: PropTypes.number,
        // 传入navigation用于返回事件
        navigation: PropTypes.object,
        getDataLength: PropTypes.func,
        lifecycleDay: PropTypes.number,
        sdState: PropTypes.bool,
        tabType: PropTypes.number,
        onFullScreenChange: PropTypes.func,
        onEditShowChange: PropTypes.func,
        onEditModeChange: PropTypes.func,
        downloadStateChangeListener: PropTypes.func,
        eventTypes: PropTypes.number,
        eventTimeLine: PropTypes.array,
        onRecordStatusChangeListener: PropTypes.func,
    };
    static defaultProps = {
        eventCurrentTitles: null,
        currentTitles: null,
        onClickCancel: null,
        isEdit: false,
        onClickSelect: null,
        isSelectedAll: false,
        dataArray: [],
        vipState: -1,
        storageType: 0,
        getDataLength: null,
        tabType: TabType.CLOUD, //标签选中类型，0云存储 1看家图片 2SD卡
        onFullScreenChange: null, //标签选中类型，0云存储 1看家图片 2SD卡
        onEditModeChange: null, //标签选中类型，0云存储 1看家图片 2SD卡
        onEditShowChange: null, //是否展示顶部的编辑按钮
        downloadStateChangeListener: null,
        eventTimeLine: [],
        eventTypes: 0,
        isSDDataEmpty: false,//SD卡的数据是否为空，没有获取到数据
        onRecordStatusChangeListener: null,//录制状态改变
    };

    constructor(props) {
        super(props);
        this.state = {
            /* 数据集合  */
            dataList: [],
            muted: false,
            videoCover: undefined,
            flatListHeight: 0,
            isDataEmpty: true,
            fullScreen: Orientation.getInitialOrientation() !== 'PORTRAIT',
            isPlay: false,
            dataSource: {},
            eventCurrentIndex: 0,
            pageStart: 0,
            /** 某个日期下日历小圆点 **/
            dateTimeDotSelect: this.props.dateTimeDotSelect,
            showCloudBuy: true,                                                                   //未购买云存，默认展示提示购买云存文案
            alarmVideoMode: (props.vipState==1 && props.storageType == 1) ? 1 : 0,                //标记是网格布局还是时间轴  0网格   1时间轴
            tabType: props.tabType,                                                               //标记当前选中的顶部tab
            fileName: '',                                                                         //看家视频播放的fileName
            isFirstVideo: false,                                                                  //网格播放时标记是否是第一天云存视频
            eventTime: '',                                                                        //网格云存当前播放的开始时间
            mute: true,                                                //默认静音
            isPlayFinish: false,
            recording: false,                                          //录制状态
            currentThumbUrl: '',                                       //当前需要展示的看家图片URL
            currentThumbUrlShowError: false,                           //当前需要展示的看家图片URL
            snapshotVisible: false,                                    //云存视频截图可见与否标志位
            snapImgType: -1,                                           //标记是截图还是录制视频缩略图
            screenShotPath: '',                                        //截图路径
            speed: 0,                                                 //倍速
            isFullScreen: false,                                      //全屏标记位，true表示全屏显示
            showFullScreenTools: false,
            showTools: true,                                          //竖屏时视频播放区域，工具栏是否显示
            currentDate: new Date((new Date()).getFullYear(), (new Date()).getMonth(), (new Date()).getDate(), 0, 0, 0),
            dateTime: DateUtils.dateFormat(new Date(), "yyyy-MM-dd"),
            sdStartTime: 0,                                            //SD卡开始播放的时间，用于网格回看播放
            sdEndTime: 0,                                              //SD卡结束播放的时间
            isShowCalendar: false,                                     //全屏时间轴模式，是否展示日历
            dataLength: 0,
            sdStartOffset: -1,                                         //SD卡开始播放时的offset
            sdStartGridOffset: 0,                                      //SD卡网格开始播放时的偏移量默认0
            connectType:'',
            sdFullscreenDayHasVideo:DateHasManager.sdDateList,         //回看时间轴模式全屏日历，标记哪些天有视频
        };
        this.reqIntelligentTypeArray = [];
        //横竖屏切换，tabType状态会重置
        this.isFirstReceiveTabType = true;
        this.isFirstReceiveStorage = true;
        this.pageIndex = 1;//页码

        this.mCurItem = null;
        this.mCurIndex = 0;
        this.num = 0;
        this.keepLoad = false;//标记是否继续加载后面的数据，true继续，false不继续
        this.preProgress = 0;
        this.isForegroundPage = true;//标记是否在前台
        this.isFirstIn = true;//是否是第一次进入页面
        this.needRefreshData = true;//是否需要重新拉取页面数据
        this.gotoOtherPageShowPause = false;//进入其他页面后，返回看家助手，是佛开始播放视频
        this.tempScrollPause = false;//播放暂停中推动进度条
        this.tempScrollTime = 0;//标记是否在前台
        this.videoProgressTime = 0;//已经播放的视频时长，返回的是毫秒
        this.videoPlayTime = 0;//已经播放的视频时长，返回的是毫秒,用于时间轴的恢复播放
        //标记回看网格是否处于准备播放中，作用主要是用于切换播放时，开始时间不置零的问题，
        //原因是回看播放器在prepare时，播放的回调还有可能回调上个视频的播放时间
        this.isSDGridVideoPrepare = false;
        this.startPlayForBack = false;//去往其他页面后，返回到当前页面
        this.recordDuration = 0;
        //**********************************处理时间轴*******************************
        //**********************************相关的操作*******************************
        this.toStartTime = 0;// 暂存要播放的view的时间戳
        this.isCloudScrollToPlay = false;// 标记是否是用户滑动时间轴后定位到具体位置播放
        this.isCloudScrollToPlayCount = 0;
        this.videoItem = null;//当前时间轴正在播放的视频item
        this.lastTimeItemEndTime = 0;//最后视频的那个结束时间
        this.sdVideoItem = null;//当前时间轴正在播放的视频item
        this.lastTimeItemSDEndTime = 0;//最后视频的那个结束时间
        this.timeIndicatorView = null;//时间轴滚动时，显示的时间
        this.startIsDo = true;//补充标记播放器是否调用了start，主要针对云存储，网格与时间轴快速切换

        //
        this.eventAry = [ALL];
        this.eventTimeline = [];
        //默认绘制时间轴的时候，默认的灰色是需要绘制的
        this.eventTypes = EVENT_TYPE.EVENT_TYPE_DEFAULT;
        this.dateTimeForTemp = new Date();
        this.deleteTagForPlayFirstVideo = false;
        this.isLoadingCloudData = false;//标记位，是否正在请求云存视频，云存播放
        //阿里sdk问题，导致回看视频播放完成后，回调complete后还会回调onErrorChange
        //加这个标记，标记视频播放完成
        this.isSDVideoPlayComplete = false;
        this.progressChangeCount = 0;//播放器回调了多少次
        this.canRetryStartRecord = true;//标记，否有已经尝试过在此开启录制
        this.isDownLoadVideo = true;//标记，是下载云存视频，还是看家图片
        this.isMute = true;//标记切换倍速时，当前的监听情况，2倍速时需要静音
        this.currentPullDataType = -1;
        this.scrollStartTime = 0;//拖动开始的时候的时间
        this.scrollEndTime = 0;//拖动结束后的时间
        //打点，记录云存、回看播放时长
        this.cloudGridStartTime = 0;//云存宫格开始时间
        this.cloudLineStartTime = 0;//云存时间轴开始时间
        this.sdGridStartTime = 0;//回看宫格开始时间
        this.sdLineStartTime = 0;//回看时间轴开始时间
        this.playRetryCount = 0;//视频播放异常时的重试次数，最多重试3次
        this.doPlayRetry = false;//视频播放异常时的重试次数，最多重试3次

        //默认的下载状态
        this.downloadState = DowaloadType.SUCCESS;

        //记录播放模式变量
        this.connectType = '';
        //标记回看时间轴，播放中，播放时间大于当天0点的次数，即跨天播放
        this.countForSDLine = 0;

        console.log("dengying",TAG,"tabType=",this.state.tabType);
    }

    UNSAFE_componentWillReceiveProps(nextProps) {
        console.warn("收到了不同的属性", this.state.tabType, nextProps.tabType);
        console.warn("收到了不同的属性storageType", this.props.storageType, nextProps.storageType);

        console.log("dengying",TAG,"componentWillReceiveProps", this.state.tabType, nextProps.tabType);

        if (nextProps.tabType !== this.state.tabType) {

            if (!this.isFirstReceiveTabType) {
                return;
            }
            this.isFirstReceiveTabType = false;
            let mode = 0;
            if (nextProps.tabType == TabType.CLOUD){
                //只针对云存的时候处理
                mode = nextProps.storageType == 1 ? 1 : 0;
            }
            this.setState({tabType: nextProps.tabType, alarmVideoMode: mode}, () => {
                if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1) {
                    //首次进来，是连续型云存
                    this.props.onEditShowChange && this.props.onEditShowChange(false);
                }

                if (this.state.tabType == TabType.SD) {
                    this.props.onEditShowChange && this.props.onEditShowChange(false);
                }
                this.alarmListPlayerComponent && this.alarmListPlayerComponent._queryDataList(true);
            });
        }
    }

    outsideChange(){
        if (this.alarmTopMainSelectBarComponent){
            this.alarmTopMainSelectBarComponent._tabChange();
        }
    }
    //刷新顶部tab选中样式
    refreshSelectIndex(index,titleArr){
        if (this.alarmTopMainSelectBarComponent){
            this.alarmTopMainSelectBarComponent.refreshSelectIndex(index,titleArr);
        }
    }

    doTabChange(tabType){

        console.log("dengying",TAG,"doTabChange=",tabType);

        //重置播放异常重试次数
        //切换模式，重置状态
        this.playRetryCount = 0;
        this.doPlayRetry = false;
        //如果是连续云存，也不展示编辑按钮
        let showEdit = false;

        this.playTimeout && clearTimeout(this.playTimeout);
        if (this.alarmListPlayerComponent) {
            //重置更新事件数组刷新标记位
            this.alarmListPlayerComponent.isInitEvent = false;
        }
        //重置播放进度，防止使得进度条显示错误
        this.videoProgressTime = 0;
        //切换tab时，这个offset需要清零，否则可能出现seekTo的时间不对
        this.offset = 0;
        if (this.state.isPlay) {
            //切换tab的时候，如果正在播放中，暂停\\
            this.IMIVideoView && this.IMIVideoView.stop();
            this.videoItem = null;
            this.sdVideoItem = null;
        }
        //如果是连续云存，切换回来后，不能展示宫格类型
        let mode = 0;
        if (tabType === TabType.CLOUD && this.props.storageType == 1) {
            mode = 1;
        }
        this.countForSDLine = 0;
        this.setState({
            tabType: tabType,
            alarmVideoMode: mode,
            isPlay: false,
            speed: 0,
            mute: true,
            fileName:"",
            dateTime: DateUtils.dateFormat(new Date(), "yyyy-MM-dd")
        }, () => {
            if ((tabType == TabType.CLOUD && this.state.alarmVideoMode == 0) || tabType == TabType.PIC) {
                showEdit = true;
            }
            this.props.onEditShowChange && this.props.onEditShowChange(showEdit);
            this.startIsDo = false;
            // this.alarmListPlayerComponent.setReqTime(DateUtils.dateFormat(new Date(), "yyyy-MM-dd"));
            this.alarmListPlayerComponent.resetReqTimeAndEvent(DateUtils.dateFormat(new Date(), "yyyy-MM-dd"));
            // this.alarmListPlayerComponent._queryDataList(true, true);
        });
    }
    render() {
        let {showHideSelect} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let {
            showPeopleEvent,
            showMoveEvent,
            showAlarmLoudSwitch,
            showNoHuman,
            showFencesSwitch,
            showCrySwitch,
            showAreaEvent
        } = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);

        this.eventAry = [ALL];

        this.eventTimeline = [];
        //默认绘制时间轴的时候，默认的灰色是需要绘制的
        this.eventTypes = EVENT_TYPE.EVENT_TYPE_DEFAULT;
        console.log("=====", this.state.tabType);
        if (showPeopleEvent) {
            this.eventAry.push(People);
            if (this.state.tabType == TabType.SD) {
                this.eventTimeline.push(SD_PEOPLE_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_SD_PEOPLE
            } else {
                this.eventTimeline.push(PEOPLE_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_PEOPLE_MOTION
            }

        }
        if (showMoveEvent) {
            this.eventAry.push(MOVE);
            if (this.state.tabType == TabType.SD) {
                this.eventTimeline.push(SD_MOVE_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_SD_MOVE
            } else {
                this.eventTimeline.push(MOVE_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_AREA_MOTION
            }

        }
        if (showAlarmLoudSwitch) {
            this.eventAry.push(SOUND)
            if (this.state.tabType == TabType.SD) {
                this.eventTimeline.push(SD_SOUND_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_SD_ABNORMAL_SOUND
            } else {
                this.eventTimeline.push(SOUND_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_ABNORMAL_SOUND
            }

        }
        if (showFencesSwitch) {
            //暂无围栏,越界
            this.eventAry.push(INTRUSION)
            if (this.state.tabType == TabType.SD) {
                this.eventTimeline.push(SD_INTRUSION_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_SD_INTRUSION
            } else {
                this.eventTimeline.push(INTRUSION_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_INTRUSION
            }
        }
        if (showAreaEvent) {
            this.eventAry.push(KEY_AREA)
            if (this.state.tabType == TabType.SD) {
                this.eventTimeline.push(SD_IAREA_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_SD_KEY_AREA
            } else {
                this.eventTimeline.push(IAREA_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_KEY_AREA
            }

        }
        if (showNoHuman) {
            this.eventAry.push(NOBODY)
            if (this.state.tabType == TabType.SD) {
                this.eventTimeline.push(SD_NOBODY_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_SD_NOBODY
            } else {
                this.eventTimeline.push(NOBODY_LINE)
                this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_NOBODY_MOTION
            }

        }

        if (showCrySwitch) {
            //暂无哭声
            this.eventAry.push(CRY)
            this.eventTimeline.push(CRY_LINE)
            this.eventTypes = this.eventTypes | EVENT_TYPE.EVENT_TYPE_BABY_CRY
        }
        return (
            <XView style={styles.container}>
                {/*<AlarmTopMainSelectBar
                    ref={component => (this.alarmTopMainSelectBarComponent = component)}
                    vipState={this.props.vipState}
                    storageType={this.props.storageType}
                    sdState={this.props.sdState}
                    currentTitles={this.props.currentTitles ? this.props.currentTitles : defaultAlarmTitles}
                    recording={this.state.recording}
                    onTabPress={(tabType) => {
                        console.warn("onTabChange", tabType);
                        //tab的回调

                        if (this.state.tabType != tabType) {
                            this.doTabChange(tabType);
                        }
                    }}
                    hideTitleSelect={this.state.isFullScreen || (showHideSelect ? this.props.isEdit : false)}

                    renderView={this._renderView.bind(this)}
                >
                </AlarmTopMainSelectBar>*/}

                {this._renderView()}
            </XView>
        );
    }

    _renderView() {
        return (
            <View style={{flex: 1}}>
                {/*云存储、看家图片、SD卡*/}
                {/*云存视频播放器*/}
                {this.state.tabType === TabType.CLOUD || this.state.tabType === TabType.SD ? this.renderPlayerVideoView() : null}
                {/*看家图片显示*/}
                {this.state.tabType === TabType.PIC || this.state.tabType == -1 ? this.renderThumbImageView() : null}
                {/*!this.state.fullScreen ? this.renderAlarmView() : null不能这么写，子组件会卸载，重新请求网络*/}
                {this.renderAlarmView()}
                {/*渲染播放列表*/}
                {/*{!this.state.fullScreen ? this._renderListView() : null}*/}

                {/*编辑底部*/}
                {/*{this._renderEditView()}*/}
            </View>
        )
    }

    //视频播放器
    renderPlayerVideoView() {
        if (this.props.isEdit) {
            return null;
        }
        //alarmVideoMode 0网格 1时间轴
        if ((this.state.tabType == TabType.CLOUD || this.state.tabType == TabType.SD) && this.state.alarmVideoMode == 0 && this.state.dataLength == 0) {
            return this.renderEmptyView();
        }
        let selectDate = new Date();
        let timeMl = moment(this.state.dateTime).valueOf();
        selectDate.setTime(timeMl);
        if (this.state.tabType == TabType.SD) {
            if (this.state.isSDDataEmpty) {
                return this.renderEmptyView();
            }
            selectDate.setHours(0);
            selectDate.setMinutes(0, 0, 0);
            console.log("startTime", selectDate.getTime(), selectDate.getTime() + 24 * 60 * 60 * 1000 - 1)
            console.log("startTime2", selectDate.valueOf(), selectDate.valueOf() + 24 * 60 * 60 * 1000 - 1)
        }
        //播放器不能复用，只能每个播放器独立出来了
        return (
            //flex:0.7
            <View pointerEvents="box-none"
                  style={[this.state.isFullScreen ? {flex: 1} : {height: getScreenWidth() * 9 / 16}, {
                      backgroundColor: "#FFFFFF",
                      flexDirection: "column"
                  }]}>
                <View style={{flex: 1}}>
                    {this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0 ?
                        <IMIVideoView
                            style={{flex: 1}}
                            ref={ref => {
                                this.IMIVideoView = ref;
                                this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                            }}
                            mute={this.state.mute}
                            playerClass={IMICameraVideoView.PlayerClass.VOD}
                            dataSource={{
                                playerClass: IMICameraVideoView.PlayerClass.VOD,
                                did: LetDevice.deviceID,
                                vod: {
                                    //单个回看视频的开始时间结束时间
                                    startTime: parseInt(this.state.sdStartTime / 1000),
                                    endTime: parseInt((this.state.sdEndTime) / 1000)
                                },
                                offsetTime: this.state.sdStartGridOffset
                            }}
                            scaleRatio={1.0}
                            lensCorrect={{use:false, x:0, y:0}}
                            onPrepared={(value) => {
                                console.log("imi sd prepare");

                                //准备开始播放记录时间
                                this.sdGridStartTime = new Date().getTime();
                                this.scrollStartTime = 0;
                                this.scrollEndTime = 0;
                                this.progressChangeCount = 0;
                                //开始播放
                                this.IMIVideoView && this.IMIVideoView.start();
                                this._afterPlayToHideTools();
                                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
                            }}
                            onVideoViewClick={() => {
                                this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                                !this.state.isFullScreen && this.state.showTools ? this._onCloseScreenTools() : this._onPressScreenTools();
                                this.props.onVideoClick && this.props.onVideoClick();
                            }}
                            onEventChange={this._onEventChange}
                            onPlayCompletion={() => {
                                if (this.sdGridStartTime > 0){
                                    GlobalUtil.sdGridVideoTime += new Date().getTime()-this.sdGridStartTime;
                                    this.sdGridStartTime = 0;
                                }
                                console.log("我播放结束了，哈哈哈哈");
                                this.playRetryCount = 0;
                                this.doPlayRetry = false;
                                if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0) {
                                    //回看网格类型的播放
                                    if (this.state.recording) {
                                        if (CONST.isAndroid) {
                                            this._stopRecord();
                                        } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                            this._saveVideoToPhotosAlbum();
                                        }
                                    }
                                    //回看播放完成后并不会自己就停止播放，固件虽然停止推流了，但是播放器还是会去拉流
                                    //这里主动去停止播放

                                    // this.tempTimeout && clearTimeout(this.tempTimeout);
                                    // this.tempTimeout = setTimeout(()=>{
                                    //
                                    // },2000);

                                    this.IMIVideoView && this.IMIVideoView.stop();
                                    this.setState({
                                        isPlay: false,
                                        showPauseView: true,
                                        isPlayFinish: true,
                                        isClickPause: false,
                                        speed: 0
                                    }, () => {
                                        this.tempScrollPause = false;
                                    });
                                    this.isSDVideoPlayComplete = true;


                                }

                            }}
                            onErrorChange={(event) => {
                                console.log("onErrorChange sd:", event, this.isSDVideoPlayComplete);
                                if (this.isSDVideoPlayComplete) {
                                    return;
                                }
                                if (this.sdGridStartTime > 0){
                                    GlobalUtil.sdGridVideoTime += new Date().getTime()-this.sdGridStartTime;
                                    this.sdGridStartTime = 0;
                                }
                                if (this.state.recording) { //直播流暂停时，停止录像
                                    if (CONST.isAndroid) {
                                        this._stopRecord();
                                    } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                        this._saveVideoToPhotosAlbum();
                                    }
                                }
                                if (this.playRetryCount < 3){
                                    console.log("do retry play",this.playRetryCount,new Date().getTime());
                                    if (this.doPlayRetry){
                                        //忽略掉这次的异常
                                        return;
                                    }
                                    //重试，调用后，再次返回onErrorChange
                                    this.IMIVideoView && this.IMIVideoView.stop();
                                    this.setState({isLoading:true});
                                    //标记是否处于播放重试中
                                    this.doPlayRetry = true;
                                    this.playRetryCount++;
                                    let startTime = this.state.sdStartTime;
                                    let endTime = this.state.sdEndTime;
                                    //ios需要除以1000，传给固件，固件现在接收的是秒
                                    let offsetGrid = this.videoProgressTime;
                                    //播放进度回调与我们的重试操作异步，可能存在偏移溢出的问题，导致重试播放失败
                                    if (startTime+offsetGrid >= endTime){
                                        //开始时间+偏移量 大于等于结束时间
                                        //偏移量重置为0,不再偏移
                                        offsetGrid = 0;
                                    }
                                    if (isIos() && IMIPackage.minApiLevel < 10011){
                                        //10011开始ios会处理这个时间与Android保持一致，传给固件的都是秒
                                        offsetGrid = offsetGrid/1000;
                                        if (offsetGrid > 24*3600){
                                            offsetGrid = 0;
                                        }
                                    }
                                    console.log("重试时的播放偏移量",offsetGrid)
                                    this.retryTimeout && clearTimeout(this.retryTimeout);
                                    this.retryTimeout = setTimeout(()=>{
                                        this.setState({
                                            sdStartTime: 0,
                                            sdEndTime: 0
                                        }, () => {
                                            this.setState({
                                                sdStartTime: startTime,
                                                sdEndTime: endTime,
                                                sdStartGridOffset: offsetGrid,
                                            },()=>{
                                                this.doPlayRetry = false;
                                                this.IMIVideoView && this.IMIVideoView.prepare();
                                            })
                                        });
                                    },500);
                                }else {
                                    // this.playRetryCount = 0;
                                    this.setState({
                                        isPlay: false,
                                        showErrorView: true,
                                        showPauseView: false,
                                        isLoading: false,
                                        errorCode: event.code,
                                    });

                                    this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);
                                }


                            }}
                            onRecordTimeChange={this._onRecordTimeChange}
                        />
                        : null}

                    {this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1 ?
                        <IMIVideoView
                            style={{flex: 1}}
                            ref={ref => {
                                this.IMIVideoView = ref;
                                this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                            }}
                            mute={this.state.mute}
                            playerClass={IMICameraVideoView.PlayerClass.VOD}
                            dataSource={{
                                playerClass: IMICameraVideoView.PlayerClass.VOD,
                                did: LetDevice.deviceID,
                                vod: {
                                    // 先暂时去掉currentPlayTime，解决在
                                    // 回看页面点击其他按钮页面卡住的问题，如果加上currentPlayTime是优化移动录制自动跳转到下个时间的
                                    // startTime: parseInt(this.state.currentDate.getTime()/ 1000)+currentPlayTime,
                                    // startTime: parseInt(this.state.sdStartTime / 1000),
                                    // endTime: parseInt((this.state.sdEndTime) / 1000),
                                    //改为设置全天播放，播放器只初始化一次，后面定位播放直接seekTo
                                    startTime: parseInt(selectDate.getTime() / 1000),
                                    endTime: parseInt((selectDate.getTime() + 24 * 60 * 60 * 1000 - 1) / 1000)
                                    // startTime: parseInt(this.state.sdStartTime / 1000),
                                    // endTime: parseInt((this.state.sdStartTime+ 24 * 60 * 60 * 1000 - 1) / 1000)

                                },

                                offsetTime: this.state.sdStartOffset
                            }}
                            scaleRatio={1.0}
                            lensCorrect={{use:false, x:0, y:0}}
                            onPrepared={(value) => {
                                //开始播放
                                this.sdLineStartTime = new Date().getTime();
                                this.scrollStartTime = 0;
                                this.scrollEndTime = 0;
                                this.isSDVideoPlayComplete = false;
                                this.progressChangeCount = 0;
                                this.IMIVideoView && this.IMIVideoView.start();
                                this._afterPlayToHideTools();
                                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
                            }}
                            onVideoViewClick={() => {
                                this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                                !this.state.isFullScreen && this.state.showTools ? this._onCloseScreenTools() : this._onPressScreenTools();
                                this.props.onVideoClick && this.props.onVideoClick();
                            }}
                            onEventChange={this._onEventChange}
                            onPlayCompletion={() => {
                                console.warn("我播放完成了")
                                this.playRetryCount = 0;
                                this.doPlayRetry = false;
                                if (this.sdLineStartTime >0){
                                    GlobalUtil.sdLineVideoTime += new Date().getTime() - this.sdLineStartTime;
                                    this.sdLineStartTime = 0;
                                }
                               if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1) {
                                    //SD卡时间轴某片段视频播放完成
                                    if (this.state.recording) {
                                        if (CONST.isAndroid) {
                                            this._stopRecord();
                                        } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                            this._saveVideoToPhotosAlbum();
                                        }
                                    }
                                    this.reachCurrentSDFileEnd();

                                }

                            }}
                            onErrorChange={(event) => {
                                if (this.sdLineStartTime >0){
                                    GlobalUtil.sdLineVideoTime += new Date().getTime() - this.sdLineStartTime;
                                    this.sdLineStartTime = 0;
                                }
                                if (this.state.recording) { //直播流暂停时，停止录像
                                    if (CONST.isAndroid) {
                                        this._stopRecord();
                                    } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                        this._saveVideoToPhotosAlbum();
                                    }
                                }
                                //时间轴回看视频，播放异常的时候，增加重试
                                if (this.playRetryCount < 3){
                                    console.log("do retry play line",this.playRetryCount,new Date().getTime());
                                    if (this.doPlayRetry){
                                        //忽略掉这次的异常
                                        return;
                                    }
                                    this.doPlayRetry = true;
                                    this.playRetryCount++;
                                    this.IMIVideoView && this.IMIVideoView.stop();
                                    this.setState({isLoading:true});
                                    //ios也需要除以1000，传给固件，固件现在接收的是秒
                                    //时间戳的播放偏移量
                                    let offsetLine = this.toStartTime - selectDate.getTime();

                                    if (isIos() && IMIPackage.minApiLevel < 10011){
                                        offsetLine = offsetLine/1000;
                                        if (offsetLine > 24*3600){
                                            offsetLine = 0;
                                        }
                                    }
                                    console.log("重试时的播放偏移量 line",offsetLine)
                                    if (offsetLine < 0){
                                        offsetLine = 0;
                                    }
                                    this.retryTimeout && clearTimeout(this.retryTimeout);
                                    this.retryTimeout = setTimeout(()=>{
                                        this.setState({
                                            sdStartOffset:offsetLine,
                                        },()=>{
                                            this.doPlayRetry = false;
                                            this.IMIVideoView && this.IMIVideoView.prepare();
                                        })
                                    },500);


                                }else {
                                    this.setState({
                                        isPlay: false,
                                        showErrorView: true,
                                        showPauseView: false,
                                        isLoading: false,
                                        errorCode: event.code,
                                    });
                                    this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);
                                }
                            }}
                            onRecordTimeChange={this._onRecordTimeChange}
                        />
                        : null}

                    {this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 0 ?
                        <IMIVideoView
                            style={{flex: 1}}
                            ref={ref => {
                                this.IMIVideoView = ref;
                                this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                            }}

                            mute={this.state.mute}
                            playerClass={IMICameraVideoView.PlayerClass.HLS}
                            dataSource={{
                                playerClass: IMICameraVideoView.PlayerClass.HLS,
                                did: LetDevice.deviceID,
                                offsetTime: 0,
                                hls: {
                                    eventID: "",
                                    fileName: this.state.fileName
                                }
                            }}
                            scaleRatio={1.0}
                            lensCorrect={{use:false, x:0, y:0}}

                            onPrepared={() => {
                                if (!this.state.fileName){
                                    return;
                                }
                                console.log("imi cloud prepare")
                                this.cloudGridStartTime = new Date().getTime();
                                this.scrollStartTime = 0;
                                this.scrollEndTime = 0;
                                this.progressChangeCount = 0;
                                //SD视频播放完成的标记位，播放其他视频时重置标记位
                                this.isSDVideoPlayComplete = false;
                                //开始播放
                                this.IMIVideoView && this.IMIVideoView.start();
                                this._afterPlayToHideTools();
                                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
                            }}
                            onVideoViewClick={() => {
                                this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                                !this.state.isFullScreen && this.state.showTools ? this._onCloseScreenTools() : this._onPressScreenTools();
                                this.props.onVideoClick && this.props.onVideoClick();
                            }}
                            onEventChange={this._onEventChange}
                            onPlayCompletion={() => {
                                if (this.cloudGridStartTime > 0){
                                    GlobalUtil.cloudGridVideoTime += new Date().getTime()-this.cloudGridStartTime;
                                    this.cloudGridStartTime = 0;
                                }
                                //时间轴类型的视频播放，在播放完成后，需要切换到下一个视频播放
                                if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 0) {
                                    //2022-03-15@byh 倍速播放完成后，播放进度与视频长度不一致问题
                                    let second = this.state.durationProgress;
                                    this.progressView && this.progressView.onSliderValueChanged(second);
                                    if (this.state.recording) { //直播流暂停时，停止录像
                                        //TODO
                                        if (CONST.isAndroid) {
                                            this._stopRecord(false, true);
                                        } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                            this._saveVideoToPhotosAlbum();
                                        }
                                    }
                                    //增加speed置为1倍速
                                    this.setState({
                                        isPlay: false,
                                        showPauseView: true,
                                        isPlayFinish: true,
                                        isClickPause: false,
                                        speed: 0
                                    }, () => {
                                        this.tempScrollPause = false;
                                    });
                                }

                            }}
                            onErrorChange={(event) => {
                                //解决第一次进来，fileName为空，报错，url is empty错误
                                if (!this.state.fileName){
                                    return;
                                }
                                if (this.cloudGridStartTime > 0){
                                    GlobalUtil.cloudGridVideoTime += new Date().getTime()-this.cloudGridStartTime;
                                    this.cloudGridStartTime = 0;
                                }
                                this.setState({
                                    isPlay: false,
                                    showErrorView: true,
                                    showPauseView: false,
                                    isLoading: false,
                                    errorCode: event.code,
                                });
                                if (this.state.recording) { //直播流暂停时，停止录像
                                    if (CONST.isAndroid) {
                                        this._stopRecord();
                                    } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                        this._saveVideoToPhotosAlbum();
                                    }
                                }
                                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);
                            }}
                            onRecordTimeChange={this._onRecordTimeChange}
                        /> : null
                    }

                    {this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1 ?
                        <IMIVideoView
                            style={{flex: 1}}
                            ref={ref => {
                                this.IMIVideoView = ref;
                                this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                            }}

                            mute={this.state.mute}
                            playerClass={IMICameraVideoView.PlayerClass.HLS}
                            dataSource={{
                                playerClass: IMICameraVideoView.PlayerClass.HLS,
                                did: LetDevice.deviceID,
                                offsetTime: 0,
                                hls: {
                                    eventID: "",
                                    fileName: this.state.fileName
                                }
                            }}

                            scaleRatio={1.0}
                            lensCorrect={{use:false, x:0, y:0}}
                            onPrepared={() => {
                                //开始播放
                                this.scrollStartTime = 0;
                                this.scrollEndTime = 0;
                                this.progressChangeCount = 0;
                                this.isSDVideoPlayComplete = false;
                                this.IMIVideoView && this.IMIVideoView.start();
                                this._afterPlayToHideTools();
                                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
                            }}
                            onVideoViewClick={() => {
                                this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                                !this.state.isFullScreen && this.state.showTools ? this._onCloseScreenTools() : this._onPressScreenTools();
                                this.props.onVideoClick && this.props.onVideoClick();
                            }}
                            onEventChange={this._onEventChange}
                            onPlayCompletion={() => {
                                //时间轴类型的视频播放，在播放完成后，需要切换到下一个视频播放
                                if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1) {
                                    if (this.cloudLineStartTime > 0){
                                        GlobalUtil.cloudLineVideoTime += new Date().getTime()-this.cloudLineStartTime;
                                        this.cloudLineStartTime = 0;
                                    }
                                    //云存视频时间轴某个视频播放完成
                                    if (this.state.recording) {
                                        if (CONST.isAndroid) {
                                            this._stopRecord();
                                        } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                            this._saveVideoToPhotosAlbum();
                                        }
                                    }
                                    this.reachCurrentFileEnd();
                                }
                            }}
                            onErrorChange={(event) => {
                                if (this.state.recording) { //直播流暂停时，停止录像
                                    if (CONST.isAndroid) {
                                        this._stopRecord();
                                    } else { //因为IOS会自动停止视频录制，所以直接转存即可
                                        this._saveVideoToPhotosAlbum();
                                    }
                                }

                                this.setState({
                                    isPlay: false,
                                    showErrorView: true,
                                    showPauseView: false,
                                    isLoading: false,
                                    errorCode: event.code,
                                });

                                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);
                            }}
                            onRecordTimeChange={this._onRecordTimeChange}
                        /> : null
                    }


                    {/*全屏?横屏UI:竖屏UI(navBar)*/}
                    <View pointerEvents="box-none" style={{
                        position: "absolute",
                        width: "100%",
                        height: "100%",
                        flexDirection: "column",
                        alignItems: "center"
                    }}>
                        {this.state.isFullScreen ? this.renderVideoFullScreenOperateView() : this.renderVideoOperateView()}
                        {this.state.isFullScreen ? this.renderFullScreenTimelineVideoOperateView() : this.renderTimelineVideoOperateView()}
                        {this.state.isFullScreen ? this.renderFullScreenTimeLineView() : null}
                        {this.getCalendarLandscapeView()}
                        {this._renderSnapshotView()}
                        {this.renderVideoProgressView()}
                        {this._renderTimeIndicatorView()}

                        {this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()}
                        {this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()}
                        {this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()}
                        {/*{this.state.isFullScreen ? this._renderLandscapeScreenVideoViewArea() : this._renderPortraitScreenVideoViewArea()}*/}
                        {this._renderRecordingView()}
                        {this._renderConnectTypeView()}
                        {/*{this._showAlarmView()}*/}
                        {/*{this._noFirstData()}*/}
                        {/*{this._noVideoView()}*/}
                    </View>
                </View>
                {/*全屏?null:渲染外层传入的UI*/}
                {/*{this.state.isFullScreen ? null : this._renderPortraitScreenPlayerToolBarArea()}*/}
                {/*{showModel && this.state.isFullScreen ? this.getCalendarLandscapeView() : this.getCalendarView()}*/}
                {/*{this._recordTimeModal()}*/}
            </View>

        )
    }

    //看家图片
    renderThumbImageView() {
        if (this.props.isEdit) {
            return null;
        }
        //显示默认带imi logo的空白页填充视频播放区域
        if (this.props.vipState == -1 || this.state.dataLength == 0) {
            return this.renderEmptyView();
        }
        return (
            <View pointerEvents="box-none"
                  style={{height: getScreenWidth() * 9 / 16, backgroundColor: "#FFFFFF", flexDirection: "column"}}>
                {this.state.currentThumbUrl && !this.state.currentThumbUrlShowError?

                    <ImageBackground style={{width: '100%', height: '100%',backgroundColor: "#eeeeee"}}
                           source={this.state.currentThumbUrl ? {uri: this.state.currentThumbUrl} : require("../../resources/images/fixed_sleep_top_bg.png")}
                           onError={(error)=>{
                               //|| !this.state.currentThumbUrlShowError
                              console.log("图片加载错误");
                              this.setState({currentThumbUrlShowError:true})
                           }}
                           onLoad={({nativeEvent: {source: {width, height}}}) => {
                               console.log("图片加载成功")
                           }}/>
                    :
                    <View pointerEvents="box-none"
                          style={{
                              width: '100%',
                              height: '100%',
                              backgroundColor: "rgba(0,0,0,0.1)",
                              flexDirection: "column",
                              alignItems: 'center',
                              justifyContent: 'center'
                          }}>
                        <Image style={{width: 60, height: 60}}
                               source={require("../../resources/images/icon_logo.png")}/>
                    </View>
                }

                {this.renderCloudBuyView()}
                {this.state.currentThumbUrl && !this.state.currentThumbUrlShowError?
                    <TouchableOpacity
                        style={{position: "absolute", right: 0, bottom: 0, marginRight: 15, marginBottom: 15}}
                        onPress={this.downloadPress}>
                        <Image style={{width: 30, height: 30}}
                               source={require("../../resources/images/icon_download_white.png")}/>
                    </TouchableOpacity>
                    : null
                }

            </View>
        )
    }

    renderEmptyView() {
        if ((this.state.tabType == TabType.CLOUD || this.state.tabType == TabType.PIC) && this.state.dataLength != 0) {
            return null;
        }

        return (
            <View pointerEvents="box-none"
                  style={{
                      height: getScreenWidth() * 9 / 16,
                      backgroundColor: "rgba(255,255,255,0.1)",
                      flexDirection: "column",
                      alignItems: 'center',
                      justifyContent: 'center'
                  }}>
                <Image style={{width: 60, height: 60}}
                       source={require("../../resources/images/icon_logo.png")}/>
            </View>
        )
    }

    renderCloudBuyView() {

        if (this.props.vipState == 1 || this.props.vipState == -1) {
            //VIP不显示云存购买
            return null;
        }
        if (LetDevice.isShareUser) {
            //共享用户，不显示购买
            return null;
        }
        let source = this.state.showCloudBuy ? require("../../resources/images/icon_arrow_right.png") : require("../../resources/images/icon_arrow_left.png");
        return (
            <View style={{position: "absolute", right: 0, marginTop: 24,  minHeight: 40}}>
                <View style={{
                    flexDirection: 'row',
                    minHeight: 40,
                    borderBottomLeftRadius: 20,
                    borderTopLeftRadius: 20,
                    backgroundColor: '#4A70A5',
                    maxWidth:windowWidth-100,
                    alignItems: 'center'
                }}>

                    <TouchableOpacity onPress={() => {
                        this.setState({showCloudBuy: !this.state.showCloudBuy})
                    }}>
                        <Image style={{width: 30, height: 30}} source={source}/>
                    </TouchableOpacity>

                    {this.state.showCloudBuy
                        ?<XText
                            raw={true}
                            text={stringsTo('buy_cloud_for_info')}
                            style={{textAlign: 'center', fontSize: 12, color: 'white',maxWidth:windowWidth-130,paddingRight:5}}
                            onPress={() => {
                                if (LetDevice.isShareUser) {
                                    showToast(stringsTo('shareUser_tip'));
                                    return
                                }
                                IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
                            }}/>: null}

                </View>

            </View>
        )
    }

    /**
     * 进入编辑模式
     */
    enterEditMode() {
        //播放中，停止播放
        this.videoProgressTime = 0;
        if (this.state.isPlay || this.state.isClickPause) {
            this.setState({isPlay: false, showPauseView: true, isLoading: false, isClickPause: false}, () => {
                this.IMIVideoView && this.IMIVideoView.stop();
                this.progressView && this.progressView.onSliderValueChanged(0);
            });
        }
        this.alarmListPlayerComponent.enterOrExitEditMode(true);
    }

    /**
     * 退出编辑模式
     * 如果有删除操作，在退出编辑模式时
     * 去播放第一个视频
     */
    exitEditMode() {

        if (this.state.tabType == TabType.CLOUD && this.deleteTagForPlayFirstVideo) {
            //视频
            let itemVideo = this.alarmListPlayerComponent && this.alarmListPlayerComponent.getFirstVideo();
            if (itemVideo != null) {
                this.alarmListPlayerComponent.enterOrExitEditMode(false, itemVideo.fileName);
                this.deleteTagForPlayFirstVideo = false;
                //播放
                this._handlerVideoStateChange({
                    type: VideoStateType.GET_FILENAME,
                    fileName: itemVideo.fileName,
                    eventTime: itemVideo.eventTime,
                    isFirstVideo: true
                })
                return;
            }
        }
        this.deleteTagForPlayFirstVideo = false;
        this.alarmListPlayerComponent.enterOrExitEditMode(false);

    }

    _selectAllOrNot() {
        this.alarmListPlayerComponent.onSelectAllChanged();
    }

    _onPressFullScreenTools = () => {
        this.setState({showFullScreenTools: true}, () => {
            this._reRenderTimeView();
        });
        this._hidePlayToolBarLater()
        // this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        // this.fullScreenTooltsTimer = setTimeout(() => {
        //     this._onCloseFullScreenTools();
        // }, 5000);
    }

    _reRenderTimeView() {
        if ((this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1) && this.state.isFullScreen) {
            //如果是时间轴类型的数据，全屏后，我们重新去填充下数据
            this.timelineView && this.timelineView.onReceiveCloudDatas();
        }

        if ((this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1) && !this.state.isFullScreen) {
            //如果是时间轴类型的数据，全屏后，我们重新去填充下数据
            this.alarmListPlayerComponent && this.alarmListPlayerComponent.reRenderCloudTimeLineView();
        }

        if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1 && this.state.isFullScreen) {
            this.timelineView && this.timelineView.onReceiveSDDatas();
        }

        if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1 && !this.state.isFullScreen) {
            this.alarmListPlayerComponent && this.alarmListPlayerComponent.reRenderSDTimeLineView();
        }
    }

    _afterPlayToHideTools() {
        if (this.state.isFullScreen && this.state.showFullScreenTools) {
            this._onPressFullScreenTools();
        }

        if (!this.state.isFullScreen && this.state.showTools) {
            this._onPressScreenTools();
        }
    }

    _onCloseFullScreenTools() {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.setState({showFullScreenTools: false});
    }

    _hidePlayToolBarLater() {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.fullScreenTooltsTimer = setTimeout(() => {
            if (this.timelineView && !this.timelineView.isTimelineIdle() && this.state.isFullScreen) {
                this._hidePlayToolBarLater();
                return;
            }
            this.setState({showFullScreenTools: false});
        }, 5000);
    }

    //-----------------竖屏工具栏的展示与隐藏 start-----------------
    _onPressScreenTools = () => {
        this.setState({showTools: true}, () => {
            //需要把毫秒转成秒
            let second = Math.round(this.videoProgressTime / 1000);
            this.progressView && this.progressView.onSliderValueChanged(second);
        });
        this._hideToolsLater();

    }
    _onCloseScreenTools = () => {
        this.screenTooltsTimer && clearTimeout(this.screenTooltsTimer);
        this.setState({showTools: false});
    }

    _hideToolsLater() {
        this.screenTooltsTimer && clearTimeout(this.screenTooltsTimer);
        this.screenTooltsTimer = setTimeout(() => {
            if (this.progressView && !this.progressView.isIdle()) {
                //如果正在拖动进度条云存视频进度条
                this._hideToolsLater();
                return;
            }
            this.setState({showTools: false});
        }, 5000);
    }

    //-----------------竖屏工具栏的展示与隐藏 end-----------------

    _onEventChange = (event) => {
        console.log("_onEventChange", event)

        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            //this.setState({bps: event.extra.bps})
            //时间轴时拖动进度条后，ios会执行这个方法
            if (isIos() && this.state.isLoading && this.state.isPlay) {
                //ios 未播放状态
                this.setState({
                    isLoading: false,
                    showPauseView: false,
                    isPlay: true,
                    isPlayFinish: false,
                    showErrorView: false,
                });
            }
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
            this.playRetryCount = 0;
            this.doPlayRetry = false;
            if (event.streamConnectType){
                this.connectType = event.streamConnectType;
            }
            if (this.isSDGridVideoPrepare && this.state.tabType === TabType.SD && this.state.alarmVideoMode == 0){
                //切换网格回看播放，播放时间置0时，有可能这个回调还会执行，导致置零后，又被至于这个回调的播放时间
                this.isSDGridVideoPrepare = false;
                return;
            }
            if (this.isSDVideoPlayComplete && this.state.tabType === TabType.SD && this.state.alarmVideoMode == 0) {
                //SD卡回看已经播放完，但是这个回调可能还在持续回调，需要屏蔽掉
                return;
            }
            if(this.state.isLoading){
                this.setState({isLoading: false});
            }
            this.videoProgressTime = event.extra.currentTime;
            //主动暂停网格型云存视频播放，这个回调回来慢的情况下会有问题，不显示暂停按钮
            //做修正，执行6次回调后，执行修正
            if (this.state.showPauseView || !this.state.isPlay) {
                if (this.progressChangeCount > 5) {
                    this.tempScrollPause = false;
                    //存在已经在播放了，还显示暂停的情况，暂时未排查出哪里出现了暂停，这里做一次修正
                    this.setState({
                        isLoading: false,
                        isPlay: true,
                        showPauseView: false,
                        isPlayFinish: false,
                        showErrorView: false,
                        isClickPause:false
                    }, () => {
                        this.progressChangeCount = 0;
                    });
                }
                this.progressChangeCount++;
            }

            // let scrollTime = currentPlayTime-1 >= event.extra.currentTime/1000.0 ? currentPlayTime : event.extra.currentTime/1000;


            if (this.tempScrollPause) {  //修改暂停逻辑 pause  需要resume后再seekto 防止回复在加载
                // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
                console.log("seekTo tempScrollTime",Math.round(this.tempScrollTime * 1000))
                this.tempScrollPause = false;
                IMILog.logD(TAG,"seekTo tempScrollTime:"+this.tempScrollTime);
                this.IMIVideoView && this.IMIVideoView.seekTo(Math.round((this.tempScrollTime<0 ? 0:this.tempScrollTime) * 1000));
            } else {
                //202200302@byh ios最后返回的时间会比，视频时长少一秒，增加0.5修正，增加返回的视频时长与实际视频时长判断
                //Android不能添加,添加播放结束后可能在00：01秒位置
                let scrollTime = event.extra.currentTime / 1000;
                //20220318@byh 在播放完成后，监听播放完成，把进度拉满，所以这里不在需要修正
                // if (isIos()){
                //     scrollTime = scrollTime+0.5;
                // }
                let secondSTime = Math.round(scrollTime);
                let second = secondSTime > this.state.durationProgress ? this.state.durationProgress : secondSTime;
                //推动后，因为播放缓冲的原因，会有个进度条的回退
                //这边做个逻辑舍弃
                //1、往后拖动：scrollStartTime<scrollEndTime
                //2、往前推动：scrollStartTime>scrollEndTime
                let updateProgress = this.handlerProgress(second);

                if (updateProgress) {
                    this.progressView && this.progressView.onSliderValueChanged(second);
                }
                if (this.offset > 0) {
                    IMILog.logD(TAG,"seekTo offset:"+this.offset);
                    console.log("seekTo progress change",this.offset);
                    this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.offset));
                    this.offset = 0;
                }
            }
            if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1) {
                //时间轴格式的
                console.log("云存时间轴回调更新",this.videoItem,this.isCloudScrollToPlayCount,this.isCloudScrollToPlay)
                if (this.videoItem != null) {
                    //一来就更新会更新的特别频繁
                    let toScrollTime = this.videoItem.startTime + event.extra.currentTime;
                    //用户滚动后，切换到另一个视频播放，会先定位到开头，再跳到对应的位置去播放
                    //这里做个修正,只丢弃一次，可能还是会出现偏移到开始播放再返回的情况
                    //这个里丢弃掉播放最初的3次时间回调
                    if (this.isCloudScrollToPlay && toScrollTime < this.toStartTime) {
                        //不去偏移时间轴
                        this.isCloudScrollToPlayCount++;
                        if (this.isCloudScrollToPlayCount > 3) {
                            this.isCloudScrollToPlay = false;
                            this.isCloudScrollToPlayCount = 0;
                        }
                        return;
                    }
                    this.isCloudScrollToPlayCount = 0;
                    this.isCloudScrollToPlay = false;
                    this.toStartTime = toScrollTime;
                    if (this.state.isFullScreen) {
                        //全屏的话，全屏的时间轴也需要位移
                        this.timelineView && this.timelineView.scrollToTimestamp(toScrollTime);
                    } else {
                        this.alarmListPlayerComponent.scrollToTimestamp(toScrollTime);
                    }

                    this._doCheckTime(toScrollTime,true);

                    this.dateTimeForTemp.setTime(toScrollTime - 1000);
                    this.dateTimeForTemp.setHours(0, 0, 0, 0);
                    let videoMap = CloudVideoUtil.cloudVideoDayMap.get(this.dateTimeForTemp.getTime());
                    if (!videoMap) {
                        //如果不存在，说明超出了今天的范围了
                        //需要去重新请求
                        this.alarmListPlayerComponent && this.alarmListPlayerComponent.getCloudTimelineData(toScrollTime - 1000, false);
                    }
                    //比较滚动后的时间是否还处于同一天
                    //减1秒的原因是，0点时间会展示第二天
                    //切换天，这里设置会有问题
                    if (!this.isLoadingCloudData) {
                        //如果正在请求云存储数据，在播放的时候，不要去切换
                        this.alarmListPlayerComponent && this.alarmListPlayerComponent.compareReqTime(toScrollTime - 1000);
                    }


                }
            } else if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1) {
                //调整，event.extra.currentTime对于某段视频开始时间的偏移，具体到某个时间点
                // let toScrollTime = this.sdVideoItem.startTime + event.extra.currentTime;
                this.dateTimeForTemp.setTime(moment(this.state.dateTime).valueOf());
                this.dateTimeForTemp.setHours(0);
                this.dateTimeForTemp.setMinutes(0);
                this.dateTimeForTemp.setSeconds(0, 0);
                let currentMls = this.dateTimeForTemp.valueOf();
                let toScrollTime = this.dateTimeForTemp.valueOf() + event.extra.currentTime;

                let lastVideo = SDVideoUtil.getLastestVideo()
                if (lastVideo != null && lastVideo.endTime < toScrollTime) {
                    //当天时间轴播放中，又录制了后面的视频
                    console.log("sd time compare",lastVideo.endTime,toScrollTime);
                    //如果当前时间轴的播放时间比我们时间轴上的最后一个视频的结束时间还大，需要重新去拉一下列表
                    this.alarmListPlayerComponent && this.alarmListPlayerComponent.refreshSDTimelineData(lastVideo.endTime);
                }


                //暂停播放后，恢复播放
                this.toStartTime = toScrollTime;
                if (this.state.isFullScreen) {
                    //全屏的话，全屏的时间轴也需要位移
                    this.timelineView && this.timelineView.scrollToTimestamp(toScrollTime);
                } else {
                    this.alarmListPlayerComponent && this.alarmListPlayerComponent.scrollToTimestamp(toScrollTime);
                }
                this._doCheckTime(toScrollTime,false);

                //回看某个视频跨天比如开始时间为前一天的23:59:55 结束时间为下一天的00:00:55，固件端不会在0点的时候发送播放完成指令，
                //会播放到下一天的00:00:55播放完成才会发送播放完成
                //这里增加一个校验
                let nextDay = currentMls+24*60*60*1000+10*1000;
                this.dateTimeForTemp.setTime(nextDay);
                this.dateTimeForTemp.setHours(0);
                this.dateTimeForTemp.setMinutes(0);
                this.dateTimeForTemp.setSeconds(0, 0);
                //因为这个回调不会准确，调用播放器stop后，此回调可能还在执行
                if (toScrollTime >= this.dateTimeForTemp.getTime()){
                    console.warn("big then",toScrollTime,this.dateTimeForTemp.getTime());
                    if (this.countForSDLine > 8){
                        //播放量大于
                        //播放时间大于下一天的0点时间，表示切换天了
                        let nextDate = DateUtils.dateFormat2(this.dateTimeForTemp);
                        this.countForSDLine = 0;
                        this.setState({dateTime:nextDate,sdStartOffset:0},()=>{
                            this.alarmListPlayerComponent && this.alarmListPlayerComponent.setReqTime(nextDate);
                            //设置完成开始去播放下一天视频吧
                            this._startTimelineSDPlay(true,true);
                        })
                    }else {
                        this.countForSDLine++;
                        console.warn("add count current",this.countForSDLine);
                    }
                }
            }
        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            console.log("云存流 ----_onEventChange,开始启用");
            this.isSDGridVideoPrepare = false;

            if (this.isSDVideoPlayComplete && this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0){
                return;
            }
            this.setState({
                isLoading: true,
                isPlay: false,
                showPauseView: false,
                isPlayFinish: false,
                showErrorView: false
            });
            this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);


        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            console.log(" 直播流----_onEventChange,出现关键帧", event.extra.duration);
            this.isSDGridVideoPrepare = false;
            if (this.isSDVideoPlayComplete && this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0){
                return;
            }
            //推动进度条后，
            if (this.state.tabType === TabType.SD && this.state.alarmVideoMode == 0) {
                //不需要再去设置播放视频的总时长
                this.setState({
                    isLoading: false,
                    showPauseView: false,
                    isPlay: true,
                    isPlayFinish: false,
                    showErrorView: false,
                });
            } else {
                this.setState({
                    isLoading: false,
                    showPauseView: false,
                    isPlay: true,
                    isPlayFinish: false,
                    showErrorView: false,
                    durationProgress: event.extra.duration && event.extra.duration
                });
            }

            this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PLAYING);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
            // console.log(" 直播流----_onEventChange,出现关键帧");
            // IMILog.logD("王 错误 PayBack _onEventChange PLAYER_EVENT_ON_STOP>",event.toString())
            //如果
            // if (this.state.tabType == 0 && this.state.alarmVideoMode == 1) {
            //     //this.reachCurrentFileEnd();
            // } else {
            //     this.setState({isLoading: false, isPlay: false, showPauseView: true, speed: 0});
            // }
            if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0 && this.sdGridStartTime >0) {
                GlobalUtil.sdGridVideoTime += new Date().getTime() - this.sdGridStartTime;
                this.sdGridStartTime = 0;
            }else if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 0 && this.cloudGridStartTime >0){
                GlobalUtil.cloudGridVideoTime += new Date().getTime() - this.cloudGridStartTime;
                this.cloudGridStartTime = 0;
            }else if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1 && this.sdLineStartTime >0){
                GlobalUtil.sdLineVideoTime += new Date().getTime() - this.sdLineStartTime;
                this.sdLineStartTime = 0;
            }else if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1 && this.cloudLineStartTime >0){
                GlobalUtil.cloudLineVideoTime += new Date().getTime() - this.cloudLineStartTime;
                this.cloudLineStartTime = 0;
            }

            if (this.state.recording) { //直播流暂停时，停止录像
                if (CONST.isAndroid) {
                    this._stopRecord();
                } else { //因为IOS会自动停止视频录制，所以直接转存即可
                    this._saveVideoToPhotosAlbum();
                }
            }
            // console.log(" 直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PAUSE);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            // console.log(" 直播流----_onEventChange,开始播放");
            //this.setState({isLoading:false});

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE) {
            if (this.state.recording) { //直播流暂停时，停止录像 安卓
                this._stopRecord(true);
            }
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE_IOS) {
            if (this.state.recording) { //直播流暂停时，停止录像 IOS
                this._stopRecord(true);
                // this._saveVideoToPhotosAlbum(true);
            }
        }


    }

    handlerProgress(second){
        if (this.scrollStartTime == 0 || this.scrollEndTime == 0){
            return true;
        }

        if (this.scrollStartTime < this.scrollEndTime && second >= this.scrollEndTime){
            this.scrollStartTime = 0;
            this.scrollEndTime = 0;
            return true;
        }

        if (this.scrollStartTime > this.scrollEndTime
            && second >= this.scrollEndTime
            && second <= this.scrollStartTime){
            //拖到前面播放的内容
            this.scrollStartTime = 0;
            this.scrollEndTime = 0;
            return true;
        }
        return false;
    }

    //IOS在视频流暂停时,将录制的视频保存到相册
    _saveVideoToPhotosAlbum(isPause = false) {
        if (this.recordDuration < 6) {
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            this.recordTimeView && this.recordTimeView.setState({recordDuration: 0});
            this.recordDuration = 0;
            this.setState({recording: false});
            this.props.onRecordStatusChangeListener && this.props.onRecordStatusChangeListener(false);
            return;
        }
        let pathUrl = VEDIO_RECORD_PATH;
        if (isIos()) {
            pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
        }
        IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID).then(_ => { //转存视频
            if (isPause) {
                return;
            }
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            this.setState({screenShotPath: this.tempSnapShotPath, snapshotVisible: true, snapImgType: 2});
            this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                this.setState({snapshotVisible: false});
            }, 3000);
            //IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        }).catch(err => {
            console.warn("save video error:", err);
        });
        //停止录制,不调用，第二次无法录制
        this.IMIVideoView && this.IMIVideoView.stopRecord().then(_ => {

        }).catch((error) => {
        });
        this.recordTimeView && this.recordTimeView.setState({recordDuration: 0});
        this.recordDuration = 0;
        this.setState({recording: false});
        this.props.onRecordStatusChangeListener && this.props.onRecordStatusChangeListener(false);
    }

    /**
     * 录制的回调
     * @param event
     * @private
     */
    _onRecordTimeChange = (event) => {
        console.log("recordDuration", event.extra);
        if (event.extra === 0) {
            //如果为0就不进行赋值操作
            return;
        }

        this.recordTimeView && this.recordTimeView.setState({recordDuration: event.extra});
        this.recordDuration = event.extra;
        if (this.recordDuration == 1) {
            // 临时截图
            let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                this.tempSnapShotPath = currentSnapshotPath;
                // console.log('临时路径---',this.tempSnapShotPath);
            });
        }

    }

    /**
     * 截图展示
     */
    _renderSnapshotView() {
        if (!this.state.snapshotVisible) {
            return null;
        }
        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                bottom: this.state.isFullScreen ? 106 : 19,
                left: isPhoneX() ? (this.state.isFullScreen ? 44 + 14 : 14) : 14,
                width: 140,
                height: 80,
                zIndex: 999,
            }}>
                <ImageButton
                    style={{
                        width: "100%",
                        height: "100%",
                        borderWidth: 2,
                        borderColor: 'white',
                        borderRadius: 10
                    }}
                    source={{uri: 'file://' + this.state.screenShotPath}}
                    onPress={(_) => { //TODO 跳转到相册预览？
                        if (this.state.recording) {
                            showToast(stringsTo('screen_recording'));
                            return;
                        }
                        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                        this.setState({snapshotVisible: false});
                        if (this.state.isFullScreen) { //横屏则退出全屏
                            if (CONST.isAndroid) {
                                this.goToAlbum();
                                setTimeout(() => {
                                    Orientation.lockToPortrait();
                                    this.props.navigation.setOptions({tabBarVisible: true});
                                    this._onCloseFullScreenTools();
                                    NavigationBar.setStatusBarHidden(false);
                                    this.setState({isFullScreen: false}, () => {
                                        console.log('Android回看退出全屏');
                                    });
                                }, 1000);
                                return;
                            } else {
                                Orientation.lockToPortrait();
                                this.props.navigation.setOptions({tabBarVisible: true});
                                this._onCloseFullScreenTools();
                                NavigationBar.setStatusBarHidden(false);
                                this.setState({isFullScreen: false}, () => {
                                    // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                                    this.goToAlbum();
                                });
                                return;
                            }
                        } else {
                            this.goToAlbum();
                        }
                    }}
                />
            </View>
        )
    }

    _pauseView() {
        if (!this.state.showPauseView) return null;
        if (this.state.showErrorView) return;
        if (this.state.todayIsNoData) return null;
        return (<View pointerEvents="box-none"
                      style={{
                          position: "absolute",
                          width: "100%",
                          height: "100%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center"
                      }}
        >
            <ImageButton
                style={{width: 52, height: 52}}
                source={require("../../resources/images/icon_play.png")}
                highlightedSource={require("../../resources/images/icon_play.png")}
                onPress={() => {
                    // 点击过暂停 需要
                    console.log('isPlayzhuangtai---', this.state.isPlayFinish, this.state.isClickPause);
                    console.log("this.tempScrollPause::::", this.tempScrollPause, Math.round(this.tempScrollTime * 1000))
                    this.progressChangeCount = 0;
                    if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1) {
                        //时间轴格式的云存视频
                        this._startTimelinePlay(true);
                        return;
                    }
                    if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1) {
                        this._startTimelineSDPlay(true);
                        return;
                    }
                    if (this.tempScrollPause) {
                        // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
                        // this.tempScrollPause = false;
                        if (this.state.isClickPause) {
                            this.IMIVideoView && this.IMIVideoView.resume()
                        } else {
                            //20220324@byh 播放完成后，拖动进度条，然后再点击播放，播放后定位到拖动位置
                            //这个标记在这里先不置为false
                            //this.tempScrollPause=false;
                            this.IMIVideoView && this.IMIVideoView.prepare();
                        }
                    } else {
                        if (this.state.isPlayFinish) {
                            //TODO
                            //currentPlayTime = 0;
                            console.warn("isPlayFinish do prepare")
                            //回看网格，播放完成后，prepare的还会接着播放后面的视频，不会重新播放
                            if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0) {
                                this.IMIVideoView && this.IMIVideoView.stop();
                                let startTime = this.state.sdStartTime;
                                let endTime = this.state.sdEndTime;
                                this.isSDVideoPlayComplete = false;
                                this.setState({
                                    sdStartTime: 0,
                                    sdEndTime: 0,
                                    speed: 0,
                                    isPlayFinish:false
                                }, () => {
                                    this.setState({
                                        sdStartTime: startTime,
                                        sdEndTime: endTime,
                                        sdStartGridOffset: 0
                                    },()=>{
                                        this.IMIVideoView && this.IMIVideoView.prepare();
                                    })
                                });

                            } else {
                                this.setState({isLoading: true, showPauseView: false});
                                this.IMIVideoView && this.IMIVideoView.prepare();
                            }
                        } else {
                            this.state.isClickPause ? this.IMIVideoView.resume() : this.IMIVideoView.prepare();
                        }
                    }

                    // this.state.isClickPause?this.IMIVideoView.resume():this.IMIVideoView.prepare();
                    if (this.state.isClickPause) {
                        this.setState({isPlay: true, showPauseView: false, isClickPause: false});
                    } else {
                        this.setState({speed: 0, isClickPause: false});
                    }
                }}
            />
        </View>);
    }

    _loadingView() {
        if (this.state.showPauseView) return;
        if (this.state.showErrorView) return;
        if (!this.state.isLoading) return;
        if (this.state.noVideo) return;
        if (this.state.todayIsNoData) return;
        return (<View pointerEvents="box-none"
                      style={{
                          position: "absolute",
                          width: "100%",
                          height: "100%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center"
                      }}
        >
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.state.showErrorView) return;
        if (this.state.todayIsNoData) return null;
        return (
            <View pointerEvents="box-none"
                  style={{
                      position: "absolute",
                      width: "100%",
                      height: "100%",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center"
                  }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
                />

                <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                   buttonStyle={{
                                       margin: 14,
                                       paddingHorizontal: 15,
                                       height: 40
                                   }}
                                   buttonTextStyle={{textAlign: 'center'}}
                                   onPress={() => {

                                       // if (!this.props.netConnected) {
                                       //     showToast(stringsTo("network_not_connected"))
                                       //     return
                                       // }
                                       if (this.state.tabType == TabType.SD) {
                                           this.IMIVideoView && this.IMIVideoView.stop();
                                       }
                                       this.playRetryCount = 0;
                                       this.doPlayRetry = false;
                                       this.setState({isLoading: true, speed: 0});
                                       this.tempScrollPause = false
                                       this.errPrepare = true;
                                       this.isSDVideoPlayComplete = false;
                                       if (this.state.tabType == TabType.SD && this.state.alarmVideoMode === 0){
                                           //宫格回看点击重试
                                           let startTime = this.state.sdStartTime;
                                           let endTime = this.state.sdEndTime;
                                           //ios需要除以1000，传给固件，固件现在接收的是秒
                                           let offsetGrid = this.videoProgressTime;
                                           //播放进度回调与我们的重试操作异步，可能存在偏移溢出的问题，导致重试播放失败
                                           if (startTime+offsetGrid >= endTime){
                                               //开始时间+偏移量 大于等于结束时间
                                               //偏移量重置为0,不再偏移
                                               offsetGrid = 0;
                                           }
                                           if (isIos() && IMIPackage.minApiLevel < 10011){
                                               //10011开始ios会处理这个时间与Android保持一致，传给固件的都是秒
                                               offsetGrid = offsetGrid/1000;
                                               if (offsetGrid > 24*3600){
                                                   offsetGrid = 0;
                                               }
                                           }

                                           this.setState({
                                               sdStartTime: 0,
                                               sdEndTime: 0
                                           }, () => {
                                               this.setState({
                                                   sdStartTime: startTime,
                                                   sdEndTime: endTime,
                                                   sdStartGridOffset: offsetGrid,
                                               },()=>{
                                                   this.doPlayRetry = false;
                                                   this.IMIVideoView && this.IMIVideoView.prepare();
                                               })
                                           });
                                       }else if (this.state.tabType == TabType.SD && this.state.alarmVideoMode === 1){
                                           //回看时间轴模式
                                           let selectDate = new Date();
                                           let timeMl = moment(this.state.dateTime).valueOf();
                                           selectDate.setTime(timeMl);
                                           selectDate.setHours(0);
                                           selectDate.setMinutes(0, 0, 0);
                                           let offsetLine = this.toStartTime - selectDate.getTime();

                                           if (isIos() && IMIPackage.minApiLevel < 10011){
                                               offsetLine = offsetLine/1000;
                                               if (offsetLine > 24*3600){
                                                   offsetLine = 0;
                                               }
                                           }
                                           console.log("点击重试时的播放偏移量 line",offsetLine)
                                           if (offsetLine < 0){
                                               offsetLine = 0;
                                           }
                                           this.setState({
                                               sdStartOffset:offsetLine,
                                           },()=>{
                                               this.doPlayRetry = false;
                                               this.IMIVideoView && this.IMIVideoView.prepare();
                                           })
                                       }else {
                                           this.offset = 0;
                                           if (this.videoItem != null){
                                               //单位毫秒
                                               let toScrollTime = this.videoItem.startTime + this.videoProgressTime;
                                               if (toScrollTime > this.videoItem.startTime && toScrollTime<this.videoItem.endTime){
                                                   this.offset = this.videoProgressTime;
                                               }
                                           }
                                           this.IMIVideoView && this.IMIVideoView.prepare();
                                       }

                                   }}/>
            </View>
        );
    }

    _renderRecordingView() {
        if (!this.state.recording) {
            return null;
        }
        return (
            <RecordTimeView
                ref={(ref) => {
                    this.recordTimeView = ref;
                }}
                isFullscreen={this.state.isFullScreen}
                showFullScreenTools={this.state.showFullScreenTools}
            >

            </RecordTimeView>
        );
    }

    _renderConnectTypeView() {
        if (!this.state.connectType) {
            return null;
        }
        return (
            <View style={
                {
                    position: "absolute",
                    width: 64,
                    height: 26,
                    backgroundColor: 'rgba(0,0,0,0.6)',
                    borderRadius: 4,
                    flexDirection: 'row',
                    justifyContent: "center",
                    alignItems: "center",
                    zIndex: 999,
                    bottom:50,
                    left:20
                }
            }>
                <Text style={{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}}>{this.state.connectType}</Text>
            </View>
        );
    }

    /**
     * 竖屏网格播放器操作按钮
     * 1、云存视频 下载 倍速 截图 全屏/取消全屏
     * 2、回看视频 录制 倍速 截图 全屏/取消全屏
     */
    renderVideoOperateView() {
        if (!this.state.showTools) {
            return null;
        }
        if (this.state.tabType === TabType.CLOUD && this.state.alarmVideoMode !== 0) {
            //云存视频 非网格模式
            return null;
        }

        if (this.state.tabType === TabType.SD && this.state.alarmVideoMode !== 0) {
            //SD视频 非网格模式
            return null;
        }

        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                width: '50%',
                top: 0,
                right: 0,
                height: 80
            }}>
                <PlayCloudToolBarView
                    speedTitle={["1X", "2X"]}
                    speedPress={this._onPressSpeed}
                    speedIndex={this.state.speed}
                    speedDisabled={this.state.isLoading || this.state.showErrorView}
                    fullscreenPress={this._onPressFullScreen}
                    screenshotPress={this._onPressScreenShot}
                    screenshotDisabled={this.state.isLoading || this.state.showErrorView}
                    downloadPress={this.downloadPress}
                    recordPress={this._onPressRecord}
                    recording={this.state.recording}
                    recordDisabled={!this.state.isPlay}
                    isCloud={this.state.tabType === TabType.CLOUD}
                    forGridPlay={true}
                />
            </View>
        )
    }

    /**
     * 全屏网格操作
     */
    renderVideoFullScreenOperateView() {
        if (!this.state.showFullScreenTools) {
            return null;
        }

        if (this.state.tabType === TabType.CLOUD && this.state.alarmVideoMode !== 0) {
            //云存视频 非网格模式
            return null;
        }

        if (this.state.tabType === TabType.SD && this.state.alarmVideoMode !== 0) {
            //SD视频 非网格模式
            return null;
        }

        return (<PlayCloudFullScreenToolBarView
                exitPress={this._exitFullScreen}
                speedTitle={["1X", "2X"]}
                speedPress={this._onPressSpeed}
                speedIndex={this.state.speed}
                speedDisabled={this.state.isLoading || this.state.showErrorView}
                mutePress={this._onPressMute}
                mute={this.state.mute}
                muteDisabled={this.state.speed != 0}
                playPress={this._onPressPlay}
                play={this.state.isPlay}
                fullscreenPress={this._onPressFullScreen}
                screenshotPress={this._onPressScreenShot}
                screenshotDisabled={this.state.isLoading || this.state.showErrorView}
                downloadPress={this.downloadPress}
                recordPress={this._onPressRecord}
                recordDisabled={!this.state.isPlay}
                recording={this.state.recording}
                isCloud={this.state.tabType === TabType.CLOUD}
                forGridPlay={true}/>
        )
    }

    /**
     * 时间轴模式，播放器控制按钮
     * 竖屏
     */
    renderTimelineVideoOperateView() {
        if (!this.state.showTools) {
            return null;
        }

        if (this.state.tabType == TabType.PIC) {
            //非云存视频  非SD卡，则为看家图片，不展示
            return null;
        }

        if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode !== 1) {
            //云存视频，，，非时间轴形式，不展示
            return null;
        }

        if (this.state.tabType == TabType.SD && this.state.alarmVideoMode !== 1) {
            //回看视频，，，非时间轴形式，不展示
            return null;
        }

        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                width: '100%',
                bottom: 0,
                height: 50,
            }}>
                <PlayCloudToolBarView
                    speedTitle={["1X", "2X"]}
                    speedPress={this._onPressSpeed}
                    speedIndex={this.state.speed}
                    speedDisabled={this.state.isLoading || this.state.showErrorView}
                    playPress={this._onPressTimelinePlay}
                    play={this.state.isPlay}
                    mutePress={this._onPressMute}
                    mute={this.state.mute}
                    muteDisabled={this.state.speed != 0}
                    recordPress={this._onPressRecord}
                    recording={this.state.recording}
                    recordDisabled={!this.state.isPlay}
                    fullscreenPress={this._onPressFullScreen}
                    screenshotPress={this._onPressScreenShot}
                    screenshotDisabled={this.state.isLoading || this.state.showErrorView}
                    forGridPlay={false}
                />
            </View>
        )
    }

    /**
     * 时间轴模式，播放器控制按钮
     * 全屏
     */
    renderFullScreenTimelineVideoOperateView() {
        console.log("alarmVideoMode", this.state.alarmVideoMode);
        if (!this.state.showFullScreenTools) {
            return null;
        }

        if (this.state.tabType != TabType.CLOUD && this.state.tabType != TabType.SD) {
            //非云存视频  非SD卡，则为看家图片，不展示
            return null;
        }


        if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode !== 1) {
            //云存视频，，，非时间轴形式，不展示
            return null;
        }

        if (this.state.tabType == TabType.SD && this.state.alarmVideoMode !== 1) {
            //回看视频，，，非时间轴形式，不展示
            return null;
        }
        return (<PlayCloudFullScreenToolBarView

                exitPress={this._exitFullScreen}
                speedTitle={["1X", "2X"]}
                speedPress={this._onPressSpeed}
                speedDisabled={this.state.isLoading || this.state.showErrorView}
                play={this.state.isPlay}
                playPress={this._onPressPlay}
                speedIndex={this.state.speed}
                mutePress={this._onPressMute}
                mute={this.state.mute}
                muteDisabled={this.state.speed != 0}
                recordPress={this._onPressRecord}
                recording={this.state.recording}
                recordDisabled={!this.state.isPlay}
                fullscreenPress={this._onPressFullScreen}
                screenshotPress={this._onPressScreenShot}
                screenshotDisabled={this.state.isLoading || this.state.showErrorView}
                downloadPress={this.downloadPress}
                forGridPlay={false}/>
        )
    }

    /**
     * 全屏时间轴显示
     */
    renderFullScreenTimeLineView() {
        console.log('fullScreen timeline:', this.state.tabType, this.state.alarmVideoMode);
        if (!this.state.showFullScreenTools) {
            return null;
        }
        if (this.state.tabType != TabType.CLOUD && this.state.tabType != TabType.SD) {
            //非云存视频  非SD卡，则为看家图片，不展示
            return null;
        }

        if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode != 1) {
            //云存视频，，，非时间轴形式，不展示
            return null;
        }

        if (this.state.tabType == TabType.SD && this.state.alarmVideoMode != 1) {
            //sd视频，，，非时间轴形式，不展示
            return null;
        }

        return (
            <View style={{
                position: 'absolute',
                width: "100%",
                bottom: 0,
                backgroundColor: 'rgba(0,0,0,0)'
            }}>
                <View>
                    <View style={{
                        flexDirection: 'row',
                        backgroundColor: 'rgba(0,0,0,.6)',
                        height: 30,
                        alignItems: 'center'
                    }}>
                        <Text numberOfLines={1}
                              ellipsizeMode={'tail'}
                              style={{fontSize: 12, color: "rgba(255,255,255,.5)", paddingLeft: 35}}
                              onPress={() => {
                                  //全屏时的日历弹出
                                  console.log("我点击了日历，开始出现日历")
                                  //全屏播放，录制中不可操作日历
                                  if (this.state.recording) {
                                      showToast(stringsTo('screen_recording'));
                                      return;
                                  }
                                  //需要获取这个月的日期相关数据
                                  this.setState({isShowCalendar: !this.state.isShowCalendar,sdFullscreenDayHasVideo:DateHasManager.sdDateList})

                              }}>
                            {dateFormat(new Date(this.state.dateTime), stringsTo("date_format_yyyy_mm_dd"))}
                        </Text>
                        <Image style={{width: 20, height: 20, marginRight: 7}}
                               source={require("../../resources/images/icon_down.png")}/>
                    </View>
                    <View style={{height: 1, backgroundColor: 'rgba(255,255,255,.3)'}}></View>
                    {/*<TextImageButton*/}
                    {/*    style={{backgroundColor:'rgba(0,0,0,.6)'}}*/}
                    {/*    type={TextImageButton.TYPE.ICON_RIGHT}*/}
                    {/*    title={dateFormat(this.state.currentDate,stringsTo("date_format_yyyy_mm_dd"))}*/}
                    {/*    source={require("../../resources/images/icon_down.png")}*/}
                    {/*    imageStyle={{width: 20, height: 20, marginLeft: 6, marginRight: 7}}*/}
                    {/*    textStyle={{fontSize: 12, color: "#7F7F7F"}}*/}
                    {/*    onPress={() => {*/}
                    {/*        this.props.calendarClick&&this.props.calendarClick();*/}
                    {/*    }}/>*/}

                    <TimeScaleView2
                        ref={(ref) => {
                            this.timelineView = ref;
                        }}
                        // onCenterValueChanged={this._onCenterValueChanged}
                        onScrolling={this._onScrolling}
                        onScrollEnd={this._onCenterValueChanged}
                        isDisabled={false}
                        landscape={false}
                        isFullScreen={true}
                        canOperate={!this.state.recording}
                        isCloud={this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1}
                        eventTypeFlags={this.eventTypes}
                    />
                </View>
            </View>
        )
    }

    _onScrolling = (timestamp) => {
        //当前滚动
        if (timestamp) {
            this._handlerScrolling(timestamp);
        }

    }

    // 这里代表时间轴滚动了
    _onCenterValueChanged = (timestamp) => {

        if (timestamp && timestamp != undefined) {
            this._handlerTimelineVideoTimestampChange(timestamp);
        }
    }

    _handlerTimelineVideoTimestampChange(timestamp) {
        //时间轴滚动后的回调事件
        this.playRetryCount = 0;
        this.doPlayRetry = false;
        //加个延迟防止第一次，timeIndicatorView还未render出来，后面无法消失问题
        //redmine#8959
        this.indicactorTimeout && clearTimeout(this.indicactorTimeout);
        this.indicactorTimeout = setTimeout(()=>{
            this.timeIndicatorView && this.timeIndicatorView.setState({centerTimestamp: 0});
        },500)
        if (this.state.tabType === TabType.CLOUD && this.state.alarmVideoMode == 1) {
            //云存时间轴，如果拖动到其他天，我们需要去请求其他天的数据
            this.dateTimeForTemp.setTime(timestamp);
            this.dateTimeForTemp.setHours(0, 0, 0, 0);

            let videoMap = CloudVideoUtil.cloudVideoDayMap.get(this.dateTimeForTemp.getTime());

            //判断是否已经跨天了，如果跨天，需要重新设置
            let currentTimeReq = DateUtils.dateFormat2(timestamp);
            //是否是相同天，否说明跨天了，需要播放timestamp对应的数据
            let isSameDay = this.state.dateTime == currentTimeReq;

            //console.log("videoMap",JSON.stringify(videoMap),videoMap,CloudVideoUtil.cloudVideoDayMap);
            this.alarmListPlayerComponent && this.alarmListPlayerComponent.compareReqTime(timestamp);
            if (!videoMap) {
                //如果不存在，说明超出了今天的范围了
                //需要去重新请求
                this.alarmListPlayerComponent && this.alarmListPlayerComponent.getCloudTimelineData(timestamp, true);
            } else {
                //云存视频时间轴类型
                if (!isSameDay){
                    //需要加个延迟加载，这里播放执行可能太快了
                    //切换后的天数据还未设置成功
                    this.waitSetOver && clearTimeout(this.waitSetOver);
                    this.waitSetOver = setTimeout(res=>{
                        if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode == 1){
                            //这个条件防止用户切换到其他tab后，还执行后面的逻辑
                            //如果不是同一天，需要切换天日期
                            this.onTimelineScrollEnd(timestamp, true,true);
                        }
                    },1000);
                }else {
                    this.onTimelineScrollEnd(timestamp);
                }

                this._doCheckTime(timestamp,true,true);

            }

        } else if (this.state.tabType === TabType.SD && this.state.alarmVideoMode == 1) {
            //SD卡也需要去动态加载其他天的数据
            this.dateTimeForTemp.setTime(timestamp);
            this.dateTimeForTemp.setHours(0, 0, 0, 0);

            let videoMap = SDVideoUtil.SDVideoDayMap.get(this.dateTimeForTemp.getTime());
            //console.log("videoMap",JSON.stringify(videoMap),videoMap,CloudVideoUtil.cloudVideoDayMap);
            //判断是否已经跨天了，如果跨天，需要重新设置
            let currentTimeReq = DateUtils.dateFormat2(timestamp);
            //是否是相同天，否说明跨天了，需要播放timestamp对应的数据
            let isSameDay = this.state.dateTime == currentTimeReq;
            this.alarmListPlayerComponent && this.alarmListPlayerComponent.compareReqTime(timestamp);
            console.log("loading不消失",timestamp,videoMap,)
            if (!videoMap) {
                //如果不存在，说明超出了今天的范围了
                //需要去重新请求
                this.alarmListPlayerComponent && this.alarmListPlayerComponent.getSDTimelineData(timestamp, true);
            }else {
                //正常播放活动后的视频
                //SD卡类型的时间轴
                //SD卡时间轴滚动后，在播放的情况下，不需要去调用stop，可以直接seekTo,
                if (!this.state.isLoading) {
                    this.setState({isLoading: true});
                }
                if (!isSameDay){
                    //需要加个延迟加载，这里播放执行可能太快了
                    //切换后的天数据还未设置成功
                    this.waitSetOver && clearTimeout(this.waitSetOver);
                    this.waitSetOver = setTimeout(res=>{
                        if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1){
                            //这个条件防止用户切换到其他tab后，还执行后面的逻辑
                            //如果不是同一天，需要切换天日期
                            this.onTimelineScrollEnd(timestamp, false,true);
                        }
                    },1000);
                }else {
                    this.onTimelineScrollEnd(timestamp, false);
                }

                this._doCheckTime(timestamp,false,true);
            }

        }

    }

    _doCheckTime(timestamp,isCloud = true,request = false){
        //10s之内只请求一次，需要监听播放中指针变化，播放中的时间进度播放会非常频繁
        //相同视频类型
        if (!request){
            if (isWait(10000) && this.currentPullDataType == this.state.tabType){
                return;
            }
            this.currentPullDataType = this.state.tabType;
        }


        //处理是否需要后台去加载数据
        //还需要去判断是否临界当天的边界
        //现在以3个小时，
        //临近开始时间3小时，并且上一天没有视频，并且上一天有回看视频，请求上一天的数据
        //临近结束时间3小时，并且下一天没有视频，并且下一天有回看视频，请求下一天的数据
        this.dateTimeForTemp.setTime(moment(this.state.dateTime).valueOf());
        this.dateTimeForTemp.setHours(0, 0, 0, 0);
        //选中天的最后时间戳
        let endTime = this.dateTimeForTemp.getTime()+24*60*60*1000 -1000;
        if (this.dateTimeForTemp.getTime() < timestamp && timestamp <= endTime){
            //还处于当天内滑动
            //选中天的开始时间
            let startTime = this.dateTimeForTemp.getTime();
            let pullNearData = false;
            let ml;
            //1、是否接近上一天
            if (timestamp - startTime < 3*60*60*1000){
                //小于三小时
                pullNearData = true;
                ml = startTime - 10000;
            }

            if (endTime - timestamp < 3*60*60*1000){
                //小于三小时
                pullNearData = true;
                ml = endTime + 10000;
            }

            if (pullNearData){
                this.dateTimeForTemp.setTime(ml);
                this.dateTimeForTemp.setHours(0, 0, 0, 0);
                //2、这天否有视频
                let existMap = isCloud?DateHasManager.cloudExistMap:DateHasManager.videoExistMap;
                if (existMap.get(this.dateTimeForTemp.getTime())){
                    //SD卡里存在视频
                    //3、上一天是否已经请求过数据
                    let videoMap = isCloud?CloudVideoUtil.cloudVideoDayMap.get(this.dateTimeForTemp.getTime()):SDVideoUtil.SDVideoDayMap.get(this.dateTimeForTemp.getTime());
                    if(!videoMap){
                        //没有视频
                        //开始请求上一天的视频数据
                        if (isCloud){
                            this.alarmListPlayerComponent && this.alarmListPlayerComponent.getCloudTimelineData(ml, true,false);
                        }else {
                            this.alarmListPlayerComponent && this.alarmListPlayerComponent.getSDTimelineData(ml, true,false);
                        }
                    }
                }
            }
            if (this.timeIndicatorView && this.timeIndicatorView != null && this.timeIndicatorView.centerTimestamp >0){
                this.timeIndicatorView.setState({centerTimestamp: 0});
            }


        }
    }

    _handlerScrolling(timestamp) {
        if (this.timeIndicatorView == null) {
            console.log('timeIndicatorView is null');
            return;
        }
        this.timeIndicatorView.setState({centerTimestamp: timestamp});
    }

    /**
     * 全屏时间轴时，
     * 点击日期选择控件，弹出日历控件
     */
    getCalendarLandscapeView() {
        if (!this.state.isShowCalendar) {
            return null;
        }
        if (!this.state.isFullScreen) {
            return null;
        }
        let dotArr = {};
        if (this.state.tabType == TabType.SD) {
            //sd卡
            dotArr = this.state.sdFullscreenDayHasVideo;
        } else {
            dotArr = DateHasManager.cloudDateList;
        }
        return (
            <ModalView style={[{
                justifyContent: 'center',
                backgroundColor: "transparent"
            }, this.state.isFullScreen ? {alignItems: 'center'} : {}]} visible={this.state.isShowCalendar}
                       onClose={() => {
                           this.setState({isShowCalendar: false})
                       }}>
                <Calendar
                    style={{}}
                    onDayPress={(day) => {
                        console.log('AlarmListPlayerPage onDayPress pressed formatDate' + day.dateString);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        let ddd = new Date(day.year, day.month - 1, day.day, 0, 0, 0);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);

                        // 刷新完成后调用确保取值正确
                        this.setState({isShowCalendar: false, dateTime: day.dateString}, () => {
                            this.alarmListPlayerComponent && this.alarmListPlayerComponent.calendarDateChange(day.dateString);
                        });
                    }}

                    onMonthChange={(day) => {
                        // this.setState({isShowCalendar:false});
                        console.log('AlarmListPlayerPage onMonthChange pressed', day)
                        // let ary = day.dateString.split('-');
                        // let month = ary[0] + ary[1];
                        // this.checkMonth(month);\
                        this.doRequestMonthData(day.dateString);
                    }}
                    hideArrows={false}
                    disabledByDefault={true}
                    hideExtraDays={true}
                    maxDate={new Date()}
                    current={this.state.dateTime}  //设置选中时间
                    markedDates={{
                        ...dotArr,
                        [this.state.dateTime]: {
                            selected: true,
                            marked: true,
                            dotColor: imiThemeManager.theme.pageBg,
                            disableTouchEvent: true,
                            selectedColor: imiThemeManager.theme.primaryColor
                        }
                    }
                    }
                    theme={{
                        arrowColor: '#000000', //左右箭头的颜色
                        todayTextColor: imiThemeManager.theme.primaryColor,
                        textMonthFontWeight: 'bold',//标题yyyy-MM的字重
                        //textDayHeaderFontWeight: 'bold',//周几的字重 20220211@byh去掉字重及周文字大小减小1号，防止有些机型文字显示不全
                        textSectionTitleColor: '#000000',//周几的颜色
                        textDayHeaderFontSize: 12,//周大小 字体设置 英文会突出

                    }}
                    // monthFormat={'yyyy-MM'}
                    // renderHeader={() => {return(<Text style={{color:"white"}}>{this.state.dateText}</Text>)}}
                    // renderHeader={(date) => {return(<Text style={{color:"#fff"}}>{moment(date).format('YYYY-MM')}</Text>)}}
                />

            </ModalView>
        );
    };

    doRequestMonthData(datetime){
        let y = datetime.substring(0, 4);
        let m = datetime.substring(5, 7);
        let ymKey = `${y}${m}`
        if (!DateHasManager.sdMonthMap.get(ymKey)){
            //未请求过这个月份的数据了
            DateHasManager.isFirstError = true;
            DateHasManager.doRequestOneMonth(ymKey,(isOk)=>{
                //请求完成
                if (isOk){
                    this.setState({sdFullscreenDayHasVideo:DateHasManager.sdDateList});
                }
            })
        }
    }

    /**
     * 时间轴滚动时显示的时间
     */
    _renderTimeIndicatorView() {
        return (
            <View
                style={{
                    width: "100%",
                    height: "100%",
                    position: "absolute",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: -50
                }}
                pointerEvents={"none"}
            >
                <CenterTimeView
                    ref={(ref) => {
                        this.timeIndicatorView = ref;
                    }}
                >

                </CenterTimeView>
            </View>
        );
    }

    /**
     * 网格时，
     * 云存视频播放器进度条
     * SD回看视频播放进度条
     */
    renderVideoProgressView() {
        if (!this.state.showFullScreenTools && this.state.isFullScreen) {
            return null;
        }
        if (!this.state.showTools && !this.state.isFullScreen) {
            return null;
        }
        if (this.state.snapshotVisible) {
            return null;
        }

        if (this.state.todayIsNoData) {
            return null;
        }
        if (this.state.tabType == TabType.CLOUD && this.state.alarmVideoMode !== 0) {
            //云存视频，非网格模式，不展示
            return null;
        }

        if (this.state.tabType == TabType.SD && this.state.alarmVideoMode !== 0) {
            //SD卡回看，非网格
            return null;
        }

        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                width: "100%",
                bottom: 0,
                height: 80,
            }}>
                <VideoSubProgressView
                    ref={ref => this.progressView = ref}
                    duration={this.state.durationProgress}
                    recording={this.state.recording}
                    isPlayFinish={this.state.isPlayFinish}
                    isPlay={this.state.isPlay}
                    mute={this.state.mute}
                    onProgressValueChanged={this.onProgressChanged}
                    onSlidingProgressStartListener={this.onProgressSlide}
                    onPlayPress={this._onPressPlay}
                    onMutePress={this._onPressMute}
                    muteDisabled={this.state.speed != 0}
                    hideMute={this.state.isFullScreen}
                >

                </VideoSubProgressView>
            </View>
        )
    }

    onProgressChanged = (currentTime) => {
        console.log("currentTime", currentTime + ":::" + this.state.isPlay)
        // this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(currentTime)*1000);
        // console.log('当前iOS播放状态---',this.state.isPlay);
        //sd宫格回看，拖动进度条的时候，直接拖到最后1秒，固件会推后面的视频流播放
        //修正方法，如果是最后一秒，就往前找3秒，去播放
        if (this.state.tabType == TabType.SD
            && this.state.alarmVideoMode ==0){
            if (this.state.durationProgress <= currentTime){
                currentTime = currentTime - 3;
            }
        }
        this.scrollEndTime = currentTime;
        //添加这个，为了解决，用户拖动进度条，视频还未播放，1、进度条消失-显示，2、页面重新加载。导致进度回弹问题
        //原因：videoSubProgressView会根据这个绘制播放进度videoProgressTime
        this.videoProgressTime = currentTime*1000;
        // 暂停状态
        this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
            this.tempScrollPause = true;
            this.tempScrollTime = currentTime;
            this.IMIVideoView && this.IMIVideoView.resume();
        });
    }

    /**
     * 网格视频播放的时候拖动进度条
     * 暂停播放器播放的目的是为了，
     * 拖动后，进度条会返回拖动前的位置，在跳到拖动后播放的位置
     * @param curTime 推动开始的时候的时间
     */
    onProgressSlide = (curTime)=>{
        this.scrollStartTime = curTime;
        this.IMIVideoView && this.IMIVideoView.pause();
        this.setState({isPlay: false});
    }
    //***
    // 操作相关
    //***
    goToAlbum() {
        // console.log('相册');
        // if (IMIPackage.minApiLevel < 10007) {
        //     IMIGotoPage.startAlbumPage(LetDevice.deviceID);
        // } else {
        //     this.getImgData();
        //     // this.props.navigation.push('CameraListPage');
        // }
        this.needRefreshData = false;
        this.getImgData();
    }

    // 获取所有图片数据
    getImgData() {
        IMIFile.getSourceFromPhotosDidAlbum(LetDevice.deviceID).then(r => {
            let imgArr = isIos() ? r : r.data;
            //排下序，让新的文件在前面
            if (imgArr.length > 1) {
                imgArr.sort((a, b) => {
                    return b.modificationDate - a.modificationDate;
                })
            }
            for (let i = 0; i < imgArr.length; i++) {
                let item = imgArr[i];
                if (this.state.snapImgType == 1 && item.mediaType == 1) {
                    // 进入截图详情
                    this.props.navigation.push('ImagePreView', {mediaData: item, hideDelete: false});
                    break;
                } else if (this.state.snapImgType == 2 && item.mediaType == 2) {
                    // 进入录屏详情
                    this.props.navigation.push('VideoPreView', {mediaData: item, hideDelete: false});
                    break;
                }
            }
        }).catch(error => {
            console.warn("_saveVideoToLocal error", JSON.stringify(error))
        });
    }

    _onPressSpeed = () => {
        // if (!this._canStepIn()) return;
        if (this.state.recording) {
            showToast(stringsTo('screen_recording'));
            return;
        }

        switch (this.state.speed) {
            case 0:
                this.isMute = this.state.mute;
                this.setState({speed: 1, mute: true});
                this.IMIVideoView && this.IMIVideoView.speed(2);
                break;
            case 1:
                //恢复到之前的监听状态
                this.setState({speed: 0,mute: this.isMute});
                this.IMIVideoView && this.IMIVideoView.speed(1);
                break;
        }
    }

    /**
     * 播放暂停按钮的点击事件
     *
     */
    _onPressPlay = () => {

        // if (!this.props.netConnected) {
        //     showToast(stringsTo("network_not_connected"))
        //     return
        // }

        if (this.state.recording) {
            showToast(stringsTo('screen_recording'));
            return;
        }

        if (this.state.showErrorView && !this.state.isPlay) {
            if (this.state.speed !== 0) {
                this.setState({speed: 0})
            }
            this.IMIVideoView && this.IMIVideoView.prepare();
            return;
        }
        this.progressChangeCount = 0;
        this.isSDVideoPlayComplete = false;
        console.log("this.tempScrollPause::::", this.tempScrollPause + " time:" + Math.round(this.tempScrollTime * 1000))
        if (this.tempScrollPause && this.state.alarmVideoMode != 1) {
            if (this.state.isClickPause) {
                // 暂停状态下滑动时间
                this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
                    this.tempScrollPause = false;
                    this.IMIVideoView && this.IMIVideoView.resume();
                });
            } else {
                if (this.state.speed !== 0) {
                    this.setState({speed: 0})
                }
                this.tempScrollPause = false;
                this.IMIVideoView && this.IMIVideoView.prepare();
                //this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime*1000));
            }

        } else {
            console.log("[[[[[[[[[[[2")
            if (!this.state.isPlay) {

                if (this.state.tabType === TabType.CLOUD && this.state.alarmVideoMode == 1) {
                    //云存视频时间轴类型
                    this._startTimelinePlay(true);
                } else if (this.state.tabType === TabType.SD && this.state.alarmVideoMode == 1) {
                    //SD卡类型的时间轴
                    this._startTimelineSDPlay(true);
                } else if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0) {
                    if (this.state.isPlayFinish){
                        this.IMIVideoView && this.IMIVideoView.stop();
                        let startTime = this.state.sdStartTime;
                        let endTime = this.state.sdEndTime;
                        this.setState({
                            sdStartTime: 0,
                            sdEndTime: 0,
                            speed: 0,
                        }, () => {
                            this.setState({
                                sdStartTime: startTime,
                                sdEndTime: endTime,
                                sdStartGridOffset: 0
                            },()=>{
                                this.IMIVideoView && this.IMIVideoView.prepare();
                            })
                        });
                    }else {
                        this.state.isClickPause ? this.IMIVideoView.resume() : this.IMIVideoView.prepare();
                        if (this.state.isClickPause) {
                            this.setState({isPlay: true, showPauseView: false, isClickPause: false});
                        } else {
                            this.setState({speed: 0, isClickPause: false});
                        }
                    }

                }else {
                    this.state.isClickPause ? this.IMIVideoView.resume() : this.IMIVideoView.prepare();
                    if (this.state.isClickPause) {
                        this.setState({isPlay: true, showPauseView: false, isClickPause: false});
                    } else {
                        this.setState({speed: 0, isClickPause: false});
                    }
                }

            } else {
                if (this.state.recording) {
                    console.log("暂停前自动关闭录屏 录屏结束-------------");
                    this._stopRecord();
                }
                this.setState({isPlay: false, showPauseView: true, isLoading: false, isClickPause: true,}, () => {
                    this.IMIVideoView && this.IMIVideoView.pause();
                });
            }
        }
    };

    //点击录屏按钮
    _onPressRecord = () => {

        //增加一个点击延迟，防止重复多次点击
        if (isWait(1000)){
            return;
        }
        if (this.state.recording) {
            console.warn("录屏结束-------------");
            this._stopRecord();
        } else {
            this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                if (status2 === 0) {
                    time = moment(new Date().getTime()).format('yyyyMMDD') + "_" + new Date().getTime();
                    let pathUrl = VEDIO_RECORD_PATH;
                    if (isIos()) {
                        pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
                    }
                    this._doStartRecord(pathUrl);
                    this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
                } else if (status2 === -1) {
                    showToast(stringsTo('storage_permission_denied'));
                    this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
                }
            })

        }
    };

    _doStartRecord(pathUrl) {
        this.IMIVideoView && this.IMIVideoView.startRecord(pathUrl).then(_ => {
            this.recordTimeView && this.recordTimeView.setState({recordDuration: 0});
            this.recordDuration = 0;
            this.canRetryStartRecord = true;
            this.setState({recording: true});
            this.props.onRecordStatusChangeListener && this.props.onRecordStatusChangeListener(true);
        }).catch(error => {
            console.warn("开始录制失败", error);
            if (this.canRetryStartRecord) {
                this.canRetryStartRecord = false;
                this.retryStartRecord && clearTimeout(this.retryStartRecord);
                if (isIos()){
                    //增加一个stoprecord，在某些场景，用户无法重新开启录制，一直录制失败的问题
                    //某些时候多次点击，导致没有正常结束stopRecord
                    this.IMIVideoView && this.IMIVideoView.stopRecord().then(()=>{});
                }
                this.retryStartRecord = setTimeout(() => {
                    this._doStartRecord(pathUrl);
                }, 1000);
            } else {
                //还是无法开启正常录制，那么提示录制视频失败吧
                showToast(stringsTo("save_album_failed"));
            }
        });
    }

    //停止录像并保存在相册
    _stopRecord(isPause = false, isPlayCompletion = false) {
        let forSave = true
        console.warn("停止录制anle", this.recordDuration);
        if (isPlayCompletion && this.recordDuration == 0) { //防止在停止时
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            forSave = false;
        } else {
            //iOS this.recordDuration会出现undefined
            if (!this.recordDuration || this.recordDuration < 6) {
                IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                forSave = false;
            }
        }
        this.IMIVideoView.stopRecord().then(_ => { //停止录制
            console.warn("停止录制-------", forSave);
            if (!forSave) { //只停止，不保存
                console.warn("停止录制-------保存失败");//save_system_album_failed
                // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                return;
            }
            let pathUrl = VEDIO_RECORD_PATH;
            if (isIos()) {
                pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
            }
            console.warn("停止录制-------pathUrl", pathUrl);

            IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID).then(_ => { //转存视频
                if (isPause) {
                    return
                }
                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                this.setState({screenShotPath: this.tempSnapShotPath, snapshotVisible: true,snapImgType:2});
                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                    this.setState({snapshotVisible: false});
                }, 3000);


            }).catch((error) => {
                console.log("停止-------视频保存失败" + error);
            });
        }).catch((error) => {
            console.log("停止录制-------保存失败" + error);
        });
        this.recordTimeView && this.recordTimeView.setState({recordDuration: 0});
        this.recordDuration = 0;
        this.setState({recording: false});
        this.props.onRecordStatusChangeListener && this.props.onRecordStatusChangeListener(false);
    }

    _onPressTimelinePlay = () => {
        if (this.state.recording) {
            showToast(stringsTo('screen_recording'));
            return;
        }

        if (isWait()) return; //防止快速点击进入多个设置页面
        if (this.state.isPlay) {
            this.setState({isPlay: false, showPauseView: true, isLoading: false, isClickPause: true}, () => {
                this.IMIVideoView && this.IMIVideoView.pause();
            });
        } else {
            // if (this.state.isClickPause) {
            //     this.setState({isPlay: true, isClickPause: false, showPauseView: false}, () => {
            //         this.IMIVideoView && this.IMIVideoView.resume();
            //     });
            //     return;
            // }
            //需要区分是SD卡时间轴，还是云存视频时间轴
            if (this.state.tabType === TabType.CLOUD && this.state.alarmVideoMode == 1) {
                //云存视频时间轴类型
                this._startTimelinePlay(true);
            } else if (this.state.tabType === TabType.SD && this.state.alarmVideoMode == 1) {
                //SD卡类型的时间轴
                this._startTimelineSDPlay(true);
            }
        }

    }

    _onPressMute = () => {
        // if (!this._canStepIn()) return;
        this.setState({mute: !this.state.mute})
    };

    _onPressFullScreen = () => {
        // if(!this._canStepIn())  return;
        isAndroid() ? Orientation.lockToLandscape() : Orientation.lockToLandscapeRight();
        this.setState({isFullScreen: true}, () => {
            this._reRenderTimeView();
        });
        NavigationBar.setStatusBarHidden(true);
        this.props.onFullScreenChange && this.props.onFullScreenChange(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
    };

    _exitFullScreen = () => {
        // if(!this._canStepIn())  return;
        //20220727@byh 横屏切竖屏，因为其他内容为绘制，会导致播放器区域高度全屏，
        //表现为黑底铺满屏幕，播放器处于中间，后展示正常
        //在state设置成功后，再去处理切竖屏
        this.setState({isFullScreen: false}, () => {
            //渲染时间轴数据
            this._reRenderTimeView();
            //设置加载时间
            this.alarmListPlayerComponent && this.alarmListPlayerComponent.setReqTimeForExitFullscreen();
            NavigationBar.setStatusBarHidden(false);
            this.props.onFullScreenChange && this.props.onFullScreenChange(false);
            this.props.navigation.setOptions({tabBarVisible: true});
            this._onCloseFullScreenTools();
            Orientation.lockToPortrait();
        })

    }
    //点击截屏按钮
    _onPressScreenShot = () => {
        // if (!this._canStepIn()) return;

        if (new Date().getTime() - lastClickSnapPhoto < 3000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
            return;
        }
        lastClickSnapPhoto = new Date().getTime();
        this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
            if (status2 === 0) {
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                    IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, LetDevice.deviceID).then(_ => {
                        this.setState({screenShotPath: currentSnapshotPath, snapshotVisible: true, snapImgType: 1});
                        this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                            this.setState({snapshotVisible: false});
                        }, 3000);
                    });
                });
                //IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM)
                this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
            } else if (status2 === -1) {
                showToast(stringsTo('storage_permission_denied'));
                this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
            }
        });

    };

    /**
     * 下载分为图片下载和视频下载
     * 分成不同的方式进行下载
     * 视频下载需要轮询去获取阿里转码后的下载地址
     * 不管是图片下载还是视频下载都需要去请求权限
     */
    downloadPress = () => {
        // if (this.preProgress > 0) {
        //     //有进行中的下载任务
        //     showToast(stringsTo("task_in_downloading_try_later"));
        //     return;
        // }
        if (this.state.tabType === TabType.PIC && (this.downloadState === DowaloadType.START || this.downloadState === DowaloadType.DOWNLOADING)){
            showToast(stringsTo("task_in_downloading_try_later"));
            return;
        }
        if (this.state.tabType === TabType.CLOUD && (this.downloadState === DowaloadType.START || this.downloadState === DowaloadType.DOWNLOADING)){
            //this.preProgress >0 无法规避调用阿里轮询方法
            //判断再极端点，点击，就表示有任务，除非成功或者失败，否者都不给视频下载
            showToast(stringsTo("task_in_downloading_try_later"));
            return;
        }
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
            if (status2 === 0) {
                if (this.state.tabType === TabType.CLOUD) {
                    if (!this.state.fileName) {
                        showToast(stringsTo('select_tip'));
                        return;
                    }
                    this.getDownloadUrl(this.state.fileName)
                } else {
                    if (!this.state.currentThumbUrl) {
                        showToast(stringsTo('select_tip'));
                        return;
                    }
                    this.props.downloadStateChangeListener && this.props.downloadStateChangeListener(0, DowaloadType.START);
                    this.isDownLoadVideo = false;
                    this.downloadFile(this.state.currentThumbUrl)
                }
                this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
            } else if (status2 === -1) {
                showToast(stringsTo('storage_permission_denied'));
                this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
            }
        });
    }


    //判断当前是否可以操作
    _canStepIn() {
        if (!this.state.isPlay) {
            showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }

    /**
     * 中部tab，日期选择、事件选择、网格与时间轴切换
     * 网格列表展示，时间轴展示
     * @returns {JSX.Element}
     */
    renderAlarmView() {

        return (
            <AlarmListPlayerComponent
                ref={component => (this.alarmListPlayerComponent = component)}
                onClickCancel={(checkFullScreen) => {
                    this.setState({isEdit: checkFullScreen})
                }}
                isEdit={this.props.isEdit}
                isSelectedAll={this.props.isSelectAll}
                dataArray={this.props.numAry}
                vipState={this.props.vipState}
                storageType={this.props.storageType}
                sdState={this.props.sdState}
                lifecycleDay={this.props.lifecycleDay}
                navigation={this.props.navigation}
                eventCurrentTitles={this.eventAry}
                tabType={this.state.tabType}
                videoStateChangeListener={this._handlerVideoStateChange}
                pictureChangeListener={this._handlerPictureChange}
                isFullScreen={this.state.isFullScreen}
                eventTypeFlags={this.eventTypes}
                eventTimeline={this.eventTimeline}
                recording={this.state.recording}
                getDataLength={(length) => {
                    //数据请求后，数据的长度
                    console.log(TAG, "getDataLength is do");

                    if ((length == 0 && this.state.dataLength > 0) || (length > 0 && this.state.dataLength == 0)) {
                        //数据长度的更新，一般不需要更新，1、从有数据到无数据 更新  2、从无数据到有数据 更新
                        //只要是减少不必要的刷新
                        this.setState({dataLength: length},()=>{
                            if (this.state.tabType == TabType.CLOUD){
                                this.alarmListPlayerComponent && this.alarmListPlayerComponent.renderListOver();
                            }
                        })
                    }

                    this.props.getDataLength && this.props.getDataLength(length);
                }}
                onAllSelectChangeListener={(allSelectMode) => {
                    //全选  全不选按钮的回调方法
                }}
                onClickSelect={(index, indexAry) => {
                    if (index != undefined) {
                        let dataAry = this.state.numAry;
                        if (dataAry.indexOf(index) < 0) {
                            dataAry.push(index);
                        } else {
                            let num = dataAry.indexOf(index);
                            dataAry.splice(num, 1);
                        }
                        this.setState({numAry: dataAry});
                    } else {
                        this.setState({numAry: indexAry});
                    }
                }}
                modeType={this.state.alarmVideoMode}
                modeChange={(mode) => {
                    console.log(TAG, "mode:" + mode);

                    if (mode != this.state.alarmVideoMode) {
                        console.log(TAG, "this is mode:" + mode);
                        this.playRetryCount = 0;
                        this.doPlayRetry = false;
                        //时间轴拖动后，时间显示可能不消失，这里做下处理
                        this.timeIndicatorView && this.timeIndicatorView.setState({centerTimestamp: 0});
                        if (this.state.tabType == TabType.CLOUD) {
                            this.props.onEditShowChange && this.props.onEditShowChange(mode != 1);
                        } else if (this.state.tabType == TabType.SD) {
                            this.props.onEditShowChange && this.props.onEditShowChange(false);
                        }

                        if (this.alarmListPlayerComponent) {
                            this.alarmListPlayerComponent.isInitEvent = false;
                        }
                        this.playTimeout && clearTimeout(this.playTimeout);
                        //如果正在播放中，清空暂停播放
                        if (this.state.isPlay) {

                            //快速切换，网格与时间轴，可能导致插件崩溃
                            //原因：快速切换，this.state.isPlay，无法正确标记播放状态，播放器调用stop之前，还没有调用start
                            this.IMIVideoView && this.IMIVideoView.stop();

                            this.progressView && this.progressView.onSliderValueChanged(0);
                            this.tempScrollPause = false
                        }
                        //回看也有网格，所以需要区分是云存视频，还是回看
                        if (this.state.tabType == TabType.CLOUD) {
                            //云存视频，切换网格时间轴
                            this.setState({
                                alarmVideoMode: mode,
                                isPlay: false,
                                speed: 0,
                                mute: true,
                                durationProgress: 0,
                                fileName: "",
                                isClickPause: false
                            }, () => {
                                //切换后，都去重新拉取数据，并直接播放第一个视频
                                this.videoItem = null;
                                this.alarmListPlayerComponent._queryDataList(true, true);
                            })
                        } else {
                            //回看视频，切换网格时间轴
                            this.setState({
                                alarmVideoMode: mode,
                                isPlay: false,
                                speed: 0,
                                mute: true,
                                fileName:"",
                                durationProgress: 0,
                                isClickPause: false
                            }, () => {
                                //切换后，都去重新拉取数据，并直接播放第一个视频
                                //需要知道是否已经拉取过回看数据，如果已经拉取过数据
                                //就不要重新去拉取数据，
                                this.dateTimeForTemp.setTime(moment(this.state.dateTime).valueOf());
                                this.dateTimeForTemp.setHours(0);
                                this.dateTimeForTemp.setMinutes(0);
                                this.dateTimeForTemp.setSeconds(0, 0);
                                let videoArr = SDVideoUtil.SDVideoDayMap.get(this.dateTimeForTemp.valueOf());
                                this.videoItem = null;
                                if (videoArr && videoArr.length > 0) {
                                    //有数据，不再去获取SD卡数据
                                    this.alarmListPlayerComponent.dealSDData();
                                } else {
                                    this.alarmListPlayerComponent._queryDataList(true, true);
                                }
                            })
                        }

                    }

                }}
                onTimelineStartPlay={(timestamp, isFirst = false,startPlayAtTimestamp = false) => {
                    //开始播放
                    //需要判断当前的时间轴类型，是SD卡时间轴，
                    //还是云存视频
                    console.log(TAG, "onTimelineStartPlay", this.state.tabType);
                    this.toStartTime = timestamp;
                    if (this.state.tabType === TabType.CLOUD && this.state.alarmVideoMode == 1) {
                        //云存视频时间轴类型
                        this._startTimelinePlay(true, isFirst);
                    } else if (this.state.tabType === TabType.SD && this.state.alarmVideoMode == 1) {
                        //SD卡类型的时间轴
                        if (this.state.isSDDataEmpty) {
                            this.setState({isSDDataEmpty: false});
                        }
                        this._startTimelineSDPlay(true, isFirst,startPlayAtTimestamp);
                    }

                }}
                onNoTimeLineDataListener={(isSD) => {
                    //暂时没有传参数，后面看看云存是否需要，现在只表示SD卡没有数据
                    if (isSD && !this.state.isSDDataEmpty) {
                        this.setState({isSDDataEmpty: true});
                    }

                    if (!isSD){
                        this.IMIVideoView && this.IMIVideoView.pause();
                        this.videoItem = null;
                        //云存轴当天没有视频
                        this.setState({isPlay: false, showPauseView: true, isLoading: false,speed: 0, isClickPause: false},()=>{
                            //对时间轴进行偏移
                            let selectDate = new Date();
                            let timeMl = moment(this.state.dateTime).valueOf();
                            selectDate.setTime(timeMl);
                            selectDate.setHours(0);
                            selectDate.setMinutes(0);
                            selectDate.setSeconds(1, 0);
                            let toScrollTime = selectDate.getTime();
                            this.timelineView && this.timelineView.scrollToTimestamp(toScrollTime);
                            this.alarmListPlayerComponent.scrollToTimestamp(toScrollTime);
                        })

                    }

                }}
                onScrollingListener={this._handlerScrolling.bind(this)}
                onTimelineVideoTimestampChangeListener={this._handlerTimelineVideoTimestampChange.bind(this)}
                onDateChangeListener={(date,ml) => {
                    if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1 && ml && ml>0){
                        this.dateTimeForTemp.setTime(new Date(date).valueOf());
                        this.dateTimeForTemp.setHours(0);
                        this.dateTimeForTemp.setMinutes(0);
                        this.dateTimeForTemp.setSeconds(0, 0);
                        //有可能是上一天，也有可能是下一天
                        let offsetTime = Math.abs(ml - this.dateTimeForTemp.getTime());
                        if (isIos() && IMIPackage.minApiLevel < 10011){
                            offsetTime = offsetTime/1000;
                            if (offsetTime > 24*3600){
                                offsetTime = 0;
                            }
                        }
                        this.setState({dateTime: date,sdStartOffset:offsetTime});
                    }else {
                        this.setState({dateTime: date});
                    }

                }}
                onCloudVideoStatusListener={(data) => {
                    switch (data.type) {
                        case CLOUD_VIDEO_STATUS.DELETE:
                            //标记，是否需要删除后，播放第一个视频
                            this.deleteTagForPlayFirstVideo = data.value;
                            break;
                        case CLOUD_VIDEO_STATUS.LOAD:
                            //是否正在加载云存储视频
                            this.isLoadingCloudData = data.value;
                            break
                    }

                }}
            >

            </AlarmListPlayerComponent>
        )
    }

    onTimelineScrollEnd(timestamp, isCloud = true,playOtherDay = false,) {
        console.log(TAG, "onTimelineScrollEnd:" + timestamp);
        this.toStartTime = timestamp;
        if (isCloud) {
            this.isCloudScrollToPlayCount = 0;
            this.isCloudScrollToPlay = true;
        }
        if (this.state.isPlay) {
            //如果后面需要流量保护相关再进行处理
            if (isCloud) {
                this._startTimelinePlay(true);
            } else {
                this._startTimelineSDPlay(true,playOtherDay,playOtherDay);
            }
        }

        // 如果没有播放  就不用管了
    }

    //一个文件播放完后，如果后面还有视频，则继续播放
    reachCurrentFileEnd() {
        if (this.videoItem != null) {
            console.log(`Video onEnd:${this.videoItem.endTime}`);
            this.toStartTime = this.videoItem.endTime + 1000; // 播放完成后，+1000 让下一次寻找的时候找到下一个。
            let video = CloudVideoUtil.getLastestVideo();
            console.log(`Video onEnd last:`,video);
            if (video == null) {
                return;
            }
            if (this.toStartTime > video.endTime) {
                //已经把所有的视频都播放完了，显示暂停，结束播放
                this.toCloudEnd();
                return;
            }
            //开始播放下一个视频
            this._startTimelinePlay(true);
        }
    }

    /**
     * 回看时间轴
     * 一天的视频播放完成后
     * 如果下一天有视频，需要跳到下一天播放
     * 需要自己切换到下一天的第一个视频开始播放
     */
    reachCurrentSDFileEnd() {
        console.warn("on sd play end");
        //获取这一天的最后一个视频
        this.dateTimeForTemp.setTime(moment(this.state.dateTime).valueOf());
        this.dateTimeForTemp.setHours(0);
        this.dateTimeForTemp.setMinutes(0);
        this.dateTimeForTemp.setSeconds(0, 0);
        let lastVideo = SDVideoUtil.getLastestVideoInDay(this.dateTimeForTemp.valueOf());

        if (lastVideo != null) {
            console.log(`Video onEnd:${lastVideo.endTime}`);
            let tempStartTime = lastVideo.endTime + 1000; // 播放完成后，+1000 让下一次寻找的时候找到下一个。
            let video = SDVideoUtil.getLastestVideo();
            if (video == null) {
                return;
            }
            if (tempStartTime >= video.endTime) {
                //已经把所有的视频都播放完了，显示暂停，结束播放
                //回看播放完成后并不会自己就停止播放，固件虽然停止推流了，但是播放器还是会去拉流
                //这里主动去停止播放
                this.toSdcardEnd();
                return;
            }
            //开始播放下一个视频
            //1、切换日期 2、重置offsetTime
            this.dateTimeForTemp.setTime(moment(this.state.dateTime).valueOf());
            this.dateTimeForTemp.setHours(0);
            this.dateTimeForTemp.setMinutes(0);
            this.dateTimeForTemp.setSeconds(0, 0);
            let nextDay = this.dateTimeForTemp.getTime()+24*60*60*1000+1000*10;
            this.dateTimeForTemp.setTime(nextDay);
            this.dateTimeForTemp.setHours(0);
            this.dateTimeForTemp.setMinutes(0);
            this.dateTimeForTemp.setSeconds(0, 0);
            let nextDate = DateUtils.dateFormat2(this.dateTimeForTemp);
            this.setState({dateTime:nextDate,sdStartOffset:0},()=>{
                this.alarmListPlayerComponent && this.alarmListPlayerComponent.setReqTime(nextDate);
                //设置完成开始去播放下一天视频吧
                this._startTimelineSDPlay(true,true);
            })

        }
    }

    toCloudEnd() {
        // if (!this.state.displayCloudList) {
        //     return;
        // }
        console.log("toCloudEnd");
        //滚到最后，然后结束播放
        this.alarmListPlayerComponent.scrollToTimestamp(this.lastTimeItemEndTime);
        this.videoItem = null;
        this._startTimelinePlay(false);
    }

    toSdcardEnd() {
        // if (!this.state.displayCloudList) {
        //     return;
        // }
        //滚到最后，然后结束播放
        this.alarmListPlayerComponent.scrollToTimestamp(this.lastTimeItemEndTime);
        this.sdVideoItem = null;
        this._startTimelineSDPlay(false);
    }

    /**
     * 播放云存视频时间轴
     * @param isPlay 播放还是暂停
     * @param isFirst isPlay为true的时候，表示第一次播放 isPlay为true的时候，表示暂停或者播放结束
     * @private
     */
    _startTimelinePlay(isPlay, isFirst = false) {
        // handle流量保护。
        if (isPlay) {
            //这里需要区分是一个片段第一次播放还是在播放中暂停
            //如果是播放中暂停，直接resume就好
            // if (this.state.isClickPause) {
            //     this.setState({isPlay:true,isClickPause:false,showPauseView: false},()=>{
            //         this.IMIVideoView && this.IMIVideoView.resume();
            //     });
            //     return;
            // }
            if (isFirst) {
                //第一次进来，如果是当天，那么播放云存视频的最后一个视频
                //否则从这一天的第一条视频开始播放
                this.dateTimeForTemp.setTime(moment(this.state.dateTime).valueOf());
                this.dateTimeForTemp.setHours(0);
                this.dateTimeForTemp.setMinutes(0);
                this.dateTimeForTemp.setSeconds(0, 0);
                //某天的最后一个视频
                let findVideo = DateUtils.isToday(this.dateTimeForTemp) ? CloudVideoUtil.getLastestVideoInDay(this.dateTimeForTemp.valueOf()) : CloudVideoUtil.getFirstVideoInDay(this.dateTimeForTemp.valueOf());
                IMILog.logD("cloud",findVideo+" ");
                if (findVideo == null) {
                    IMILog.logD("cloud","findVideo is null");
                    return;
                }
                IMILog.logD("cloud","findVideo is not null");
                //当天，从当天视频的最后一个视频开始播
                this.videoItem = findVideo;
                this.offset = 0;

                let fileName = findVideo.fileName;

                //如果滑动的时候正在处于播放状态
                //需要重置下播放器的相关状态
                this.IMIVideoView && this.IMIVideoView.stop();
                this.progressView && this.progressView.onSliderValueChanged(0);
                this.tempScrollPause = false
                this.isLoadingCloudData = false
                this.toStartTime = 0;
                this.setState({
                    isPlay: false,
                    speed: 0,
                    durationProgress: 0,
                    fileName: fileName,
                    isClickPause: false
                }, () => {
                    console.log(TAG, "video timeline filename set success")
                    IMILog.logD("RN cloud","IMIVideoView set success");
                    this.IMIVideoView && this.IMIVideoView.prepare()
                })
                return;
            }


            let selectedItem = null;
            let offset = 0;
            //移动后找到最后一条，视频
            let lastestItem = CloudVideoUtil.getLastestVideo();
            if (lastestItem == null) { // 压根都没有最后一条数据；
                return;
            }
            console.log("_startTimelinePlay", lastestItem.endTime, this.toStartTime)
            //最后的一个时间点
            this.lastTimeItemEndTime = lastestItem.endTime;
            if (lastestItem.endTime < this.toStartTime) {
                //结束时间比移动时间轴后的时间小，就把最后一个视频，当做选中的视频
                selectedItem = lastestItem;
                if (lastestItem.endTime - lastestItem.startTime > 2000) {
                    offset = lastestItem.endTime - lastestItem.startTime - 2000;
                } else {
                    offset = 0;
                }
            } else {
                selectedItem = CloudVideoUtil.searchNeareastVideoItem(this.toStartTime);
                if (selectedItem == null) {
                    this.isCloudScrollToPlay = false;
                    return;
                }
                if (selectedItem.startTime > this.toStartTime) {
                    offset = 0;
                } else {
                    console.log("this.toStartTime:" + this.toStartTime + " selectedItem.startTime:" + selectedItem.startTime);
                    offset = (this.toStartTime - selectedItem.startTime);
                }
            }
            if (!this.state.isLoading) {
                this.setState({isLoading: true});
            }
            console.log("_startTimelinePlay", selectedItem, this.videoItem);
            //如果正在播放，并且当前播放的视频片段，为滑动后选择的视频片段，那么可以直接进行偏移播放
            if (offset < 0){
                //防止offset小于0出现异常
                offset = 0;
            }
            this.offset =  offset;
            if (this.videoItem != null && this.videoItem.fileName == selectedItem.fileName) {
                console.log(`state:${JSON.stringify(this.state)} state.isPlaying:${this.state.isPlay} offset${offset}`);
                this.isCloudScrollToPlay = false;
                if (this.state.isPlay) {
                    console.log("seekTo _startTimelinePlay pl",this.offset);
                    if (this.offset>0){
                        this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.offset));
                        this.offset = 0;
                    }
                    return;
                } else {
                    this.setState({isPlay: true, showPlayToolBar: true, showPauseView: false});
                    //暂停状态下，直接seekTo好像不会直接播放
                    this.IMIVideoView && this.IMIVideoView.resume();
                    console.log("seekTo _startTimelinePlay np",this.offset);
                    return;
                }
            }
            this.videoItem = selectedItem;
            this.offset = offset;

            let fileName = selectedItem.fileName;

            //如果滑动的时候正在处于播放状态
            //需要重置下播放器的相关状态
            this.IMIVideoView && this.IMIVideoView.stop();
            this.progressView && this.progressView.onSliderValueChanged(0);
            this.tempScrollPause = false
            this.isLoadingCloudData = false
            this.setState({
                isPlay: false,
                durationProgress: 0,
                speed: 0,
                fileName: fileName,
                isClickPause: false
            }, () => {
                console.log(TAG, "video timeline filename set success")
                this.IMIVideoView && this.IMIVideoView.prepare()
            })

            // this.setState({
            //     fileName: fileName
            // }, () => {
            //     console.log(TAG, "video timeline filename set success")
            //     this.IMIVideoView && this.IMIVideoView.prepare()
            // });

        } else {
            // stop播放
            this.setState({isPlay: false, showPauseView: true, isLoading: false, isClickPause: false}, () => {
                this.IMIVideoView && this.IMIVideoView.pause();
            });
        }
    }

    /**
     * SD卡类型的时间轴播放
     * @param isPlay 是否播放 TRUE播放 FALSE暂停播放
     * @param isFirst 是否播放第一次进入播放 TRUE是第一次
     *                FALSE不是第一次，不需要prepare播放器，直接seekTo
     * @param startPlayAtTimestamp 标记SD卡是否从 this.toStartTime开始播放回看视频
     * @private
     */
    _startTimelineSDPlay(isPlay, isFirst = false,startPlayAtTimestamp = false) {
        // handle流量保护。

        if (isPlay) {
            // if (this.state.isClickPause){
            //     this.setState({isPlay:true,isClickPause:false,showPauseView: false},()=>{
            //         this.IMIVideoView && this.IMIVideoView.resume();
            //     });
            //     return;
            // }

            let selectedItem = null;
            let offset = 0;
            //移动后找到最后一条，视频
            let lastestItem = SDVideoUtil.getLastestVideo();

            if (lastestItem == null) { // 压根都没有最后一条数据；
                if (this.state.isLoading) {
                    this.setState({isLoading: false});
                }
                return;
            }
            let selectDate = new Date();
            let timeMl = moment(this.state.dateTime).valueOf();

            selectDate.setTime(timeMl);
            selectDate.setHours(0);
            selectDate.setMinutes(0);
            selectDate.setSeconds(0, 0);
            if (isFirst) {
                //2022-09-30@byh 当天的从视频最后往前退2分钟开始播放，其他天的从视频开始播放
                //初始的话，我们就找最后一个视频播放
                //初始化播放器，初始化完成后，重新定位到最后的那段视频开始播放
                //需要定位播放的位置
                //考虑到切换日期，这边可能需要重新初始化播放器
                this.countForSDLine = 0;
                console.log("sd时间轴选中的日期第一次播放");
                this.IMIVideoView && this.IMIVideoView.stop();
                if (startPlayAtTimestamp){
                    //需要减去这一天起始时间点
                    this.offset = this.toStartTime - selectDate.getTime();
                }else {
                    if (DateUtils.isToday(selectDate)) {
                        //当天，从视频最后往前移2分钟播放,offset是从这一天的第一秒开始后的偏移量
                        this.offset = lastestItem.endTime - selectDate.getTime() - 2 * 60 * 1000;
                    } else {
                        this.offset = 0;
                    }
                }

                this.sdVideoItem = lastestItem;
                if (this.state.speed !== 0) {
                    this.setState({speed: 0})
                }
                console.log("初始偏移量",this.offset);
                if (this.offset < 0){
                    this.offset = 0;
                }

                //ios也需要除以1000，传给固件，固件现在接收的是秒
                if (isIos() && IMIPackage.minApiLevel < 10011){
                    this.offset = this.offset/1000;
                    if (this.offset > 24*3600){
                        this.offset = 0;
                    }
                }
                console.warn("时间轴偏移量",this.offset);
                this.setState({sdStartOffset:this.offset});
                this.offset = 0;
                this.IMIVideoView && this.IMIVideoView.prepare();
                return;
            }
            console.warn("_startTimelineSDPlay", lastestItem.endTime, this.toStartTime)
            //最后的一个时间点
            this.lastTimeItemSDEndTime = lastestItem.endTime;
            if (lastestItem.endTime < this.toStartTime) {
                //结束时间比移动时间轴后的时间小，就把最后一个视频，当做选中的视频
                //直接播放最后一个视频
                this.offset = lastestItem.startTime - selectDate.getTime();
                selectedItem = lastestItem;
            } else {
                //查找附件的
                selectedItem = SDVideoUtil.searchNeareastVideoItem(this.toStartTime);
                if (selectedItem == null) {
                    if (this.state.isLoading) {
                        this.setState({isLoading: false});
                    }
                    return;
                }

                if (selectedItem.startTime > this.toStartTime) {
                    this.offset = selectedItem.startTime - selectDate.getTime();
                } else {
                    this.offset = this.toStartTime - selectDate.getTime();
                }
            }
            if (this.offset < 0){
                this.offset = 0;
            }
            //当前所在播放的SD卡videoItem
            this.sdVideoItem = selectedItem;
            if (!this.state.isLoading) {
                this.setState({isLoading: true});
            }
            if (this.state.isPlay) {
                //vod 需要seek到播放的时间点toStartTime
                console.log("seekTo _startTimelineSDPlay pl",this.offset);
                this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.offset));
            } else {
                this.setState({isPlay: true, showPlayToolBar: true, showPauseView: false});
                console.log("seekTo _startTimelineSDPlay np",this.offset);

                this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.offset));
            }
            this.offset = 0;
        } else {
            // stop播放
            this.setState({isPlay: false, showPauseView: true, isLoading: false, isClickPause: false}, () => {
                this.IMIVideoView && this.IMIVideoView.stop();
            });
        }
    }

    /**
     * 云存视频播放、切换播放状态回调
     * @param data
     * @private
     */
    _handlerVideoStateChange = (data) => {
        console.log(TAG, "videoStateChangeListener", data);
        switch (data.type) {
            case VideoStateType.GET_FILENAME:
                if (this.state.isPlay) {
                    //已经在播放中了，需要停止调前面的播放，换成现在新的播放
                    this.IMIVideoView && this.IMIVideoView.stop();
                    this.progressView && this.progressView.onSliderValueChanged(0);
                    this.tempScrollPause = false
                    this.tempScrollTime = 0;
                    this.videoProgressTime = 0;
                    this.setState({isPlay: true, durationProgress: 0, isClickPause: false}, () => {
                        this.setState({
                            fileName: data.fileName,
                            eventTime: data.eventTime,
                            isFirstVideo: data.isFirstVideo,
                            speed: 0
                        }, () => {
                            this.IMIVideoView && this.IMIVideoView.prepare()
                        });
                    });
                } else {
                    this.setState({
                        fileName: data.fileName,
                        eventTime: data.eventTime,
                        isFirstVideo: data.isFirstVideo,
                        speed: 0
                    }, () => {
                        this.IMIVideoView && this.IMIVideoView.prepare()
                    });
                }
                break
            case VideoStateType.VIDEO_CHANGE:
                //resume()如果报错，try{}catch(){}无法截获
                // this.IMIVideoView && this.IMIVideoView.resume();
                this.IMIVideoView && this.IMIVideoView.stop();
                this.progressView && this.progressView.onSliderValueChanged(0);
                this.tempScrollPause = false
                this.tempScrollTime = 0;
                this.videoProgressTime = 0;
                this.setState({isPlay: false, durationProgress: 0, isClickPause: false});
                break
            case VideoStateType.ERROR:
                //something wrong
                break
        }
    };

    _handlerPictureChange = (data) => {
        if (this.state.tabType == TabType.PIC) {
            this.setState({currentThumbUrl: data.pictureUrl,currentThumbUrlShowError:false})
        } else if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 0) {
            //需要重置下播放器的相关状态
            if (this.state.recording){
                //如果正在录制中，保存下
                //这个做备选方案，一般切换，如果在录制中，切换不成功，这里是防止录制中切换成功了
                this._stopRecord();
            }
            this.playRetryCount = 0;
            this.doPlayRetry = false;
            //这个会导致插件闪退，start function has not been called
            //为了修复，暂停状态无法正常切换视频源，，，这个需要APP修掉
            // this.IMIVideoView && this.IMIVideoView.resume();
            this.IMIVideoView && this.IMIVideoView.stop();
            this.isSDGridVideoPrepare = true;
            this.videoProgressTime = 0;
            this.offset = 0;
            this.tempScrollTime = 0;
            this.progressView && this.progressView.onSliderValueChanged(0);
            //网格展示的SD卡视频，这里需要去播放第一个视
            if (this.state.isPlay) {
                //如果滑动的时候正在处于播放状态
                this.tempScrollPause = false
                this.setState({isPlay: false, durationProgress: 0, fileName: "", isClickPause: false})
            }
            this.isSDVideoPlayComplete = false;
            let duration = parseInt((data.endTime - data.startTime) / 1000);

            this.setState({
                sdStartTime: data.startTime,
                sdEndTime: data.endTime,
                durationProgress: duration,
                isLoading:true,
                speed: 0,
                isPlayFinish:false,
                sdStartGridOffset:0
            }, () => {
                if (!this.IMIVideoView) {
                    //可能播放器还没有创建完成，这个回调已经过来了，导致无法播放
                    //加个延迟
                    this.playTimeout && clearTimeout(this.playTimeout);
                    this.playTimeout = setTimeout(() => {
                        this.IMIVideoView && this.IMIVideoView.prepare();
                    }, 500);
                    return;
                }
                this.IMIVideoView && this.IMIVideoView.prepare();
            });
        }


    }
    /**
     * 处理当前播放器事件
     */
    _doHandleOnEventChange = (data) => {
        console.log(TAG + 'doHandleOnEventChange ', data + "  IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_MUTED.code" + IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_MUTED.code);
        if (!data.hasOwnProperty('extra')) {
            return
        }
        let {extra = {}, code = undefined} = data;
        //如果缓存完毕
        if (code === PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFER_FINISH) {
            this.mCurItemLocal = extra;
            console.log('_saveVideoToLocal  ', this.mCurItemLocal);

            //判断 此处下载完成后 用户是否需要保存视频
            if (this.waitingSaveVideo) {
                this._saveVideoToLocal();
            }
        }
    };

    /**
     * 播放器内部状态变化监听
     */
    _onInteriorStateCall = (data) => {
        let {extra = {}, code = undefined} = data;
        //如果缓存完毕
        if (code === IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_START().code ||
            code === IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_STOP().code) {
            this._startOrPause();
        } else if (code === IMIPlayerViewBase.EVENT_PLAYER.RN_PLAYER_EVENT_ON_MUTED().code) {
            let {data} = extra;
            this.setState({muted: data.muted});
            console.log(TAG + 'doHandleOnEventChange data.muted ', data.muted);
        }
    };

    _startOrPause() {
        console.log("_startOrPause  this.state.isPlay " + this.state.isPlay + "   this.mCurItem " + this.mCurItem);
        /***判断是否第一次播放，默认播放第一条数据**/
        if ((!this.state.isPlay && this.mCurItem == null)) {
            this.mCurItem == null && (this.mCurItem = this.state.dataList.length > 0 ? this.state.dataList[0] : null);

            this.mCurIndex = 0;

            this._onItemPress(this.mCurItem, this.mCurIndex);
        } else {
            // this.videoPlayer.togglePlayPause();
            this.setState({isPlay: !this.state.isPlay});
        }
    }


    // _renderPlayerVideoViewComponents() {
    //     return (
    //         <View>
    //
    //             {!this.state.fullScreen ? <RNLine/> : null}
    //
    //             {this.getPlayerVideoView()}
    //
    //             {!this.state.fullScreen ? this._renderVideoToolBar() : null}
    //
    //             {!this.state.fullScreen ? <RNLine/> : null}
    //
    //         </View>
    //     )
    // }


    // getPlayerVideoView() {
    //
    //     /** ! 此处暂时备注 这样写法有风险，猜测：多个地方引用会导致共享变量 !*/
    //     CoverVideoImage.defaultProps = {
    //         videoCover: this.state.videoCover
    //     };
    //
    //     return (
    //         <VideoPlayerContainer
    //             ref={ref => this.videoPlayer = ref}
    //             muted={this.state.muted}
    //             PlayerElement={RNVideoImiCloudEx}
    //             isPlay={this.state.isPlay}
    //             dataSource={this.state.dataSource}
    //             cover={[CoveLoading, CoverVideoImage, CoverVideoToolProgress, CoveError]}
    //             videoCover={this.state.videoCover}
    //             onEventChange={this.doHandleOnEventChange.bind(this)}
    //             onCompletion={() => {
    //                 showToast('播放完成');
    //             }}
    //             onUpdateOrientation={(orientation) => {
    //                 this.setState({fullScreen: orientation !== 'PORTRAIT'});
    //                 this.props.onClickFullScreen && this.props.onClickFullScreen(orientation !== 'PORTRAIT');
    //             }
    //          }/>
    //     )
    // }


    onBackAndroid() {
        if (this.state.isFullScreen) {
            this._toggleOrientation(!this.state.isFullScreen);
            return true;
        }
        return super.onBackAndroid();
    }

    /**
     * 视频功能工具栏
     * @private
     */
    _renderVideoToolBar() {
        console.log("    _renderVideoToolBar() " + JSON.stringify(this.state.isPlay));

        return (
            <View style={{
                backgroundColor: colors.white,
                flexDirection: 'row',
                paddingVertical: 10,
                justifyContent: 'space-between',
                paddingHorizontal: 15
            }}>

                <CheckBoxButton
                    checked={this.state.isPlay}
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconPlay, iconPause]}
                    onValueChange={(checkValue) => {
                        console.log("CheckBoxButton 暂停/播放  checkValue  " + +checkValue);
                        if (checkValue && this.mCurItem == null) {
                            this.mCurItem == null && (this.mCurItem = this.state.dataList.length > 0 ? this.state.dataList[0] : null);
                            this.mCurIndex = 0;
                            this._onItemPress(this.mCurItem, this.mCurIndex);
                        } else {
                            this.videoPlayer.togglePlayPause();
                        }
                    }}/>

                <CheckBoxButton
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconVoiceOpen, iconVoiceClose]}
                    onValueChange={(checkValue) => {
                        this.videoPlayer.muted(checkValue)
                    }}/>

                <CheckBoxButton
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconSave]}
                    onValueChange={() => {
                        this._saveVideoToLocal();
                    }}/>

                <CheckBoxButton
                    buttonStyle={styles.videoToolBtn}
                    buttonImage={[iconFull]}
                    onValueChange={(checkValue) => {
                        this._toggleOrientation(checkValue);
                    }}/>

            </View>
        )
    }

    _toggleOrientation(fullScreen) {
        this.videoPlayer.toggleOrientation();

        console.log("_toggleOrientation   " + this.state.isFullScreen + " checkValue " + fullScreen);
        this.setState({isFullScreen: fullScreen});
        this.props.onClickFullScreen && this.props.onClickFullScreen(fullScreen);
    }


    /**
     * 获取数据
     * 列表、时间轴
     */
    _getAlarmListData() {
        //让list滚动到顶部
        let clearData = false;
        if (this.state.tabType === TabType.CLOUD && this.state.alarmVideoMode == 0){
            clearData = true;
        }
        this.alarmListPlayerComponent && this.alarmListPlayerComponent.listScrollToTop();
        this.alarmListPlayerComponent && this.alarmListPlayerComponent._queryDataList(true, true, clearData);
        //this._queryDataList(true);
    }

    /**
     * 去往其他页面是否能
     */
    _setShowPauseForBack() {
        this.gotoOtherPageShowPause = true;
        this.needRefreshData = false;
    }


    /**
     * 需要轮询接口获取下载地址
     * 此接口需要转码MP4耗时
     * @param filename
     */
    getDownloadUrl(fileName) {
        // showLoading(stringsTo("alarm_download_downloading"),true);
        this.props.downloadStateChangeListener && this.props.downloadStateChangeListener(0, DowaloadType.DOWNLOADING);
        this.pollToGetUrl(fileName);
    }

    pollToGetUrl(fileName) {
        this.downloadState = DowaloadType.START;
        this.props.downloadStateChangeListener && this.props.downloadStateChangeListener(0, DowaloadType.START);
        aliAlarmEventCloudApi.downloadAliVideoList(LetDevice.deviceID, fileName).then(data => {
            console.log("data", data);
            let result = typeof (data) == "object" ? data : JSON.parse(data);
            if (result.progress == 100) {
                //说明已经转码完成了
                //转码完成后，我们去下载
                this.isDownLoadVideo = true;
                this.downloadFile(result.url);
            } else {
                this.pollTimeout && clearTimeout(this.pollTimeout);
                this.pollTimeout = setTimeout(() => {
                    this.pollToGetUrl(fileName);
                }, 500);
            }
        }).catch(error => {
            showToast(stringsTo('video_download_fail'));
            showLoading(false);

            this.downloadFail();

        })
    }

    downloadFile(url) {
        this.downloadState = DowaloadType.START;
        let nameDate = new Date().getTime();
        //文件命名不能重复，相同文件名，会存在多个文件，
        //会出现转存相册一次转存多个文件进入
        // if (this.downLoadEventTime){
        //     nameDate = this.downLoadEventTime;
        // }
        console.log("--------------")
        let fileType = this.isDownLoadVideo ? '.mp4' : '.jpg';
        let random = Math.round(Math.random()*100000);
        let fileName = `${moment(nameDate).format('YYYYMMDDHHmmss')}_${random}${fileType}`;
        // const path = `${CLOUD_DOWNLOAD}/${fileName}`;
        //回调下载进度给父组件
        IMIDownload.downloadToPath(EVENT_NAME, url, LetDevice.deviceID, fileName);

        this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
            //console.log('DeviceEventEmitter 下载 '+JSON.stringify(event));
            if (event.status === IMIDownload.STATUS_START) {
            }
            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                //这里可以显示下载进度，UI展示进度等，都OK
                //Android端与iOS端返回的数据，暂时不统一。
                //要求ios不显示进度，Android显示
                let progress = Math.round(event.progress * 100);
                if (isAndroid()) {
                    progress = Math.round(event.progress * 100 / event.max);
                }

                if (progress > this.preProgress) {
                    this.preProgress = progress;
                    this.downloadState = DowaloadType.DOWNLOADING;
                    this.props.downloadStateChangeListener && this.props.downloadStateChangeListener(progress, DowaloadType.DOWNLOADING);
                    // showLoading(progress+"%",true,false);
                }

            }

            if (event.status === IMIDownload.STATUS_ERROR) {
                // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
                this.preProgress = 0;
                this.downloadFail();

                showLoading(false);
                showToast(stringsTo('video_download_fail'));
            }
            if (event.status === IMIDownload.STATUS_CANCEL) {
                // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
                this.preProgress = 0;
                this.downloadFail();

                showLoading(false);
                showToast(stringsTo('video_download_fail'));
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {
                console.log("--------save to album");
                this.preProgress = 0;
                const path = `${event.downloadPath}/${fileName}`;
                if (this.isDownLoadVideo) {
                    IMIFile.saveVideoToPhotosAlbum(path, LetDevice.deviceID).then((data) => {
                        // showToast(stringsTo('save_success'));
                        this.downloadState = DowaloadType.SUCCESS;
                        this.props.downloadStateChangeListener && this.props.downloadStateChangeListener(100, DowaloadType.SUCCESS);
                        console.log('保存相册' + data);
                    }).catch(error => {
                        console.log('保存相册error--' + path + JSON.stringify(error));
                        this.downloadFail();
                        showToast(stringsTo('save_failed'));
                    });
                } else {
                    IMIFile.saveImageToPhotosAlbum(path, LetDevice.deviceID).then((data) => {
                        // showToast(stringsTo('save_success'));
                        this.downloadState = DowaloadType.SUCCESS;
                        this.props.downloadStateChangeListener && this.props.downloadStateChangeListener(100, DowaloadType.SUCCESS);
                        console.log('保存相册' + data);
                    }).catch(error => {
                        console.log('保存相册error--' + path + JSON.stringify(error));
                        this.downloadFail();
                        showToast(stringsTo('save_failed'));
                    });
                }

                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
        });

    }

    downloadFile2(downStart, dataList) {
        console.log(TAG + "start downloadFile  ===================  ");
        return new Promise((resolve, reject) => {
            let item = dataList[downStart];
            let {pictureTime, pictureUrl} = item;
            let fileName = `${pictureTime}.jpg`;

            IMIDownload.downloadToPath(EVENT_NAME, pictureUrl, LetDevice.deviceID, fileName);

            this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
                if (event.status === IMIDownload.STATUS_START) {
                }
                if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                }

                if (event.status === IMIDownload.STATUS_ERROR) {
                    reject(downStart, event.status);//IOS在错误时会一直显示加载

                    // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                    //用过一次必须释放
                    this.listener && this.listener.remove();
                    this.listener = null;
                }
                if (event.status === IMIDownload.STATUS_CANCEL) {
                    // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                    //用过一次必须释放
                    this.listener && this.listener.remove();
                    this.listener = null;
                }

                if (event.status === IMIDownload.STATUS_SUCCESS) {
                    const path = `${event.downloadPath}/${fileName}`;

                    IMIFile.saveImageToPhotosAlbum(path, LetDevice.deviceID).then((data) => {
                        resolve(downStart, data);
                    }).catch(error => {

                        reject(downStart, error);
                    });
                    //用过一次必须释放
                    this.listener && this.listener.remove();
                    this.listener = null;
                }
            });
        }).then((start, data) => {
            console.log(TAG + "start downloadFile ++++++++++++++++ ", data);
            return start;
        });
    }

    downloadFail() {
        //标记是否提示不同的标记
        let showAnther = false;
        //1、第一条视频
        if (this.state.isFirstVideo) {
            //2、在三十分钟内
            let eventTimeMl = moment(this.state.eventTime).valueOf();
            let curTimeMl = new Date().getTime();
            if (Math.abs(eventTimeMl - curTimeMl) < 30 * 60 * 1000) {
                showAnther = true;
            }
        }
        this.downloadState = DowaloadType.FAIL;
        this.props.downloadStateChangeListener && this.props.downloadStateChangeListener(0, DowaloadType.FAIL, showAnther);
    }

    /**
     * 事件选择监听按下
     * @param item
     * @param index
     */
    _onItemPress(item, index) {
        if (isEmpty(item)) {
            return
        }
        if (!this.props.isEdit) {
            // if (LetDevice.isShareUser) {
            //     showToast(stringsTo("shareUser_tip"));
            //     return;
            // }
            let {pictureTime, pictureTimeUTC, pictureId} = item;
            if (this.props.vipState != 1) {
                //  IMILog.logD("王 看家缩略图点击事件 _onItemPress: ",item.toString())
                this.props.navigation.push('AlbumPhotoViewPage', {
                    item: item,
                    vipState: this.props.vipState,
                    storageType: this.props.storageType
                });
            } else {
                //VIP时看家，点击item直接跳转到云存播放页面
                let {showUseCloudEvent} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
                if (showUseCloudEvent) {
                    // 052
                    if (IMIPackage.minApiLevel >= 10005) {
                        //this.props.navigation.push('AlbumPhotoViewPage',{item:item,vipState:this.props.vipState,storageType:this.props.storageType});
                        if (this.props.storageType == 1) {
                            let eventStr = {
                                "eventList": [AlarmType.MOVE, AlarmType.PEOPLE, AlarmType.SOUND]
                            };
                            IMIGotoPage.startAliMoveDetectionPageConfig(item.pictureTime, LetDevice.deviceID, JSON.stringify(eventStr));
                        } else if (this.props.storageType == 2) {
                            let event = 100;
                            item["eventAlarmTypeProp"] = event;
                            item["pageStartProp"] = 0;//this.state.pageStart;//
                            this.props.navigation.push('CloudStoragePage', {item: item});
                        } else {
                            this.props.navigation.push('AlbumPhotoViewPage', {
                                item: item,
                                vipState: this.props.vipState,
                                storageType: this.props.storageType
                            });
                        }
                    } else {
                        if (IMIPackage.minApiLevel > 10002 && IMIPackage.minApiLevel < 10005) {
                            //支持云存储筛选事件最低版本为10003
                            let eventStr = {
                                "eventList": [AlarmType.MOVE, AlarmType.PEOPLE]
                            };
                            IMIGotoPage.startAliMoveDetectionPageConfig(moment(pictureTimeUTC).format('yyyy-MM-DD HH:mm:ss'), LetDevice.deviceID, JSON.stringify(eventStr));
                        } else {
                            IMIGotoPage.startAliMoveDetectionPageV2(moment(pictureTimeUTC).format('yyyy-MM-DD HH:mm:ss'), LetDevice.deviceID);
                        }
                    }
                } else {
                    // 038
                    if (IMIPackage.minApiLevel > 10001) {
                        IMIGotoPage.startAliMoveDetectionPageV2(moment(pictureTimeUTC).format('yyyy-MM-DD HH:mm:ss'), LetDevice.deviceID);
                    } else {
                        IMIGotoPage.startAliMoveDetectionPage(moment(pictureTimeUTC).format('yyyy-MM-DD HH:mm:ss'));
                    }
                }


            }

        } else {
            if (this.props.onClickSelect) {
                this.props.onClickSelect(index);
            }
        }

    }

    onSelectAllChanged(isSelectedAll) {
        let selectedCount = 0;
        for (let file of this.state.dataList) {
            file.isSelected = isSelectedAll;
            if (file.isSelected) {
                selectedCount++;
            }
        }
        this.num = selectedCount;
        this.setState({dataList: this.state.dataList});// 刷新页面 状态不要保留在ui控件里
        if (this.props.onClickSelect) {
            let isSelect = undefined;
            if (this.num == 0) {
                isSelect = false;
            }
            if (this.num == this.state.dataList.length) {
                isSelect = true;
            }
            this.props.onClickSelect(this.num, isSelect);
        }
    }

    /**
     * 保存当前播放视频到本地相册中
     * @private
     */
    _saveVideoToLocal() {
        this.waitingSaveVideo = true;

        showLoading(stringsTo('commWaitText'), true);

        console.log('_saveVideoToLocal', this.mCurItemLocal);
        if (this.mCurItemLocal != null) {
            console.log('_saveVideoToLocal22222222222222222222', this.mCurItemLocal);
            // IMIVideoUtils.downloadToPath(pathList)
        }
    }

    /**
     * 处理当前播放器事件
     */
    doHandleOnEventChange(data) {
        console.log(TAG + 'doHandleOnEventChange ', data);

        if (!data.hasOwnProperty('extra')) {
            return
        }

        let {extra = {}, code = undefined} = data;
        //如果缓存完毕
        if (code === PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFER_FINISH) {
            this.mCurItemLocal = extra;

            //判断 此处下载完成后 用户是否需要保存视频
            if (this.waitingSaveVideo) {
                this._saveVideoToLocal();
            }
        }
    }

    /***           系統函數   START    ****/

    componentWillUnmount() {
        super.componentWillUnmount();
        console.warn("componentWillUnmount");
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.screenTooltsTimer && clearTimeout(this.screenTooltsTimer);
        this.playTimeout && clearTimeout(this.playTimeout);
        this._subscribe_focus && this._subscribe_focus();
        this._subscribe_blur && this._subscribe_blur();
        this._enterBackground && this._enterBackground.remove();
        this._enterForeground && this._enterForeground.remove();
        this.deviceInfoListener && this.deviceInfoListener.remove();
        this.indicactorTimeout && clearTimeout(this.indicactorTimeout);
        this.retryStartRecord && clearTimeout(this.retryStartRecord);
        this.retryTimeout && clearTimeout(this.retryTimeout);
        this.waitSetOver && clearTimeout(this.waitSetOver);
    }

    componentDidMount() {
        console.warn("componentDidMount");
        this._subscribe_focus = this.props.navigation.addListener('focus', () => {
            if (isAndroid()) {
                this.alarmBack = BackHandler.addEventListener('hardwareBackPress', this.onBackHandler.bind(this));
            }
            //设备在线，并且是从后台返回到前台的时候，我们去初始化播放器
            //第一次进来不初始化
            console.warn("alarm focus");
            if (!this.isFirstIn) {
                if (this.needRefreshData) {
                    //是否需要重新拉取数据
                    this._getAlarmListData();
                } else {
                    if (!this.gotoOtherPageShowPause) {
                        if (this.state.tabType == TabType.CLOUD || this.state.tabType == TabType.SD) {
                            if (this.startPlayForBack) {
                                this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
                                    this.IMIVideoView && this.IMIVideoView.resume();
                                });
                            }
                        }
                    }
                }


                //无云存数据，云存时间轴回来，这个会提示播放失败
                // if ((this.state.tabType == TabType.CLOUD || this.state.tabType == TabType.SD) && this.state.alarmVideoMode == 1){
                //     //云存时间轴
                //     this.dateTimeForTemp.setTime(new Date(this.state.dateTime).valueOf());
                //     this.dateTimeForTemp.setHours(0);
                //     this.dateTimeForTemp.setMinutes(0);
                //     this.dateTimeForTemp.setSeconds(0,0);
                //     //某天的最后一个视频
                //     let hasVideo = this.state.tabType == TabType.CLOUD?CloudVideoUtil.hasVideoInDay(this.dateTimeForTemp.valueOf()):SDVideoUtil.hasVideoInDay(this.dateTimeForTemp.valueOf());
                //     if (hasVideo){
                //         this.IMIVideoView && this.IMIVideoView.prepare();
                //     }
                //
                // }else {
                //     this.IMIVideoView && this.IMIVideoView.prepare();
                // }
                this.needRefreshData = true;
                this.gotoOtherPageShowPause = false;
            }
            this.isFirstIn = false;
            this.isForegroundPage = true;
            this.setState({isOnline: LetDevice.isOnline});
            // console.log('focus回看全屏状态',this.leaveFullScreen);
            // if (this.leaveFullScreen){
            this._exitFullScreen();
        });

        this.deviceInfoListener = LetDevice.addInfoChangeListener((info) => {
            let data = typeof (info) == 'object' ? info : JSON.parse(info);
            console.log('info----------' + JSON.stringify(data));
            if (data.key == 'isOnline') {
                this.setState({isOnline: data.value});
                if (this.state.tabType == TabType.SD && this.state.alarmVideoMode == 1 && this.isForegroundPage) {
                    //如果是回看时间轴的形式，播放器离线、在线才去操作播放器
                    //增加一个条件，在前台
                    if (data.value == false) {
                        this.IMIVideoView && this.IMIVideoView.stop();
                    } else {
                        if (this.state.speed !== 0) {
                            this.setState({speed: 0})
                        }
                        this.IMIVideoView && this.IMIVideoView.prepare();
                    }
                }

            }

        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            console.warn("----------blur");
            if (isAndroid()) {
                this.alarmBack && this.alarmBack.remove();
                // BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
            }
            this.isForegroundPage = false;
            //需要重置一天状态
            //20221103@byh 修改，去往其他页面
            //从看家助手到实时视频页面，我们把播放器停掉，其他的只做暂停
            if (this.needRefreshData) {
                this.IMIVideoView && this.IMIVideoView.stop();
                this.videoProgressTime = 0;
                this.progressView && this.progressView.onSliderValueChanged(0);
            } else {
                if (this.state.isPlay) {
                    this.startPlayForBack = true;
                    this.setState({isPlay: false, showPauseView: true, isClickPause: true}, () => {
                        this.IMIVideoView && this.IMIVideoView.pause();
                    });
                } else {
                    this.startPlayForBack = false;
                }
            }
        });

        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            console.warn("alarm 后台");
            if (!this.isForegroundPage) {
                //与首页同级tab
                return;
            }
            //停止视频播放
            if (this.state.tabType == TabType.CLOUD || this.state.tabType == TabType.SD) {
                //进入后台，Android在播放器的pause回调中，无法转存视频成功，
                if (this.state.recording) {
                    //处于录制中
                    this._stopRecord(true);
                }
                if (this.state.isPlay) {
                    backupIsPlay = true;
                    this.setState({isPlay: false, showPauseView: true, isClickPause: true}, () => {
                        this.IMIVideoView && this.IMIVideoView.pause();
                    });
                } else {
                    backupIsPlay = false;
                }
            }
        });

        this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
            console.warn('alarm--前台--');
            if (!this.isForegroundPage) {
                //与首页同级tab
                return;
            }
            if (this.state.tabType == TabType.CLOUD || this.state.tabType == TabType.SD) {
                if (backupIsPlay) {
                    // console.warn('获取当前声音状态===',this.muteStatus);
                    this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
                        this.IMIVideoView && this.IMIVideoView.resume();
                    });
                }
            }
        });


        // this._getAlarmListData();
        //横屏播放，在此进入
        if (this.state.isFullScreen) {
            this.setState({isFullScreen: false})
        }

        console.log("dengying",TAG,"componentDidMount");

        setTimeout(()=>{
            this.doTabChange(2);
        },500);


    }

    onBackHandler() {
        console.log("back is do");
        if (this.props.isEdit && this.props.isEdit) {
            //退出编辑模式
            this.props.onEditModeChange && this.props.onEditModeChange(true);
            return true;
        } else if (this.state.isFullScreen) {
            this._exitFullScreen();
            return true;
        }
        this.props.navigation.goBack();
        return true;
    }

    /***           系統函數   END    ****/


}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        //position: 'absolute',
    },

    itemParent: {
        // alignItems: 'center',
        paddingLeft: 14, paddingTop: 14,
        // flexDirection: 'row',
        backgroundColor: colors.white
    },

    itemIcon: {
        borderRadius: 4, width: (windowWidth - 4 * 14) / 3, height: (windowWidth - 4 * 14) / 3 * 57 / 101.33,
    },

    itemTitle: {
        fontSize: 12,

        // fontWeight: 'bold',
        color: colors.black,
        // paddingLeft: 14
    },

    itemDesc: {
        fontSize: 12,
        paddingLeft: 14,
        // marginTop: 10,
        color: colors.black,
    },
    videoToolBtn: {
        marginHorizontal: 15,
        width: 30,
        height: 30,
    },
});

