/**
 * CloudStoragePagePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 *
 * 示例:
 * <CloudStoragePagePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </CloudStoragePagePlayerComponent>
 *
 * @author: yanmin
 * @date: 2020/12/28
 */

import React, {Component} from 'react';
import {
    View,
    Text,
    BackHandler,
    Platform,
    ActivityIndicator,
    StatusBar,
    StyleSheet,
    Dimensions,
    ScrollView,
    DeviceEventEmitter
} from 'react-native';
import {
    AlarmType,
    aliAlarmEventCloudApi,
    DateUtils, IMIDownload,
    IMIGotoPage,
    IMIVideoView,
    LetDevice,
    IMIPackage, IMILog
} from "../../../../imilab-rn-sdk";
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from "../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIFile from "../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import {stringsTo,locales} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';

import PropTypes from 'prop-types';
import {isAndroid, isIos, isPhoneX, objHasKey} from "../../../../imilab-rn-sdk/utils/Utils";
import IMIToast from "../../../../imilab-design-ui/src/widgets/IMIToast";
import PlayBackToolBarView from "../../../../imi-rn-commonView/PlayerToolBarView/PlayBackToolBarView";
import {Calendar} from "react-native-calendars";
import {
    colors,
    imiThemeManager,
    showLoading,
    RoundedButtonView,
    showToast,
    IMIImageView, IMIDesignEmptyView
} from "../../../../imilab-design-ui";
import ImageButton from "../../../../imi-rn-commonView/ImageButton/ImageButton";
import ModalView from "../../../../imi-rn-commonView/ModalView/ModalView";
import I18n from "../../../../globalization/Localize";
import {PLAYER_EVENT_CODE} from "../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import {XFlatList, XImage, XText, XView} from 'react-native-easy-app';
import moment from "moment";
import Toast from "react-native-root-toast";
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {CONST} from "../../../../imilab-design-ui/src/style/ThemeManager";
import {IMINativeLifeCycleEvent} from "../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import {startUnix, timeFilter} from "../../../../imilab-rn-sdk/utils/DateUtils";
import PlayBackFullScreenToolBarView from "../../../../imi-rn-commonView/PlayerToolBarView/PlayBackFullScreenToolBarView";
import SelectEventTimeView from "./selectEvent/SelectEventTimeView";
import MessageDialog from "../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog";
import ChoiceItem from "../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import VideoProgressView from "./videoProgressView/VideoProgressView";
import iconDot from "../../resources/images/pic_move.png";
import iconDots from "../../resources/images/pic_move.png";
import iconDot1 from "../../resources/images/pic_move.png";

const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
const speedTitleAry = ["1X", "2X", "4X"];
const speedAccessibilityLabelTitleAry = ["play_back_clarity_show_1X","play_back_clarity_show_2X",
    "play_back_clarity_show_4X","play_back_clarity_show_/8X","play_back_clarity_show_16X"];
const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});
let windowWidth = Dimensions.get('window').width;
const VOD_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading' ,
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};

let nowDay = new Date();
//shenyonggang@20210330 fixed bug IMI_HMI510_A01-327
let time;//相册名字
let backupIsPlay = false;
let currentPlayTime = 0;//记录断开前的时间
let lastClickSnapPhoto = 0; //上一次点击截图的时间
let dateDataArr = new Map();
let eventTypeArr = [];//事件类型
let secondsDay=24*3600;
const threeMin=3*60*1000;
let ms=3000;//沟通固件多出3秒
let tempVideoList = [];//用于存储临时视频列表
let tempEventList = [];//用于存储根据视频列表开始时间与结束时间请求返回的事件列表
let tempRealVideoList = [];//用于存储筛选出来的视频有效数据
let reqEventType = 0;//当前筛选的事件类型，默认是0，表示全部事件
let reqNext = false;//标记是否是进行了下一次请求，作用就是再下拉刷新时，合并请求回来的数据
const EVENT_NAME = "cloudStoragePageDownload";
const CLOUD_DOWNLOAD = `${IMIFile.storageBasePath}/cloud/`;
const tag = "CloudStoragePagePlayerComponent";
export default class CloudStoragePagePlayerComponent extends Component {
    static VOD_PLAYER_STATUS = VOD_PLAYER_STATUS;

    constructor(props, context) {
        super(props, context);
        this.state = {
            bps: -1,
            isFullScreen: false,//是否全屏
            mute: true,
            recording: false,//录屏监听  true为录屏中
            recordDuration: 0, //录屏时长,小于6秒提示失败
            showFullScreenTools: false,

            isLoading: true,  //加载中,进入显示加载中
            showErrorView: false, //错误view
            showPauseView: false, //暂停view
            errorCode: null, //错误code

            dataArray: [],
            currentDate: this.props.queryTime,
            speed: 0,  //倍速
            snapshotVisible: false, //截图是否显示中
            screenShotPath: null,
            isPlay:false,  //是否播放中 true为播放中
            isShowCalendar:false,
            isClickPause:false,
            dateData: {},//日期显示
            isPlayFinish:false,//是否播放结束
            dateText:'',
            showEventModal:false,//是否显示事件弹窗
            eventAlarmType:AlarmType.ALL,
            // pageStart:this.props.pageStartProp,
            pageStart:0,
            dataList: [],//图片集合
            videoList:[],//视频结果集合
            videoListOld:[],//视频不足9个时
            videoListEvent:[],//筛选事件云存
            selectEventId:this.props.eventIdProp,
            selectEventAlarmType:this.props.eventAlarmTypeProp,
            isPlayFromBeginning:false,
            durationProgress:0,
            fileName:"",
            noVideo:false,//是否有视频文件，默认有视频文件，
            noVideoPic:'',//没有视频文件，在播放器上面，展示缩略图
            isFirstIn:true,//标记是否是第一次进入云存，请求数据，第一次进入云存，
                           // 请求回数据后，需要自动播放第一条数据，其他情况不在自动播放第一条数据
            todayIsNoData:false,//标记今天没有云存数据，切换其他天，不管
            videoPage:0,//视频
            eventVideoList:[],//显示用的视频事件集合
            nextValid:false,//视频是否加载完成
            selectClickDay:false,//今天是否选择其他日期
            netFileName:"",
            flatListHeight:0
        }
        let reqTime=this._getDataFormatTime();
        if (this.props.eventIdProp!="0"){
            let times=  moment(this.props.pictureTime).format("YYYY-MM-DD");

            reqTime=times;
        }
        // console.log("time u:"+reqTime)
        this.reqTime =reqTime;
        this.homeFrom=false;
        this.selectBeginTime=0;
        this.loadingTimes=0;//每次加载为0
        this.netError=false;//点击重试,防止page+1
        // this.reqTime = this._getDataFormatTime();
        this.preProgress = 0;//下载进度可能出现比上一次小的情况，做个中间变量，下载时，下一次的下载进度如果比上一次的小，不在更新下载进度
    }

    static propTypes = {
        navBar: PropTypes.func,
        videoRef: PropTypes.func,
        navBarRight: PropTypes.array,

        toolBarMoreItems: PropTypes.array,
        fullScreenToolBarMoreItems: PropTypes.array,
        lensCorrect: PropTypes.shape({
            use: PropTypes.bool.isRequired,
            x: PropTypes.number,
            y: PropTypes.number
        }),
        onVideoClick: PropTypes.func,
        onHlsPlayerStatusChange: PropTypes.func,
        fileNameStatusChangeListener:PropTypes.func,
        loadingView: PropTypes.func,
        pauseView: PropTypes.func,
        errorView: PropTypes.func,
        onCheckPermissionStatusChange:PropTypes.func, //回调是否处于权限检测状态中
        pageStartProp:PropTypes.number,
        eventAlarmTypeProp:PropTypes.number,
        eventIdProp:PropTypes.string,
        pictureTime:PropTypes.string,
        showAlarmView:PropTypes.boolean,//看家是否打开
        netConnected:PropTypes.boolean,//是否联网
        componentContainerRef:PropTypes.func,  //回调组件容器的引用，方便调用者以此来获取容器的宽高等参数
    };

    static defaultProps = {
        navBar: undefined,
        navBarRight: [],
        toolBarMoreItems: [],
        fullScreenToolBarMoreItems: [],
        lensCorrect: {use: false, x: 0, y: 0},
        pageStartProp:0,
        eventAlarmTypeProp:AlarmType.ALL,
        eventIdProp:"0",
        pictureTime:"0",
        showAlarmView:false,//看家是否打开
        netConnected:true,//是否联网
    };

    _getDataFormatTime() {
        return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`;
    };
    UNSAFE_componentWillMount() {
        Orientation.lockToPortrait();
    }

    /**
     * 事件类型配置
     * 不同model，云存储展示的事件类型不同
     */
    getEventTypeList(){
        let {showPeopleEvent,showMoveEvent,showAlarmLoudSwitch,showFencesSwitch,showNoHuman,showCryEvent} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let people = {eventTit:stringsTo('people_event'),eventValue:AlarmType.PEOPLE,label:'storage_assistant_people_event'};
        let move = {eventTit:stringsTo('move_event'),eventValue:AlarmType.MOVE,label:'storage_assistant_move_event'};
        let sound = {eventTit:stringsTo('alarm_loud_switch'),eventValue:AlarmType.ABNORMAL_SOUND,label:'storage_assistant_alarm_loud_switch'};
        let fences = {eventTit:stringsTo('fence_detect_switch'),eventValue:AlarmType.CROSSING,label:'storage_assistant_fence_detect_switch'};
        let nobody = {eventTit:stringsTo('no_human_event'),eventValue:AlarmType.LAUGHTER,label:'storage_assistant_unattend_detect_switch'};
        let cry = {eventTit:stringsTo('cry_event'),eventValue:AlarmType.CRY,label:'storage_assistant_cry_detect_switch'};
        let eventArr = [{eventTit:stringsTo('all_events_str'),eventValue:AlarmType.ALL,label:'storage_assistant_all_events_str'}];
        if (showPeopleEvent){
            eventArr.push(people);
        }
        if (showMoveEvent){
            eventArr.push(move);
        }
        if (showAlarmLoudSwitch){
            eventArr.push(sound);
        }
        if (showFencesSwitch){
            eventArr.push(fences);
        }
        if (showNoHuman){
            eventArr.push(nobody);
        }
        if (showCryEvent){
            eventArr.push(cry);
        }
        eventTypeArr = eventArr;

    }
    /**
     * 获取当前数据           **  可以进行子类重写 **
     * @param isPullDown
     * @private
     */
    _queryVideoList = (isPullDown) => {
        this.refreshList && this.refreshList.refreshPreLoad(isPullDown);

        let page = isPullDown?0:this.netError?this.state.pageStart:this.state.pageStart+1;//断网点击重试会自动+1
        this.setState({pageStart:page});
        let paramsToken = isPullDown ? "" : this.reqToken;
        // this._getAliVideoListOnly(this.reqTime,   isPullDown,page);
        console.log(" this._queryDataList:"+isPullDown);
        //外部触发请求数据，都需要把这个清空
        tempRealVideoList = [];
        tempVideoList = [];
        tempVideoList = [];
        this._getAliVideoListOnlyNew(this.reqTime,   isPullDown,page);
    };
    /**
     * 看家页跳转到云存播放对应的云存视频
     * @private
     */
    _getAliVideoListFirst(){
        this._getAliPictureTimePlay();
    }

    /**
     * 根据看家图片的时间查找对应的云存视频播放
     * @private
     */
    _getAliPictureTimePlay(){
        let pictureTime=moment(this.props.pictureTime).valueOf();
        let startTime =pictureTime/1000-1900;
        let endTime =pictureTime/1000+1900;
        // console.log('_getAliVideoList this.props.pictureTime:',this.props.pictureTime)
        aliAlarmEventCloudApi.getAliVideoList(LetDevice.deviceID,
            startTime,
            endTime,
            1,
           0, 300,true).then((data) => {
           // console.log('__getAliVideoList   _getAliVideoListOnly then:',data.recordFileList.length)
            console.log('jeff __getAliVideoList  _getAliVideoListFirst  recordFileList:'+ JSON.stringify(data))
            let {recordFileList,nextValid,nextBeginTime}=data;
            let isShowNoVideo=-1;
            let noShowVideo=-1;
            let selectNoVideo=true;
            let FileListArr=[];//获取数据整合为数组,防止播放不是最接近图片时间的视频
            for (let value in recordFileList) {
                let {beginTime, endTime,fileName,beginTimeUTC,endTimeUTC} = recordFileList[value];
                let isTure=(pictureTime<=(moment(endTimeUTC).valueOf()+ms))&&((moment(beginTimeUTC).valueOf()-ms)<=pictureTime)
                // console.log("测试 value pictureTime: "+pictureTime+"endTimeUTC:"+(moment(endTimeUTC).valueOf()+ms)+" beginTimeUTC："+(moment(beginTimeUTC).valueOf()-ms))
                FileListArr.push(moment(beginTimeUTC).valueOf()) //添加一个小时内所有的时间戳
                // console.log("测试 value _getAliVideoListOnly: "+value+"Math:"+(pictureTime-moment(beginTimeUTC).valueOf()))
               /* if (((pictureTime-moment(beginTimeUTC).valueOf())>=0)&&selectNoVideo){ //Math.abs(pictureTime-moment(beginTimeUTC).valueOf())<30000
                    selectNoVideo=false;
                    this.selectBeginTime=(pictureTime-moment(beginTimeUTC).valueOf())
                    noShowVideo=value==0?0:value-1;
                }*/
                if (isTure){
                    console.log("jeff测试 value pictureTime: "+this.props.pictureTime+",endTime:"+endTime+" ,beginTime："+beginTime)
                    isShowNoVideo=value;
                    this.selectBeginTime=(pictureTime-moment(beginTimeUTC).valueOf());
                    break;
                }
            }
            console.log("jeff测试 value id isShowNoVideo: "+isShowNoVideo+",selectBeginTime = ",this.selectBeginTime)
            if (isShowNoVideo!=-1){
                this.getByEventName(recordFileList[isShowNoVideo].fileName,true,true)
                this.downLoadEventTime = recordFileList[0].eventTimeUTC
            }else {
                if (FileListArr.length>0){
                   // FileListArr.push(pictureTime)
                   // FileListArr.sort((a,b)=> b-a)  //排序升序(a,b)=> a-b
                    let index=FileListArr.findIndex((num)=>{ //获取图片时间在数组的位置
                        console.log("jeff index FileListArr:"+num)
                        return num <= pictureTime
                    })

                    if (index==-1){
                        console.log("jeff 事件时间点前云存未有对应的视频，播放最接近的视频");
                        this.getByEventName(recordFileList[FileListArr.length -1].fileName,true,true)
                    }else {
                        console.log("jeff index::"+index+",recordFileList[index]::"+recordFileList[index].beginTimeUTC)
                        // this.selectBeginTime=(pictureTime-moment(recordFileList[index].beginTimeUTC).valueOf())//todo 因为视频不不包含事件的时间点不去选择位置播放

                        if(index > 0 && (pictureTime-moment(recordFileList[index].beginTimeUTC).valueOf())
                            > (moment(recordFileList[index-1].beginTimeUTC).valueOf() - pictureTime)){//todo 判断哪个视频更接近事件的时间点
                            console.log("上个视频更接近事件时间点 jeff index::",index-1,",recordFileList[index-1]::"+recordFileList[index-1].beginTimeUTC);
                            index = index -1;
                        }
                        this.getByEventName(recordFileList[index].fileName,true,true)
                        console.log("云存未找到对应的视频，查找最近的时间点的视频 jeff selectBeginTime = ",this.selectBeginTime);
                    }


                }else {
                    this.setState({
                        isPlay:false,
                        durationProgress:0,
                        fileName:"",
                        noVideo:true,
                        todayIsNoData:false,
                        isPlayFinish:false,
                    } ,()=>{
                        // console.log("测试 id _getAliVideoListOnly: 999")
                        this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(0);
                       // this._getAliVideoListOnly(this.reqTime,  true,0);
                        this._getAliVideoListOnlyNew(this.reqTime,  true,0,true,true)
                    })
                }
            }

        })
    }

    _getAliVideoListOnly(reqTime,isPullDown=true,page,isHome=false){
        let startTime=moment(reqTime).valueOf()/1000;
        let endTime=startTime+secondsDay;
        aliAlarmEventCloudApi.getAliVideoList(LetDevice.deviceID,
            startTime,
            endTime,
            1,
            page, 20,true).then((data) => {
            let {recordFileList,nextValid,nextBeginTime}=data;
            console.log('__getAliVideoList  _getAliVideoListOnly  recordFileList:'+JSON.stringify(data))
            console.log('__getAliVideoList    recordFileList99:'+nextValid+isHome)
            if (page==0&&!nextValid){
                this.setState({
                    isPlay:false,
                    durationProgress:0,
                    fileName:"",
                    noVideo:false,
                    todayIsNoData:true,
                    isPlayFinish:false,
                    selectEventId:"0",
                })
            }else {
                this.setState({
                    videoList:recordFileList,
                    dataList:[]
                },()=>{
                    if (recordFileList.length>0){
                        this.getEventListAll(moment(recordFileList[recordFileList.length-1].endTime).valueOf(),
                            moment(recordFileList[0].beginTime).valueOf(),
                            0,isPullDown,nextValid)
                        if (!isHome){
                            this.getByEventName(recordFileList[0].fileName,false,true)
                        }

                    }

                })
            }


        }).catch(()=>{
            this.setState({
                isPlay:false,
                durationProgress:0,
                fileName:"",
                noVideo:false,
                todayIsNoData:true,
                isPlayFinish:false,
                selectEventId:"0",
            })
        })
    }

    /**
     * 请求新的视频前的准备工作
     *
     */
    reqDataPre(shouldPlayFirstVideo = false){
        //切换事件
        //清空临时
        tempRealVideoList = [];
        tempVideoList = [];
        tempVideoList = [];
        reqEventType = (this.state.selectEventAlarmType==AlarmType.ALL)?0:this.state.selectEventAlarmType;
        this.refreshList && this.refreshList.refreshPreLoad(true);
        this._getAliVideoListOnlyNew(this.reqTime,true,0,shouldPlayFirstVideo);
    }

    /**
     *
     * @param reqTime
     * @param isPullDown
     * @param page
     * @private
     */
    _getAliVideoListOnlyNew(reqTime,isPullDown=true,page,isFirst = false,isOnly=false){
        let startTime=moment(reqTime).valueOf()/1000;
        let endTime=startTime+secondsDay;
        console.log("page::then:"+page)
        aliAlarmEventCloudApi.getAliVideoList(LetDevice.deviceID,
            startTime,
            endTime,
            1,
            page, 20,true).then( (data) => {
            let {recordFileList, nextValid, nextBeginTime} = data;
            console.log('__getAliVideoList  _getAliVideoListOnlyNew  recordFileList:' + JSON.stringify(data))
            //console.log('__getAliVideoList    recordFileList77:' + nextValid+isFirst+isOnly)
            if (page == 0 && !nextValid && recordFileList.length === 0) {
                showLoading(false);
                this.refreshList && this.refreshList.refreshLoaded(true, isPullDown, true, false);
                this.netError=false,
                this.loadingTimes=0;
                this.setState({
                    isPlay: false,
                    durationProgress: 0,
                    fileName: "",
                    noVideo: false,
                    todayIsNoData: true,
                    isPlayFinish: false,
                    selectEventId: "0",
                })
            } else {
                if(!nextValid && recordFileList.length === 0){
                    showLoading(false);
                    this.refreshList && this.refreshList.refreshLoaded(true, false, true, false);
                }

                if (recordFileList.length > 0) {
                    this.netError=false,
                    this.loadingTimes=0;
                    //每次请求前清空这个数组
                    tempEventList = [];
                    //请求回来的视频列表
                    tempVideoList = recordFileList;
                    //最小时间-3分钟是因为，用视频最小时间请求不到对应事件
                    console.log("recordFileList::::"+moment(recordFileList[recordFileList.length - 1].beginTimeUTC).valueOf()+" ---"+moment(recordFileList[recordFileList.length - 1].beginTime).valueOf())
                    this.getEventListAllNew(moment(recordFileList[recordFileList.length - 1].beginTimeUTC).valueOf()-threeMin,
                        moment(recordFileList[0].endTimeUTC).valueOf(),
                        0, isPullDown, nextValid,isFirst,page);
                    if (isFirst&&!isOnly && reqEventType == 0){
                        this.getByEventName(recordFileList[0].fileName);
                        this.downLoadEventTime = recordFileList[0].eventTimeUTC;
                    }
                    // this.getEventListAll(moment(recordFileList[recordFileList.length - 1].endTime).valueOf(),
                    //     moment(recordFileList[0].beginTime).valueOf(),
                    //     0, isPullDown, nextValid)


                    // this.getByEventName(recordFileList[0].fileName, this.state.selectEventId, false, true)
                }
            }
        }).catch(err=>{
            console.log("page::catch:"+page)
            this.netError=true,
            this.refreshList && this.refreshList.refreshLoaded(false, isPullDown, true, true); //加载失败
            showLoading(false);
        })
    }



    _getAliVideoList(selectTime,isShowTime=true,pageSize=500,only=false){//DateUtils.startUnix(selectTime)
        let startTime=moment(selectTime).valueOf()/1000;
        let endTime=startTime+secondsDay;
        console.log('_getAliVideoList  then  startTime:',selectTime+isShowTime+this.reqTime)
        console.log('_getAliVideoList  then  page:',selectTime)
        let videoList=this.state.videoList;
        if (!isShowTime&&selectTime!=this.reqTime){
            return;
        }
        aliAlarmEventCloudApi.getAliVideoList(LetDevice.deviceID,
            startTime,
            endTime,
            1,
            this.state.videoPage, pageSize,true).then((data) => {
            let {recordFileList,nextValid,nextBeginTime}=data;
            //console.log('_getAliVideoList  then:',this.state.videoPage)
            console.log('_getAliVideoList  then recordFileList:'+JSON.stringify(recordFileList)+nextValid+nextBeginTime)
            let reqTimeStart=moment(this.reqTime).valueOf()/1000;
            let reqTimeEnd=reqTimeStart+secondsDay;
            if (nextBeginTime!=undefined){
                if (reqTimeStart>nextBeginTime&&nextBeginTime>reqTimeEnd){
                    return;
                }
                if (recordFileList.length>0){
                    let replaceTome= DateUtils.startUnix(recordFileList[0].beginTime.split(" ")[0]);
                    let videoTime = moment(replaceTome).valueOf()/1000
                    if (reqTimeStart>videoTime&&videoTime>reqTimeEnd){
                        return;
                    }
                }
            }else if (nextBeginTime==undefined){
                if (recordFileList.length>0){
                    let replaceTome= DateUtils.startUnix(recordFileList[0].beginTime.split(" ")[0]);
                    let videoTime = moment(replaceTome).valueOf()/1000
                    if (reqTimeStart>videoTime&&videoTime>reqTimeEnd){
                        return;
                    }
                }
            }

            if (recordFileList.length<=0&&videoList.length==0){ //当天没有视频
                showLoading(false);
                this.refreshList && this.refreshList.refreshLoaded(true, true, false, false);
                this.setState({
                    videoPage:0,
                    videoList:[],
                    isPlay:false,
                    durationProgress:0,
                    fileName:"",
                    noVideo:false,
                    todayIsNoData:true,
                    isPlayFinish:false,
                    selectEventId:"0",
                    nextValid:false,
                })
                this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(0);
            }else {
                //  console.log('__getAliVideoList    selectEventId:',this.state.selectEventId)
                if (!isShowTime&&selectTime!=this.reqTime){
                    return;
                }
                let itemList =[...videoList, ...recordFileList];
                let firstLoad=this.state.selectEventId=="0";
                this.setState({
                    videoPage:this.state.videoPage+1,
                    videoList:itemList,
                    nextValid:nextValid
                    // fileName:this.state.selectEventId=="0"?itemList[0].fileName:"",
                    /* isPlay:false,
                     durationProgress:0,
                     noVideo:false,
                     todayIsNoData:false,
                     isPlayFinish:false,*/
                },()=>{
                    //   console.log('__getAliVideoList    videoPage:',this.state.videoPage)
                    if (this.state.videoPage==1){
                        if (firstLoad&&!only){
                            this.getByEventName(itemList[0].fileName);
                        }
                        //   console.log('__getAliVideoList   00 videoPage:',this.state.videoPage)
                        this._queryDataList(true);
                    }


                    if (nextValid){
                        this._getAliVideoList(selectTime,isShowTime,50,only)
                    }

                })
            }
            //  console.log('__getAliVideoList   fileName:',itemList[0].fileName+only)

        }).catch(err=>{
            showLoading(false);
            this.setState({
                videoPage:0,
                videoList:[],
                isPlay:false,
                durationProgress:0,
                fileName:"",
                noVideo:false,
                todayIsNoData:true,
                isPlayFinish:false,
                selectEventId:"0",
                nextValid:false,
            })
        });
    }

    /**
     * 获取全部事件数据
     * @param time
     * @param paramsToken
     * @param isPullDown
     * @param page
     */
    getEventListAll(curStartTime,curEndTime, page,isPullDown=true,nextValidParam=true) {
        let dataList = this.state.dataList;
        let videoList=this.state.videoList;
        let stateVideoList=this.state.eventVideoList;
        //根据选择类型获取数据，获取后不需要额外处理
        //console.log( '__getAliVideoList:' +" curStartTime " + curStartTime + " curEndTime " + curEndTime + " time  " + page );
        aliAlarmEventCloudApi.getAliFileList(LetDevice.deviceID,
            curStartTime,
            curEndTime,
            0,
            page, 100).then((data) => {
            showLoading(false);
            console.log('__getAliVideoList  _getAliVideoListOnly  getEventListAll:'+data)
            let result = JSON.parse(data);
            let {eventList, nextValid} = result;
            if (eventList.length>0){
                let itemList = [...dataList, ...eventList];
                //console.log("我的测试:333333:"+itemList.length)
                if (nextValid){//加载当前所有数据完成
                    this.setState({
                        dataList:itemList,
                    },()=>{
                        this.getEventListAll(curStartTime,curEndTime,page+1,isPullDown,nextValidParam)
                    })
                }else {
                    this.setState({
                        dataList:itemList,
                    },()=>{
                        let eventVideoList=[];
                        let valueSelect=false;
                        //console.log("我的测试:333333;;:"+videoList.length)
                        for (let value in videoList) {
                            let {beginTime,endTime} = videoList[value];
                            valueSelect=false;
                            //console.log("我的测试:666666")
                            let times=0;//加载数据次数,为了一个视频对应多种类型
                            if (itemList.length>0){
                                for (let valueInt in itemList) {
                                    // console.log("我的测试:22222")
                                    let {eventTime,eventType} =itemList[valueInt];
                                    eventTime=moment(eventTime).valueOf()
                                    let isTure=(eventTime<=(moment(endTime).valueOf()+ms))&&((moment(beginTime).valueOf()-ms)<=eventTime)
                                    if (isTure){
                                        // console.log("我的测试:333333")
                                        valueSelect=true;
                                        times=times+1;
                                        let videoValue=videoList[value];
                                        let itemValue={"eventType":eventType,"eventType1":-1,"eventTime":beginTime,"thumbUrl":videoList[value].snapshotUrl}
                                        if (times==2){
                                            itemValue={"eventType1":eventType,}
                                        }
                                        let list= Object.assign(videoValue,itemValue)
                                        eventVideoList.push(list)
                                        break;
                                    }
                                }
                            }
                            // console.log("我的测试:1111")
                            if (!valueSelect){
                                let list=videoList[value];
                                list["eventType"]=-1;
                                list["eventType1"]=-1;
                                list["eventTime"]=beginTime;
                                list["thumbUrl"]=videoList[value].snapshotUrl;
                                eventVideoList.push(list);
                            }
                        }


                        // console.log("eventVideoList 结果:",eventVideoList.length+"分开"+isPullDown+!nextValidParam)
                        this.refreshList && this.refreshList.refreshLoaded(true, false, !nextValidParam, false);
                        let result= isPullDown?eventVideoList:[...stateVideoList,...eventVideoList]
                        this.setState({
                            eventVideoList:result,
                            //selectEventId:setID?eventVideoList[0].eventId:this.state.selectEventId,
                        })


                    })


                }
            }else {

            }


        }).catch((error) => {
            showLoading(false);
        });

    }

    /**
     *
     * @param curStartTime 开始时间
     * @param curEndTime 结束时间
     * @param page 页数
     * @param isPullDown 是下拉刷新还是加载更多数据
     * @param nextValidParam 标记视频列表后面是否还有数据
     * @param isFirst 这个标记主要用于请求完数据后，是否播放第一个视频，刚进入页面，第一次请求需要播放视频
     */
    getEventListAllNew(curStartTime,curEndTime, page,isPullDown=true,nextValidParam=true,isFirst = false,pageVideo=0) {
        let stateVideoList=this.state.eventVideoList;
        //根据选择类型获取数据，获取后不需要额外处理
        // console.log( '__getAliVideoList:' +" curStartTime " + curStartTime + " curEndTime " + curEndTime + " time  " + page +"isFirst"+isFirst);
        aliAlarmEventCloudApi.getAliFileList(LetDevice.deviceID,
            curStartTime,
            curEndTime,
            reqEventType,
            page, 100).then((data) => {
            this.loadingTimes=0;
            console.log('__getEventListAllNew  _getAliVideoListOnly  getEventListAll:'+data)
            let result = JSON.parse(data);
            let {eventList, nextValid} = result;
            //后面还有数据
            tempEventList = [...tempEventList,...eventList];
            console.log("[[[[[[[[[[[[[",tempVideoList);
            console.log("[[[[[[[[[[[[[",tempEventList);
            console.log("[[[[[[[[[[[[[nextValid",nextValid);
            if (nextValid){
                let tempPage = page+1;
                this.getEventListAllNew(curStartTime,curEndTime,tempPage,isPullDown,nextValidParam,isFirst,pageVideo);
            }else {
                //已加载完时间段内所有的事件
                //这边开始处理数据，从我们获取的视频列表中，筛选出事件
                let valueSelect=false;
                let realVideoList = [];
                for (let value in tempVideoList) {
                    let {beginTime,endTime,beginTimeUTC,endTimeUTC} = tempVideoList[value];
                    //标记是否能加入到展示的视频队列里面
                    valueSelect=false;
                    let times=0;//加载数据次数,为了一个视频对应多种类型
                    let videoValue=tempVideoList[value];
                    let isTure = false;
                    let isThreeAdd = false;

                    if (tempEventList.length>0){
                        for (let valueInt in tempEventList) {
                            let {eventTime,eventType,eventTimeUTC} =tempEventList[valueInt];
                            eventTime=moment(eventTimeUTC).valueOf()
                            isTure=(eventTime<=(moment(endTimeUTC).valueOf()+ms))&&((moment(beginTimeUTC).valueOf()-ms)<=eventTime)
                            if (isTure){
                                valueSelect=true;

                                if (videoValue.hasOwnProperty("eventType")){
                                    if (videoValue.eventType.length==1){
                                        if (videoValue.eventType[0]!=eventType){
                                            videoValue.eventType.push(eventType);
                                        }
                                    }else if (videoValue.eventType.length==2){
                                        if (videoValue.eventType[1]!=eventType){
                                            videoValue.eventType.push(eventType);
                                        }
                                    }

                                }else {
                                    let itemValue={"eventType":[eventType],"eventTime":beginTimeUTC,"thumbUrl":tempVideoList[value].snapshotUrl}
                                    videoValue = Object.assign(videoValue,itemValue);
                                }

                                if (videoValue.eventType.length===3){
                                    //已经添加3个事件到一个视频中了，我们不在遍历事件，往视频里面添加
                                    isThreeAdd = true;
                                    realVideoList.push(videoValue);
                                    console.log("[[[[[[[[[[[[[");
                                    break;
                                }
                            }
                        }
                        if (valueSelect && !isThreeAdd){
                            //有事件在我们这个视频里面，又不超过3个事件，这里加进去
                            realVideoList.push(videoValue);
                        }

                    }
                    //如果不是筛选事件，而是全部事件，这个视频筛选完全部事件，还是没有对应事件，给个默认
                    //这里属于异常情况
                    if (!valueSelect && reqEventType === 0){
                        console.log("[[[[[[[[[[[[[1111111");
                        let list=tempVideoList[value];
                        //类型给1，移动侦测吧
                        list["eventType"]=[1];
                        list["eventTime"]=beginTimeUTC;
                        list["thumbUrl"]=tempVideoList[value].snapshotUrl;
                        realVideoList.push(list);
                    }
                }
                // console.log("加载后的realVideoList：",realVideoList.length+"-------"+tempRealVideoList.length);
                //下拉刷新情况下：处理筛选条件，如果是事件筛选，筛选出的视频不满足我们需要的条数，我们需要继续加载，下一页视频继续筛选
                //筛选时暂时选择10条作为判断依据，如果小于10条，我们再去加载下一页数据
                //加载更多数据时，如果小于5条，我们再去加载下一页数据
                //如果是加载更多数据时，realVideoList

                //这个存储的是，一次表面的请求的数据集合，不管是上拉加载还是下拉刷新，这个数据每次都要清空掉
                tempRealVideoList = [...tempRealVideoList,...realVideoList]
                // console.log("加载后的realVideoList222：",realVideoList.length+"-------"+tempRealVideoList.length);
                if(isPullDown){

                    if (tempRealVideoList.length<10 && nextValidParam){
                        //这时去加载下一页视频数据
                        let nextPage = this.state.pageStart + 1;
                        this.setState({pageStart:nextPage});
                        this._getAliVideoListOnlyNew(this.reqTime,isPullDown,nextPage,isFirst);
                        return;
                    }


                }else {
                    if (tempRealVideoList.length<5 && nextValidParam){
                        //这时去加载下一页视频数据
                        let nextPage = this.state.pageStart + 1;
                        this.setState({pageStart:nextPage});
                        this._getAliVideoListOnlyNew(this.reqTime,isPullDown,nextPage,isFirst);
                        return;
                    }
                }
                showLoading(false);
                this.refreshList && this.refreshList.refreshLoaded(true, isPullDown, !nextValidParam, false);
                // console.log("加载后的realVideoList33333：",stateVideoList.length+"-------"+tempRealVideoList.length+isPullDown);
                let result= isPullDown?tempRealVideoList:[...stateVideoList,...tempRealVideoList];
                //寻找第一个视频并播放
                // console.log("[[[[[[[[[[[[[1111111"+isPullDown+"  "+isFirst+"  "+tempRealVideoList.length);
                //切换日期后，显示选择后的
                if (isFirst && tempRealVideoList.length>0 && reqEventType !=0){
                    this.getByEventName(tempRealVideoList[0].fileName);
                    this.downLoadEventTime = tempRealVideoList[0].eventTime
                }
                this.setState({
                    eventVideoList:result,
                })
            }
        }).catch((error) => {
            console.log("[[[[[[[[[[[",error)
             if (this.loadingTimes<2){
                 this.loadingTime&&clearTimeout(this.loadingTime);
                 this.loadingTime = setTimeout(()=>{
                     this.loadingTimes=this.loadingTimes+1;
                     this.getEventListAllNew(curStartTime,curEndTime,page,isPullDown,nextValidParam,isFirst,pageVideo);
                 },500);
             }else {
                 console.log("page:event:"+pageVideo)
                 tempVideoList = [];
                 this._getAliVideoListOnlyNew(this.reqTime,true,pageVideo==0?0:pageVideo,isFirst,true);
               //  showLoading(false);
             }
        });

    }
    /**
     * 点击时通过evetnId判断视频
     * only:是否第一次有ID加载
     * @param eventId
     */
    getByEventName(fileName,isOnly=false,isFirst=false){
        aliAlarmEventCloudApi.getByEventName(LetDevice.deviceID,
            fileName, true).then((data) => {
            // console.log("getVideo   fileName: data:",data)
            this.setState({
                noVideo:false,
                todayIsNoData:false,
                isPlayFinish:false,
                isPlay:false,
                durationProgress:0,
                fileName:fileName,
            },()=>{
                if (isOnly){
                    tempRealVideoList = [];
                    tempVideoList = [];
                    tempVideoList = [];
                    this._getAliVideoListOnlyNew(this.reqTime,  true,0,true,true)
                }
                if (isIos()){
                    this.setState({isLoading:true,showPauseView:false});
                }
                this.IMIVideoView &&this.IMIVideoView.prepare();
                this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(1);
            })
        }).catch(_=>{
            //如果未找到视频，需要在播放上面盖一层图片
            // console.log('getVideo   error:',_)
            this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(0);
            this.setState({
                noVideo:true,
                todayIsNoData:false,
                isPlayFinish:false,
                isPlay:false,
                durationProgress:0,
                selectEventId:"0",
            })
        });
    }

    /**
     * 暂时缓存最近三个月的数据
     * 请求最近三个月的月份对应数据
     */
    queryThreeMonthData(){
        //当前月数据
        let date = new Date();
        this._queryMonthData(DateUtils.dateFormat(date, "yyyyMM"),true);
        //上一月数据
        date.setMonth(date.getMonth()-1);
        this._queryMonthData(DateUtils.dateFormat(date, "yyyyMM"),true);
        //上上月数据
        date.setMonth(date.getMonth()-1);
        this._queryMonthData(DateUtils.dateFormat(date, "yyyyMM"),true);
    }

    /**
     * 检测某个月份是否有缓存数据
     * @param month
     */
    checkMonth(month){
        if (dateDataArr.has(month)){
            let dateArr = dateDataArr.get(month)
            this.setState({dateData:dateArr})
        }else {
            showLoading(true);
            this._queryMonthData(month);
        }
    }

    _queryMonthData(month,isFirst = false) {
        aliAlarmEventCloudApi.getMonthRecordList(LetDevice.deviceID,
            month?month:DateUtils.dateFormat(new Date(), "yyyyMM"), 8,true).then((data) => {
            console.log('CloudStorage getMonthRecordList--',data);
            let ary = data.recordFlags.split('');
            this._setDateString(ary,month?month:DateUtils.dateFormat(new Date(), "yyyyMM"),isFirst);
        }).catch((error) => {
        });
    }

    /**
     * 获取当前数据  用于事件分类
     * @param isPullDown
     * @private
     */
    _queryDataList = (isPullDown,is=false) => {
        if (!is){
            this.refreshList && this.refreshList.refreshPreLoad(isPullDown);
        }
        let page = isPullDown?0:this.state.videoEventPage+1;
        this.setState({videoEventPage:page});
        let paramsToken = isPullDown ? "" : this.reqToken;
        this.getEventList(this.reqTime, paramsToken,  isPullDown,page);
    };
    /**
     * 事件分类
     * @param time
     * @param paramsToken
     * @param isPullDown
     * @param page
     * @param isEvent
     */
    getEventList(time, paramsToken, isPullDown=false, page,isEvent) {

        console.log( '__getAliVideoList time:',time)
        let curStartTime=moment(time).valueOf()/1000;
        let curEndTime=curStartTime+secondsDay;
        //根据选择类型获取数据，获取后不需要额外处理
        // let curStartTime = DateUtils.startUnix(time);
        // let curEndTime = DateUtils.endUnix(time);
        let eventType=this.state.selectEventAlarmType==AlarmType.ALL?0:this.state.selectEventAlarmType;
        aliAlarmEventCloudApi.getAliFileList(LetDevice.deviceID,
            curStartTime*1000,
            curEndTime*1000,
            eventType,
            page, 100).then((data) => {
            console.log("event::::",data)
            let result = JSON.parse(data);
            let {eventList, nextValid} = result;
            this._getAliVideoEventList(eventList[eventList.length-1].eventTime,eventList[0].eventTime,eventList)


        }).catch((error) => {
            showLoading(false);
            let isSuccess = false;
            this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown);
            if (this.props.getDataLength){
                this.props.getDataLength(this.state.dataList.length);
            }
        });

    }



    _getAliVideoEventList(startTimes,endTimes,eventList){//DateUtils.startUnix(selectTime)
        console.log('_getAliVideoList  then:',startTimes,endTimes)
        let startTime=moment(startTimes).valueOf()/1000;
        let endTime=moment(endTimes).valueOf()/1000;
        console.log('_getAliVideoList  then:',startTime,endTime)
        aliAlarmEventCloudApi.getAliVideoList(LetDevice.deviceID,
            startTime,
            endTime,
            1,
            0, 500,true).then((data) => {
            let {recordFileList,nextValid,nextBeginTime}=data;
            console.log('_getAliVideoList  then:',this.state.videoPage)
            console.log('_getAliVideoList  then recordFileList:'+JSON.stringify(recordFileList)+nextValid+nextBeginTime)
            let eventVideoList=[];
            let valueSelect=false;
            for (let value in recordFileList) {
                let {beginTime,endTime} = recordFileList[value];
                if (eventList.length>0){
                    // console.log('__getAliVideoList  lengths 666 isPullDown-> ' + videoList[value].endTime+itemList[itemList.length-1].pictureTime);
                    // console.log('__getAliVideoList  lengths 666 isPullDown 22-> ' + videoList[videoList.length-1].endTime+itemList[0].pictureTime);
                    if (moment(recordFileList[value].endTime).valueOf()<moment(eventList[eventList.length-1].eventTime).valueOf()){
                        break;
                    }

                }
                valueSelect=false;
                // console.log("_getAliVideoList endTime isTure:length",eventVideoList.length)
                if (eventList.length>0){
                    for (let valueInt in eventList) {
                        let {eventTime,eventType,eventId} =eventList[valueInt];
                        eventTime=moment(eventTime).valueOf()
                        let isTure=(eventTime<=(moment(endTime).valueOf()+ms))&&((moment(beginTime).valueOf()-ms)<=eventTime)
                        if (isTure){
                            valueSelect=true;
                            let videoValue=recordFileList[value];
                            let itemValue={"eventType":eventType,"eventId":eventId,"eventTime":recordFileList[value].beginTime,"thumbUrl":recordFileList[value].snapshotUrl}
                            let list= Object.assign(itemValue,videoValue)
                            eventVideoList.push(list)
                            break;
                        }
                    }
                }
                if (!valueSelect){
                    let list=recordFileList[value];
                    list["eventType"]=-1;
                    list["eventId"]="-1";
                    list["eventTime"]=recordFileList[value].beginTime;
                    list["thumbUrl"]=recordFileList[value].snapshotUrl;
                    eventVideoList.push(list);
                }
            }
            let eventVideoListR=[...this.state.videoListEvent,...eventVideoList]

            if (eventVideoListR.length-this.state.videoListEvent.length>3){
                this.setState({
                    videoEventPage:this.state.videoEventPage+1,
                    eventVideoList:eventVideoListR,
                    videoListEvent:eventVideoListR
                },()=>{


                })
            }else {
                this.setState({
                    videoEventPage:this.state.videoEventPage+1,
                    videoListEvent:eventVideoListR
                },()=>{
                    this._queryDataList(false,true)
                })
            }


        });
    }




    _setDateString(dataAry,month,isFirst){
        let dateAry = {};
        for (let m = 0; m < dataAry.length; m++){
            let day = new Date(month.substr(0,4), parseInt(month.substr(4,2))-1, m+1);
            let disabled = parseInt(dataAry[m])?false:true;
            let seleteDay = new Date(this.state.currentDate).getDate();
            let seleteMonth = new Date(this.state.currentDate).getMonth()+1;
            if (seleteMonth != parseInt(month.substr(4,2)) || seleteDay != m+1){
                dateAry[moment(day).format('yyyy-MM-DD')]={disabled: disabled, disableTouchEvent: disabled, };
            }
        }

        if (isFirst){
            dateDataArr.set(month,dateAry);
        }else {
            this.setState({dateData:dateAry});
        }
    }

    resetSelectDay(preDay){
        // this.state.dateData[moment(this.state.currentDate).format('yyyy-MM-DD')] = {disabled: false, disableTouchEvent: false };
        // this.state.dateData[day] = {selected: true, marked: false, disableTouchEvent: true, selectedColor: imiThemeManager.theme.primaryColor};
        console.log("currentDate:"+moment(this.state.currentDate).format('yyyy-MM-DD')+"  day:"+preDay);
        //由于缓存最近三个月的数据，this.state.dateData为当前选中的月份，preDay可能是不在当前月份
        //需要把缓存的三个月 找到preDayYMD所对应的月份，更新对应天数据，并且更新dateDataArr
        let preDayYMD = moment(preDay).format('yyyy-MM-DD');
        let preYM = moment(preDay).format('yyyyMM');
        let chooseDayYMD = moment(this.state.currentDate).format('yyyy-MM-DD');
        let chooseYM = moment(this.state.currentDate).format('yyyyMM');
        let items = this.state.dateData;
        console.log("preYM"+preYM,preDayYMD,chooseDayYMD,chooseYM)
        if (preYM == chooseYM){
            //属于同一个月
            items[preDayYMD] = {disabled: false, disableTouchEvent: false };
            items[chooseDayYMD] = {selected: true, marked: false, disableTouchEvent: true, selectedColor: imiThemeManager.theme.primaryColor};
            this.setState({
                dateData: items
            });
        }else {
            //属于不同月份
            if (dateDataArr.has(preYM)){
                console.log("preYM"+preYM)
                let dateArr = dateDataArr.get(preYM)
                dateArr[preDayYMD] = {disabled: false, disableTouchEvent: false };
                dateDataArr.set(preYM,dateArr)
            }

            items[chooseDayYMD] = {selected: true, marked: false, disableTouchEvent: true, selectedColor: imiThemeManager.theme.primaryColor};
            this.setState({
                dateData: items
            });
        }

    }

    componentDidMount() {
        this.props.componentContainerRef&&this.props.componentContainerRef(this.componentContainer);
        //事件类型
        this.getEventTypeList();
        // console.log("事件显示:"+this.state.currentDate)
        // 物理返回键需打开，否则全屏返回事件不对
        this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onPressBack());
        this.setState({dateTime:DateUtils.dateFormat(this.state.currentDate, "yyyy-MM-dd")});
        //this._queryDataList(true);
        this.queryThreeMonthData()
        //this._queryMonthData();
        tempRealVideoList = [];
        tempVideoList = [];
        tempVideoList = [];
        reqEventType = 0;
        //20220323@byh loading时间过长可能会挡住播放器的播放,改为在九空格中loading
        //showLoading(stringsTo('commLoadingText'),true);
        this.refreshList && this.refreshList.refreshPreLoad(true);
        this.state.selectEventId!="0"?this._getAliVideoListFirst():this._getAliVideoListOnlyNew(this.reqTime,  true,0,true);

        let monthStr = new Date(this.state.currentDate).getMonth()+1;
        let yearStr = new Date(this.state.currentDate).getFullYear();
        //进入后台
        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            //如果处于播放状态退到后台暂停
            //关闭录像
            if(this.state.isPlay) {
                backupIsPlay = true;
                this.setState({isPlay: false, showPauseView: true,isClickPause:true}, () => {
                    this.IMIVideoView&&this.IMIVideoView.pause();
                });
            }else{
                backupIsPlay = false;
            }
            console.log(`addListener CloudStorage enterBackgroundListener : backupIsPlay= `+backupIsPlay);
        });
        //回到前台
        this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
            //启动
            console.log(`addListener CloudStorage enterForegroundListener : backupIsPlay= `+backupIsPlay);
            // console.log('回到前台当前声音状态---',this.state.mute);
            if(backupIsPlay) {
                // console.log('获取当前声音状态===',this.muteStatus);
                this.setState({isPlay: true, showPauseView: false,isClickPause:false}, () => {
                    this.IMIVideoView&&this.IMIVideoView.resume();
                });
            }
        });


        this._subscribe_focus = this.props.navigation.addListener('focus', () => {
            // console.log("CloudStoragePage _subscribe_focus"+this.camera)
            Orientation.addOrientationListener(this._orientationDidChange);
            console.log('focus---进入当前云存页面全屏状态',this.state.isFullScreen);
            // StatusBar.setBarStyle("light-content");
            // StatusBar.setBarStyle("light-content");
            // StatusBar.setBackgroundColor('transparent');//状态栏字体刷黑色
            NavigationBar.setBarStyle('light-content');
        });

        // this._subscribe_blur = this.props.navigation.addListener('blur', () => {
        //     console.log('blur--离开当前云存页面全屏状态',this.state.isFullScreen);
        // });


        //510的播放结束回调
        /* LetDevice.addDeviceEventChangeListener((data) => {
             let {iotId, identifier, value} = JSON.parse(data);
             if (iotId == LetDevice.deviceID && identifier === "onPlayBackEnd") {
                 console.log("szm ============== 播放结束了:")
                 this.IMIVideoView&&this.IMIVideoView.pause();
                 this.setState({isPlay: false, showPauseView: true});
             }
         });*/
    }

    componentWillUnmount() {
        Orientation.removeOrientationListener(this._orientationDidChange);
        this._subscribe_focus && this._subscribe_focus();
        this.IMIVideoView && this.IMIVideoView.stop();
        this.IMIVideoView && this.IMIVideoView.destroy();
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.preparetimer && clearTimeout(this.preparetimer);
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.backHandler && this.backHandler.remove();
        this._enterBackground&&this._enterBackground.remove();
        this._enterForeground&&this._enterForeground.remove();
        this.playTimeOut&&clearTimeout(this.playTimeOut);
        this.loadingTime&&clearTimeout(this.loadingTime);
        //下载的监听
        this.listener && this.listener.remove();
        this.listener = null;
        this.intervalTimer && clearInterval(this.intervalTimer);
        this.intervalTimer = null;
        this.pollTimeout && clearTimeout(this.pollTimeout);
        this.pollTimeout = null;
        //防止退出后还有加载框显示
        showLoading(false);
        this.setState = (state,callback)=>{
            return;
        };
    }

    _downloadPress = () =>{
        if (!this.state.fileName){
            //可以做个提示
            return;
        }
        //需要检测权限
        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
            if (status === 0) {
                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                    if (status2 === 0) {
                        this.getDownloadUrl(this.state.fileName)
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    } else if (status2 === -1) {
                        showToast(stringsTo('storage_permission_denied'));
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    }
                });
            } else if (status === -1) {
                showToast(stringsTo('storage_permission_denied'))
            }
        })


    }
    /**
     * 需要轮询接口获取下载地址
     * 此接口需要转码MP4耗时
     * @param filename
     */
    getDownloadUrl(fileName){
        showLoading(stringsTo("alarm_download_downloading"),true);
        console.log("getDownloadUrl");
        let startTime = new Date().getTime();
        this.intervalTimer && clearInterval(this.intervalTimer);
        let isStartDownload = false;
        // this.intervalTimer = setInterval(() => {
        //     if (!isStartDownload){
        //         //未开始下载，才去轮询获取下载地址
        //         aliAlarmEventCloudApi.downloadAliVideoList(LetDevice.deviceID,fileName).then(data=>{
        //             console.log("data",data);
        //             let result = typeof (data) == "object"?data:JSON.parse(data);
        //             if (result.progress == 100 &&  !isStartDownload){
        //                 isStartDownload = true;
        //                 //说明已经转码完成了
        //                 let endTime = new Date().getTime();
        //                 let time = endTime - startTime;
        //                 console.log("转码使用时间："+time);
        //                 this.intervalTimer && clearInterval(this.intervalTimer);
        //
        //                 //转码完成后，我们去下载
        //                 this.downloadVideo(result.url);
        //             }
        //
        //         }).catch(error=>{
        //             showToast(stringsTo('video_download_fail'));
        //             this.intervalTimer && clearInterval(this.intervalTimer);
        //             showLoading(false);
        //         })
        //     }
        //
        // },1000);

        this.pollToGetUrl(fileName);


    }

    pollToGetUrl(fileName){
        aliAlarmEventCloudApi.downloadAliVideoList(LetDevice.deviceID,fileName).then(data=>{
            console.log("data",data);
            let result = typeof (data) == "object"?data:JSON.parse(data);
            if (result.progress == 100){
                //说明已经转码完成了
                console.log("转码完成");
                this.intervalTimer && clearInterval(this.intervalTimer);

                //转码完成后，我们去下载
                this.downloadVideo(result.url);
            }else {
                this.pollTimeout && clearTimeout(this.pollTimeout);
                this.pollTimeout = setTimeout(()=>{
                    this.pollToGetUrl(fileName);
                },500);

            }

        }).catch(error=>{
            showToast(stringsTo('video_download_fail'));
            this.intervalTimer && clearInterval(this.intervalTimer);
            showLoading(false);
        })
    }

    downloadVideo(url){
        let nameDate = new Date().getTime();
        //文件命名不能重复，相同文件名，会存在多个文件，
        //会出现转存相册一次转存多个文件进入
        // if (this.downLoadEventTime){
        //     nameDate = this.downLoadEventTime;
        // }
        let fileName = `${moment(nameDate).format('YYYYMMDDHHmmss')}.mp4`;
        const path = `${CLOUD_DOWNLOAD}/${fileName}`;

        IMIDownload.downloadToPath(EVENT_NAME, url, CLOUD_DOWNLOAD, fileName);

        this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
            //console.log('DeviceEventEmitter 下载 '+JSON.stringify(event));
            if (event.status === IMIDownload.STATUS_START) {
            }
            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                //这里可以显示下载进度，UI展示进度等，都OK
                //Android端与iOS端返回的数据，暂时不统一。
                //要求ios不显示进度，Android显示
                let progress = Math.round(event.progress*100);
                if (isAndroid()){
                    progress = Math.round(event.progress*100/event.max);
                }
                if (progress >= this.preProgress){
                    this.preProgress = progress;
                    showLoading(progress+"%",true,false);
                }

            }

            if (event.status === IMIDownload.STATUS_ERROR) {
                // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
                this.preProgress = 0;
                showLoading(false);
                showToast(stringsTo('video_download_fail'));
            }
            if (event.status === IMIDownload.STATUS_CANCEL) {
                // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
                this.preProgress = 0;
                showLoading(false);
                showToast(stringsTo('video_download_fail'));
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {
                console.log("--------save to album");
                this.preProgress = 0;
                //const path = `${event.downloadPath}/${fileName}`;
                IMILog.logD("传参 jeff path :",path);
                IMILog.logD("传参 jeff deviceID :",LetDevice.deviceID);
                IMIFile.saveVideoToPhotosAlbum(path,LetDevice.deviceID).then((data)=>{
                    showLoading(false);
                    showToast(stringsTo('save_success'));
                    console.log('保存相册'+data);
                }).catch(error=>{
                    showLoading(false);
                    IMILog.logD("返回值 error :",JSON.stringify(error));
                    console.log('保存相册error--'+path+JSON.stringify(error));
                    showToast(stringsTo('save_failed'));
                });
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
        });

    }


    _orientationDidChange = (orientation) => {
        console.log('云存页面方向---',orientation,this.state.isFullScreen);
        if (orientation === 'LANDSCAPE') {

        } else {
            // do something with portrait layout
            console.log('云存页退出全屏--全屏状态-gengxin',this.state.isFullScreen);
            if (this.state.isFullScreen && isIos()){
                this._exitFullScreen();
                // StatusBar.setBarStyle("light-content");
                // StatusBar.setBackgroundColor('transparent');//状态栏字体刷黑色
                // NavigationBar.setBarStyle('dark-content');
            }

        }
    }


    _onPressFullScreen = () => {
        // if(!this._canStepIn())  return;
        isAndroid()?Orientation.lockToLandscape():Orientation.lockToLandscapeRight();
        this.setState({isFullScreen: true});
        NavigationBar.setStatusBarHidden(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
    };

    _exitFullScreen = () => {
        // if(!this._canStepIn())  return;
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
    }

    _onPressBack = () => {
        if (this.state.isFullScreen) {
            this._exitFullScreen();
        } else {
            if (this.state.recording) {
                showToast(stringsTo('screen_recording'));
                return ;
            }

            this.props.navigation.goBack();
        }
        return true;
    };


    _onPressFullScreenTools = () => {
        this.setState({showFullScreenTools: true});
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.fullScreenTooltsTimer = setTimeout(() => {
            this._onCloseFullScreenTools();
        }, 5000);
    }

    _onCloseFullScreenTools() {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.setState({showFullScreenTools: false});
    }

    _onPressMute = () => {
        if (!this.props.netConnected){
            showToast(stringsTo("network_not_connected"))
            return
        }
        if(!this._canStepIn())  return;
        this.setState({mute: !this.state.mute})
    };


    //点击截屏按钮
    _onPressScreenShot = () => {
        if (!this.props.netConnected){
            showToast(stringsTo("network_not_connected"))
            return
        }
        if(!this._canStepIn())  return;

        if(new Date().getTime()-lastClickSnapPhoto<3000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
            return;
        }
        lastClickSnapPhoto = new Date().getTime();


        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {

            if (status === 0) {
                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                    if (status2 === 0) {
                        let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                        this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                            IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, LetDevice.deviceID).then(_ => {
                                this.setState({screenShotPath:currentSnapshotPath,snapshotVisible: true});
                                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                                    this.setState({ snapshotVisible: false});
                                }, 3000);
                            });
                        });
                        //IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM)
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    } else if (status2 === -1) {
                        showToast(stringsTo('storage_permission_denied'));
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    }
                });
            } else if (status === -1) {
                showToast(stringsTo('storage_permission_denied'))
            }
        })


    };

    //点击录屏按钮
    _onPressRecord = () => {
        if (!this.props.netConnected){
            showToast(stringsTo("network_not_connected"))
            return
        }
        if(!this._canStepIn())  return;
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        } else {
            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
                if (status === 0) {
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                        if (status2 === 0) {
                            time=moment(new Date().getTime()).format('yyyyMMDD')+"_"+new Date().getTime();
                            let pathUrl=VEDIO_RECORD_PATH;
                            if (isIos()){
                                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
                            }
                            this.IMIVideoView.startRecord(pathUrl).then(_ => {
                                this.setState({recording: true, recordDuration: 0});
                            });
                            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                        } else if (status2 === -1) {
                            showToast(stringsTo('storage_permission_denied'));
                            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                        }
                    })
                } else if (status === -1) {
                    showToast(stringsTo('storage_permission_denied'))
                }
            })

        }
    };

    //停止录像并保存在相册
    _stopRecord(isPause=false,isPlayCompletion=false){
        let forSave=true
        console.log("停止录制anle",this.state.recordDuration);
        if (isPlayCompletion&&this.state.recordDuration==0){ //防止在停止时
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            forSave = false;
        }else {
            if(this.state.recordDuration<6){
                IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                forSave = false;
            }
        }
        this.IMIVideoView.stopRecord().then(_ => { //停止录制
            console.log("停止录制-------",forSave);
            if(!forSave){ //只停止，不保存
                console.log("停止录制-------保存失败");//save_system_album_failed
                // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                return;
            }
            let pathUrl=VEDIO_RECORD_PATH;
            if (isIos()){
                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
            }
            IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID).then(_ => { //转存视频
                if (isPause){
                    return
                }
                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                    this.setState({screenShotPath: currentSnapshotPath, snapshotVisible: true});
                    this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                        this.setState({snapshotVisible: false});
                    }, 3000);
                });
                //IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
            }).catch((error)=>{
                console.log("停止图片-------保存失败"+error);
            });
        }).catch((error)=>{
            console.log("停止录制-------保存失败"+error);
        });
        this.setState({recording: false, recordDuration: 0});
    }
    //IOS在视频流暂停时,将录制的视频保存到相册
    _saveVideoToPhotosAlbum(isPause=false) {
        if (this.state.recordDuration < 6) {
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            this.setState({recording: false, recordDuration: 0});
            return;
        }
        let pathUrl=VEDIO_RECORD_PATH;
        if (isIos()){
            pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
        }
        IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID).then(_ => { //转存视频
            if (isPause){
                return;
            }
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            // let tempShotPath = `${IMIFile.storageBasePath}/tmp/snapshot.jpg`;
            // this.IMIVideoView.screenShot(tempShotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
            //     this.setState({screenShotPath: tempShotPath, snapshotVisible: true});
            //     this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
            //         this.setState({snapshotVisible: false});
            //     }, 3000);
            // })
            //  console.log('显示临时路径---',this.tempSnapShotPath);
            this.setState({screenShotPath: this.tempSnapShotPath, snapshotVisible: true});
            this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                this.setState({snapshotVisible: false});
            }, 3000);
            //IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        }).catch(err=>{
            console.log("save video error:",err);
        });
        this.setState({recording: false, recordDuration: 0});
    }

    _onPressSpeed = () => {
        if(!this._canStepIn())  return;
        if (!this.props.netConnected){
            showToast(stringsTo("network_not_connected"))
            return
        }
        if (this.state.recording)
        {
            showToast(stringsTo('screen_recording'));
            return ;
        }
        // currentPlayTime = 0;
        //2022-03-22@byh 暂时去掉4倍速
        switch (this.state.speed) {
            case 0:
                this.setState({speed: 1,mute: true});
                this.IMIVideoView && this.IMIVideoView.speed(2);
                break;
            case 1:
                this.setState({speed: 0});
                this.IMIVideoView && this.IMIVideoView.speed(1);
                break;
            // case 2:
            //     this.setState({speed: 0});
            //     this.IMIVideoView && this.IMIVideoView.speed(1);
            //     break;
            /*   case 3:
                   this.setState({speed: 4,mute: true});
                   this.IMIVideoView && this.IMIVideoView.speed(16);
                   break;
               case 4:
                   this.setState({speed: 0});
                   this.IMIVideoView && this.IMIVideoView.speed(1);
                   break;*/
        }
        // this.IMIVideoView && this.IMIVideoView.prepare();
    }

    _onPressPlay = () => {

        if (!this.props.netConnected){
            showToast(stringsTo("network_not_connected"))
            return
        }

        if (this.state.recording)
        {
            showToast(stringsTo('screen_recording'));
            return ;
        }

        if (this.state.showErrorView && !this.state.isPlay){
            this.IMIVideoView&&this.IMIVideoView.prepare();
            return;
        }
        // this.setState({isClickPause:true});
        console.log("this.tempScrollPause::::",this.tempScrollPause+" time:"+Math.round(this.tempScrollTime*1000))
        if (this.tempScrollPause){
            console.log("[[[[[[[[[[[1")
            if (this.state.isClickPause){
                // 暂停状态下滑动时间轴
                this.setState({isPlay:true,showPauseView:false,isClickPause:false},()=> {
                    this.IMIVideoView&&this.IMIVideoView.resume();
                    this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(1);
                });
            }else {
                this.IMIVideoView&&this.IMIVideoView.prepare();
                //this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime*1000));
            }


        }else {
            console.log("[[[[[[[[[[[2")
            if (!this.state.isPlay){

                // this.setState({isPlay:true,showPauseView:false,isClickPause:false},()=>{
                //     // if (isResume) {
                //     //  this.IMIVideoView&&this.IMIVideoView.resume();
                //
                //     if (this.state.isPlayFinish){
                //         //结束调用了stop方法 所以需要调用prepare方法
                //         this.setState({isPlayFinish:false});
                //         currentPlayTime = 0;
                //         this.IMIVideoView&&this.IMIVideoView.prepare();
                //         if (isIos()){
                //             this.setState({isLoading:true,showPauseView:false});
                //         }
                //     }else {
                //         this.IMIVideoView&&this.IMIVideoView.resume();
                //     }
                //     this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(1);
                // });

                if (this.state.isPlayFinish){
                    currentPlayTime = 0;
                    this.IMIVideoView&&this.IMIVideoView.prepare();
                    if (isIos()){
                        this.setState({isLoading:true,showPauseView:false});
                    }
                }else {
                    this.state.isClickPause?this.IMIVideoView.resume():this.IMIVideoView.prepare();
                }

                this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(1);
                if (this.state.isClickPause){
                    this.setState({isPlay:true,showPauseView:false,isClickPause:false});
                }else {
                    this.setState({speed: 0,isClickPause:false});
                }

            } else {
                if (this.state.recording) {
                    console.log("暂停前自动关闭录屏 录屏结束-------------");
                    this._stopRecord();
                }
                this.setState({isPlay:false,showPauseView:true,isLoading: false,isClickPause:true,},()=>{
                    this.IMIVideoView&&this.IMIVideoView.pause();
                    this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(0);
                });
            }
        }


    };

    _onEventChange = (event) => {

        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            //this.setState({bps: event.extra.bps})

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
            //console.log(event.extra.currentTime);
            // let scrollTime = currentPlayTime-1 >= event.extra.currentTime/1000.0 ? currentPlayTime : event.extra.currentTime/1000;
            if (this.state.noVideo){
                this.progressView && this.progressView.onSliderValueChanged(0);
                this.state.homePictureTime=0
            }else {
                if(this.tempScrollPause){  //修改暂停逻辑 pause  需要resume后再seekto 防止回复在加载
                    // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
                    console.log(Math.round(this.tempScrollTime*1000))
                    this.tempScrollPause = false;
                    this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime*1000));
                }else {
                    //202200302@byh ios最后返回的时间会比，视频时长少一秒，增加0.5修正，增加返回的视频时长与实际视频时长判断
                    //Android不能添加,添加播放结束后可能在00：01秒位置
                    let scrollTime = event.extra.currentTime/1000;
                    //20220318@byh 在播放完成后，监听播放完成，把进度拉满，所以这里不在需要修正
                    // if (isIos()){
                    //     scrollTime = scrollTime+0.5;
                    // }
                    let secondSTime = Math.round(scrollTime);
                    let second = secondSTime > this.state.durationProgress?this.state.durationProgress:secondSTime;
                    // console.log("jeff","secondSTime = ",secondSTime,",currentTime = ",event.extra.currentTime,"second =",second);
                    if (second > 0){
                        this.progressView&& this.progressView.onSliderValueChanged(second);
                    }
                }

            }
        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            // console.log("云存流 ----_onEventChange,开始启用");
         /*   if (this.errPrepare){
                // console.log('播放报错走这里');
                this.errPrepare = false;
                this.setState({isLoading: true, isPlay: false, showPauseView: false, showErrorView: false});
                this.IMIVideoView&&this.IMIVideoView.seekTo(this.errPlayTime*1000);
            }else {  }*/
                this.setState({isLoading: true, isPlay: false, showPauseView: false, isPlayFinish:false,showErrorView: false});
                this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);



        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            // console.log(" 直播流----_onEventChange,出现关键帧");
            this.setState({isLoading: false,showPauseView: false, isPlay: true,isPlayFinish:false,showErrorView: false,durationProgress:event.extra.duration&&event.extra.duration});
            if (this.selectBeginTime>0){
                //如果这个偏移时间比实际视频时长大，Android端是无法发生偏移的，需要这里做个修正
                if (this.state.durationProgress >0 && this.selectBeginTime>(this.state.durationProgress * 1000)){
                    //如果大于进度时间，可能没找到视频，找了最近的播放，那么我们从头开始播放不进行偏移
                    // this.selectBeginTime = this.state.durationProgress * 1000;
                    this.selectBeginTime=0;
                    return;
                }
                this.IMIVideoView && this.IMIVideoView.seekTo(this.selectBeginTime);
                this.selectBeginTime = 0;
            }
            this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PLAYING);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
            // console.log(" 直播流----_onEventChange,出现关键帧");
            // IMILog.logD("王 错误 PayBack _onEventChange PLAYER_EVENT_ON_STOP>",event.toString())
            this.setState({isLoading: false, isPlay: false, showPauseView: true,speed: 0});
            if(this.state.recording){ //直播流暂停时，停止录像
                if(CONST.isAndroid){
                    this._stopRecord();
                }else{ //因为IOS会自动停止视频录制，所以直接转存即可
                    this._saveVideoToPhotosAlbum();
                }
            }
            // console.log(" 直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PAUSE);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            // console.log(" 直播流----_onEventChange,开始播放");
            //this.setState({isLoading:false});

        }else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE) {
            if(this.state.recording){ //直播流暂停时，停止录像 安卓
                    this._stopRecord(true);
            }
        }else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE_IOS) {
            if(this.state.recording){ //直播流暂停时，停止录像 IOS
                this._stopRecord(true);
                // this._saveVideoToPhotosAlbum(true);
            }
        }

    }



    _onRecordTimeChange = (event) => {
        console.log("recordDuration",event.extra);
        if (event.extra===0){
            //如果为0就不进行赋值操作
            return;
        }
        this.setState({recordDuration: event.extra},callback=>{
            if (this.state.recordDuration == 1){
                // 临时截图
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                    this.tempSnapShotPath = currentSnapshotPath;
                    // console.log('临时路径---',this.tempSnapShotPath);
                });
            }
        })
    }

    //判断当前是否可以操作
    _canStepIn(){
        if(!this.state.isPlay){
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }


    /**
     * 竖屏状态视屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenVideoViewArea() {
        return (

            this.props.navBar ? this.props.navBar(this.state.bps, this.state.isFullScreen) : (
                <NavigationBar
                    type={NavigationBar.TYPE.DARK}
                    backgroundColor={"transparent"}
                    title={stringsTo('bottom_cloud_storage')}
                    // subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
                    left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                    right={[
                        { //回看列表页面
                            key: NavigationBar.ICON.CUSTOM,
                            n_source:require('../../resources/images/icon_go_renewal.png'),
                            onPress: _ => {
                                if (this.state.recording)
                                {
                                    showToast(stringsTo('screen_recording'));
                                    return ;
                                }

                                this._goTpVipPage();
                            }
                        }
                    ]}
                    /*  rightText={{title:I18n.t('fees_for_renewal'),onPress:_=> this._goTpVipPage(),disable:false,}}*/
                    showColor={true}
                />)

        );
    }

    _goTpVipPage(){
        IMIGotoPage.starCloudBuyPage(LetDevice.deviceID);
    }


    /**
     * 全屏状态videoView区域填充UI
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenVideoViewArea() {
        let title = speedTitleAry[this.state.speed];
        let speedAccessibilityLabel= speedAccessibilityLabelTitleAry[this.state.speed];
        let item = {
            isText: false,
            data: [require("../../resources/images/icon_download_white.png")],
            onPress: this._downloadPress,
            disabled: !this.state.fileName,
            dataIndex: 0,
            accessibilityLabel:["play_back_full_screen"]
        }
        let {cloudDownload} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let moreItems = [];
        if (cloudDownload){
            moreItems = [{item:item,insertIndex:5}]
        }

        return (
            this.state.showFullScreenTools ? (
                <View pointerEvents="box-none" style={{width: "100%",height:'100%'}}>
                    <PlayBackFullScreenToolBarView
                        exitPress={this._exitFullScreen}
                        playPress={this._onPressPlay}
                        isPlay={this.state.isPlay}
                        mutePress={this._onPressMute}
                        mute={this.state.mute}
                        muteDisabled={!this.state.isPlay || this.state.speed != 0}
                        speedTitle={title}
                        speedPress={this._onPressSpeed}
                        speedDisabled={!this.state.isPlay}
                        speedAccessibilityLabel={speedAccessibilityLabel}
                        screenshotPress={this._onPressScreenShot}
                        screenshotDisabled={!this.state.isPlay}
                        recordPress={this._onPressRecord}
                        recording={this.state.recording}
                        recordDisabled={!this.state.isPlay || this.state.speed != 0}
                        fullScreenPress={this._exitFullScreen}
                        moreItems={moreItems}
                        isShowSpeedItem={true}
                    />

                    <View style={{
                        position: "absolute",left: 0,bottom:10,right:0
                    }}>

                    </View>
                </View>
            ) : null
        );
    }



    /**
     * 事件展示列表
     * @returns {*}
     * @private
     */
    _renderListView() {
        let dataList = this.state.eventVideoList;
        //console.log(" _renderListView dataList  -> " + JSON.stringify(dataList));
        return (
            <XFlatList
                style={{
                    backgroundColor: colors.white
                }}
                data={dataList}
                onRefresh={() => this._queryVideoList(true)}
                onLoadMore={() => {
                    console.log(" this._queryDataList(false)");
                    this._queryVideoList(false)
                }
                }
                refreshStatus={{
                    PreLoading: {text: I18n.t('housekeeping_no_event')},//不传值默认是first loading，在当天视频为空且续费成功回来后必现
                    RefreshingData: {text: I18n.t('commLoadingText')},
                    NoData: {text: I18n.t('housekeeping_no_event')},
                    LoadFailure: {text: I18n.t('commLoadingFailText')},
                    //列表底部提示文案
                    LoadingMoreData: {moreText: I18n.t('commLoadingMoreDataText')},
                    NoMoreData: {moreText: I18n.t('commNoMoreDataText')},
                    LoadMoreFailure: {moreText: I18n.t('commLoadingClickText')},
                    NetException: {moreText: I18n.t('commLoadingFailText')},

                }}
                onEndReachedThreshold={0.1}
                numColumns={3}
                renderEmptyViewFunc={(status, isEmpty) =>this._renderEmptyNewView(status, isEmpty)}
                scrollEnabled={dataList.length == 0 ? false:true}
                onLayout={e => {
                    let height = e.nativeEvent.layout.height;
                    if (this.state.flatListHeight < height) {
                        this.setState({flatListHeight: height})
                    }
                }}
                ref={refreshList => this.refreshList = refreshList}

                renderItem={({item, index}) => this._renderItem(item, index)}/>
        );
    }
    _renderEmptyNewView(status, isEmpty) {
        return (
            <IMIDesignEmptyView rootStyle={{height: this.state.flatListHeight,borderTopWidth:0}}
                                defaultText={status.text}/>
        )
    }
    /**
     * 绘制item view    **  可以进行子类重写 **
     * @param item
     * @param index
     * @returns {*}
     * @private
     */
    _renderItem = (item, index) => {

        console.log('_renderItem' + JSON.stringify(item));

        let { eventType, thumbUrl, eventTime,fileName} = item;

        let time = moment(eventTime).format('YYYY-MM-DD HH:mm:ss');
        let formatStarTime = time.substr(10);
        let topLineWidth = index === 0 ? 0 : 1;
        let iconDot = require('../../resources/images/pic_move.png');
        let iconDot1 = require('../../resources/images/pic_move.png');
        for(let value in eventType) {
            let iconDots = require('../../resources/images/pic_move.png');
            switch (eventType[value]) {
                case AlarmType.MOVE:
                    iconDots = require('../../resources/images/pic_move.png');
                    break;
                case AlarmType.PEOPLE:
                    iconDots = require('../../resources/images/pic_person.png');
                    break;
                case AlarmType.CRY:
                    iconDots = require('../../resources/images/pic_crying.png');
                    break;
                case AlarmType.ABNORMAL_SOUND:
                    iconDots = require('../../resources/images/pic_sound.png');
                    break;
                case AlarmType.CROSSING:
                    iconDots = require('../../resources/images/pic_fence.png');
                    break;
                case AlarmType.LAUGHTER:
                    iconDots = require('../../resources/images/pic_nobody.png');
                    break;
                default:
                    iconDots ="";
                    break;
            }
            if (value==0){
                iconDot=iconDots;
            }else if (value==1){
                iconDot1=iconDots;
            }
        }

        return <XView key={index} style={styles.itemParent}
                      onPress={()=>{
                          if (!this.props.netConnected){
                              showToast(stringsTo("network_not_connected"))
                              return
                          }
                          if (this.state.recording)
                          {
                              showToast(stringsTo('screen_recording'));
                              return ;
                          }
                          this.IMIVideoView &&this.IMIVideoView.stop();
                          this.progressView&& this.progressView.onSliderValueChanged(0);
                          this.tempScrollPause=false
                          this.setState({isPlay:true,durationProgress:0,fileName:"",isClickPause:false},()=> {
                              this.getByEventName(fileName);
                          });
                           // this.getByEventId(item);
                          //this.getDownloadUrl(fileName,eventTime);
                      }}

        >
            <IMIImageView style={styles.itemIcon} source={{uri: thumbUrl}}/>

            { this.state.fileName==fileName?<XImage style={styles.itemIconCenter} icon={require('../../resources/images/storage_icon_play.png')}/>:null}
            <XView style={{flex: 1, alignItems: 'center',flexDirection: 'row',}}>
                <XText style={styles.itemTitle} text={formatStarTime}/>
                <XView style={{flex:1,alignItems: 'center',flexDirection: 'row',marginLeft:15}}>
                <XImage style={{marginLeft:0,width: 14, height: 14}} icon={iconDot}/>
                {eventType.length>=2? <XImage style={{marginLeft:0,width: 14, height: 14}} icon={iconDot1}/>:null}
                {eventType.length>=3?<XText style={{marginLeft:0,width: 14, height: 14 , fontSize:10,}} text={".."}/>:null}
                </XView>
            </XView>
        </XView>;
    };
    /**
     * 竖屏状态下半屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenPlayerToolBarArea() {
        let eventName=stringsTo('all_events_str');
        if (this.state.selectEventAlarmType==AlarmType.ALL){
            eventName= stringsTo('all_events_str')
        }else if (this.state.selectEventAlarmType==AlarmType.PEOPLE){
            eventName= stringsTo('people_event')
        }else if (this.state.selectEventAlarmType==AlarmType.MOVE){
            eventName= stringsTo('move_event')
        }else if (this.state.selectEventAlarmType==AlarmType.CROSSING){
            eventName= stringsTo('fence_detect_switch')
        }else if (this.state.selectEventAlarmType==AlarmType.ABNORMAL_SOUND){
            eventName= stringsTo('alarm_loud_switch')
        }else if (this.state.selectEventAlarmType==AlarmType.LAUGHTER){
            eventName= stringsTo('no_human_event')
        }else if (this.state.selectEventAlarmType==AlarmType.CRY){
            eventName= stringsTo('cry_event')
        }
        let item = {
            isText: false,
            data: [require("../../resources/images/icon_alarm_down.png")],
            onPress: this._downloadPress,
            disabled: !this.state.fileName,
            dataIndex: 0,
            accessibilityLabel:["play_back_full_screen"]
        }
        let {cloudDownload} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let moreItems = [];
        if (cloudDownload){
            moreItems = [{item:item,insertIndex:5}]
        }
        return (
            <View style={{flex: 1, flexDirection: "column"}}>
                <PlayBackToolBarView
                    playDisabled={this.state.noVideo || this.state.todayIsNoData}
                    speedTitle={["1X", "2X"]}
                    speedPress={this._onPressSpeed}
                    speedDisabled={!this.state.isPlay || this.state.recording}
                    speedIndex={this.state.speed}
                    fullscreenPress={this._onPressFullScreen}
                    mutePress={this._onPressMute}
                    mute={this.state.mute}
                    muteDisabled={!this.state.isPlay ||this.state.speed != 0}
                    screenshotPress={this._onPressScreenShot}
                    screenshotDisabled={!this.state.isPlay}
                    recordPress={this._onPressRecord}
                    recordDisabled={!this.state.isPlay || this.state.speed != 0}
                    fullscreenDisabled={this.state.noVideo || this.state.todayIsNoData}
                    recording={this.state.recording}
                    moreItems={moreItems}
                    playPress = {this._onPressPlay}
                    play={this.state.isPlay}
                    isShowSpeedItem={true}
                />
                <SelectEventTimeView
                    style={{backgroundColor: "#ffffff"}}
                    currentDate={this.state.currentDate}
                    eventDate={eventName}
                    calendarClick={()=>{
                        if (!this.props.netConnected){
                            showToast(stringsTo("network_not_connected"))
                            return
                        }
                        if (this.state.recording)
                        {
                            showToast(stringsTo('screen_recording'));
                            return false;
                        }
                        let month = moment(this.state.currentDate).format('yyyyMM');
                        this.checkMonth(month);
                        // this._queryMonthData(moment(this.state.currentDate).format('yyyyMM'));
                        this.setState({isShowCalendar:!this.state.isShowCalendar})
                    }}
                    eventClick={()=>{
                        if (!this.props.netConnected){
                            showToast(stringsTo("network_not_connected"))
                            return
                        }
                        if (this.state.recording)
                        {
                            showToast(stringsTo('screen_recording'));
                            return false;
                        }
                        this.setState({showEventModal:true,eventAlarmType:this.state.selectEventAlarmType});
                    }}
                />
                { this._renderListView()}
                {this.props.children}
            </View>
        );
    }
    //显示切换事件弹框
    _recordTimeModal() {
        let {showPeopleEvent,showMoveEvent,showAlarmLoudSwitch,showFencesSwitch,showNoHuman} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);

        return(<View>
            <MessageDialog
                title={stringsTo('change_event_str')}
                visible={this.state.showEventModal}
                canDismiss={true}
                onDismiss={()=>{this.setState({showEventModal: false})}}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({selectAlarmTime:this.state.AlarmFrequencyLevel,showEventModal: false});
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            // this.IMIVideoView&&this.IMIVideoView.pause();
                            //showLoading(stringsTo('commLoadingText'),true);
                            this.setState({showEventModal:false,selectEventAlarmType:this.state.eventAlarmType,
                                isPlay:true, showPauseView: false,isClickPause:false,eventVideoList:[],pageStart:0},() => {
                                // this.progressView&& this.progressView.onSliderValueChanged(0);
                                // this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(0);
                                this.refreshList.flatList.scrollToOffset({animated:true,offset:0});
                                this.reqDataPre();
                                // if (this.state.selectEventAlarmType==AlarmType.ALL){
                                //     this._getAliVideoListOnly(this.reqTime ,true,0)
                                // }else {
                                //     this._queryDataList(true);
                                // }

                            });
                        }
                    },
                ]}
            >
                <ScrollView showsVerticalScrollIndicator={false}>
                    {
                        eventTypeArr.map((item, index) => {
                            return (
                                <ChoiceItem
                                    title={item.eventTit}
                                    containerStyle={{margin: 14, marginTop: 0, height: 60, borderRadius: 0}}
                                    backgroundColor={'#fff'}
                                    onlyShowCheckedIcon={true}
                                    onlyChecked={true}
                                    checked={this.state.eventAlarmType == item.eventValue}
                                    onValueChange={(value) => {
                                        if (value) {
                                            //无人暂时用笑声代替
                                            this.setState({
                                                eventAlarmType:item.eventValue,
                                            })
                                        }
                                    }}
                                    key={index}
                                    accessibilityLabel={item.label}/>
                            )
                        })
                    }
                </ScrollView>
            </MessageDialog>
        </View>)
    }

    _renderRecordingView() {
        if (!this.state.recording) {
            return null;
        }
        let duration = this.state.recordDuration > 0 ? `${timeFilter(this.state.recordDuration)}` : "00:00";
        return (
            <View style={this.state.isFullScreen? //修改全屏状态下的录屏时间显示不准确问题
                {
                    position: "absolute",
                    width: 64,
                    height: 26,
                    backgroundColor: 'rgba(0,0,0,0.6)',
                    borderRadius: 4,
                    flexDirection: 'row',
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 10 + this.state.showFullScreenTools ? 50 : 0,
                    zIndex: 999,
                    // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
                }
                :
                {
                    width: 64,
                    height: 26,
                    backgroundColor: 'rgba(0,0,0,0.6)',
                    borderRadius: 4,
                    flexDirection: 'row',
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 10 + this.state.showFullScreenTools ? 50 : 0,
                    // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
                }
            }>
                <View style={{backgroundColor: "#E74D4D", opacity: 0.9, width: 6, height: 6, borderRadius: 3}}/>
                <Text style={{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}}>{duration}</Text>
            </View>
        );
    }

    _loadingView() {
        if (this.state.showPauseView) return;
        if (!this.state.isLoading) return;
        if (this.state.noVideo)return;
        if (this.state.todayIsNoData) return;
        return (<View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center"
                       }}
        >
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.state.showErrorView) return;
        if (this.state.todayIsNoData)return null;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       width: "100%",
                       height: "100%",
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center"
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
                />

                <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                   buttonStyle={{
                                       margin: 14,
                                       paddingHorizontal:15,
                                       height: 40
                                   }}
                                   buttonTextStyle={{textAlign:'center'}}
                                   onPress={() => {
                                       // ;
                                       console.log("视频名称:"+this.state.fileName)
                                       // this.timeLine && this.timeLine.scrollToTimestamp(scrollTime);
                                       // console.log('播放失败---',playEndTime);
                                       if (!this.props.netConnected){
                                           showToast(stringsTo("network_not_connected"))
                                           return
                                       }
                                       this.tempScrollPause=false
                                       this.errPrepare = true;
                                       this.IMIVideoView&&this.IMIVideoView.prepare();
                                       this.setState({speed: 0});
                                       //  this.errPlayTime = playEndTime;
                                     /*  this.IMIVideoView &&this.IMIVideoView.stop()
                                       this.playTimeOut&&clearTimeout(this.playTimeOut);
                                       this.playTimeOut = setTimeout(()=>{
                                           this.IMIVideoView&&this.IMIVideoView.prepare();
                                           this.setState({speed: 0});
                                       },1500);*/
                                      // this.IMIVideoView&&this.IMIVideoView.prepare();
                                      // this.setState({speed: 0});

                                       // this.errTimer && clearTimeout(this.errTimer);
                                       // this.errTimer=setTimeout(() =>{
                                       //     this.IMIVideoView&&this.IMIVideoView.seekTo(playEndTime*1000);
                                       // }, 2000);

                                   }}/>
            </View>
        );
    }
    _showAlarmView() {
        if (!this.props.showAlarmView&&this.state.todayIsNoData) return ;
        if (this.state.todayIsNoData&&this.props.showAlarmView){
            return (
                <View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center"
                       }}
                >
                    <XText style={{
                        textAlign: 'center',
                        color: colors.white,
                        fontSize: 15,
                    }}
                           text={I18n.t('alarm_turn_on_str', {code: this.state.errorCode})}
                    />

                    <RoundedButtonView buttonText={stringsTo('open_setting')}
                                       buttonStyle={{
                                           margin: 14,
                                           paddingHorizontal:15,
                                           height: 40
                                       }}
                                       buttonTextStyle={{textAlign:'center'}}
                                       onPress={() => {
                                           this.props.navigation.push("HouseKeepSetting");
                                       }}/>
                </View>
            );
        }

    }
    _noVideoView(){
        if (!this.state.noVideo)return;
        if (this.state.todayIsNoData)return null;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       width: "100%",
                       height: "100%",
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center"
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('no_video_data')}
                />

            </View>
        );
    }
    _noFirstData(){
        if (this.state.todayIsNoData&&this.props.showAlarmView) return
        if (!this.props.showAlarmView&&this.state.todayIsNoData){
            return (
                <View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center",
                       }}
                >
                    <XText style={{
                        textAlign: 'center',
                        color: colors.white,
                        fontSize: 15,
                    }}
                           text={I18n.t('playback_no_video_data_tip')}
                    />

                </View>
            );
        }


    }

    _pauseView() {
        if (!this.state.showPauseView) return null;
        if (this.state.showErrorView) return;
        if (this.state.noVideo) return;
        if (this.state.todayIsNoData)return null;
        return (<View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center"
                       }}
        >
            <ImageButton
                style={{width: 52, height: 52}}
                source={require("../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png")}
                highlightedSource={require("../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png")}
                onPress={() => {
                    // 点击过暂停 需要
                    console.log('isPlayzhuangtai---',this.state.isPlayFinish,this.state.isClickPause);
                    console.log("this.tempScrollPause::::",this.tempScrollPause+Math.round(this.tempScrollTime*1000))
                    if(this.tempScrollPause){
                        // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
                        // this.tempScrollPause = false;
                        // this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime*1000));
                        if (this.state.isClickPause){
                            this.IMIVideoView&&this.IMIVideoView.resume()
                        }else {
                            //20220324@byh 播放完成后，拖动进度条，然后再点击播放，播放后定位到拖动位置
                            //这个标记在这里先不置为false
                            //this.tempScrollPause=false;
                            this.IMIVideoView&&this.IMIVideoView.prepare();
                        }
                    }else {
                        if (this.state.isPlayFinish){
                            currentPlayTime = 0;
                            this.IMIVideoView&&this.IMIVideoView.prepare();
                            if (isIos()){
                                this.setState({isLoading:true,showPauseView:false});
                            }
                        }else {
                            this.state.isClickPause?this.IMIVideoView.resume():this.IMIVideoView.prepare();
                        }
                    }

                    // this.state.isClickPause?this.IMIVideoView.resume():this.IMIVideoView.prepare();
                    this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(1);
                    if (this.state.isClickPause){
                        this.setState({isPlay:true,showPauseView:false,isClickPause:false});
                    }else {
                        this.setState({speed: 0,isClickPause:false});
                    }
                }}
            />
        </View>);
    }

    _renderSnapshotView() {
        if (!this.state.snapshotVisible) {
            return null;
        }
        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                bottom: this.state.isFullScreen ? 106 : 19,
                left: isPhoneX()?(this.state.isFullScreen?44+14:14):14,
                width: 140,
                height: 80,
                zIndex: 999,
            }}>
                <ImageButton
                    style={{
                        width: "100%",
                        height: "100%",
                        borderWidth: 2,
                        borderColor: 'white',
                        borderRadius: 10
                    }}
                    source={{uri: 'file://' + this.state.screenShotPath}}
                    onPress={(_) => { //TODO 跳转到相册预览？
                        if (this.state.recording)
                        {
                            showToast(stringsTo('screen_recording'));
                            return ;
                        }
                        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                        this.setState({snapshotVisible:false});
                        if(this.state.isFullScreen){ //横屏则退出全屏
                            if(CONST.isAndroid){
                                this.goToAlbum();
                                setTimeout(() =>{
                                    Orientation.lockToPortrait();
                                    this.props.navigation.setOptions({tabBarVisible: true});
                                    this._onCloseFullScreenTools();
                                    NavigationBar.setStatusBarHidden(false);
                                    this.setState({isFullScreen: false},()=>{
                                        console.log('Android回看退出全屏');
                                    });
                                }, 1000);
                                return;
                            }else {
                                Orientation.lockToPortrait();
                                this.props.navigation.setOptions({tabBarVisible: true});
                                this._onCloseFullScreenTools();
                                NavigationBar.setStatusBarHidden(false);
                                this.setState({isFullScreen: false},()=>{
                                    // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                                    this.goToAlbum();
                                    console.log('iOS回看退出全屏');
                                });
                                return;
                            }
                        }else {
                            this.goToAlbum();
                        }


                        // if(this.state.isFullScreen){ //横屏则退出全屏
                        //     Orientation.lockToPortrait();
                        //     this.props.navigation.setOptions({tabBarVisible: true});
                        //     this._onCloseFullScreenTools();
                        //     NavigationBar.setStatusBarHidden(false);
                        //     this.setState({isFullScreen: false},()=>{
                        //         // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                        //     this.goToAlbum();
                        //     });
                        //     return;
                        // }
                        // // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                        // this.goToAlbum();
                    }}
                />
            </View>
        )
    }

    goToAlbum() {
        console.log('相册');
        if (IMIPackage.minApiLevel < 10007) {
            IMIGotoPage.startAlbumPage(LetDevice.deviceID);
        } else {
            this.props.fileNameStatusChangeListener && this.props.fileNameStatusChangeListener(3);
            this.props.navigation.push('CameraListPage');
        }
    }

    /**
     *
     * @returns {JSX.Element|null}
     * @private
     */
    renderVideoProgressView() {
        if (this.state.snapshotVisible) {
            return null;
        }
        if (this.state.noVideo){
            return null;
        }
        if (this.state.todayIsNoData){
            return null;
        }
        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                width: "100%",
                bottom: 1,
                height: 80,
                zIndex: 999,
            }}>
                <VideoProgressView
                    ref={ref => this.progressView = ref}
                    isPlayFromBeginning={this.state.isPlayFromBeginning}
                    duration={this.state.durationProgress}
                    recording={this.state.recording}
                    isPlayFinish={this.state.isPlayFinish}
                    onProgressValueChanged={this.onProgressChanged}
                >

                 </VideoProgressView>
            </View>
        )
    }
    onProgressChanged = (currentTime) => {
        console.log("currentTime",currentTime+":::"+this.state.isPlay)
        // this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(currentTime)*1000);
       // console.log('当前iOS播放状态---',this.state.isPlay);
        if (!this.state.isPlay){
            // 暂停状态
            this.setState({isPlay: true, showPauseView: false,isClickPause:false}, () => {
                this.tempScrollPause = true;
                this.tempScrollTime = currentTime;
                this.IMIVideoView&&this.IMIVideoView.resume();
            });
        }else {
            this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(currentTime*1000));
        }
    }
    /**
     * 日历控件逻辑
     * @returns {*}
     * @constructor
     */
    getCalendarView() {
        return (
            <ModalView style={[{justifyContent:'center'},this.state.isFullScreen?{alignItems:'center'}:{}]} visible={this.state.isShowCalendar} onClose={()=>{this.setState({isShowCalendar:false})}}>
                <Calendar
                    style={{width:'100%'}}
                    onDayPress={(day) => {

                        let ddd = new Date(day.year,day.month-1,day.day, 0, 0, 0);
                        console.log('AlarmListPlayerPage onDayPress pressed formatDate' + ddd);
                        //this.resetSelectDay(day.dateString);
                        // 刷新完成后调用确保取值正确
                        showLoading(false);
                        this.IMIVideoView&& this.IMIVideoView.stop();
                        this.tempScrollPause=false;
                        //,selectEventId:"0",durationProgress:0
                        let preDay = this.state.currentDate;
                        //切换日期后，选中的事件类型不变selectEventAlarmType:AlarmType.ALL
                        this.setState({isShowCalendar:false,dateTime: day.dateString,currentDate: ddd,
                            isClickPause:false,videoPage:0,videoList:[],eventVideoList:[],selectEventId:"0",
                            videoEventPage:0,
                            videoListEvent:[],
                            fileName:"",isPlayFinish:false,pageStart:0}, () => {
                            //showLoading(stringsTo('commLoadingText'),true);
                            this.progressView && this.progressView.onSliderValueChanged(0);
                            let month=ddd.getMonth()+1;
                            let day=ddd.getDate();
                            if (month<10){
                                month= "0"+month;
                            }
                            if (day<10){
                                day="0"+day;
                            }
                            this.reqTime = ddd.getFullYear()+"-"+month+"-"+day;
                            console.log('AlarmListPlayerPage othis.reqTime ' + this.reqTime );
                            this.resetSelectDay(preDay);
                            this.refreshList.flatList.scrollToOffset({animated:true,offset:0});
                            this.reqDataPre(true);
                            //this._getAliVideoList(this.reqTime,false)
                        });
                        // this.setState({isShowCalendar:false});
                    }}

                    onMonthChange={(day) => {
                        // this.setState({isShowCalendar:false});
                        console.log('AlarmListPlayerPage onMonthChange pressed' + day.month+day.year);
                        console.log('切换日期显示----',day.year,day.month);
                        let ary = day.dateString.split('-');
                        let month = ary[0]+ary[1];
                        showLoading(true);
                        //this._queryMonthData(month);
                        this.checkMonth(month);
                    }}
                    hideArrows={false}
                    disabledByDefault={true}
                    hideExtraDays={true}
                    maxDate={new Date()}
                    current={this.state.dateTime}  //设置选中时间
                    markedDates={{
                        [this.state.dateTime]: {
                            selected: true,
                            marked: false,
                            disableTouchEvent: true,
                            selectedColor: imiThemeManager.theme.primaryColor
                        }, ...this.state.dateData
                    }}
                    theme={{
                        arrowColor: '#000000', //左右箭头的颜色
                        todayTextColor: imiThemeManager.theme.primaryColor,
                        textMonthFontWeight: 'bold',//标题yyyy-MM的字重
                        textDayHeaderFontWeight: 'bold',//周几的字重
                        textSectionTitleColor: '#000000',//周几的颜色
                        textDayHeaderFontSize:12,//周大小 字体设置 英文会突出
                    }}

                    // renderHeader={(date) => {return(<Text style={{color:"black"}}>{this.state.dateText}</Text>)}}

                    // renderHeader={(date) => {return(<Text style={{color:"red"}}>{moment(date).format('YYYY-MM')}</Text>)}}
                    // monthFormat={'yyyy-MM'}
                />

            </ModalView>
        );
    };


    //暂时的052横屏日历控件，与其他项目分开
    getCalendarLandscapeView() {
        return (
            <ModalView style={[{justifyContent:'center',backgroundColor:"transparent"},this.state.isFullScreen?{alignItems:'flex-end'}:{}]} visible={this.state.isShowCalendar} onClose={()=>{this.setState({isShowCalendar:false})}}>
                <Calendar
                    style={{width:360,height:"100%",backgroundColor:"#000000",borderTopLeftRadius:20,borderBottomLeftRadius:20}}
                    onDayPress={(day) => {
                        console.log('AlarmListPlayerPage onDayPress pressed formatDate' + day.dateString);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        let ddd = new Date(day.year,day.month-1,day.day, 0, 0, 0);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        // 刷新完成后调用确保取值正确
                        this.setState({isShowCalendar:false,dateTime: day.dateString}, () => {
                            showLoading(stringsTo('commLoadingText'),true);
                            this.IMIVideoView&&this.IMIVideoView.stop();
                            /*this._queryDayData(ddd).then(dataArr => {
                                showLoading(false);
                                if (dataArr.length>=1){
                                    currentPlayTime = 0;
                                    this.resetSelectDay(day.dateString);
                                    this.setState({dataArray: dataArr,currentDate: ddd,isClickPause:false,dateTime: day.dateString},()=>{

                                    });
                                } else {
                                    showToast(stringsTo('commLoadingFailText'));
                                    this.setState({isClickPause:false});
                                }

                            });*/
                        });

                        // this.setState({isShowCalendar:false});

                    }}

                    onMonthChange={(day) => {
                        // this.setState({isShowCalendar:false});
                        console.log('AlarmListPlayerPage onMonthChange pressed' + day.month)
                        let ary = day.dateString.split('-');
                        let month = ary[0]+ary[1];
                        showLoading(true);
                        this._queryMonthData(month);
                    }}
                    hideArrows={false}
                    hideExtraDays={true}
                    maxDate={new Date()}
                    current={this.state.dateTime}  //设置选中时间
                    markedDates={{
                        [this.state.dateTime]: {
                            selected: true,
                            marked: false,
                            disableTouchEvent: true,
                            selectedColor: imiThemeManager.theme.primaryColor
                        },...this.state.dateData
                    }
                    }
                    theme={{
                        arrowColor: '#ffffff', //左右箭头的颜色
                        backgroundColor: '#000000',
                        calendarBackground: '#000000',
                        todayTextColor: imiThemeManager.theme.primaryColor,
                        dayTextColor: '#ffffff', //有数据的天
                        textDisabledColor: '#888888', //没数据的天
                        monthTextColor: '#ffffff',//标题yyyy-MM的颜色
                        textMonthFontWeight: 'bold',//标题yyyy-MM的字重
                        textDayHeaderFontWeight: 'bold',//周几的字重
                        textSectionTitleColor: '#ffffff',//周几的颜色
                    }}
                    // monthFormat={'yyyy-MM'}
                    // renderHeader={() => {return(<Text style={{color:"white"}}>{this.state.dateText}</Text>)}}
                    // renderHeader={(date) => {return(<Text style={{color:"#fff"}}>{moment(date).format('YYYY-MM')}</Text>)}}
                />

            </ModalView>
        );
    };

    /*告知外部调用者监听状态*/
    getFullscreen() {
        return this.state.isFullScreen;
    }
    /*退出全屏状态*/
    quitFullScreen() {
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
        console.log('云存页面调用退出全屏方法');
    }

    //获取录屏状态
    getRecordStatus(){
        return this.state.recording;
        // console.log('云存获取当前是否录像',this.state.recording);
    }

    render() {
        let showModel= false;
        return (
            <View ref={(ref) => this.componentContainer = ref} pointerEvents="box-none" style={{flex: 1, backgroundColor: "#FFFFFF", flexDirection: "column"}}>
                <View style={{flex: 1}} >
                   <IMIVideoView
                        style={{flex: 1}}
                        ref={ref => {
                            this.IMIVideoView = ref;
                            this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                        }}
                        mute={this.state.mute}
                        playerClass={IMICameraVideoView.PlayerClass.HLS}
                        dataSource={{
                            playerClass: IMICameraVideoView.PlayerClass.HLS,
                            did: LetDevice.deviceID,
                            offsetTime: 0,
                            hls:{
                                eventID:"" ,
                                fileName: this.state.fileName,
                            }
                        }}

                        onPrepared={() => {
                            this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
                        }}
                        onVideoViewClick={()=>{
                            this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                            this.props.onVideoClick && this.props.onVideoClick();
                        }}
                        onEventChange={this._onEventChange}
                        onPlayCompletion={()=>{
                            //2022-03-15@byh 倍速播放完成后，播放进度与视频长度不一致问题
                            let second = this.state.durationProgress;
                            this.progressView&& this.progressView.onSliderValueChanged(second);
                            if(this.state.recording){ //直播流暂停时，停止录像
                                if(CONST.isAndroid){
                                    this._stopRecord(false,true);
                                }else{ //因为IOS会自动停止视频录制，所以直接转存即可
                                    this._saveVideoToPhotosAlbum();
                                }
                            }
                            //增加speed置为1倍速
                            this.setState({isPlay: false, showPauseView: true,isPlayFinish:true,isClickPause:false,speed: 0}, () => {
                                this.tempScrollPause=false;
                                //20220318@byh 播放完成后这个状态置为，未播放状态，此状态作用是再次进入这个页面是否需要播放视频
                                this.props.fileNameStatusChangeListener&& this.props.fileNameStatusChangeListener(0);
                                // this.IMIVideoView&&this.IMIVideoView.stop();
                                // this.IMIVideoView&&this.IMIVideoView.pause();
                            });
                        }}
                        onErrorChange={(event)=>{
                            this.setState({
                                isPlay:false,
                                showErrorView: true,
                                showPauseView: false,
                                isLoading:false,
                                errorCode: event.code,
                            });
                            if(this.state.recording){ //直播流暂停时，停止录像
                                if(CONST.isAndroid){
                                    this._stopRecord();
                                }else{ //因为IOS会自动停止视频录制，所以直接转存即可
                                    this._saveVideoToPhotosAlbum();
                                }
                            }
                            this.props.onHlsPlayerStatusChange && this.props.onHlsPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);
                        }}
                        onRecordTimeChange={this._onRecordTimeChange}
                        /*   lensCorrect={this.props.lensCorrect}*/
                    />

                    {/*全屏?横屏UI:竖屏UI(navBar)*/}
                    <View pointerEvents="box-none" style={{position: "absolute", width: "100%", height: "100%", flexDirection: "column", alignItems: "center"}}>

                        {
                            this._renderSnapshotView()
                        }
                        {
                            this.renderVideoProgressView()
                        }
                        {
                            this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()
                        }
                        {
                            this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()
                        }
                        {
                            this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()
                        }
                        {
                            this.state.isFullScreen ? this._renderLandscapeScreenVideoViewArea() : this._renderPortraitScreenVideoViewArea()
                        }
                        {
                            this._renderRecordingView()
                        }
                        {this._showAlarmView()}
                        {this._noFirstData()}
                        {this._noVideoView()}
                    </View>
                </View>
                {/*全屏?null:渲染外层传入的UI*/}
                {this.state.isFullScreen ? null : this._renderPortraitScreenPlayerToolBarArea()}
                {showModel&&this.state.isFullScreen?this.getCalendarLandscapeView():this.getCalendarView()}
                { this._recordTimeModal()}
            </View>
        );
    }
}
const styles = StyleSheet.create({
    container: {
        flex: 1,
        //position: 'absolute',
    },

    itemParent: {
        alignItems: 'center',
        paddingLeft: 14,paddingTop: 14,
        // flexDirection: 'row',
        backgroundColor: colors.white
    },

    itemIcon: {
        borderRadius: 4, width: (windowWidth-4*14)/3, height: (windowWidth-4*14)/3*57/101.33,
    },
    noVideoImage:{width:'100%',height:windowWidth*9/16},
    noVideoImageFullscreen:{width:'100%',height:'100%'},
    itemIconCenter: {
        position: "absolute",width: 30, height: 30,
        left: (windowWidth-4*14)/3/2, top: (windowWidth-4*14)/3*57/101.33/2,
    },
    itemTitle: {
        fontSize: 12,
        // flex: 1,
        // width:(windowWidth-4*14)/3-14*3,
        // fontWeight: 'bold',
        color: colors.black,
        // paddingLeft: 14
    },

    itemDesc: {
        fontSize: 12,
        paddingLeft: 14,
        // marginTop: 10,
        color: colors.black,
    },
    videoToolBtn: {
        marginHorizontal: 15,
        width: 30,
        height: 30,
    },
});

