import React, {Component} from 'react';
import {
    Image, View, Dimensions, TouchableOpacity, Text, StyleSheet, PanResponder, ScrollView,
} from 'react-native';
import Svg, {
    G, Line, Path, Rect
} from 'react-native-svg';

import {RNLine, showLoading, showToast, Separator, MessageDialog, SingleChoiceItem} from '../../../../imilab-design-ui';
import I18n, {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import HomePageLivePlayerComponent
    from "../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent";
import {
    LetDevice, BaseDeviceComponent, imiAlarmEventCloudApi,
} from '../../../../imilab-rn-sdk';
import {IMIStorage} from "../../../../imilab-rn-sdk";
import NetInfo from "@react-native-community/netinfo";
import {TinyWindowLivePlayer} from "../../../../imilab-modules/com.chuangmi.camera.moudle";
import {XText, XView} from 'react-native-easy-app';
import Toast from "react-native-root-toast";
import ListItmeWithSwitch from "../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import IMIPackage from "../../../../imilab-rn-sdk/native/local-kit/IMIPackage";
import {EVENT_NOTICE_TYPE} from "../../../../imilab-rn-sdk/components/camera/alarm/api/IMIAlarmEventCloudApi";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";

let windowWidth = Dimensions.get('window').width;
let windowHeight = Dimensions.get('window').height;
let minWidth = 64 / 320 * (windowWidth - 28);
//设计图，视频播放区域的宽高比是332：187
let Height = (windowWidth - 28) * 187 / 332;
let minHeight = 64 / 192 * Height;
let area_Width = 320;
let area_Height = 192;

let maxLength = (345 / 320 * (windowWidth - 28)) * (345 / 320 * (windowWidth - 28));

/**
 * 新版越界侦测
 * 越界可直线、矩形
 * 生效时间
 */
export default class FenceDetectionSettingV2 extends BaseDeviceComponent {
    constructor(props, context) {
        super(props, context);
        this.state = {
            currentStatus: HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.LOADING,
            isFullScreen: false,
            isCalling: false,//是否正在通话
            isSleep: false,//设备是否休眠
            isOnline: LetDevice.isOnline,
            FenceSwitchValue: false,                                             //越界侦测开关  t打开 f关闭
            effectTimeStr: stringsTo('allDay_time'),                       //生效时间
            effectTimeData: {
                "start_time": "00:00", "end_time": "23:59"
            },//生效时间
            switchClickNum: 0,//单击切换方向次数 0 向上enter 1向下leaving 2 穿越cross
            rectDir: 0,//单击切换方向次数 0 向上enter 1向下leaving 2 穿越cross
            //xy@20210316 手势相关
            minX: -5, //X轴起始点
            minY: -5, //Y轴起始点
            maxX: (windowWidth - 28),
            maxY: Height,
            drawColor: '#4A70A5',
            data: [],
            location: 1,//this.props.navigation.state.params.location,//当前位置
            tempFenceStr: "0,0,100,100",//围栏侦测数值
            isLine: true,//是否是直线，true越界直线  false越界矩形
            lineArrData: [{
                // itemMoveToX: (windowWidth - 28) / 2,
                // itemMoveToY: 0,
                // itemLineToX: (windowWidth - 28) / 2,
                // itemLineToY: Height,
                // itemRoundOneX: (windowWidth - 28) / 2,
                // itemRoundOneY: 0,
                // itemRoundTwoX: (windowWidth - 28) / 2,
                // itemRoundTwoY: Height,
                itemMoveToX: 0,
                itemMoveToY: Height / 2,
                itemLineToX: (windowWidth - 28),
                itemLineToY: Height / 2,
                itemRoundOneX: 0,
                itemRoundOneY: Height / 2,
                itemRoundTwoX: (windowWidth - 28),
                itemRoundTwoY: Height / 2,
                isShowLine: false,
                isEditLine: true,
                dir: 0,
                itemAngle: "0deg"
            }],//itemMoveToX,itemMoveToY 线的第一个点的x,y坐标，itemLineToX，itemLineToY线的第二个点坐标越界直线时的数组
            pathIndex: 0,//默认编辑线段第一条
            isPathEdit: true,//默认显示编辑线段
            imgAngle: '0deg',
            addLineVisible: true,//添加越界线按钮
            deleteLineVisible: false,//删除越界线按钮
            //声光报警
            soundLightSwitchValue: false,//声光报警开关
            voiceSwitch: false,//语音报警
            voiceName: stringsTo('voice_for_warning'),// 语音名称
            voiceUrl: '1-alarm',
            lightSwitch: false,//聚光灯开关
            lightMode: 1,//聚光灯模式
            containTime: 10,//聚光灯模式

            //越界生效时间开关
            fenceSwitchEffectValue: false,
            repeat_time: "0000,2400",//重复时间
            repeat_week: "1,1,1,1,1,1,1",//重复星期
            sound_alarm: JSON.stringify({switch: 0, name: '1-alarm'}),//智能语音提示

            //消息推送
            infoPush: true,
            tempInfoPush: true
        }

        this.myPanResponder = {};
        this.topPanResponder = {};
        this.bottomPanResponder = {};
    }

    UNSAFE_componentWillMount() {
        this.zoomLastDistance = 0;
        this.myPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => {
                // let isMulti = evt.nativeEvent.changedTouches.length > 1;
                // console.log(`onStartShouldSetPanResponder ${ isMulti }`);
                // if (isMulti) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            }, onStartShouldSetPanResponderCapture: (evt, gestureState) => {
                // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            }, onMoveShouldSetPanResponder: (evt, gestureState) => {
                // let isMulti = evt.nativeEvent.changedTouches.length > 1;
                // // console.log("onMoveShouldSetPanResponder:" + isMulti);
                // if (isMulti) { // 多指才拦截
                return true;
                // } else {
                //     return false;
                // }
            }, onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
                // console.log('000000');
                // if (evt.nativeEvent.changedTouches.length > 1) { // 多指才拦截
                return true;
                // } else {
                //   return false;
                // }
            }, onPanResponderTerminationRequest: (evt, gestureState) => false,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('平移----');
                console.log('平移--show:', this.state.minX, "minY:" + this.state.minY, "-maxX" + this.state.maxX, "-maxY" + this.state.maxY);
                //需要区分是直线，还是矩形
                if (this.state.isLine) {
                    let item = this.state.lineArrData[this.state.pathIndex];
                    this.tempStartX = item.itemMoveToX;
                    this.tempStartY = item.itemMoveToY;
                    this.tempEndX = item.itemLineToX;
                    this.tempEndY = item.itemLineToY;
                } else {
                    this._top = this.state.minY;
                    this._left = this.state.minX;
                }

                this.forceUpdate();
                if (evt.nativeEvent.changedTouches.length > 1) { // 双指时的中心点
                    console.log('平移中心点');
                    let distantX = evt.nativeEvent.changedTouches[1].pageX - evt.nativeEvent.changedTouches[0].pageX;
                    let distantY = evt.nativeEvent.changedTouches[1].pageY - evt.nativeEvent.changedTouches[0].pageY;
                    this.touchDownDistant = Math.sqrt(distantX * distantX + distantY * distantY);
                    // console.log("双指:downDistance" + this.touchDownDistant);
                }
            }, onPanResponderMove: (evt, gestureState) => {
                let isMulti = evt.nativeEvent.changedTouches.length > 1;

                console.log(`onPanResponderMove ${isMulti}`);
                if (this.state.isLine) {
                    this.onLineViewMove(evt, gestureState);
                } else {
                    if (isMulti) {
                        let minX = 0;
                        let maxX = 0;
                        if (evt.nativeEvent.changedTouches[0].locationX > evt.nativeEvent.changedTouches[1].locationX) {
                            minX = evt.nativeEvent.changedTouches[1].pageX;
                            maxX = evt.nativeEvent.changedTouches[0].pageX;
                            console.log('平移中心点==isMulti');
                        } else {
                            minX = evt.nativeEvent.changedTouches[0].pageX;
                            maxX = evt.nativeEvent.changedTouches[1].pageX;
                            console.log('平移中心点==isMulti-minX');
                        }

                        let minY = 0;
                        let maxY = 0;
                        if (evt.nativeEvent.changedTouches[0].locationY > evt.nativeEvent.changedTouches[1].locationY) {
                            minY = evt.nativeEvent.changedTouches[1].pageY;
                            maxY = evt.nativeEvent.changedTouches[0].pageY;
                            console.log('平移中心点==minY');
                        } else {
                            minY = evt.nativeEvent.changedTouches[0].pageY;
                            maxY = evt.nativeEvent.changedTouches[1].pageY;
                            console.log('平移中心点==maxy');
                        }
                        const widthDistance = maxX - minX;
                        const heightDistance = maxY - minY;
                        const diagonalDistance = Math.sqrt(widthDistance * widthDistance + heightDistance * heightDistance);
                        this.zoomCurrentDistance = Number.parseInt(diagonalDistance);
                        if (this.zoomLastDistance == 0) {
                            this.zoomLastDistance = this.zoomCurrentDistance;
                            this.touchDownDistant = this.zoomCurrentDistance;
                            // console.log("双指刚放下");

                        } else {
                            let diff = this.zoomCurrentDistance - this.zoomLastDistance;
                            let diffScale = Number(((diff / 100)).toFixed(2));
                            // console.log(`双指:缩放 zoomCurrentDistance:${ this.zoomCurrentDistance } zoomLastDistance:${ this.zoomLastDistance } touchDownDistance ${ this.touchDownDistant } diff:${ diff } diffScale${ diffScale }`);
                            this.onScaleChanged(diffScale);
                            this.zoomLastDistance = this.zoomCurrentDistance;
                        }
                    } else {
                        let minX = this._left + gestureState.dx;
                        minX = minX < -5 ? -5 : minX;
                        minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                        let minY = this._top + gestureState.dy;
                        minY = minY < -5 ? -5 : minY;
                        minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                        this.setState({minX: minX, minY: minY});
                        console.log('平移--isMuit--false');
                        console.log('平移--show:', parseInt(this.state.minX), "minY:" + parseInt(this.state.minY), "-maxX" + parseInt(this.state.maxX), "-maxY" + parseInt(this.state.maxY));
                    }
                }

            }, onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
                this.zoomLastDistance = 0;
                if (this.state.isLine) {
                    if (this.state.lineArrData.length == 1) {
                        this.setState({isPathEdit: true});
                    } else {
                        let oldIndex = this.state.pathIndex;
                        console.log('平移记住上次编辑选中--', oldIndex);
                        let item = this.state.lineArrData[oldIndex];
                        item.isShowLine = true;
                        item.isEditLine = true;
                        this.setState({isPathEdit: false});
                    }
                }
            }, onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.leftTopPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('左上处理----');
                console.log('左上处理--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let minX = this._left + gestureState.dx;
                minX = minX < -5 ? -5 : minX;
                minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                let minY = this._top + gestureState.dy;
                minY = minY < -5 ? -5 : minY;
                minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width - gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28 - this._left - 5) ? (windowWidth - 28 - this._left - 5) : maxX;
                let maxY = this._height - gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({minX: minX, minY: minY, maxX: maxX, maxY: maxY});
                console.log('左上处理Move--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.rightTopPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('右上处理----');
                console.log('右上处理--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                // let minX = this._left + gestureState.dx;
                // minX = minX < -5 ? -5 : minX;
                // minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                let minY = this._top + gestureState.dy;
                minY = minY < -5 ? -5 : minY;
                minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width + gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28 - this._left - 5) ? (windowWidth - 28 - this._left - 5) : maxX;
                let maxY = this._height - gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height ? Height : maxY;
                this.setState({minY: minY, maxX: maxX, maxY: maxY});
                console.log('右上处理Move--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.leftBottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('左下处理----');
                console.log('左下处理--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let minX = this._left + gestureState.dx;
                minX = minX < -5 ? -5 : minX;
                minX = minX > (windowWidth - 28 - 5 - this.state.maxX) ? (windowWidth - 28 - 5 - this.state.maxX) : minX;
                // let minY = this._top + gestureState.dy;
                // minY = minY < -5 ? -5 : minY;
                // minY = minY > (Height - 5 - this.state.maxY) ? (Height - 5 - this.state.maxY) : minY;
                let maxX = this._width - gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28 - this._left - 5) ? (windowWidth - 28 - this._left - 5) : maxX;
                let maxY = this._height + gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height - this._top - 5 ? Height - this._top - 5 : maxY;
                this.setState({minX: minX, maxX: maxX, maxY: maxY});
                console.log('左下处理Move--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        this.rightBottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('右下处理----');
                console.log('右下处理-show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);

                this._top = this.state.minY;
                this._left = this.state.minX;
                this._width = this.state.maxX;
                this._height = this.state.maxY;
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                let maxX = this._width + gestureState.dx;
                maxX = maxX < minWidth ? minWidth : maxX;
                maxX = maxX > (windowWidth - 28 - this._left - 5) ? (windowWidth - 28 - this._left - 5) : maxX;
                let maxY = this._height + gestureState.dy;
                maxY = maxY < minHeight ? minHeight : maxY;
                maxY = maxY > Height - this._top - 5 ? Height - this._top - 5 : maxY;
                this.setState({maxX: maxX, maxY: maxY});
                console.log('右下处理Move-show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        //直线时手势
        //直线顶部点，手势响应
        this.topPanResponder = PanResponder.create({

            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('左上处理----,编辑第几个线条', this.state.pathIndex);
                console.log('左上处理--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
                // this.tempStartX = this.state.startX;
                // this.tempStartY = this.state.startY;
                // this.tempEndX = this.state.endX;
                // this.tempEndY = this.state.endY;
                let item = this.state.lineArrData[this.state.pathIndex];
                this.tempStartX = item.itemMoveToX;
                this.tempStartY = item.itemMoveToY;
                this.tempEndX = item.itemLineToX;
                this.tempEndY = item.itemLineToY;
                this.tempSecRoundX = item.itemRoundTwoX;
                this.tempSecRoundY = item.itemRoundTwoY;
                console.log('左上记录上次的值--', this.tempStartX, this.tempStartY, this.tempEndX, this.tempEndY);
                console.log('左上记录第二个圆点的值--', this.tempSecRoundX, this.tempSecRoundY);
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {

                console.log('左上移动---dx--dy-', gestureState.dx, gestureState.dy);
                let minX = this.tempStartX + gestureState.dx;
                console.log('左上Minx-最小值--', minX, this.tempStartX, gestureState.dx);
                minX = minX < 0 ? 0 : minX;
                console.log('左上Minx-最小值1---', minX);
                minX = minX > (windowWidth - 28) ? (windowWidth - 28) : minX;
                console.log('左上Minx-最小值2---', minX);

                let maxX = windowWidth - 28 - minX;
                console.log('左上MaxX---最大值--', maxX);

                let minY = this.tempStartY + gestureState.dy;
                console.log('左上MinY--最小值---', minY, this.tempStartY, gestureState.dy);
                minY = minY < 0 ? 0 : minY
                console.log('左上MinY--最小值1---', minY);
                minY = minY > Height ? Height : minY;
                console.log('左上MinY--最小值2---', minY, Height);
                let maxY = Height - minY;
                console.log('左上的MAXY--最大值--', maxY);

                console.log('当前minx---endX', minX, this.tempEndX);
                console.log('当前minY---endX', minY, this.tempEndY);

                let angleFinal;
                if (minX == this.tempEndX) {
                    angleFinal = "90deg";
                    this.setState({imgAngle: "90deg"});
                } else if (minY == this.tempEndY) {
                    if (minX < this.tempEndX) {
                        angleFinal = "0deg";
                        this.setState({imgAngle: "0deg"});
                    } else {
                        angleFinal = "180deg";
                        this.setState({imgAngle: "180deg"});
                    }
                } else {
                    let angleY = Math.abs(parseInt(Math.atan2((this.tempEndX - minX), (this.tempEndY - minY)) * (180 / Math.PI)));
                    // let angleY = parseInt(Math.atan2((this.tempEndX-minX),(this.tempEndY-minY)) * (180 / Math.PI));
                    console.log('上面角度与-与Y轴夹角---', angleY);
                    let angleYNew = angleY + 'deg';

                    let angleX = Math.abs(parseInt(Math.atan2((this.tempEndY - minY), (this.tempEndX - minX)) * (180 / Math.PI)));
                    // let angleX = parseInt(Math.atan2((this.tempEndY-minY),(this.tempEndX-minX)) * (180 / Math.PI));

                    let angleXNew = '';
                    if (angleY > 90) {
                        angleXNew = '-' + angleX + 'deg';
                    } else {
                        angleXNew = angleX + 'deg';
                    }
                    // let angleXNew = angleX + 'deg';
                    angleFinal = angleXNew;
                    this.setState({imgAngle: angleXNew});
                    console.log('当前上面角度与X轴夹角计算---', angleX, angleXNew);
                }

                let tempA = (this.tempEndX - minX) * (this.tempEndX - minX);
                // console.log('左上勾股定理A--',tempA,minX,this.tempEndX);
                let tempB = (this.tempEndY - minY) * (this.tempEndY - minY);
                // console.log('左上勾股定理B--',tempB,minY,this.tempEndY);
                let tempC = minWidth * minWidth;
                // console.log('勾股定理X,Y值',tempA,tempB,tempC);
                if (tempA + tempB < tempC) {
                    console.log('左上不满足勾股定理', tempA, tempB, tempC);
                    Toast.show(stringsTo("imi_distance_tip"));
                    // this.setState({showLineTip:true});
                    // this.setState({minX:minX,minY: minY,maxX: maxX,maxY: maxY,firstX:minX-5,firstY:minY-5,startX:minX,startY:minY,endX:this.tempEndX,endY:this.tempEndY,showLineTip:true})
                } else {
                    // if (tempA + tempB > maxLength) {
                    //     this._updateAddLineVisible(true);
                    // } else {
                    //     this._updateAddLineVisible(false);
                    // }
                    console.log('左上大于勾股定理', tempA, tempB, tempC);
                    console.log('更新前数组--', this.state.lineArrData);
                    for (let i = 0; i < this.state.lineArrData.length; i++) {
                        if (i == this.state.pathIndex) {
                            console.log('第几个--', i);
                            let item = this.state.lineArrData[i];
                            console.log('更新前,item', item);
                            item.itemMoveToX = minX;
                            item.itemMoveToY = minY;
                            item.itemLineToX = this.tempSecRoundX;
                            item.itemLineToY = this.tempSecRoundY;
                            item.itemRoundOneX = minX;
                            item.itemRoundOneY = minY;
                            item.itemAngle = angleFinal;
                            if (this.state.lineArrData.length == 1) {
                                item.isShowLine = false;
                                item.isEditLine = true;
                            } else {
                                item.isShowLine = true;
                                item.isEditLine = true;
                            }

                            // item.itemRoundTwoX = this.tempSecRoundX;
                            // item.itemRoundTwoY = this.tempSecRoundY;
                            console.log('更新后,item', item);
                        }
                    }
                }
            },
            onPanResponderRelease: (evt, gestureState) => {
                console.log('上面抬手');
                if (this.state.lineArrData.length == 1) {
                    this.setState({isPathEdit: true});
                } else {
                    let oldIndex = this.state.pathIndex;
                    console.log('上面圆点记住上次编辑选中--', oldIndex);
                    let item = this.state.lineArrData[oldIndex];
                    item.isShowLine = true;
                    item.isEditLine = true;
                    this.setState({isPathEdit: false});
                }

            },
            onPanResponderTerminate: (evt, gestureState) => {
                console.log('上面另一个组件');
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
            }
        });
        //直线底部点，手势响应
        this.bottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => true,
            onStartShouldSetPanResponderCapture: (evt, gestureState) => true,
            onMoveShouldSetPanResponder: (evt, gestureState) => true,
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => true,
            onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('底部处理--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);

                let item = this.state.lineArrData[this.state.pathIndex];
                this.tempStartX = item.itemMoveToX;
                this.tempStartY = item.itemMoveToY;
                this.tempEndX = item.itemLineToX;
                this.tempEndY = item.itemLineToY;
                console.log('底部记录上次的值--', this.tempStartX, this.tempStartY, this.tempEndX, this.tempEndY);
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                console.log('底部移动距离---', gestureState.dx, gestureState.dy);
                let maxX = this.tempEndX + gestureState.dx;
                console.log('底部MaxX--最大值---', maxX, this.tempEndX, gestureState.dx);
                maxX = maxX < 0 ? 0 : maxX;
                console.log('底部MaxX--最大值1---', maxX);
                maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
                console.log('底部MaxX--最大值2---', maxX);

                let minX = windowWidth - 28 - maxX;
                console.log('底部MinX--最小值---', minX);

                let maxY = this.tempEndY + gestureState.dy;
                console.log('底部的MAXY--最大值---', maxY, this.tempEndY, gestureState.dy);
                maxY = maxY < 0 ? 0 : maxY;
                console.log('底部的MAXY--最大值1---', maxY);
                maxY = maxY > Height ? Height : maxY;
                console.log('底部的MAXY--最大值2---', maxY, Height);
                let minY = Height - maxY;
                console.log('底部MinY--最小值---', minY);

                console.log('底部当前maxX---startX', maxX, this.tempStartX);
                console.log('底部当前maxY---startY', maxY, this.tempStartY);

                let angleFinal;
                if (maxX == this.tempStartX) {
                    angleFinal = "90deg";
                    this.setState({imgAngle: "90deg"});
                } else if (maxY == this.tempStartY) {
                    if (maxX < this.tempStartX) {
                        angleFinal = "180deg";
                        this.setState({imgAngle: "180deg"});
                    } else {
                        angleFinal = "0deg";
                        this.setState({imgAngle: "0deg"});
                    }
                } else {
                    let angleY = Math.abs(parseInt(Math.atan2((maxX - this.tempStartX), (maxY - this.tempStartY)) * (180 / Math.PI)));
                    // let angleY = parseInt(Math.atan2((this.tempEndX-minX),(this.tempEndY-minY)) * (180 / Math.PI));
                    console.log('底部角度与-与Y轴夹角---', angleY);
                    let angleYNew = angleY + 'deg';
                    let angleX = Math.abs(parseInt(Math.atan2((this.tempStartY - maxY), (maxX - this.tempStartX)) * (180 / Math.PI)));
                    // let angleX = parseInt(Math.atan2((this.tempStartY-maxY),(maxX-this.tempStartX)) * (180 / Math.PI));

                    // let angleXNew = angleX + 'deg';
                    let angleXNew = '';
                    if (angleY > 90) {
                        angleXNew = '-' + angleX + 'deg';
                    } else {
                        angleXNew = angleX + 'deg';
                    }
                    angleFinal = angleXNew;
                    this.setState({imgAngle: angleXNew});
                    console.log('当前底部角度与X轴夹角计算---', angleX, angleXNew);
                }

                let tempA = (maxX - this.tempStartX) * (maxX - this.tempStartX);
                console.log('底部勾股定理A--', tempA, maxX, this.tempStartX);
                let tempB = (maxY - this.tempStartY) * (maxY - this.tempStartY);
                console.log('底部勾股定理B--', tempB, maxY, this.tempStartY);
                let tempC = minWidth * minWidth;
                console.log('底部勾股定理X,Y值', tempA, tempB, tempC);
                if (tempA + tempB < tempC) {
                    console.log('底部不满足勾股定理', tempA, tempB, tempC);
                    Toast.show(stringsTo("imi_distance_tip"));
                } else {
                    // if (tempA + tempB > maxLength) {
                    //     this._updateAddLineVisible(true);
                    // } else {
                    //     this._updateAddLineVisible(false);
                    // }
                    console.log('底部大于勾股定理', tempA, tempB, tempC);
                    for (let i = 0; i < this.state.lineArrData.length; i++) {
                        if (i == this.state.pathIndex) {
                            console.log('底部第几个--', i);
                            let item = this.state.lineArrData[i];
                            console.log('底部更新前,item', item);
                            item.itemLineToX = maxX;
                            item.itemLineToY = maxY;
                            item.itemRoundTwoX = maxX;
                            item.itemRoundTwoY = maxY;
                            item.itemAngle = angleFinal;
                            if (this.state.lineArrData.length == 1) {
                                item.isShowLine = false;
                            } else {
                                item.isShowLine = true;
                            }
                            item.isEditLine = true;
                            // item.itemRoundTwoX = this.tempSecRoundX;
                            // item.itemRoundTwoY = this.tempSecRoundY;
                            console.log('底部更新后,item', item);
                        }
                    }
                }
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
                console.log('底部抬手');
                if (this.state.lineArrData.length == 1) {
                    this.setState({isPathEdit: true});
                } else {
                    let oldIndex = this.state.pathIndex;
                    console.log('底部记住上次编辑选中--', oldIndex);
                    let item = this.state.lineArrData[oldIndex];
                    item.isShowLine = true;
                    item.isEditLine = true;
                    this.setState({isPathEdit: false});
                }
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
                console.log('底部其他组件');
            }
        });
    }

    /**
     * 直线时手势移动
     * @param evt
     * @param gestureState
     */
    onLineViewMove(evt, gestureState) {
        console.log("on edit line move");
        let minX = this.tempStartX + gestureState.dx;
        console.log('平移Minx-最小值--', minX, this.tempStartX, gestureState.dx);
        minX = minX < 0 ? 0 : minX;
        console.log('平移Minx-最小值1---', minX);
        minX = minX > (windowWidth - 28) ? (windowWidth - 28) : minX;
        console.log('平移Minx-最小值2---', minX);

        let minY = this.tempStartY + gestureState.dy;
        console.log('平移MinY--最小值---', minY, this.tempStartY, gestureState.dy);
        minY = minY < 0 ? 0 : minY
        console.log('平移MinY--最小值1---', minY);
        minY = minY > Height ? Height : minY;
        console.log('平移MinY--最小值2---', minY, Height);

        let maxX = this.tempEndX + gestureState.dx;
        console.log('平移MaxX--最大值---', maxX, this.tempEndX, gestureState.dx);
        maxX = maxX < 0 ? 0 : maxX;
        console.log('平移MaxX--最大值1---', maxX);
        maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
        console.log('平移MaxX--最大值2---', maxX);

        let maxY = this.tempEndY + gestureState.dy;
        console.log('平移MAXY--最大值---', maxY, this.tempEndY, gestureState.dy);
        maxY = maxY < 0 ? 0 : maxY;
        console.log('平移MAXY--最大值1---', maxY);
        maxY = maxY > Height ? Height : maxY;
        console.log('平移MAXY--最大值2---', maxY, Height);

        console.log('底部当前maxX---startX', maxX, this.tempStartX);
        console.log('底部当前maxY---startY', maxY, this.tempStartY);

        let angleFinal;
        if (minX == maxX) {
            angleFinal = "90deg";
            this.setState({imgAngle: "90deg"});
        } else if (maxY == minY) {
            if (maxX > minX) {
                angleFinal = "0deg";
                this.setState({imgAngle: "0deg"});
            } else {
                angleFinal = "180deg";
                this.setState({imgAngle: "180deg"});
            }
        } else {
            let angleY = Math.abs(parseInt(Math.atan2((maxX - minX), (maxY - minY)) * (180 / Math.PI)));
            // let angleY = parseInt(Math.atan2((this.tempEndX-minX),(this.tempEndY-minY)) * (180 / Math.PI));
            console.log('平行角度与-与Y轴夹角---', angleY);
            let angleYNew = angleY + 'deg';
            let angleX = Math.abs(parseInt(Math.atan2((maxY - minY), (maxX - minX)) * (180 / Math.PI)));
            // let angleX = parseInt(Math.atan2((maxY-minY),(maxX-minX)) * (180 / Math.PI));

            // let angleXNew = angleX + 'deg';
            let angleXNew = '';
            if (angleY > 90) {
                angleXNew = '-' + angleX + 'deg';
            } else {
                angleXNew = angleX + 'deg';
            }

            angleFinal = angleXNew;
            this.setState({imgAngle: angleXNew});
            console.log('当前平行角度与X轴夹角计算---', angleX);
        }

        let tempA = (maxX - minX) * (maxX - minX);
        console.log('平移勾股定理A--', tempA, minX, maxX);
        let tempB = (maxY - minY) * (maxY - minY);
        console.log('平移勾股定理B--', tempB, minY, maxY);
        let tempC = minWidth * minWidth;
        console.log('勾股定理X,Y值', tempA, tempB, tempC);
        if (tempA + tempB < tempC) {
            Toast.show(stringsTo("imi_distance_tip"));
            // this.setState({firstX:minX-5,firstY:minY-5,secX:maxX == (windowWidth-28) ? (windowWidth-28) : maxX,secY:maxY-5,startX:minX,startY:minY,endX:maxX,endY:maxY,showLineTip:true});
        } else {
            // if (tempA + tempB > maxLength) {
            //     this._updateAddLineVisible(true);
            // } else {
            //     this._updateAddLineVisible(false);
            // }
            for (let i = 0; i < this.state.lineArrData.length; i++) {
                if (i == this.state.pathIndex) {
                    console.log('第几个--', i);
                    let item = this.state.lineArrData[i];
                    console.log('平移更新前,item', item);
                    item.itemMoveToX = minX;
                    item.itemMoveToY = minY;
                    item.itemLineToX = maxX;
                    item.itemLineToY = maxY;
                    item.itemRoundOneX = minX;
                    item.itemRoundOneY = minY;
                    item.itemRoundTwoX = maxX;
                    item.itemRoundTwoY = maxY;
                    item.itemAngle = angleFinal;
                    if (this.state.lineArrData.length == 1) {
                        item.isShowLine = false;
                        item.isEditLine = true;
                    } else {
                        item.isShowLine = true;
                        item.isEditLine = true;
                    }
                    console.log('平移更新后,item', item);
                }
            }

        }
    }

    _updateAddLineVisible(force) {
        console.log("_updateAddLineVisible ===> ", force);
        if (force) {
            this.setState({addLineVisible: false});
            return;
        }
        if (this.state.lineArrData.length >= 2) {
            this.setState({addLineVisible: false});
        } else {
            this.setState({addLineVisible: true});
        }
    }

    onScaleChanged(diffScale) {
        console.log('双指处理---');
        let width = this.state.maxX;
        let height = this.state.maxY;
        width = width * (diffScale + 1);
        height = height * (diffScale + 1);
        width = width > (windowWidth - 28) ? (windowWidth - 28) : width;
        height = height > Height ? Height : height;
        width = width < minWidth ? minWidth : width;
        height = height < minHeight ? minHeight : height;
        let minX = ((windowWidth - 28 - 5) - width) / 2;
        minX = minX > 0 ? minX : -5;
        let minY = (Height - height) / 2;
        minY = minY > 0 ? minY : -5;
        this.setState({
            minX: minX, minY: minY, maxX: width, maxY: height
        }, callback => {
            console.log('双指处理-show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
        });
    }

    componentDidMount() {
        this._subscribe_focus = this.props.navigation.addListener('focus', () => { //获取焦点监听
            this.getNetWork();
        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => { //去往其他界面监听
            this.IMIVideoView && this.IMIVideoView.stop();
            showLoading(false);
        });

        // Subscribe
        const unsubscribe = NetInfo.addEventListener(state => {
            if (state.type != 'wifi' && this.state.isDataUsage) {
                this.getNetWork();
            }
            console.log("Is connected?", state.isConnected);
        });

        // Unsubscribe
        unsubscribe();

        this.getAllValue();


    }

    /**
     * 获取越界数据
     * 越界分为直线、矩形
     * 默认未设置时，展示直线，默认有一条直线
     */
    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        /**
         * {
         * 	"BoundaryFenceAttr": [
         * 		{
         * 			"idx": 1,
         * 			"switch": 1,
         * 			"push_flag": 1,
         * 			"active_time_switch": 1,
         * 			"repeat_week": "1,1,1,1,1,1,1",
         * 			"repeat_time": "0800,2000",
         * 		    "sound_alarm\":{\"switch\":1,\"name\":\"6-safety_female\"},\"light_alarm\":{\"switch\":1,\"param\":\"[1,20,50,300,500]\"}
         * 			"line": "{\"num\":4,\"L1\":\"50,10,50,70\",\"D1\":\"10,40,80,40\",\"dir1\":\"0\",\"angle1\":\"90deg\",\"L2\":\"60,20,50,70\",\"D2\":\"10,40,80,40\",\"dir2\":\"0\",\"angle2\":\"90deg\",\"L3\":\"70,10,50,70\",\"D3\":\"10,40,80,40\",\"L4\":\"80,10,50,70\",\"D4\":\"10,40,80,40\"}",
         * 			"rect": "{\"dir\":2,\"P1\":\"10,10\",\"P2\":\"50,10\",\"P3\":\"50,80\",\"P4\":\"10,80\",\"sound_alarm\":{\"switch\":1,\"name\":\"2-dingdong\"},\"light_alarm\":{\"switch\":1,\"param\":\"[1,20,50,300,500]\"}}"
         * 		}
         * 	]
         * }
         */
        LetDevice.getAssignedPropertyCloud("BoundaryFenceAttr").then(data => {
            console.log("BoundaryFenceAttr", data)
            showLoading(false);
            let dataArr= typeof (data) === "string" ? JSON.parse(data) : data;
            if (dataArr.length==0){
                //最初未定义数据时，获取到的数据时"[]",string类型的空数组
                return;
            }
            let dataObject = dataArr[0];
            if (!dataObject.line && !dataObject.rect) {
                //直线数据   矩形数据都不存在
                //展示默认数据吧
                return;
            }
            console.log("dataObject",dataObject);
            //编辑时的方向
            let dir = 0;
            let itemAngle = '0deg';

            //默认有条越界直线
            let lineData = [{
                itemMoveToX: 0,
                itemMoveToY: Height / 2,
                itemLineToX: (windowWidth - 28),
                itemLineToY: Height / 2,
                itemRoundOneX: 0,
                itemRoundOneY: Height / 2,
                itemRoundTwoX: (windowWidth - 28),
                itemRoundTwoY: Height / 2,
                isShowLine: false,
                isEditLine: true,
                dir: 0,
                itemAngle: "0deg"
            }]
            let isLine = true;
            if (dataObject.line && dataObject.line !== "{}") {
                //说明是直线越界方式
                isLine = true;
                lineData = this.dealLineData(JSON.parse(dataObject.line));
                if (lineData.length > 0) {
                    dir = lineData[0].dir;
                    itemAngle = lineData[0].itemAngle;
                }
            }
            let rect1 = -5; //X轴起始点
            let rect2 = -5;//Y轴起始点
            let rect3 = (windowWidth - 28);
            let rect4 = Height;

            if (dataObject.rect && dataObject.rect !== "{}") {
                //说明是矩形越界方式
                isLine = false;
                let rectData = JSON.parse(dataObject.rect);
                let p1Arr = rectData.P1.split(',');
                let p3Arr = rectData.P3.split(',');

                rect1 = -5 + parseInt(parseInt(p1Arr[0]) / 100 * (windowWidth - 28));
                rect2 = -5 + parseInt(parseInt(p1Arr[1]) / 100 * Height);
                rect3 = parseInt(parseInt(p3Arr[0]) / 100 * (windowWidth - 28));
                rect4 = parseInt(parseInt(p3Arr[1]) / 100 * Height);
                dir = rectData.dir;
            }

            let sound_alarm = JSON.parse(dataObject.sound_alarm);
            let light_alarm = JSON.parse(dataObject.light_alarm);
            let lightParam = typeof (light_alarm.param) == "string" ? JSON.parse(light_alarm.param) : light_alarm.param;
            this.setState({
                FenceSwitchValue:dataObject.switch==1,
                lineArrData: lineData,
                addLineVisible: lineData.length<2,//添加越界线按钮
                deleteLineVisible: lineData.length>=2,//删除越界线按钮

                minX: rect1, //X轴起始点
                minY: rect2, //Y轴起始点
                maxX: rect3 - rect1-5, //矩形宽
                maxY: rect4 - rect2-5, //矩形高
                rectDir: typeof (dir) === 'string'?parseInt(dir):dir,
                switchClickNum: typeof (dir) === 'string'?parseInt(dir):dir,
                imgAngle: itemAngle,
                //声光报警
                soundLightSwitchValue: light_alarm.switch == 1,//声光报警开关
                voiceSwitch: sound_alarm.switch == 1,//语音报警
                voiceUrl: sound_alarm.name,
                lightSwitch: lightParam.switch == 1,//聚光灯开关
                lightMode: lightParam[0],//聚光灯模式
                containTime: lightParam[1],//持续时间

                //越界生效时间开关
                fenceSwitchEffectValue: dataObject.active_time_switch==1,
                repeat_time: dataObject.repeat_time,//重复时间
                repeat_week: dataObject.repeat_week,
                sound_alarm: dataObject.sound_alarm,
                isLine:isLine,
                //消息推送
                infoPush: dataObject.push_flag == 1,
                tempInfoPush: dataObject.push_flag == 1
            })

        }).catch(error => {
            console.log("BoundaryFenceAttr error",error)
            showLoading(false);
            showToast(I18n.t('commLoadingFailText'));
        });

    }

    /**
     * 处理越界直线数据
     * {
     * 	"num":2,
     * 	"L1":"lxs,lys,lxe,lye",
     * 	"D1":"dxs,dys,dxe,dxe",
     * 	"L2":"lxs,ys,xe,ye",
     * 	"D2":"dxs,dys,dxe,dxe",
     * 	"sound_alarm":{"switch":%d,"name":"%s"},
     * 	"light_alarm":{"switch":%d,"param":"[%d,%d,%d,%d,%d]"}
     * 	}
     */
    dealLineData(lineData) {
        let num = lineData.num;
        let newArr = [];
        for (let i = 0; i < num; i++) {
            let lineOneArr;
            let lineKey = `L${i + 1}`;
            let dirKey = `dir${i + 1}`;
            let angleKey = `angle${i + 1}`;
            lineOneArr = lineData[lineKey].split(',');
            let tempOneStartX = parseInt((lineOneArr[0] / 100) * (windowWidth - 28));
            let tempOneStartY = parseInt((lineOneArr[1] / 100) * Height);
            let tempOneEndX = parseInt((lineOneArr[2] / 100) * (windowWidth - 28));
            let tempOneEndY = parseInt((lineOneArr[3] / 100) * Height);
            let item = {
                itemMoveToX: tempOneStartX,
                itemMoveToY: tempOneStartY,
                itemLineToX: tempOneEndX,
                itemLineToY: tempOneEndY,
                itemRoundOneX: tempOneStartX,
                itemRoundOneY: tempOneStartY,
                itemRoundTwoX: tempOneEndX,
                itemRoundTwoY: tempOneEndY,
                isShowLine: true,
                isEditLine: i===0,
                dir: lineData[dirKey],
                itemAngle: lineData[angleKey]
            };
            newArr.push(item);
        }
        return newArr;
    }

    getNetWork() {
        IMIStorage.load({
            key: LetDevice.deviceID + 'isDataUsageWarning', autoSync: true, syncInBackground: true,
        }).then(res => {
            this.setState({isDataUsage: res.isDataUsage});
            if (res.isDataUsage) {
                NetInfo.fetch().then(state => {
                    if (state.type == 'wifi') {
                        this.IMIVideoView.prepare();
                    } else {
                        this.IMIVideoView && this.IMIVideoView.stop();
                    }
                });
            } else {
                this.IMIVideoView.prepare();
            }
        }).catch(_ => {
            this.setState({isDataUsage: false});
            this.IMIVideoView.prepare();
        });
    }

    componentWillUnmount() {
        /*  if (isAndroid()) { }*/
        this.IMIVideoView && this.IMIVideoView.destroy();
        LetDevice.removeDeviceEventChangeListener();
        this._subscribe_focus && this._subscribe_focus();
        this._subscribe_blur && this._subscribe_blur();
        this.timerVideoView && clearTimeout(this.timerVideoView);
    }

    _onPressBack = () => {
        this.props.navigation.pop();
    };

    _saveFence() {
        let rectData = {}
        let lineData = {}
        if (this.state.isLine) {
            //直线，保存越界
            lineData = this._getLineData();
        } else {
            //矩形保存越界
            rectData = this._getRectData();
        }

        //语音播报相关
        let soundStr = {
            switch: this.state.soundLightSwitchValue ? 1 : 0,
            name: this.state.voiceUrl
        };
        //闪光灯
        let param = [this.state.lightMode,this.state.containTime,50,500,500];
        let lightStr = {
            switch:this.state.soundLightSwitchValue ? 1 : 0,
            param:JSON.stringify(param),
        }
        //定时生效 生效时间 生效周期
        let uploadValue = {
            switch:this.state.FenceSwitchValue?1:0,
            idx: 1,
            push_flag: this.state.infoPush?1:0,
            active_time_switch: this.state.fenceSwitchEffectValue?1:0,
            repeat_week: this.state.repeat_week,
            repeat_time: this.state.repeat_time,
            sound_alarm: JSON.stringify(soundStr),
            light_alarm:JSON.stringify(lightStr),
            line: JSON.stringify(lineData),
            rect:JSON.stringify(rectData)
        }
        let child = [];
        child.push(uploadValue);
        let params = {
            BoundaryFenceAttr:child
        }
        console.log("开始JSON.stringify(params):", JSON.stringify(params));
        LetDevice.setPropertyCloud(JSON.stringify(params)).then((data) => {
            //设置云端的推送开关值
            this.opPushNotice(this.state.FenceSwitchValue,this.state.infoPush);
            if (this.props.route.params.callback) {
                console.log('异响侦测返回传值', this.state.FenceSwitchValue)
                let tempStr;
                if (this.state.FenceSwitchValue) {
                    tempStr = "1"; // 已开启
                } else {
                    tempStr = "0"; // 未开启
                }
                console.log('返回值---', tempStr)
                this.props.route.params.callback(tempStr);
            }
            this.props.navigation.pop();
        }).catch(_ => {
            console.log("失败:", _)
            showToast(I18n.t('operationFailed'));
            showLoading(false);
        });
        let jsonParams = {
            IntrusionDetectSwitch:this.state.FenceSwitchValue ? 1 : 0,
            IntrusionDetectType:this.state.isLine ? 0 : 1,
            IntrusionLightSoundSwitch:this.state.soundLightSwitchValue ? 1 : 0,
            IntrusionScheduledSwitch:this.state.fenceSwitchEffectValue ? 1 : 0,
            IntrusionPushSwitch:this.state.infoPush ? 1 : 0
        };
        if (this.state.isLine){
            //直线模式，上传用户设置了几条直线
            jsonParams['IntrusionLineNumber'] = this.state.addLineVisible?1:2;
        }
        IMILogUtil.uploadClickEventValue(jsonParams);

    }

    /**
     * 推送打开的条件，总开关打开，并且消息推送开关也打开
     * @param switch_on 总开关
     * @param push_flag 消息推送开关
     */
    opPushNotice(switch_on, push_flag) {
        let pushNotice = false;
        if (switch_on && push_flag) {
            //开关全部开的情况下，才会有推送
            pushNotice = true;
        }
        imiAlarmEventCloudApi.setEventPushNotice(LetDevice.deviceID, LetDevice.model, EVENT_NOTICE_TYPE.OVER_AREA, pushNotice)
            .then(res => {
            }).catch(err => {
        });
    }

    /**
     * 处理矩形越界数据的上传
     * @return {{P1: string, P2: string, P3: string, P4: string, dir: string}}
     */
    _getRectData() {
        let rect1 = parseInt((this.state.minX + 5) / (windowWidth - 28) * 100);
        let rect2 = parseInt((this.state.minY + 5) / Height * 100);
        let rect3 = parseInt((this.state.maxX + this.state.minX + 5) / (windowWidth - 28) * 100);
        let rect4 = parseInt((this.state.maxY+ this.state.minY + 5) / Height * 100);
        rect1 = rect1<0?0:rect1>100?100:rect1;
        rect2 = rect2<0?0:rect2>100?100:rect2;
        rect3 = rect3<0?0:rect3>100?100:rect3;
        rect4 = rect4<0?0:rect4>100?100:rect4;
        //p1->p2->p3->p4
        return {
            dir: typeof (this.state.rectDir) === 'string'?parseInt(this.state.rectDir):this.state.rectDir,
            P1: `${rect1},${rect2}`,
            P2: `${rect3},${rect2}`,
            P3: `${rect3},${rect4}`,
            P4: `${rect1},${rect4}`,
        }
    }

    _getLineData(){
        let lineData = {num:this.state.lineArrData.length}
        for (let i = 0; i < this.state.lineArrData.length; i++) {
            let oneItem = this.state.lineArrData[i];
            console.log("item",oneItem);
            let startX = parseInt(oneItem.itemMoveToX * area_Width / (windowWidth - 28)) > 0 ? parseInt(oneItem.itemMoveToX * area_Width / (windowWidth - 28)) : 0;
            let startY = parseInt(oneItem.itemMoveToY * area_Height / Height) > 0 ? parseInt(oneItem.itemMoveToY * area_Height / Height) : 0;
            let endX = parseInt(oneItem.itemLineToX * area_Width / (windowWidth - 28)) > 0 ? parseInt(oneItem.itemLineToX * area_Width / (windowWidth - 28)) : 0;
            let endY = parseInt(oneItem.itemLineToY * area_Height / Height) > 0 ? parseInt(oneItem.itemLineToY * area_Height / Height) : 0;
            let tempStartX = startX <= 0 ? 0 : parseInt(parseFloat(startX / area_Width).toFixed(2) * 100);
            let tempStartY = startY <= 0 ? 0 : parseInt(parseFloat(startY / area_Height).toFixed(2) * 100);
            let tempEndX = endX >= 320 ? 100 : parseInt(parseFloat(endX / area_Width).toFixed(2) * 100);
            let tempEndY = endY >= 192 ? 100 : parseInt(parseFloat(endY / area_Height).toFixed(2) * 100);
            let centerX = parseInt((tempStartX + tempEndX) / 2);
            let centerY = parseInt((tempStartY + tempEndY) / 2);
            let dstartX;
            let dstartY;
            let dendX;
            let dendY;
            let angle = oneItem.itemAngle.substring(0,oneItem.itemAngle.length-3);
            console.log(tempStartX,tempStartY,tempEndX,tempEndY,oneItem.dir,angle)
            if (tempStartX == tempEndX) {
                //竖直方向
                if ((oneItem.dir == 0 && angle >0)
                    || (oneItem.dir == 1 && angle < 0)){
                    //方向向右
                    if (centerX == 0){
                        dstartX = centerX;
                        dstartY = centerY;
                        dendX = centerX+5;
                        dendY = centerY;
                    }else if (centerX == 100){
                        dstartX = centerX-5;
                        dstartY = centerY;
                        dendX = centerX;
                        dendY = centerY;
                    }else {
                        dstartX = centerX - 1;
                        dstartY = centerY;
                        dendX = centerX + 1;
                        dendY = centerY;
                    }
                }else if ((oneItem.dir == 0 && angle <0)
                    || (oneItem.dir == 1 && angle > 0)){
                    //方向向左 右---》左
                    if (centerX == 0){
                        dstartX = centerX+5;
                        dstartY = centerY;
                        dendX = centerX;
                        dendY = centerY;
                    }else if (centerX == 100){
                        dstartX = centerX;
                        dstartY = centerY;
                        dendX = centerX-5;
                        dendY = centerY;
                    }else {
                        dstartX = centerX + 1;
                        dstartY = centerY;
                        dendX = centerX - 1;
                        dendY = centerY;
                    }
                }else if (oneItem.dir == 2) {
                    dstartX = tempStartX;
                    dstartY = tempStartY;
                    dendX = tempStartX;
                    dendY = tempStartY;
                }

            } else if (tempStartY == tempEndY) {
                //水平方向
                if ((oneItem.dir == 0 && angle == 0)
                    || (oneItem.dir == 1 && angle == 180)) {
                    //向上
                    if (centerY == 0){
                        dstartX = centerX;
                        dstartY = centerY+5;
                        dendX = centerX;
                        dendY = centerY;
                    }else if (centerY == 100){
                        dstartX = centerX;
                        dstartY = centerY;
                        dendX = centerX;
                        dendY = centerY - 5;
                    }else {
                        dstartX = centerX;
                        dstartY = centerY+1;
                        dendX = centerX;
                        dendY = centerY-1;
                    }
                }else if ((oneItem.dir == 0 && angle == 180)
                    || (oneItem.dir == 1 && angle == 0)){
                    //向下
                    if (centerY == 0){
                        dstartX = centerX;
                        dstartY = centerY;
                        dendX = centerX;
                        dendY = centerY+5;
                    }else if (centerY == 100){
                        dstartX = centerX;
                        dstartY = centerY - 5;
                        dendX = centerX;
                        dendY = centerY;
                    }else {
                        dstartX = centerX;
                        dstartY = centerY-1;
                        dendX = centerX;
                        dendY = centerY+1;
                    }
                }else if (oneItem.dir == 2) {
                    dstartX = tempStartX;
                    dstartY = tempStartY;
                    dendX = tempStartX;
                    dendY = tempStartY;
                }
            } else {
                // 其他方向
                if (oneItem.dir == 0 || oneItem.dir == 1) {
                    let endXY = this.getMethodLine(tempStartX,tempStartY,tempEndX,tempEndY,oneItem.dir,angle);
                    // dstartX = centerX;
                    // dstartY = centerY;
                    dstartX = parseInt(endXY.B.x+"");
                    dstartY = parseInt(endXY.B.y+"");
                    dendX = parseInt(endXY.A.x+"");
                    dendY = parseInt(endXY.A.y+"");
                } else if (oneItem.dir == 2) {
                    dstartX = tempStartX;
                    dstartY = tempStartY;
                    dendX = tempStartX;
                    dendY = tempStartY;
                }
            }
            let lineKey = `L${i+1}`;
            let dKey = `D${i+1}`;
            let dirKey = `dir${i+1}`;
            let itemAngleKey = `angle${i+1}`;
            let lineValue = tempStartX + ',' + tempStartY + ',' + tempEndX + ',' + tempEndY;
            let dValue = dstartX + ',' + dstartY + ',' + dendX + ',' + dendY;
            lineData[lineKey] = lineValue;
            lineData[dKey] = dValue;
            lineData[dirKey] = oneItem.dir;
            lineData[itemAngleKey] = oneItem.itemAngle;
        }
        console.log("lineData",lineData);
        return lineData;

    }

    /**
     * y轴方向向下，所以Y方向先用-值代替
     * @param tempStartX
     * @param tempStartY
     * @param tempEndX
     * @param tempEndY
     */
    getMethodLine(tempStartX,tempStartY,tempEndX, tempEndY,dir,angle){
        tempStartY = -tempStartY;
        tempEndY = -tempEndY;
        //直线的斜率
        let k = (tempStartY-tempEndY)/(tempStartX-tempEndX);
        let centerX = (tempStartX + tempEndX) / 2;
        let centerY = (tempStartY + tempEndY) / 2;
        let start = 0;
        let end = 100;
        let startB = 0;
        let endB = 100;
        if (dir == 0){
            if (0< angle && angle <180){
                start = centerX+1;
                end = 100;

                startB = 0;
                endB = centerX -1;
            }
            if (-180 < angle && angle <0){
                start = 0;
                end = centerX-1;

                startB = centerX + 1;
                endB = 100;
            }
        }else if (dir == 1){
            if (0< angle && angle <180){
                start = 0;
                end = centerX-1;

                startB = centerX + 1;
                endB = 100;
            }
            if (-180 < angle && angle <0){
                start = centerX+1;
                end = 100;

                startB = 0;
                endB = centerX -1;
            }
        }
        let resX = 0;
        let resY = 0;
        for (let i = start;i <= end; i++) {
            let y = centerY-(i - centerX)/k;
            console.log("y= "+y,"x= "+i,"k= "+k);
            if (-100 <=y && y<=0){
                resY = y;
                resX = i;
                break;
            }
        }

        let resXB = 0;
        let resYB = 0;
        for (let i = startB;i <= endB; i++) {
            let y = centerY-(i - centerX)/k;
            console.log("y= "+y,"x= "+i,"k= "+k);
            if (-100 <=y && y<=0){
                resYB = y;
                resXB = i;
                break;
            }
        }
        //a 终止点  b开始点
        let a = {x:resX,y:-resY};
        let b = {x:resXB,y:-resYB};
        return {A:a,B:b};

    }

    /*监听直播流播放状态*/
    _onLivePlayerStatusChangeListener(status) {
        if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
            this.IMIVideoView.start();
        } else if (this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR && status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE) {
            //目前发现IOS ERROR后还会调用PAUSE，所以ERROR和暂停View重叠
            return;
        }
        this.setState({currentStatus: status});

    }

    //判断当前是否可以操作
    _canStepIn() {
        console.log('通话打印当前直播流状态----', this.state.isStartMove);
        if (this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING) {
            return true;
        }
        if (this.state.isSleep) {
            showToast(stringsTo('power_off'));
            return false;
        }
        if (!this.state.isOnline) {
            showToast(stringsTo('device_offline'));
            return false;
        }
        showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
        return false;
    }


    render() {
        return (<View style={{flex: 1, backgroundColor: '#FFFFFF'}}>
                <NavigationBar
                    title={stringsTo('cross_detect')}
                    left={[{
                        key: NavigationBar.ICON.BACK,
                        onPress: () => this.props.navigation.pop(),
                        accessibilityLabel: "fence_detect_switch_go_back"
                    }]}
                    right={[{
                        key: NavigationBar.ICON.CUSTOM,
                        n_source: require('../../resources/images/icon_complete.png'), onPress: () => {
                            console.log('平移--show:', this.state.minX, "minY:" + this.state.minY, "-maxX" + this.state.maxX, "-maxY" + this.state.maxY);
                            this.IMIVideoView && this.IMIVideoView.stop();

                            this._saveFence();

                        }, accessibilityLabel: "fence_detect_switch_COMPLETE"
                    }]}
                />
                {/*围栏侦测开关*/}
                <ListItmeWithSwitch title={stringsTo('cross_detect')} value={this.state.FenceSwitchValue}
                                    accessibilityLabel={["cross_detect_switch_off","cross_detect_switch_on"]}
                                    onValueChange={(value) => {
                                        if (IMIPackage.minApiLevel < 10005) {
                                            showToast(stringsTo("show_upgrade_app"))
                                            return
                                        }
                                        console.log("value:", value)
                                        this.setState({
                                            FenceSwitchValue: value
                                        })
                                        if (value == false) {
                                            this.IMIVideoView && this.IMIVideoView.stop();
                                        } else {
                                            this.timerVideoView = setTimeout(() => {
                                                this.IMIVideoView && this.IMIVideoView.prepare();
                                            }, 1000);
                                        }
                                    }}/>
                {/*直线、矩形*/}
                <ScrollView showsVerticalScrollIndicator={false} style={{flex: 1}}>

                    {this.state.FenceSwitchValue ? <View>
                        <SingleChoiceItem
                            title={stringsTo('line')}
                            titleColor={this.state.isLine ? "#4A70A5" : "#7F7F7F"}
                            containerStyle={{margin: 14, marginTop: 0, height: 60}}
                            checked={this.state.isLine}
                            onValueChange={(value) => {
                                console.log("value0", value);
                                this.setState({
                                    isLine: value
                                })
                            }}
                            accessibilityLabel={"housekeeping_assistant_time_interval_1_minutes"}
                        />
                        <SingleChoiceItem
                            title={stringsTo('rectangle')}
                            titleColor={!this.state.isLine ? "#4A70A5" : "#7F7F7F"}
                            containerStyle={{margin: 14, marginTop: 0, height: 60}}
                            checked={!this.state.isLine}
                            onValueChange={(value) => {
                                console.log("value1", value);
                                this.setState({
                                    isLine: !value
                                })
                            }}
                            accessibilityLabel={"housekeeping_assistant_time_interval_1_minutes"}
                        />
                    </View> : null}

                    <XText raw={true} style={{marginTop: 14, marginLeft: 14,marginRight:14, fontSize: 12, color: '#000', lineHeight: 16}}
                           allowFontScaling={false}
                           text={!this.state.FenceSwitchValue ? stringsTo('cross_detect_close_warning') : this.state.isLine ? stringsTo('cross_detect_line') : stringsTo('cross_detect_rectangle')}/>

                    <View style={{marginTop: 12, justifyContent: 'center', alignItems: 'center', marginHorizontal: 14}}>
                        <TinyWindowLivePlayer  {...this.props}
                                               videoRef={ref => this.IMIVideoView = ref}
                                               onLivePlayerStatusChange={(status) => this._onLivePlayerStatusChangeListener(status)}
                        />
                        {this.state.isLine ? this._renderLineGestureLayoutView() : this._renderGestureLayoutView()}
                    </View>
                    {/*{this.state.FenceSwitchValue ? this._renderShowImg() : null}*/}
                    {this.state.FenceSwitchValue ? this._renderOpDirection() : null}

                    {this.state.FenceSwitchValue ?
                        <ListItem title={stringsTo('soundLightAlarm')}
                                  value={this.state.soundLightSwitchValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                                  accessibilityLabel={this.state.soundLightSwitchValue ?'preset_opened': 'preset_closed'}
                                  onPress={() => {
                                      this.props.navigation.navigate('AudioAlarmSetPageV2', {
                                          location: this.state.location,
                                          soundLightSwitchValue: this.state.soundLightSwitchValue,
                                          voiceSwitch: this.state.voiceSwitch,
                                          voiceUrl: this.state.voiceUrl,
                                          lightSwitch: this.state.lightSwitch,
                                          lightMode: this.state.lightMode,
                                          containTime: this.state.containTime,
                                          callback: ((backParams) => {
                                              console.log('返回声光报警--', backParams);
                                              //声光报警的总开关
                                              let totalSwitch = backParams.totalSwitch;
                                              let tempVoiceSwitch = false;
                                              let tempLightSwitch = false;
                                              if (totalSwitch) {
                                                  //声光报警的开关打开了
                                                  tempVoiceSwitch = true;
                                                  tempLightSwitch = true;
                                              }
                                              this.setState({
                                                  soundLightSwitchValue: totalSwitch,
                                                  voiceSwitch: tempVoiceSwitch,
                                                  voiceUrl: backParams.voiceUrl,
                                                  lightSwitch: tempLightSwitch,
                                                  lightMode: backParams.spotlightMode,
                                                  containTime: backParams.containTime,
                                              })

                                          })
                                      })
                                  }}/> : null
                    }
                    {this.state.FenceSwitchValue ?
                        <ListItem title={stringsTo('timed_effective')}
                                  value={this.state.fenceSwitchEffectValue ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                                  accessibilityLabel={this.state.fenceSwitchEffectValue ? 'preset_opened':'preset_closed'}
                                  onPress={() => {

                                      this._gotoEffectiveTimeSetting();
                                  }}/> : null
                    }


                    {/*移动侦测推送开关*/}
                    {this.state.FenceSwitchValue ?
                        <ListItmeWithSwitch title={stringsTo('message_push')}
                                            value={this.state.infoPush}
                                            onValueChange={(value) => {
                                                console.log("----push value",value);
                                                this.setState({infoPush:value});
                                            }}
                                            accessibilityLabel={["housekeeping_assistant_motion_detecting_on", "housekeeping_assistant_motion_detecting_off"]}
                        /> : null
                    }
                </ScrollView>
            </View>


        );
    }

    /**
     * 矩形越界，绘制区域，手势控制
     */
    _renderGestureLayoutView() {
        if (!this.state.FenceSwitchValue) {
            return null;
        }
        return (<View style={{
            position: 'absolute',
            flexDirection: 'column',
            width: windowWidth - 28,
            height: '100%',
        }}>
            <View style={{
                marginTop: this.state.minY, flexDirection: 'row', zIndex: 2,
            }}>
                <View style={{
                    marginLeft: this.state.minX,
                    width: 10,
                    height: 10,
                    borderRadius: 5,
                    backgroundColor: this.state.drawColor
                }} hitSlop={{
                    left: 10, right: 10, top: 10, bottom: 10
                }} {...this.leftTopPanResponder.panHandlers}/>

                <View hitSlop={{
                    left: 10, right: 10, top: 10, bottom: 10
                }} style={{
                    marginLeft: this.state.maxX - 10, width: 10, height: 10, borderRadius: 5, backgroundColor: '#4A70A5'
                }}{...this.rightTopPanResponder.panHandlers}/>
            </View>

            <View style={{
                zIndex: 1,
                marginTop: -5,
                marginLeft: this.state.minX + 5,
                width: this.state.maxX,
                height: this.state.maxY, // backgroundColor: '#4A70A5',
                // opacity: 0.3,

            }} {...this.myPanResponder.panHandlers}/>

            <View style={{
                marginTop: -5, flexDirection: 'row', zIndex: 2,

            }}>
                <View hitSlop={{
                    left: 10, right: 10, top: 10, bottom: 10
                }} style={{
                    marginLeft: this.state.minX, width: 10, height: 10, borderRadius: 5, backgroundColor: '#4A70A5'
                }}{...this.leftBottomPanResponder.panHandlers}/>

                <View hitSlop={{
                    left: 10, right: 10, top: 10, bottom: 10
                }} style={{
                    marginLeft: this.state.maxX - 10, width: 10, height: 10, borderRadius: 5, backgroundColor: '#4A70A5'
                }}{...this.rightBottomPanResponder.panHandlers}/>
            </View>
            {this._renderRectView()}
            {this._renderShowImg()}


        </View>);
    }

    // 画矩形
    _renderRectView() {
        console.log('画矩形---', this.state.minX + 5, this.state.minY + 5, this.state.maxX, this.state.maxY);
        return (<View style={{
            position: 'absolute', width: '100%', height: '100%',
        }}>
            <Svg height="100%" width="100%">
                <Rect
                    x={this.state.minX + 5}
                    y={this.state.minY + 5}
                    width={this.state.maxX}
                    height={this.state.maxY}
                    stroke={"#4A70A5"}
                    strokeWidth="4"
                    strokeDasharray="10"
                    fill={"transparent"}
                />
            </Svg>
        </View>)
    }

    _renderShowImg() {
        // console.log('show--minX--minY---maxX--maxY',this.state.minX,this.state.minY,this.state.maxX,this.state.maxY);
        let marLeft = this.state.minX + (this.state.maxX - 20 + 5) / 2;
        let marTop = this.state.minY + this.state.maxY - 20 / 2 + 5;
        // console.log('上----左',marTop,marLeft);
        let imgPath;
        if (this.state.rectDir == 0) {
            imgPath = require("../../resources/images/imi_fence_up.png");
        } else if (this.state.rectDir == 1) {
            imgPath = require("../../resources/images/imi_fence_down.png");
        } else if (this.state.rectDir == 2) {
            imgPath = require("../../resources/images/imi_fence_change.png");
        } else if (this.state.rectDir == 3) {
            imgPath = require("../../resources/images/imi_fence_up.png");
        }
        return (<View style={{
            marginTop: -20,
            marginLeft: this.state.minX + this.state.maxX / 2,
            height: 20,
            width: 20,
            flexDirection: 'row'
        }}>
            <Image
                style={{width: 20, height: 20}}
                source={imgPath}
            />

        </View>)
    }

    //==========================直线============================
    //==========================越界============================
    _renderLineGestureLayoutView() {
        if (!this.state.FenceSwitchValue) {
            return null;
        }
        return (<View style={{
            position: 'absolute',
            flexDirection: 'column',
            width: '100%',
            height: '100%',
        }}>
            <View style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
            }}>
                <Svg height="100%" width="100%">
                    <G>

                        {this.state.lineArrData.map((item, index) => {
                            return this._renderPathLine(item, index);
                        })}
                    </G>
                </Svg>
            </View>


            {this.state.lineArrData.map((item, index) => {
                return this._renderItemLine(item, index);
            })}

            {this._renderPathView()}
        </View>);
    }

    // 顶部平移手势
    doTopPanResponder(evt, gestureState) {
        let item = this.state.lineArrData[this.state.pathIndex];
        this.setState({switchClickNum: item.dir});
        this.tempStartX = item.itemMoveToX;
        this.tempStartY = item.itemMoveToY;
        this.tempEndX = item.itemLineToX;
        this.tempEndY = item.itemLineToY;
        this.tempSecRoundX = item.itemRoundTwoX;
        this.tempSecRoundY = item.itemRoundTwoY;
        console.log('左上记录上次的值--', this.tempStartX, this.tempStartY, this.tempEndX, this.tempEndY);
        console.log('左上记录第二个圆点的值--', this.tempSecRoundX, this.tempSecRoundY);
        console.log('左上移动---dx--dy-', gestureState.dx, gestureState.dy);
        let minX = this.tempStartX + gestureState.dx;
        console.log('左上Minx-最小值--', minX, this.tempStartX, gestureState.dx);
        minX = minX < 0 ? 0 : minX;
        console.log('左上Minx-最小值1---', minX);
        minX = minX > (windowWidth - 28) ? (windowWidth - 28) : minX;
        console.log('左上Minx-最小值2---', minX);

        let maxX = windowWidth - 28 - minX;
        console.log('左上MaxX---最大值--', maxX);

        let minY = this.tempStartY + gestureState.dy;
        console.log('左上MinY--最小值---', minY, this.tempStartY, gestureState.dy);
        minY = minY < 0 ? 0 : minY
        console.log('左上MinY--最小值1---', minY);
        minY = minY > Height ? Height : minY;
        console.log('左上MinY--最小值2---', minY, Height);
        let maxY = Height - minY;
        console.log('左上的MAXY--最大值--', maxY);

        console.log('当前minx---endX', minX, this.tempEndX);
        console.log('当前minY---endX', minY, this.tempEndY);

        let angleFinal;
        if (minX == this.tempEndX) {
            angleFinal = "90deg";
            this.setState({imgAngle: "90deg"});
        } else if (minY == this.tempEndY) {
            if (minX < this.tempEndX) {
                angleFinal = "0deg";
                this.setState({imgAngle: "0deg"});
            } else {
                angleFinal = "180deg";
                this.setState({imgAngle: "180deg"});
            }
        } else {
            let angleY = Math.abs(parseInt(Math.atan2((this.tempEndX - minX), (this.tempEndY - minY)) * (180 / Math.PI)));
            // let angleY = parseInt(Math.atan2((this.tempEndX-minX),(this.tempEndY-minY)) * (180 / Math.PI));
            console.log('上面角度与-与Y轴夹角---', angleY);
            let angleYNew = angleY + 'deg';

            let angleX = Math.abs(parseInt(Math.atan2((this.tempEndY - minY), (this.tempEndX - minX)) * (180 / Math.PI)));
            // let angleX = parseInt(Math.atan2((this.tempEndY-minY),(this.tempEndX-minX)) * (180 / Math.PI));
            // let angleXNew = angleX + 'deg';
            let angleXNew = '';
            if (angleY > 90) {
                angleXNew = '-' + angleX + 'deg';
            } else {
                angleXNew = angleX + 'deg';
            }
            angleFinal = angleXNew;
            this.setState({imgAngle: angleXNew});
            console.log('当前上面角度与X轴夹角计算---', angleX, angleXNew);
        }


        let tempA = (this.tempEndX - minX) * (this.tempEndX - minX);
        // console.log('左上勾股定理A--',tempA,minX,this.tempEndX);
        let tempB = (this.tempEndY - minY) * (this.tempEndY - minY);
        // console.log('左上勾股定理B--',tempB,minY,this.tempEndY);
        let tempC = minWidth * minWidth;
        // console.log('勾股定理X,Y值',tempA,tempB,tempC);
        if (tempA + tempB < tempC) {
            console.log('左上不满足勾股定理', tempA, tempB, tempC);
            Toast.show(stringsTo("imi_distance_tip"));
            // this.setState({showLineTip:true});
            // this.setState({minX:minX,minY: minY,maxX: maxX,maxY: maxY,firstX:minX-5,firstY:minY-5,startX:minX,startY:minY,endX:this.tempEndX,endY:this.tempEndY,showLineTip:true})
        } else {
            // if (tempA + tempB > maxLength) {
            //     this._updateAddLineVisible(true);
            // } else {
            //     this._updateAddLineVisible(false);
            // }
            console.log('左上大于勾股定理', tempA, tempB, tempC);
            console.log('更新前数组--', this.state.lineArrData);
            for (let i = 0; i < this.state.lineArrData.length; i++) {
                if (i == this.state.pathIndex) {
                    console.log('第几个--', i);
                    let item = this.state.lineArrData[i];
                    console.log('更新前,item', item);
                    item.itemMoveToX = minX;
                    item.itemMoveToY = minY;
                    item.itemLineToX = this.tempSecRoundX;
                    item.itemLineToY = this.tempSecRoundY;
                    item.itemRoundOneX = minX;
                    item.itemRoundOneY = minY;
                    item.itemAngle = angleFinal;
                    if (this.state.lineArrData.length == 1) {
                        item.isShowLine = false;
                        item.isEditLine = true;
                    } else {
                        item.isShowLine = true;
                        item.isEditLine = true;
                    }

                    // item.itemRoundTwoX = this.tempSecRoundX;
                    // item.itemRoundTwoY = this.tempSecRoundY;
                    console.log('更新后,item', item);
                }
            }
            //     console.log('更新后数组--',this.state.lineArrData);
            this.setState({minX: minX, minY: minY, maxX: maxX, maxY: maxY}, callback => {
                // console.log('左角度----',angle);
                // console.log('左上最终----minX---maxX-minY----maxY',this.state.minX,this.state.maxX,this.state.minY,this.state.maxY);
                // console.log('左上上第一个圆点坐标和第二个圆点坐标',this.state.firstX,this.state.firstY,this.state.secX,this.state.secY);
                // console.log('左上最终画线坐标--',this.state.startX,this.state.startY,this.state.endX,this.state.endY);
            });
        }
    }

    // 中心平移手势
    doCenterPanResponder(evt, gestureState) {
        let item = this.state.lineArrData[this.state.pathIndex];
        this.setState({switchClickNum: item.dir});
        this.tempStartX = item.itemMoveToX;
        this.tempStartY = item.itemMoveToY;
        this.tempEndX = item.itemLineToX;
        this.tempEndY = item.itemLineToY;

        let minX = this.tempStartX + gestureState.dx;
        console.log('平移Minx-最小值--', minX, this.tempStartX, gestureState.dx);
        minX = minX < 0 ? 0 : minX;
        console.log('平移Minx-最小值1---', minX);
        minX = minX > (windowWidth - 28) ? (windowWidth - 28) : minX;
        console.log('平移Minx-最小值2---', minX);

        let minY = this.tempStartY + gestureState.dy;
        console.log('平移MinY--最小值---', minY, this.tempStartY, gestureState.dy);
        minY = minY < 0 ? 0 : minY;
        console.log('平移MinY--最小值1---', minY);
        minY = minY > Height ? Height : minY;
        console.log('平移MinY--最小值2---', minY, Height);

        let maxX = this.tempEndX + gestureState.dx;
        console.log('平移MaxX--最大值---', maxX, this.tempEndX, gestureState.dx);
        maxX = maxX < 0 ? 0 : maxX;
        console.log('平移MaxX--最大值1---', maxX);
        maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
        console.log('平移MaxX--最大值2---', maxX);

        let maxY = this.tempEndY + gestureState.dy;
        console.log('平移MAXY--最大值---', maxY, this.tempEndY, gestureState.dy);
        maxY = maxY < 0 ? 0 : maxY;
        console.log('平移MAXY--最大值1---', maxY);
        maxY = maxY > Height ? Height : maxY;
        console.log('平移MAXY--最大值2---', maxY, Height);

        console.log('底部当前maxX---startX', maxX, this.tempStartX);
        console.log('底部当前maxY---startY', maxY, this.tempStartY);

        let angleFinal;
        if (minX == maxX) {
            angleFinal = "90deg";
            this.setState({imgAngle: "90deg"});
        } else if (maxY == minY) {
            if (maxX > minX) {
                angleFinal = "0deg";
                this.setState({imgAngle: "0deg"});
            } else {
                angleFinal = "180deg";
                this.setState({imgAngle: "180deg"});
            }
        } else {
            let angleY = Math.abs(parseInt(Math.atan2((maxX - minX), (maxY - minY)) * (180 / Math.PI)));
            // let angleY = parseInt(Math.atan2((this.tempEndX-minX),(this.tempEndY-minY)) * (180 / Math.PI));
            console.log('平行角度与-与Y轴夹角---', angleY);
            let angleYNew = angleY + 'deg';
            let angleX = Math.abs(parseInt(Math.atan2((maxY - minY), (maxX - minX)) * (180 / Math.PI)));
            // let angleX = parseInt(Math.atan2((maxY-minY),(maxX-minX)) * (180 / Math.PI));
            // let angleXNew = angleX + 'deg';
            let angleXNew = '';
            if (angleY > 90) {
                angleXNew = '-' + angleX + 'deg';
            } else {
                angleXNew = angleX + 'deg';
            }
            angleFinal = angleXNew;
            this.setState({imgAngle: angleXNew});
            console.log('当前平行角度与X轴夹角计算---', angleX);
        }

        let tempA = (maxX - minX) * (maxX - minX);
        console.log('平移勾股定理A--', tempA, minX, maxX);
        let tempB = (maxY - minY) * (maxY - minY);
        console.log('平移勾股定理B--', tempB, minY, maxY);
        let tempC = minWidth * minWidth;
        console.log('勾股定理X,Y值', tempA, tempB, tempC);
        if (tempA + tempB < tempC) {
            Toast.show(stringsTo("imi_distance_tip"));
            // this.setState({firstX:minX-5,firstY:minY-5,secX:maxX == (windowWidth-28) ? (windowWidth-28) : maxX,secY:maxY-5,startX:minX,startY:minY,endX:maxX,endY:maxY,showLineTip:true});
        } else {
            // if (tempA + tempB > maxLength) {
            //     this._updateAddLineVisible(true);
            // } else {
            //     this._updateAddLineVisible(false);
            // }
            for (let i = 0; i < this.state.lineArrData.length; i++) {
                if (i == this.state.pathIndex) {
                    console.log('第几个--', i);
                    let item = this.state.lineArrData[i];
                    console.log('平移更新前,item', item);
                    item.itemMoveToX = minX;
                    item.itemMoveToY = minY;
                    item.itemLineToX = maxX;
                    item.itemLineToY = maxY;
                    item.itemRoundOneX = minX;
                    item.itemRoundOneY = minY;
                    item.itemRoundTwoX = maxX;
                    item.itemRoundTwoY = maxY;
                    item.itemAngle = angleFinal;
                    if (this.state.lineArrData.length == 1) {
                        item.isShowLine = false;
                        item.isEditLine = true;
                    } else {
                        item.isShowLine = true;
                        item.isEditLine = true;
                    }
                    console.log('平移更新后,item', item);
                }
            }
        }
    }

    // 底部手势
    doBottomPanResponder(evt, gestureState) {
        let item = this.state.lineArrData[this.state.pathIndex];
        console.log('当前选中的---', this.state.pathIndex);
        this.setState({switchClickNum: item.dir});
        this.tempStartX = item.itemMoveToX;
        this.tempStartY = item.itemMoveToY;
        this.tempEndX = item.itemLineToX;
        this.tempEndY = item.itemLineToY;
        console.log('底部记录上次的值--', this.tempStartX, this.tempStartY, this.tempEndX, this.tempEndY);
        let maxX = this.tempEndX + gestureState.dx;
        console.log('底部MaxX--最大值---', maxX, this.tempEndX, gestureState.dx);
        maxX = maxX < 0 ? 0 : maxX;
        console.log('底部MaxX--最大值1---', maxX);
        maxX = maxX > (windowWidth - 28) ? (windowWidth - 28) : maxX;
        console.log('底部MaxX--最大值2---', maxX);

        let minX = windowWidth - 28 - maxX;
        console.log('底部MinX--最小值---', minX);

        let maxY = this.tempEndY + gestureState.dy;
        console.log('底部的MAXY--最大值---', maxY, this.tempEndY, gestureState.dy);
        maxY = maxY < 0 ? 0 : maxY;
        console.log('底部的MAXY--最大值1---', maxY);
        maxY = maxY > Height ? Height : maxY;
        console.log('底部的MAXY--最大值2---', maxY, Height);
        let minY = Height - maxY;
        console.log('底部MinY--最小值---', minY);

        console.log('底部当前maxX---startX', maxX, this.tempStartX);
        console.log('底部当前maxY---startY', maxY, this.tempStartY);

        let angleFinal;
        if (maxX == this.tempStartX) {
            angleFinal = "90deg";
            this.setState({imgAngle: "90deg"});
        } else if (maxY == this.tempStartY) {
            if (maxX < this.tempStartX) {
                angleFinal = "180deg";
                this.setState({imgAngle: "180deg"});
            } else {
                angleFinal = "0deg";
                this.setState({imgAngle: "0deg"});
            }
        } else {
            let angleY = Math.abs(parseInt(Math.atan2((maxX - this.tempStartX), (maxY - this.tempStartY)) * (180 / Math.PI)));
            // let angleY = parseInt(Math.atan2((this.tempEndX-minX),(this.tempEndY-minY)) * (180 / Math.PI));
            console.log('底部角度与-与Y轴夹角---', angleY);
            let angleYNew = angleY + 'deg';
            let angleX = Math.abs(parseInt(Math.atan2((this.tempStartY - maxY), (maxX - this.tempStartX)) * (180 / Math.PI)));
            // let angleX = parseInt(Math.atan2((this.tempStartY-maxY),(maxX-this.tempStartX)) * (180 / Math.PI));

            // let angleXNew = angleX + 'deg';

            let angleXNew = '';
            if (angleY > 90) {
                angleXNew = '-' + angleX + 'deg';
            } else {
                angleXNew = angleX + 'deg';
            }
            angleFinal = angleXNew;
            this.setState({imgAngle: angleXNew});
            console.log('当前底部角度与X轴夹角计算---', angleX);
        }

        let tempA = (maxX - this.tempStartX) * (maxX - this.tempStartX);
        console.log('底部勾股定理A--', tempA, maxX, this.tempStartX);
        let tempB = (maxY - this.tempStartY) * (maxY - this.tempStartY);
        console.log('底部勾股定理B--', tempB, maxY, this.tempStartY);
        let tempC = minWidth * minWidth;
        console.log('底部勾股定理X,Y值', tempA, tempB, tempC);
        if (tempA + tempB < tempC) {
            console.log('底部不满足勾股定理', tempA, tempB, tempC);
            Toast.show(stringsTo("imi_distance_tip"));
        } else {
            // if (tempA + tempB > maxLength) {
            //     this._updateAddLineVisible(true);
            // } else {
            //     this._updateAddLineVisible(false);
            // }
            console.log('底部大于勾股定理', tempA, tempB, tempC);
            for (let i = 0; i < this.state.lineArrData.length; i++) {
                if (i == this.state.pathIndex) {
                    console.log('底部第几个--', i);
                    let item = this.state.lineArrData[i];
                    console.log('底部更新前,item', item);
                    item.itemLineToX = maxX;
                    item.itemLineToY = maxY;
                    item.itemRoundTwoX = maxX;
                    item.itemRoundTwoY = maxY;
                    item.itemAngle = angleFinal;
                    if (this.state.lineArrData.length == 1) {
                        item.isShowLine = false;
                    } else {
                        item.isShowLine = true;
                    }
                    item.isEditLine = true;
                    // item.itemRoundTwoX = this.tempSecRoundX;
                    // item.itemRoundTwoY = this.tempSecRoundY;
                    console.log('底部更新后,item', item);
                }
            }
        }
    }

    _renderItemLine(item, index) {
        // let path = ART.Path();
        // path.moveTo(item.itemMoveToX,item.itemMoveToY); //将起始点移动到(1,1) 默认(0,0)
        // path.lineTo(item.itemLineToX,item.itemLineToY); //连线到目标点(300,1)

        let descriptions = [
            'M',
            item.itemMoveToX,
            item.itemMoveToY,
            'L',
            item.itemLineToX,
            item.itemLineToY,
        ].join(' ');

        let marLeft = (item.itemMoveToX + item.itemLineToX - 20) / 2;
        let marTop = (item.itemMoveToY + item.itemLineToY - 20) / 2;
        let imgPath;

        if (item.dir == 0) {
            // imgPath = require("../../resources/Images/imi_fence_right.png");
            imgPath = require("../../resources/images/imi_fence_up.png");
        } else if (item.dir == 1) {
            imgPath = require("../../resources/images/imi_fence_down.png");
        } else if (item.dir == 2) {
            imgPath = require("../../resources/images/imi_fence_change.png");
        } else if (item.dir == 3) {
            imgPath = require("../../resources/images/imi_fence_up.png");
        }

        let centerPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => {
                return true;
                console.log('循环中心手势--onStartShouldSetPanResponder');
                // return false;
            },
            onStartShouldSetPanResponderCapture: (evt, gestureState) => {
                console.log('循环中心手势--onStartShouldSetPanResponderCapture');
                return true;
            },
            onMoveShouldSetPanResponder: (evt, gestureState) => {
                return false;
                console.log('循环中心手势--onMoveShouldSetPanResponder');
                // return false;
            },
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
                console.log('循环中心手势--onMoveShouldSetPanResponderCapture');
                return true;
            },
            onPanResponderTerminationRequest: (evt, gestureState) => {
                console.log('循环中心手势--onPanResponderTerminationRequest');
                // return false;
                return true;
            },
            // onPanResponderTerminationRequest: (evt, gestureState) => true,

            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('平移----第几个---', index, item);
                this.tempIndex = index;
                console.log('循环平移手势当前线段选择', this.tempIndex, this.state.pathIndex);
                if (this.state.pathIndex == index) {
                    //同一条线
                    // item.isShowLine = false;
                    // this.setState({isPathEdit: true});
                    // console.log('循环平移同一条线');
                    // return;

                    this.doCenterPanResponder(evt, gestureState);

                } else {
                    // 不是同一条线
                    let oldItem = this.state.lineArrData[this.state.pathIndex];
                    oldItem.isShowLine = true;
                    oldItem.isEditLine = false;
                    console.log('平移上次选中---', this.state.pathIndex);
                    this.setState({pathIndex: index}, callback => {
                        this.doCenterPanResponder(evt, gestureState);
                    });
                }

                this.forceUpdate();
                if (evt.nativeEvent.changedTouches.length > 1) { // 双指时的中心点
                    console.log('平移中心点');
                    let distantX = evt.nativeEvent.changedTouches[1].pageX - evt.nativeEvent.changedTouches[0].pageX;
                    let distantY = evt.nativeEvent.changedTouches[1].pageY - evt.nativeEvent.changedTouches[0].pageY;
                    this.touchDownDistant = Math.sqrt(distantX * distantX + distantY * distantY);
                    // console.log("双指:downDistance" + this.touchDownDistant);
                }
            },
            onPanResponderMove: (evt, gestureState) => {
                console.log('开始移动---');
                console.log('平移移动距离---', gestureState.dx, gestureState.dy);
                console.log('平移移动----第几个---', index, item);
                // if (this.tempIndex == index) {
                //     console.log('平移不用做处理---', this.tempIndex, index);
                // }

                this.tempIndex = index;
                console.log('循环平移手势当前线段选择', this.tempIndex, this.state.pathIndex);
                if (this.state.pathIndex == index) {
                    //同一条线
                    // item.isShowLine = false;
                    // this.setState({isPathEdit: true});
                    // console.log('循环平移同一条线');
                    // return;

                    this.doCenterPanResponder(evt, gestureState);

                } else {
                    // 不是同一条线
                    let oldItem = this.state.lineArrData[this.state.pathIndex];
                    oldItem.isShowLine = true;
                    oldItem.isEditLine = false;
                    console.log('平移上次选中---', this.state.pathIndex);
                    this.setState({pathIndex: index}, callback => {
                        this.doCenterPanResponder(evt, gestureState);
                    });
                }
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
                console.log('抬手平移');
                this.zoomLastDistance = 0;
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
                console.log('另一个组件已经成为了新的响应者平移,', this.tempIndex);
                return true;
            }
        });
        let topPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => {
                console.log('循环上面手势--onPanResponderTerminationRequest');
                return true;
            },
            onStartShouldSetPanResponderCapture: (evt, gestureState) => {
                console.log('循环上面手势--onStartShouldSetPanResponderCapture');
                // return true;
                return false;
            },
            onMoveShouldSetPanResponder: (evt, gestureState) => {
                console.log('循环上面手势--onMoveShouldSetPanResponder');
                // return true;
                return false;
            },
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
                console.log('循环上面手势--onMoveShouldSetPanResponderCapture');
                return true;
            },
            onPanResponderTerminationRequest: (evt, gestureState) => {
                console.log('循环上面手势--onPanResponderTerminationRequest');
                return true;
            },

            onPanResponderGrant: (evt, gestureState) => {
                console.log('循环上面手势----第几个---', index, item);
                this.tempIndex = index;
                console.log('循环上面手势当前线段选择', this.tempIndex, this.state.pathIndex);
                this.tempIndex = index;
                console.log('循环平移手势当前线段选择', this.tempIndex, this.state.pathIndex);
                if (this.state.pathIndex == index) {
                    //同一条线
                    // item.isShowLine = false;
                    // this.setState({isPathEdit: true});
                    // console.log('循环平移同一条线');
                    // return;

                    this.doTopPanResponder(evt, gestureState);

                } else {
                    // 不是同一条线
                    let oldItem = this.state.lineArrData[this.state.pathIndex];
                    oldItem.isShowLine = true;
                    oldItem.isEditLine = false;
                    console.log('平移上次选中---', this.state.pathIndex);
                    this.setState({pathIndex: index}, callback => {
                        this.doTopPanResponder(evt, gestureState);
                    });
                }

                // if (this.state.pathIndex == index) {
                //     //同一条线
                //     item.isShowLine = false;
                //     this.setState({isPathEdit: true});
                //     console.log('循环上面手势同一条线');
                //     return;
                // } else {
                //     // 不是同一条线
                //     let oldItem = this.state.lineArrData[this.state.pathIndex];
                //     oldItem.isShowLine = true;
                //     oldItem.isEditLine = false;
                //     console.log('循环上面手势上次选中---', this.state.pathIndex);
                //     item.isShowLine = false;
                //     this.setState({pathIndex: index, isPathEdit: true});
                // }
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                console.log('循环上面移动---dx--dy-', gestureState.dx, gestureState.dy);
                console.log('循环上面移动手势----第几个---', index, item);
                this.tempIndex = index;
                console.log('循环平移手势当前线段选择', this.tempIndex, this.state.pathIndex);
                if (this.state.pathIndex == index) {
                    //同一条线
                    // item.isShowLine = false;
                    // this.setState({isPathEdit: true});
                    // console.log('循环平移同一条线');
                    // return;

                    this.doTopPanResponder(evt, gestureState);

                } else {
                    // 不是同一条线
                    let oldItem = this.state.lineArrData[this.state.pathIndex];
                    oldItem.isShowLine = true;
                    oldItem.isEditLine = false;
                    console.log('平移上次选中---', this.state.pathIndex);
                    this.setState({pathIndex: index}, callback => {
                        this.doTopPanResponder(evt, gestureState);
                    });
                }

                // if (this.tempIndex == index) {
                //     console.log('循环上面不用做处理---', this.tempIndex, index);
                // }
            },
            onPanResponderRelease: (evt, gestureState) => {
                console.log('循环上面离开---dx--dy-', gestureState.dx, gestureState.dy);
                console.log('循环上面抬手');
            },
            onPanResponderTerminate: (evt, gestureState) => {
                console.log('循环上面另一个组件');
                return true;
            }
        });


        let bottomPanResponder = PanResponder.create({
            // 要求成为响应者：
            onStartShouldSetPanResponder: (evt, gestureState) => {
                console.log('循环底部手势--onPanResponderTerminationRequest');
                console.log('1循环底部图标点击第几个---', item, index);
                return true;
            },
            onStartShouldSetPanResponderCapture: (evt, gestureState) => {
                console.log('循环底部手势--onStartShouldSetPanResponderCapture');
                // return true;
                console.log('2循环底部图标点击第几个---', item, index);
                return true;
            },
            onMoveShouldSetPanResponder: (evt, gestureState) => {
                console.log('循环底部手势--onMoveShouldSetPanResponder');
                return true;
            },
            onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
                console.log('循环底部手势--onMoveShouldSetPanResponderCapture');
                return true;
            },
            onPanResponderTerminationRequest: (evt, gestureState) => {
                console.log('循环底部手势--onPanResponderTerminationRequest');
                return true;
            },
            onPanResponderTerminate: (evt, gestureState) => {
                // this.setState( {eventName:'另一个组件已经成为了新的响应者'} )
                console.log('底部其他组件');
                return true;
            },
            // 响应对应事件后的处理:
            onPanResponderGrant: (evt, gestureState) => {
                console.log('循环底部图标点击第几个---', item, index);
                console.log('循环底部处理--show--minX--minY---maxX--maxY', this.state.minX, this.state.minY, this.state.maxX, this.state.maxY);
                this.tempIndex = index;
                console.log('循环底部手势当前线段选择', this.tempIndex, this.state.pathIndex);
                if (this.state.pathIndex == index) {
                    //同一条线
                    // item.isShowLine = false;
                    // this.setState({isPathEdit: true});
                    // console.log('循环底部手势同一条线');
                    // return;
                    // 新的修改
                    this.doBottomPanResponder(evt, gestureState);

                } else {
                    let oldItem = this.state.lineArrData[this.state.pathIndex];
                    oldItem.isShowLine = true;
                    oldItem.isEditLine = false;
                    console.log('不是同一条线---', index);
                    this.setState({pathIndex: index}, callback => {
                        console.log('不是同一条线--最终', this.state.pathIndex);
                        this.doBottomPanResponder(evt, gestureState);
                    })
                }
                this.forceUpdate();
            },
            onPanResponderMove: (evt, gestureState) => {
                console.log('循环底部移动距离---', gestureState.dx, gestureState.dy);
                console.log('循环底部图标点击移动第几个---', item, index);
                this.tempIndex = index;
                console.log('循环底部手势当前线段选择', this.tempIndex, this.state.pathIndex);
                if (this.state.pathIndex == index) {
                    //同一条线
                    // item.isShowLine = false;
                    // this.setState({isPathEdit: true});
                    // console.log('循环底部手势同一条线');
                    // return;
                    // 新的修改
                    this.doBottomPanResponder(evt, gestureState);
                } else {
                    // 不是同一条线

                    // console.log('底部上次选中---', this.state.pathIndex);
                    // item.isShowLine = false;
                    // this.setState({pathIndex: index, isPathEdit: true});
                    let oldItem = this.state.lineArrData[this.state.pathIndex];
                    oldItem.isShowLine = true;
                    oldItem.isEditLine = false;
                    console.log('不是同一条线---', index);
                    this.setState({pathIndex: index}, callback => {
                        console.log('不是同一条线--最终', this.state.pathIndex);
                        this.doBottomPanResponder(evt, gestureState);
                    })
                }
            },
            onPanResponderRelease: (evt, gestureState) => {
                // this.setState( {eventName:'抬手'} );
                // console.log('底部抬手');
                // this.setState({isPathEdit:false});
            },
        });

        return (<View key={`key_${index}`}>
            {item.isShowLine ?
                (<View style={{
                    marginTop: item.itemMoveToY - (item.isEditLine ? 8 : 5), zIndex: 2,
                    position: 'absolute'
                }}>
                    <View style={{
                        marginLeft: item.itemMoveToX - (item.isEditLine ? 8 : 5),
                        width: item.isEditLine ? 16 : 10,
                        height: item.isEditLine ? 16 : 10,
                        borderRadius: item.isEditLine ? 16 : 5,
                        backgroundColor: '#1E5BA9',
                    }}
                          hitSlop={{
                              left: 10,
                              right: 10,
                              top: 10,
                              bottom: 10
                          }} {...topPanResponder.panHandlers}

                    />
                </View>) : null
            }
            {item.isShowLine ? (<View style={{
                marginTop: item.itemLineToY - (item.isEditLine ? 8 : 5), zIndex: 2,
                position: 'absolute'
            }}>
                <View style={{
                    marginLeft: item.itemLineToX - (item.isEditLine ? 8 : 5),
                    width: item.isEditLine ? 16 : 10,
                    height: item.isEditLine ? 16 : 10,
                    borderRadius: item.isEditLine ? 16 : 5,
                    backgroundColor: '#1E5BA9'
                }} hitSlop={{
                    left: 10,
                    right: 10,
                    top: 10,
                    bottom: 10
                }} {...bottomPanResponder.panHandlers}
                />
            </View>) : null}

            {item.isShowLine ? (<View style={{
                position: 'absolute',
                marginTop: marTop,
                marginLeft: marLeft,
                height: 20,
                width: 20,
                flexDirection: 'row',
            }} hitSlop={{
                left: 15,
                right: 15,
                top: marTop,
                bottom: marTop
            }}{...centerPanResponder.panHandlers}>

                <Image
                    style={{
                        width: 20,
                        height: 20,
                        transform: [{rotate: item.itemAngle}]
                    }}
                    source={imgPath}
                />
            </View>) : null}

        </View>)
    }

    _renderPathLine(item, index) {
        if (!item.isShowLine) {
            return null;
        }

        let descriptions = [
            'M',
            item.itemMoveToX,
            item.itemMoveToY,
            'L',
            item.itemLineToX,
            item.itemLineToY,
        ].join(' ');

        return (
            <Path
                key={index}
                d={descriptions}
                stroke={"#1E5BA9"}
                strokeWidth={2}
                strokeDasharray={item.isEditLine ? 10 : 0}
            />
        )
    }

    _renderPathView() {
        if (!this.state.isPathEdit) {
            return;
        }
        let itemPath = this.state.lineArrData[this.state.pathIndex];
        let descriptions = [
            'M',
            itemPath.itemMoveToX,
            itemPath.itemMoveToY,
            'L',
            itemPath.itemLineToX,
            itemPath.itemLineToY,
        ].join(' ');

        let marLeft = (itemPath.itemRoundOneX + itemPath.itemRoundTwoX - 20) / 2;
        let marTop = (itemPath.itemRoundOneY + itemPath.itemRoundTwoY - 20) / 2;
        let imgPath;
        if (this.state.switchClickNum == 0) {
            // imgPath = require("../../resources/Images/imi_fence_right.png");
            imgPath = require("../../resources/images/imi_fence_up.png");
        } else if (this.state.switchClickNum == 1) {
            imgPath = require("../../resources/images/imi_fence_down.png");
        } else if (this.state.switchClickNum == 2) {
            imgPath = require("../../resources/images/imi_fence_change.png");
        } else if (this.state.switchClickNum == 3) {
            imgPath = require("../../resources/images/imi_fence_up.png");
        }

        // console.log('itemPath--',itemPath);
        return (
            <View style={{
                position: 'absolute',
                flexDirection: 'column',
                width: '100%',
                height: '100%',
            }}>
                <View style={{marginTop: itemPath.itemRoundOneY - 8, zIndex: 2, position: 'absolute'}}>
                    <View style={{
                        marginLeft: itemPath.itemRoundOneX - 8,
                        width: 16,
                        height: 16,
                        borderRadius: 8,
                        backgroundColor: '#1E5BA9',
                    }}
                          hitSlop={{
                              left: 10,
                              right: 10,
                              top: 10,
                              bottom: 10
                          }}
                          {...this.topPanResponder.panHandlers}/>
                </View>

                <View style={{marginTop: itemPath.itemRoundTwoY - 8, zIndex: 2, position: 'absolute'}}>
                    <View style={{
                        marginLeft: itemPath.itemRoundTwoX - 8,
                        width: 16,
                        height: 16,
                        borderRadius: 8,
                        backgroundColor: '#1E5BA9'
                    }}
                          hitSlop={{
                              left: 10,
                              right: 10,
                              top: 10,
                              bottom: 10
                          }}
                          {...this.bottomPanResponder.panHandlers}
                    />
                </View>


                <View style={{
                    position: 'absolute',
                    width: '100%',
                    height: '100%',
                }}
                      hitSlop={{
                          left: 30,
                          right: 30,
                          top: 30,
                          bottom: 30
                      }}
                      {...this.myPanResponder.panHandlers}
                >
                    <Svg height="100%" width="100%">
                        <Path
                            d={descriptions}
                            stroke={'#1E5BA9'}
                            strokeWidth={2}
                            strokeDasharray={10}
                        />
                    </Svg>
                </View>


                <View style={{
                    position: 'absolute',
                    marginTop: marTop,
                    marginLeft: marLeft,
                    height: 20,
                    width: 20,
                    flexDirection: 'row',
                }}
                      hitSlop={{
                          left: 10,
                          right: 10,
                          top: 10,
                          bottom: 10
                      }}
                      {...this.myPanResponder.panHandlers}
                >

                    <Image
                        style={{
                            width: 20,
                            height: 20,
                            transform: [{rotate: this.state.imgAngle}]
                        }}
                        source={imgPath}

                    />
                </View>

            </View>)
    }

    /**
     * 功能按钮
     * 删除、切换方向、添加越界线
     */
    _renderOpDirection() {
        return (<View style={{flexDirection: "row", height: 50, alignItems: 'center', marginHorizontal: 14}}>
            {this.state.isLine && this.state.deleteLineVisible ? <TouchableOpacity
                style={{
                    flexDirection: "row",
                    height: 50,
                    alignItems: 'center',
                    justifyContent: 'flex-start',
                    flex:1
                }}
                onPress={() => {
                    console.log('删除');
                    let curArr = this.state.lineArrData;
                    console.log('删除前数组---', curArr);
                    let newArr = curArr.splice(this.state.pathIndex, 1);
                    let selectNum = this.state.lineArrData.length - 1;
                    let selectItem = this.state.lineArrData[selectNum];
                    selectItem.isShowLine = false;
                    selectItem.isEditLine = true;
                    this.setState({
                        pathIndex: selectNum,
                        isPathEdit: true,
                        switchClickNum: selectItem.dir,
                        addLineVisible: true,
                        deleteLineVisible: false,
                        imgAngle: selectItem.itemAngle,
                    }, callback => {
                        console.log('删除选中num', selectNum);
                    });
                }}>
                <Image
                    style={{width: 20, height: 20}}
                    source={require("../../resources/images/icon_cross_delete.png")}
                />
                <View style={{flex:1}}>
                    <Text
                        numberOfLines={2}
                        allowFontScaling={false}
                        style={{
                            marginLeft: 3,
                            textAlign: 'left', textAlignVertical: 'center', color: '#333333', fontSize: 12,
                        }}>{stringsTo("delete")}</Text>
                </View>
            </TouchableOpacity> : null}
            <TouchableOpacity
                style={[{
                    flexDirection: "row",
                    height: 50,
                    alignItems: 'center',
                    flex:1,
                },this.state.isLine && this.state.addLineVisible?{justifyContent: 'flex-start'}:{justifyContent: 'flex-end'}]}
                onPress={() => {
                    console.log('切换方向');
                    if (this.state.isLine) {
                        //直线
                        let tempItem = this.state.lineArrData[this.state.pathIndex];
                        console.log('当前选中线的方向---', tempItem.dir, this.state.switchClickNum);
                        let clickNum = this.state.switchClickNum + 1;
                        // clickNum += 1;
                        console.log('点击次数===', clickNum, this.state.switchClickNum);
                        if (clickNum == 3) {
                            console.log('当前点击次数---', clickNum);
                            this.setState({switchClickNum: 0})
                            tempItem.dir = 0;
                        } else {
                            tempItem.dir = clickNum;
                            this.setState({switchClickNum: clickNum}, callback => {
                                console.log('当前次数---', this.state.switchClickNum, tempItem.dir);
                            })
                        }
                    } else {
                        //矩形
                        let clickNum = this.state.rectDir + 1;
                        console.log('点击次数===', clickNum, this.state.rectDir);
                        if (clickNum == 3) {
                            console.log('当前点击次数---', clickNum);
                            this.setState({rectDir: 0})
                        } else {
                            this.setState({rectDir: clickNum}, callback => {
                                console.log('当前次数---', this.state.switchClickNum);
                            })
                        }
                    }
                }}>
                <Image
                    style={{width: 20, height: 20}}
                    source={require("../../resources/images/imi_switch_det.png")}
                />
                <View style={{flex:1}}>
                    <Text
                        numberOfLines={2}
                        allowFontScaling={false}
                        style={{
                            marginLeft: 3, // lineHeight:19,
                            textAlign: 'left', textAlignVertical: 'center', color: '#333333', fontSize: 12,
                        }}>{stringsTo("imi_switch_dection")}</Text>
                </View>
            </TouchableOpacity>
            {/*<View style={{flex: 1}}></View>*/}

            {this.state.isLine && this.state.addLineVisible ? <TouchableOpacity
                style={{
                    flexDirection: "row",
                    height: 50,
                    alignItems: 'center',
                    flex:1,
                    justifyContent: 'flex-end'
                }}
                onPress={() => {
                    //添加越界线,最多添加两条越界线
                    //不允许删除全部越界线
                    let curArr = this.state.lineArrData;
                    console.log('当前数组---', curArr);
                    let oldItem = this.state.lineArrData[0];
                    oldItem.isEditLine = false;
                    oldItem.isShowLine = true;
                    let lineStrNew = {
                        itemMoveToX: 0,
                        itemMoveToY: Height / 3,
                        itemLineToX: windowWidth - 28,
                        itemLineToY: Height / 3,
                        itemRoundOneX: 0,
                        itemRoundOneY: Height / 3,
                        itemRoundTwoX: windowWidth - 28,
                        itemRoundTwoY: Height / 3,
                        isShowLine: false,
                        isEditLine: true,
                        dir: 0,
                        itemAngle: "0deg"
                    };
                    console.log('新的线', lineStrNew);
                    curArr.push(lineStrNew);
                    this.setState({
                        lineArrData: curArr,
                        pathIndex: 1,
                        isPathEdit: true,
                        switchClickNum: 0,
                        imgAngle: "0deg",
                        addLineVisible: false,
                        deleteLineVisible: true
                    })
                }}>

                <Image
                    style={{width: 20, height: 20}}
                    source={require("../../resources/images/icon_add_cross.png")}
                />
                <View style={{flex:1}}>
                    <Text
                        numberOfLines={2}
                        style={{
                            marginLeft: 3,
                            textAlign: 'left', textAlignVertical: 'center', color: '#333333', fontSize: 12
                        }}>{stringsTo("add_cross_line")}</Text>
                </View>


            </TouchableOpacity> : null}
        </View>)
    }


    /**
     * 进入定时生效时间设置界面
     */
    _gotoEffectiveTimeSetting() {
        let areaAllArr = [];
        let startTime = "0000";
        let endTime = "2400";
        //生效时间改为0001->00:01 2201->22:01格式形式
        if (this.state.repeat_time) {
            let timeArr = this.state.repeat_time.split(',');
            startTime = timeArr[0];
            endTime = timeArr[1];
        }
        let dataArr = {
            detectSwitch: this.state.soundLightSwitchValue,
            push_flag: this.state.infoPush,
            active_time_switch: this.state.fenceSwitchEffectValue,
            active_time: this.state.effectTimeData,
            startTime: startTime,
            endTime: endTime,
            repeatTime: this.state.repeat_week,
            sound_alarm: this.state.sound_alarm,
        };
        areaAllArr.push(dataArr);

        this.props.navigation.navigate('AngelTimeSetting', {
            type: 'fenceEffectTime',
            location: 1,
            timeSwitch: this.state.fenceSwitchEffectValue,
            areaAllArr: areaAllArr,
            callback: ((timeData) => {
                let valueData = JSON.parse(timeData);
                this.setState({
                    fenceSwitchEffectValue: valueData.timeSwitch,
                    effectTimeData: '[' + valueData.active_time + ']',
                    repeat_time: valueData.startTime + "," + valueData.endTime,
                    repeat_week: valueData.repeatTime,
                });
            })
        })
    }

}


const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex", width: "100%", height: 80, flexDirection: "row", flexWrap: 'nowrap', alignItems: "center"
    }, bottomLayoutItem: {
        flex: 1, height: "100%", marginTop: 15, display: "flex", justifyContent: "center", alignItems: "center"
    },
});
