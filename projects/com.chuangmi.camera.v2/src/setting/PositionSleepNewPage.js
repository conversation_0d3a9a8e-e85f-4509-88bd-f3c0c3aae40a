import React, {Component} from 'react';
import {
    Image,
    View,
    Dimensions,
    ImageBackground,
    Text,
    StyleSheet, DeviceEventEmitter
} from 'react-native';

let ptz_pressed = false; //当前是否在操作云台
import {RNLine, showLoading, showToast, Separator, MessageDialog} from '../../../../imilab-design-ui';
import I18n, {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import HomePageLivePlayerComponent
    from "../../../../imi-rn-commonView/HomePageLivePlayerComponent/HomePageLivePlayerComponent";
import {
    LetDevice,
    BaseDeviceComponent,
    LetIMIIotRequest, LetIProperties, imiAlarmEventCloudApi, IMIGotoPage,
} from '../../../../imilab-rn-sdk';
import {IMIStorage} from "../../../../imilab-rn-sdk";
import NetInfo from "@react-native-community/netinfo";
import {TinyWindowLivePlayer} from  "../../../../imilab-modules/com.chuangmi.camera.moudle";
import { XText,XView} from 'react-native-easy-app';
import JoystickControlView
    from "../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/ui/JoystickControlView/JoystickControlView";
import Toast from "react-native-root-toast";
import IMIPermission from "../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import IMIFile from "../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import IMIIotRequest, {METHOD} from "../../../../imilab-rn-sdk/native/iot-kit/IMIIotRequest";
import AlertDialog from "../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";
import AngleDataManager from "./angel/AngelDataManager";
import IMIDownload from "../../../../imilab-rn-sdk/native/local-kit/IMIDownload";
import RNFS from "react-native-fs";
const ScreenWidth = Dimensions.get('window').width;
let videoWidth = ScreenWidth - 28;
let videoHeight = videoWidth*9/16;
let lastClickSnapPhoto = 0;
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/area/`;
const EVENT_NAME = "IMIDownloadPositionImgPathScheduler --- ";

/**
 * 20221101@byh
 * 重构固定角度休眠
 * 1、图片加密上传
 * 2、修改删除服务器上已存在的图片
 * 3、图片本地缓存（由展示在线url改为展示本地图片，减少服务器访问）
 */
export default class PositionSleepNewPage extends BaseDeviceComponent {
    constructor(props, context) {
        super(props, context);
        this.state = {
            currentStatus:HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.LOADING,
            isFullScreen: false,
            isCalling: false,//是否正在通话
            isSleep: false,//设备是否休眠
            isOnline:LetDevice.isOnline,
            spotlightSliderVisible: false, //控制聚光灯亮度滑动条的隐现
            spotlightBrightness: 0,
            isPanoramic:false,//是否云台绘制中
            sdCardStatus:0,
            isDataUsage:false,
            vipState:0,
            panoramicType:3,
            overAllImgStoreUrl:null,
            leftValue:1,
            rightValue:90,
            isEnabledOpen:false,
            sleepPosition:false,//是否开启
            XValue:1,
            yValue:50,
            isStartMove:false,//是否开始移动圆盘
            urlImage:"",//存储图片的url
            isImageUrl:false,//是否有imageurl
            showSleepModal:false,//保存弹窗
            isNoSetText:false,//没有保存时显示文字
            showMsgDialog:false,//显示激活/失效弹窗
            msgTitle:'',
        }
        this.XValue=1;
        this.yValue=50;
        this.serverPath = "";
        this.serverFileName = "";
        this.serverKey = "";
    }

    componentDidMount() {
      /* this.IMIVideoView.prepare();*/
        this._subscribe_focus = this.props.navigation.addListener('focus', () => { //获取焦点监听
                this.getNetWork();
        });

        this._subscribe_blur = this.props.navigation.addListener('blur', () => { //去往其他界面监听
            this.IMIVideoView && this.IMIVideoView.stop();
        });

        // Subscribe
        const unsubscribe = NetInfo.addEventListener(state => {
            if (state.type!='wifi'&&this.state.isDataUsage){
                this.getNetWork();
            }
            console.log("Is connected?", state.isConnected);
        });

        // Unsubscribe
        unsubscribe();
        this.getSameData();
        this.getPanoramaProperty();
        this.getPushSleepImage();
        this.devicePropertyListener = LetIProperties.addPropertyChangeListener((event)=>{
            //监听回调
            let data = typeof(event)=='object'?event:JSON.parse(event);
            if (data.MotorPositon!=undefined&&data.MotorPositon!="undefined"){
                //console.log("motorPosition = "+data.MotorPositon);
                //返回的格式string=>"0,23,34"
                // let arrData =JSON.parse(data.MotorPositon?data.MotorPositon:'[]');
                let arrData = data.MotorPositon.split(",");
                if (arrData.length>2){
                    let isMoveError  = Number(arrData[0]);
                    if (isMoveError<0){
                        showToast(stringsTo('direction_end_009'));
                    }
                    this.XValue=arrData[1]
                    this.yValue=arrData[2]
                }
            }

            //监听激活位置信息
            if (data.FavAreaActive != undefined){
                //在前台的时候执行，防止不在前台弹出，看护区域失效提示
                if (data.FavAreaActive == 0){
                    //区域失效
                    this.setState({showMsgDialog:true,msgTitle:stringsTo("active_fail_tip")});
                }
            }

        });
        LetDevice.registerDeviceEventChangeListener((data)=>{

            let {iotId,identifier,value} = JSON.parse(data);

            if (iotId == LetDevice.deviceID){
                if (identifier=='PanoramaCompoundFinishE'){
                    this.setState({ isPanoramic:false});
                }
            }
        });
        imiAlarmEventCloudApi.panoramaGetPhotoInfo(LetDevice.deviceID).then(res=>{

            let  isEnabledOpen = res.status==0||res.status==1?true:false;
            if (isEnabledOpen&&!res.timeOutFlag){
                this.setState({
                    isPanoramic:true
                })
            }
        }).catch(error=>{

        });
        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            showLoading(false);
        });
    }

    getPanoramaProperty(){
        LetDevice.updateAllPropertyCloud().then((data)=>{

            let dataObject = JSON.parse(data);
            // let dataObject = data;
            let stateProps = {};
            //侦测时间
            if (dataObject.PanoramStartMotorPositon) {
                if (dataObject.PanoramStartMotorPositon.value != '') {
                    let ary = dataObject.PanoramStartMotorPositon.value.split(',');
                    stateProps.leftValue = parseInt(ary[0].substr(1));
                }
            }
            if (dataObject.PanoramEndMotorPositon) {
                if (dataObject.PanoramEndMotorPositon.value != ''){
                    let ary = dataObject.PanoramEndMotorPositon.value.split(',');
                    stateProps.rightValue = parseInt(ary[0].substr(1));
                }
            }
            if (dataObject.SleepPosition){
                if (dataObject.SleepPosition.value=='off'||dataObject.SleepPosition.value == ''){
                    stateProps.sleepPosition = false;
                   /* let ary =  dataObject.SleepPosition.value.split(',');
                    this.XValue  = parseInt(ary[1]);
                    this.yValue= parseInt(ary[2]);*/
                    console.log("dataObject.SleepPosition:",this.XValue, this.yValue)
                } else {
                    stateProps.sleepPosition = true;

                }
            }

            if (dataObject.FixedSleepPath){

            }
            this.setState(stateProps);

        }).catch(error=>{

        });

    }

    getSameData(){
        LetDevice.getPropertyCloud('SleepStatus').then((data) =>{ //0休眠 1关闭
            console.log('设备休眠--------SleepStatus' + data,typeof(data));
            if(data==="0"){
                this.setState({isSleep:true,
                    isOnline : LetDevice.isOnline
                });

            }else {
                this.setState({
                    isOnline : LetDevice.isOnline
                });
            }

        }).catch(error => {
            console.log(JSON.stringify(error))
        });

    }

    getNetWork(){
        IMIStorage.load({
            key: LetDevice.deviceID+'isDataUsageWarning',
            autoSync: true,
            syncInBackground: true,
        }).then(res => {
            this.setState({isDataUsage: res.isDataUsage});
            if (res.isDataUsage){
                NetInfo.fetch().then(state => {
                    if (state.type=='wifi'){
                        this.IMIVideoView && this.IMIVideoView.prepare();
                    } else {
                        this.IMIVideoView && this.IMIVideoView.stop();
                    }
                });
            }else {
                this.IMIVideoView && this.IMIVideoView.prepare();
            }
        }).catch(_=> {
            this.setState({isDataUsage: false});
            this.IMIVideoView && this.IMIVideoView.prepare();
        });
    }

    componentWillUnmount() {
      /*  if (isAndroid()) { }*/
        this.IMIVideoView&&this.IMIVideoView.destroy();

        //移除监听
        if (this.listener) {
            this.listener.remove();
        }
        LetDevice.removeDeviceEventChangeListener();
        this._subscribe_focus && this._subscribe_focus();
        this._subscribe_blur && this._subscribe_blur();
        this.devicePropertyListener&&this.devicePropertyListener.remove();

    }

    _onPressBack = () => {
        this.props.navigation.pop();
    };

    /*绘制摇杆式云台View*/
    renderPtzControlView(){
     //   let sleepPosition = this.state.sleepPosition?"none":"box-none";
        return (<View
            style={{flexGrow: 1,
                width: "100%",
                flexDirection: "column",
                display: "flex",
                // marginTop: 20,
                alignItems: "center",
                justifyContent: "center"}} >

                <JoystickControlView
                    onStart={() => {
                        if (this.state.isPanoramic){
                            showToast(stringsTo('Panoramic_loading'));
                            return
                        }
                        if (!this.state.isStartMove){ //是否有滑动滑杆
                            this.setState({
                                isStartMove:true,
                            });
                            this.getNettimeOut && clearTimeout(this.getNettimeOut);
                            this.getNettimeOut = setTimeout(() => {
                                if(this.state.currentStatus != HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
                                    this.IMIVideoView&&this.IMIVideoView.prepare();
                                }
                            }, 200);
                        }

                    }}
                    onMove={(type) => {
                            ptz_pressed = false;
                            clearInterval(this.setPTZIntervalID);
                            this._doDirection(type);


                    }}
                    onLoosen={() => {
                            ptz_pressed = false;
                            clearInterval(this.setPTZIntervalID);
                            this._getMotorPositonStatus();

                    }}
                    isFullscreen={false}
                    isNewBgImg={true}
                    diameterPan={206}
                    diameterMid={40}/>

            </View>);
    }



    /*判断当前是否可操作*/
    _checkDirection(m_direction){
        console.log("_checkDirection m_direction="+m_direction+",ptz_pressed="+ptz_pressed);
        if(ptz_pressed){
            this.sendDirectionCmd(m_direction);
        }
        return ptz_pressed;
    }
    /*发送控制云台的指令*/
    sendDirectionCmd(m_direction) {
        let paramsJSON = {ActionType:m_direction,Step:0};
        LetDevice.sendDeviceServerRequest("PTZActionControl", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------' +m_direction+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    /*控制云台转动*/
    _doDirection(m_direction){
        ptz_pressed = true;

        this._checkDirection(m_direction);
        if(m_direction !== 5) {
            // 每100毫秒一次操作
            clearInterval(this.setPTZIntervalID);
            this.setPTZIntervalID = setInterval(() => {
                if (!this._checkDirection(m_direction)) {
                    clearInterval(this.setPTZIntervalID);
                }
            }, 100);
        }else{
            ptz_pressed = false;
        }

    }

    _setPanoramaRotateAngle(h,v){

        let paramsJSON = {position:"["+h+','+v+"]"};

        LetDevice.sendDeviceServerRequest("PanoramSlide", JSON.stringify(paramsJSON)).then((data) => {
            console.log(' 控制云台 转动-------'+ data);
        }).catch((error) => {
            console.log('sendDeviceServerRequest error ' + error)
        });
    }

    /*监听直播流播放状态*/
    _onLivePlayerStatusChangeListener(status){
        if (status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PREPARED) {
            this.IMIVideoView.start();
        }else if(this.state.currentStatus==HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.ERROR
            &&status == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE){
            //目前发现IOS ERROR后还会调用PAUSE，所以ERROR和暂停View重叠
            return;
        }
        this.setState({currentStatus: status});

    }
    //判断当前是否可以操作
    _canStepIn(){

        if(this.state.currentStatus == HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PLAYING){
            return true;
        }
        if (this.state.isSleep){
            showToast(stringsTo('power_off'));
            return false;
        }
        if (!this.state.isOnline){
            showToast(stringsTo('device_offline'));
            return false;
        }
        showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
        return false;
    }
    //保存截图
    _onPressScreenShot = () => {
        console.log("排查页面卡死问题------截图-----点击截图按钮");
        if(!this._canStepIn())  return;
        if(new Date().getTime()-lastClickSnapPhoto<1000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
            return;
        }
        showLoading(stringsTo('preset_sleep_set'),true);
        lastClickSnapPhoto = new Date().getTime();

        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
            if (status2 === 0) {
                let filename = `IMG_${new Date().getTime()}.jpg`;
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}${filename}`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                    console.log("排查页面卡死问题------截图成功",currentSnapshotPath);
                    this.getUploadUrl(currentSnapshotPath,filename);
                });
            } else if (status2 === -1) {
                showLoading(stringsTo('preset_sleep_set'),false);
                showToast(stringsTo('storage_permission_denied'));
            }else {
                showLoading(stringsTo('preset_sleep_set'),false);
                showToast(stringsTo('storage_permission_denied'));
            }
        });



    };

    /**
     * 获取上传图片的URL和path
     * @param currentSnapshotPath
     */
    getUploadUrl(currentSnapshotPath,fileName){
        let params = {
            Path: 'api/app_file/device/upload_by_user',
            Method:'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                productKey:LetDevice.model,
                folder: 'fixed_angle',
                storeType:2,
                suffix:'jpg',
                timer:600000,//一年
            }
        };
        console.log('获取上传文件参数',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            let tempStr = JSON.parse(data);
            //对截图文件进行加密
            AngleDataManager.getInstance().encryptFile(currentSnapshotPath).then(res=>{
                //加密成功，返回加密后数据，包含加密后的文件全路径，加密时用的key
                //加密后的文件全路径
                let ePath = res.filename;
                //加密后的key
                let eKey = res.key;
                //上传加密了的文件
                this.uploadUrl(tempStr,fileName,ePath,eKey);

            }).catch(error => {
                console.log("错误2：",error);
                showToast(I18n.t('waitFailedTip'));
                showLoading(false);
            });
              // this.uploadUrl(tempStr,currentSnapshotPath);
        }).catch((error) => {
            console.log('获取文件上传失败--',error);
            showLoading(stringsTo('preset_sleep_set'),false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    /**
     * 获取云端图片URL
     * @param downPath
     * status  是否保存
     */
    getDownImageUrl(downPath,fileName,key) {
        const params = {
            Path: 'api/app_file/device/download_by_user',
            Method:'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path: downPath,
                timer:600000,
            }
        };
        console.log('获取下载文件参数',params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            let tempStr = JSON.parse(data);
            this.getDownImgPath(tempStr,fileName,key);

            // if (status){
            //     this.setPushSleepImage(downPath,tempStr.downloadUrl);
            // }else {
            //     this.setState({
            //         urlImage:tempStr.downloadUrl,
            //         isImageUrl:true,
            //     });
            // }
        }).catch((error) => {
            showLoading(stringsTo('preset_sleep_set'),false);
            // showToast(I18n.t('commLoadingFailText'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    getDownImgPath(dataStr,fileName,key) {
        IMIDownload.downloadToPath(EVENT_NAME, dataStr.downloadUrl, TMP_SNAPSHOT_PATH_PREFIX, fileName);
        this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
            if (event.status === IMIDownload.STATUS_START) {
                console.log('开始下载');
            }
            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                console.log('正在下载');
            }

            if (event.status === IMIDownload.STATUS_ERROR) {
                this.listener && this.listener.remove();
                this.listener = null;
            }

            if (event.status === IMIDownload.STATUS_CANCEL) {
                this.listener && this.listener.remove();
                this.listener = null;
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {

                let imgPath = `${TMP_SNAPSHOT_PATH_PREFIX}${fileName}`;
                if (this.serverKey){
                    //加密文件需要解密
                    //这块是解密读本地文件
                    RNFS.readFile(imgPath, 'base64')
                        .then((content) => {
                            let decrypted = AngleDataManager.getInstance().decryptAes(content,key);
                            RNFS.writeFile(imgPath,decrypted,'base64')
                                .then((result) => {
                                    //写入文件成功
                                    console.log("result 写入成功",result);
                                    this.setState({
                                        urlImage:imgPath,
                                        isImageUrl:true,
                                    });
                                })
                                .catch((err) => {
                                    console.log("图片写失败");
                                });
                        })
                        .catch((err) => {
                            console.log("错误1：",err);
                        });
                }else {
                    this.setState({
                        urlImage:imgPath,
                        isImageUrl:true,
                    });
                }


                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
        });

    }


    /**
     * 上传图片
     * @param dataStr
     * @param currentSnapshotPath
     */
    uploadUrl(dataStr,fileName,ePath,eKey) {
        IMIIotRequest.uploadFileToIMICloud(dataStr.uploadUrl,ePath,dataStr.path,"").then((data)=>{
            //加密的图片已经上传成功
            //保存图片信息
            this.addImageInfoToServer(dataStr.path,fileName,eKey);
        }).catch((error)=>{
            showLoading(stringsTo('preset_sleep_set'),false);
            // showToast(I18n.t('operationFailed'));
        })

    }

    addImageInfoToServer(path,fileName,key,justUpdateInfo = false){
        const params = {
            Path: 'api/app_device/extra_info/put',
            Method: METHOD.POST,
            ParamMap: {
                iotId: LetDevice.deviceID,
                extraInfo: {
                    "FixedSleepPath": path,
                    "fileName": fileName,
                    "key":key
                }
            }
        };
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            if (justUpdateInfo){
                return;
            }
            if (isNaN(this.XValue)){
                this.XValue=1
            }
            //保存位置信息
            let paramsJSON = {SleepPosition:"["+this.XValue+','+this.yValue+"]"};
            let imgPath = `${TMP_SNAPSHOT_PATH_PREFIX}${fileName}`;
            this.saveXYToLetDevice(imgPath,paramsJSON,true);
        }).catch((error) => {
            if (justUpdateInfo){
                return;
            }
            showLoading(stringsTo('preset_sleep_set'),false);
        });
    }

    /**
     * 保存固定休眠摄像机定位XY和开关
     * @param urlImage
     * @param paramsJSON  定位
     * @param condition 开关
     */
    saveXYToLetDevice(urlImage,paramsJSON,condition){
        LetDevice.setPropertyCloud(JSON.stringify(paramsJSON)).then(res=>{
            showToast(stringsTo('settings_set_success'))
            showLoading(stringsTo('preset_sleep_set'),false);
            this.setState({
                sleepPosition:condition,
                urlImage:urlImage,
                isImageUrl:true,
                isStartMove:false,
                isNoSetText:true,
                currentStatus:HomePageLivePlayerComponent.LIVE_PLAYER_STATUS.PAUSE,
            });
            this.IMIVideoView && this.IMIVideoView.pause();
            this.deleteImg()
        }).catch(error=>{
            showLoading(stringsTo('preset_sleep_set'),false);
        });
    }

    /**
     * 删除云端服务器上的图片
     * 在转动电机位置后，更改固定休眠图片后，需要删除原来服务器上的图片
     */
    deleteImg(){
        //20220916@byh 现在删除云端图片，可能有概率会失败
        //而且不能以删除云端图片的成功与否作为前置条件
        if (this.serverPath){
            let params = {
                Path: 'api/app_file/device/delete_by_user',
                Method: 'POST',
                ParamMap: {
                    iotId: LetDevice.deviceID,
                    path:this.serverPath,
                    timer:600000
                }
            };
            LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
                console.log('删除图片成功',JSON.parse(data));
                let imgPath = `${TMP_SNAPSHOT_PATH_PREFIX}${this.serverFileName}`;
                IMIFile.deleteFileByPath(imgPath).then((data)=>{
                    console.log('删除本地图片成功');
                }).catch((error)=>{
                    console.log('删除本地图片失败',JSON.stringify(error))
                });
            }).catch((error) => {
                console.log('删除图片失败',JSON.stringify(error));
            });
        }
    }

    /**
     * 获取云端存储的FixedSleepPath对应的path,用于换取图片URL
     * fileName文件名称，本地存储的
     * key加密使用的key
     */
    getPushSleepImage() {
        const params = {
            Path: 'api/app_device/extra_info/get',
            Method: METHOD.POST,
            ParamMap: {
                iotId: LetDevice.deviceID,
                keyList:  ["FixedSleepPath","fileName","key"]
            }
        };
        console.log('传值params--', params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            console.log(' 推送获取数据--sendUserServerRequest  then->' + data);
            let tempStr = JSON.parse(data);
            this.serverPath = tempStr.FixedSleepPath;
            this.serverFileName = tempStr.fileName;
            this.serverKey = tempStr.key;
            //判断是否需要文件到本地
            if (this.serverPath){

                //说明服务器path存在，并且存在serverFileName
                let picName = `IMG_${new Date().getTime()}.jpg`;
                let fileName = this.serverFileName?this.serverFileName:picName;
                let imgPath = `${TMP_SNAPSHOT_PATH_PREFIX}${fileName}`;
                console.log('图片完整路径',imgPath);
                if (!this.serverFileName){
                    this.addImageInfoToServer(this.serverPath,fileName,this.serverKey,true);
                }
                IMIFile.fileExists(imgPath).then((result)=>{
                    if (result) {
                        this.setState({
                            urlImage:imgPath,
                            isImageUrl:true,
                        });
                    } else {
                        //先下载，后解密，然后设置
                        this.getDownImageUrl(this.serverPath,fileName,this.serverKey);
                    }
                }).catch((error) => {

                    console.log('获取本地存在失败',JSON.stringify(error));
                });
            }

        }).catch((error) => {
            console.log('推送数据获取错误--', error);
            console.log('sendUserServerRequest error ' + error)
        });
    }

    /**
     * 保存获取的path到云端,方面下次直接使用云端path换取
     * @param urlImage
     * @param downloadUrl
     */

  setPushSleepImage(urlImage,downloadUrl){
      const params = {
          Path: 'api/app_device/extra_info/put',
          Method: METHOD.POST,
          ParamMap: {
              iotId: LetDevice.deviceID,
              extraInfo: {
                  "FixedSleepPath": urlImage,
              }
          }
      };
      LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
          console.log(' 推送获取数据--sendUserServerRequest  then->' + data);
          if (isNaN(this.XValue)){
              this.XValue=1
          }
          let paramsJSON = {SleepPosition:"["+this.XValue+','+this.yValue+"]"};
          this.saveXYToLetDevice(downloadUrl,paramsJSON,true);
      }).catch((error) => {
          showLoading(stringsTo('preset_sleep_set'),false);
          console.log('推送数据获取错误--', error);
          console.log('sendUserServerRequest error ' + error)
      });

  }


    render() {
        let btnText=""
        let imageBtnState=0;//imageBtnState多种状态 1为视频播放中状态
        if (this.state.isStartMove){ //开启移动状态播放视频  则只能显示保存 图片隐藏
            btnText=stringsTo('save_the_open')
            imageBtnState=1;
        }else{ //关闭移动状态
            if (this.state.sleepPosition){ //关闭移动开启固定状态 则显示文字关闭 图片显示(开启则图片存在)
                btnText=stringsTo('closeStr')
                imageBtnState=2; //关闭直播,开启固定休眠 显示图片状态
            }else{  //关闭固定休眠
                if (this.state.isImageUrl){
                    imageBtnState=0; //关闭固定休眠 但有图状态
                    btnText=stringsTo('save_the_open')
                }else {
                    imageBtnState=0; //关闭固定休眠 无图状态  从未设置固定休眠位置
                    btnText=stringsTo('save_the_open')
                }
            }
        }
        let url = `file://`+this.state.urlImage+`?v=${Math.random()}`;
        console.log("---",url)
        return (
            <View style={{flex:1,backgroundColor:'#FFF'}}>
                <NavigationBar
                    title={stringsTo('Preset_Sleep')}
                    subtitle={this.state.sleepPosition?stringsTo('preset_opened'):stringsTo('preset_closed')}
                    left={[{key: NavigationBar.ICON.BACK, onPress: ()=>this.props.navigation.pop(),
                        accessibilityLabel:"dormancy_fixed_go_back"
                    }]}
                    right={[]}
                />
                <XText style={{marginTop:24,marginLeft:14,fontSize:12,color:'#00000080'}} text={stringsTo('Preset_Sleep_subTitle')}/>
                <View style={{marginTop:12,justifyContent:'center',alignItems:'center'}}>
                    {this.state.isStartMove? <TinyWindowLivePlayer style={{marginLeft:14,borderRadius:8}} {...this.props}
                                                                     tinyPlayerStyle={{borderRadius:8}}
                                                                     videoRef={ref => this.IMIVideoView = ref}
                                                                     onLivePlayerStatusChange={(status)=>this._onLivePlayerStatusChangeListener(status)}
                                                                     playerMarginHorizontal={14}
                    />:this.state.isImageUrl&&imageBtnState==2?
                            <Image
                                style={{height: videoHeight, width: videoWidth}}
                                source={{uri:url}}
                            ></Image>
                            :
                        <ImageBackground
                            style={{height: videoHeight, width: videoWidth,justifyContent: "center",alignItems:"center"}}
                            source={require('../../resources/images/fixed_sleep_top_bg.png')}
                        >{!this.state.sleepPosition? <Text style={{ color: "#333333",width:170,
                            fontSize: 15,
                            lineHeight:18,
                            textAlign: "center",}}>{stringsTo("preset_sleep_set_holder")}</Text>:null}
                        </ImageBackground>

                    }

                </View>

                {this.renderPtzControlView()}
                <XView style={{marginRight:20,marginLeft:20,height:48,marginBottom:20,alignItems:"center",borderRadius:8,
                                    backgroundColor:imageBtnState==2?"#EEEEEE":imageBtnState==0?'rgba(73, 110, 224, 0.3)':'#4A70A5',justifyContent:'center'}} onPress={()=>{
                     if (imageBtnState==1){
                        // this._onPressScreenShot();
                         if(!this._canStepIn())  return;
                         this.setState({
                             showSleepModal:true
                         })
                     }else if (imageBtnState==2||imageBtnState==3){
                         this._getMotorPositonStatus(true)

                    }
                }}>
                    <XText style={{fontSize:15,textAlign:'center',color:imageBtnState==2?"#E74D4D":"#ffffff"}} text={btnText}
                           accessibilityLabel={this.state.sleepPosition?"dormancy_fixed_close":"dormancy_fixed_open"}
                    />
                </XView>

                {this.renderDialog()}
                {this._renderMsgDialog()}
            </View>


        );
    }

    renderDialog(){
      return(
          <AlertDialog
              showTitle={false}
              visible={this.state.showSleepModal}
              message={stringsTo('save_current_angle_sleep')}
              messageStyle={{color:'#333333',fontSize:17,marginTop:20}}
              showNewDialogStyle={true}
              canDismiss={true}
              buttons={[
                  {
                      text: I18n.t("temporarily_not"),

                      callback: _ => {
                          this.setState({showSleepModal: false});
                      }
                  },
                  {
                      text: I18n.t('save_the_open'),
                      callback: _ => {
                          if(!this._canStepIn())  return;
                          showLoading(stringsTo('preset_sleep_set'),true);
                          this.setState({showSleepModal: false});
                          //  showLoading(stringsTo('preset_sleep_set'),false);
                          this._onPressScreenShot();
                      }
                  },
              ]}
          />
      )
    }

    // 显示激活角度弹窗/失效弹窗
    _renderMsgDialog() {
        return (
            <MessageDialog
                showTitle={false}
                visible={this.state.showMsgDialog}
                containerStyle={{marginBottom:15}}
                message={this.state.msgTitle}
                messageStyle={{textAlign: 'center'}}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            this.setState({
                                showMsgDialog: false,
                            });
                        }
                    }
                ]}
            >
            </MessageDialog>
        );
    }

    /**
     * 电机位置获取
     * @param isOpen true 只做固定位置休眠，开关的打开关闭，不操作图片
     *               false 只获取转动后的电机位置
     */
    _getMotorPositonStatus(isOpen=false){
        LetDevice.getPropertyCloud('MotorPositon').then((data) =>{ //0休眠 1关闭
            //let arrData =JSON.parse(data);
            let arrData = data.split(",");
            if (arrData.length>2){
                let isMoveError =parseInt(arrData[0]);
                if (isMoveError<0){
                    showToast(stringsTo('direction_end_009'));
                }
             this.XValue=arrData[1]
             this.yValue=arrData[2]

            }
            if (isOpen){
                if (isNaN(this.XValue)){
                    this.XValue=1
                }
                let paramsJSON = {SleepPosition:'['+this.XValue+','+this.yValue+']'};
                if (this.state.sleepPosition){
                    paramsJSON = {SleepPosition:"off"};
                }
                this.saveXYToLetDevice(this.state.urlImage,paramsJSON,!this.state.sleepPosition)
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

}


const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex",
        width: "100%",
        height: 80,
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center"
    },
    bottomLayoutItem: {
        flex: 1,
        height: "100%",
        marginTop: 15,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
});
