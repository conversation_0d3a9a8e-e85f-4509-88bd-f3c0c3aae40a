import React from 'react';

import {StyleSheet, View,Image} from 'react-native';

import NavigationBar from "../../../com.chuangmi.door/src/CommonView/NavigationBar/NavigationBar";
import ListItmeWithSwitch from "../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import Separator from "../../../../imilab-design-ui/src/widgets/settingUI/Separator"
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {XText,XView} from "react-native-easy-app";
import {LetDevice} from "../../../../imilab-rn-sdk";
import {showLoading, showToast} from "../../../../imilab-design-ui";
/**
 * 微光全彩设置页面
 */

export default class FullColorSettingPage extends BaseDeviceComponent {

    static propTypes = {};

    constructor(props, context) {
        super(props, context);
        this.state = {
            fullColorSwitch:false
        }
    }
    componentDidMount(){

        LetDevice.getPropertyCloud('FullColor').then((value) => {
            console.log('getPropertyCloud$$$$$$$$$$$$' + value);
            this.setState({
                fullColorSwitch: value==1?true:false
            });
        }).catch(error => {
            console.log(JSON.stringify(error))
        });

    }

    render() {


        return (<XView style={styles.container}>

            <NavigationBar
                title={I18n.t('settings_light_full_color_title')}
                left={[{key: NavigationBar.ICON.BACK, onPress: ()=>this.props.navigation.pop(),
                    accessibilityLabel:"more_setting_dynamic_model_go_back"
                }]}
                right={[]}
            />
            {/*<Separator/>*/}

                <ListItmeWithSwitch title={I18n.t('settings_light_full_color_title')} value={this.state.fullColorSwitch}
                                    onValueChange={(value) => {
                                        if(value){
                                            showLoading(stringsTo('commWaitText'), true);
                                            LetDevice.propertyOn("FullColor").then(()=>{
                                                showLoading(false);
                                                this.setState({
                                                    fullColorSwitch:value
                                                });
                                                showToast(I18n.t('settings_set_success'));
                                                console.log('成功');
                                            }).catch(err=>{
                                                this.setState({
                                                    fullColorSwitch:!value
                                                });
                                                showLoading(false);
                                                showToast(I18n.t('operationFailed'));
                                            });
                                        }else{
                                            showLoading(stringsTo('commWaitText'), true);
                                            LetDevice.propertyOff("FullColor").then(()=>{
                                                showLoading(false);
                                                this.setState({
                                                    fullColorSwitch:value
                                                });
                                                showToast(I18n.t('settings_set_success'));

                                                console.log('off-成功');
                                            }).catch(err=>{
                                                this.setState({
                                                    fullColorSwitch:!value
                                                });
                                                showLoading(false);
                                                showToast(I18n.t('operationFailed'));
                                            });
                                        }
                                    }}
                                    accessibilityLabel={["more_setting_dynamic_model_off","more_setting_dynamic_model_on"]}
                />
            <XText style={styles.smallTextStyle} allowFontScaling={false} text={I18n.t('full_color_subtitle')}
                   numberOfLines={3}/>

            <View style={styles.imageContainer}>
                <View style={styles.imageItemStyle}>
                    <Image style={styles.imageStyle} source={require('../../resources/images/full_color_normal.png')}></Image>
                    <XText style={styles.textBelowImageStyle} text={I18n.t("full_color_turn_off")}/>
                </View>
                <View style={styles.imageItemStyle}>
                    <Image style={styles.imageStyle} source={require('../../resources/images/full_color_mode.png')}></Image>
                    <XText style={styles.textBelowImageStyle} allowFontScaling={false} text={I18n.t('full_color_turn_on')}/>
                </View>
            </View>


            <XText style={styles.smallTextStyle} allowFontScaling={false} text={I18n.t('full_color_hint')+"\n"} numberOfLines={6}/>


        </XView>);
    }

}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#FFFFFF",
    },
    smallTextStyle: {
        color: '#7F7F7F',
        fontSize: 12,
        marginTop: 13,
        marginHorizontal: 14,
    },
    imageContainer:{
        flexDirection: 'row',
        marginTop:29,
        marginBottom:14
    },
    imageItemStyle:{
        flex: 1,
        alignItems: 'center'
    },
    imageStyle:{
        width:159,
        height:89
    },
    textBelowImageStyle:{
        color:"#333333",
        fontSize: 14,
        marginTop:14
    }
});
