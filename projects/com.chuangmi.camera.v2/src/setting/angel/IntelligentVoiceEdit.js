import React from 'react';

import {
    StyleSheet,
    View,
    Image,
    Text,
    Platform,
    Modal,
    TouchableWithoutFeedback,
    Dimensions,
    StatusBar,
    TouchableOpacity,
    TextInput,
    BackHandler,
    DeviceEventEmitter
} from 'react-native';


import ListItem from "../../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import RoundedButtonView from "../../../../../imilab-design-ui/src/widgets/settingUI/RoundedButtonView"

import MessageDialog from "../../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog"
import I18n from '../../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../../imilab-rn-sdk/components/BaseDeviceComponent";

import {LetDevice} from "../../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {LetIMIIotRequest} from "../../../../../imilab-rn-sdk";
import {showToast} from "../../../../../imilab-design-ui";
import {showLoading} from "../../../../../imilab-design-ui";
import {stringsTo} from "../../../../../globalization/Localize";
import WaveView from '../../../../../imilab-design-ui/src/widgets/WaveView'
import IMIPermission from "../../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {AudioRecorder} from 'react-native-audio';
import Sound from 'react-native-sound';
import IMIIotRequest from "../../../../../imilab-rn-sdk/native/iot-kit/IMIIotRequest"
import IMIDownload from "../../../../../imilab-rn-sdk/native/local-kit/IMIDownload";
import {isAndroid} from "../../../../../imilab-rn-sdk/utils/Utils";
import IMIFile from "../../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import IMIPackage from "../../../../../imilab-rn-sdk/native/local-kit/IMIPackage";
import {IMINativeLifeCycleEvent} from "../../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import NavigationBar from "../../../../../imi-rn-commonView/NavigationBar/NavigationBar";

const screen_width = Dimensions.get('window').width;
const EVENT_NAME = "IMIDownloadAudioPathScheduler - ";


let isCheckingPermission = false;


let tempPlayPath = '';
let whoosh = new Sound(tempPlayPath, '', (error) => {
    if (error) {
        console.log('资源加载失败', error);
        return;
    }
});

let tryListen = false;
let countTime = 0;
let timeValueCopy = 0;
let currentRecordPath = ""; //当前录制铃声的存储路径


let lastClickTime = 0;


/**
 * 编辑提示音
 */
export default class IntelligentVoiceEdit extends BaseDeviceComponent {
    static propTypes = {};

    constructor(props) {
        super(props);
        this.state = {
            nameDiaLogVisible: false,
            nameValue: stringsTo('voice_for_custom'),
            nameCancleTit: stringsTo('voice_for_custom'),
            showRecordAlert: false,// 显示录音弹窗
            isRecording: false,// 是否正在录音
            recordTime: 0,
            recordTimeValue: 0,
            recordPlay: false,
            playingItemId: '',
            showVoiceTime: false,
            isVoiceAdd: this.props.route.params.isAdd,
            location: this.props.route.params.index,
            typeStr: this.props.route.params.type,
            customVoiceList: this.props.route.params.customVoiceList,//当前定义数组,
            selectIndex: this.props.route.params.selectIndex,// 当前用户选中的音频序号
            // audioPath: AudioUtils.DocumentDirectoryPath + `quick_audio_${new Date().getTime()}.aac`, // 文件路径
            audioPath: IMIFile.storageBasePath + `/quick_audio_${new Date().getTime()}.aac`, // 音频文件路径，用于录制播放和上传
            audioPathTemp:IMIFile.storageBasePath + `/quick_audio_${new Date().getTime()}.aac`,
            hasPermission: undefined, //录音 授权状态
            stop: false,     //录音是否停止
            //  tempAudioPath: '',//临时保存的音频路径
            //  tempShowVoiceTime: false,//临时保存重新录制取消
            tempAudioTime: 0,//临时保存的音频时间
            isDownUrl: false,//判断是否下载文件
            downTempAudioPath: '',//获取下载的音频path
            deviceDownloadUrl: '',//供设备下载使用的Url
            uploadOpusPath: '',//上传的opus路径
            editTempTime: 0,//编辑提示音时获取的音频时间用于重新录制又取消时重新赋值
            isShowDeleteAlert: false,//显示删除弹窗
            isNotChangeAudio: true,//编辑状态下不修改音频
            isCanPress: true,//是否可以点击播放

            showBottomDeleteButton : false,
            audioPlaying: false
        }

    }

    UNSAFE_componentWillMount() {
        if (isAndroid()) {
            BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
        }
    }

    componentDidMount() {
        console.log('当前本地路径和本地时间--',this.state.audioPath);
        this.getAllValue();
        this.getAudioAuthorize();
        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(()=>{
            if (this.state.showVoiceTime) {
                this.countdownTimer && clearInterval(this.countdownTimer);
                this.editDownTimer && clearInterval(this.editDownTimer);
                this.setState({
                    recordTimeValue: this.state.recordTime,
                    isCanPress: true,
                });
                this.handlePauseAudio();
            }
        });
        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            if(this.state.audioPlaying){
                this.handlePauseAudio();
                this.countdownTimer && clearInterval(this.countdownTimer);
                this.editDownTimer && clearInterval(this.editDownTimer);
                this.setState({
                    recordTimeValue: timeValueCopy,
                    isCanPress: true,
                });
            }
            if(this.state.showRecordAlert){
                this.stopRecordAudio();
            }
            this.setState({
                nameDiaLogVisible: false,
                showRecordAlert:false,
                isShowDeleteAlert: false
            });
            showLoading(false);
        });

    }

    getAllValue() {
        let customListArray = this.state.customVoiceList;
        let stateProps = {};
        console.log('显示自定义音频数组',customListArray);
        if (customListArray.length != 0) {
            console.log('自定义数据不为空');
            if (this.state.isVoiceAdd == false) {   // 编辑
                console.log('编辑');
                for (let i= 0;i<customListArray.length;i++){
                    let voiceInfo = JSON.parse(customListArray[i]) ;
                    console.log('走这里',this.state.location,voiceInfo.index);
                    if (voiceInfo.index == this.state.location){
                        stateProps.nameValue = voiceInfo.name;
                        stateProps.nameCancleTit = voiceInfo.name;
                        stateProps.recordTimeValue = voiceInfo.time;
                        stateProps.recordTime = voiceInfo.time;
                        stateProps.editTempTime = voiceInfo.time;
                        stateProps.downTempAudioPath = voiceInfo.url;
                        stateProps.deviceDownloadUrl = voiceInfo.deviceDownloadUrl ? voiceInfo.deviceDownloadUrl : "";
                        stateProps.showVoiceTime = true;
                        stateProps.showBottomDeleteButton = voiceInfo.index != this.state.selectIndex; //选中的是当前的铃声，则不显示删除按钮
                        timeValueCopy = voiceInfo.time;
                        console.log('重新赋值--- ',stateProps.nameValue);
                    }
                }
            }
        } else { // 无自定义音频数据
            stateProps.customVoiceList = [];
        }
        console.log('更新自定义音频数组',stateProps);
        // 统一设置从设备端获取的值
        this.setState(stateProps);

        // showLoading(stringsTo('commWaitText'), true);
        // LetDevice.updateAllPropertyCloud().then((data) => {
        //     showLoading(false);
        //     let dataObject = JSON.parse(data);
        //     let stateProps = {};
        //     if (dataObject.SoundLightCustomAudioList) {
        //         if (dataObject.SoundLightCustomAudioList.value.length > 0) {
        //             // 有声光报警自定义音频数据
        //             let customAudioArr = dataObject.SoundLightCustomAudioList.value;
        //             stateProps.customVoiceList = customAudioArr;
        //             console.log('hhhh--', stateProps.customVoiceList);
        //             console.log('当前自定义音频数组', customAudioArr, customAudioArr.length);
        //             if (this.state.isVoiceAdd == false) {
        //                 console.log('编辑');
        //                 // 编辑
        //                 for (let i = 0; i < customAudioArr.length; i++) {
        //                     let voiceInfo = JSON.parse(customAudioArr[i]);
        //                     if (this.state.location == voiceInfo.index) {
        //                         stateProps.nameValue = voiceInfo.name;
        //                                         stateProps.nameCancleTit = voiceInfo.name;
        //                                         stateProps.recordTimeValue = voiceInfo.time;
        //                                         stateProps.recordTime = voiceInfo.time;
        //                                         stateProps.editTempTime = voiceInfo.time;
        //                                         stateProps.downTempAudioPath = voiceInfo.url;
        //                                         stateProps.deviceDownloadUrl = voiceInfo.deviceDownloadUrl ? voiceInfo.deviceDownloadUrl : "";
        //                                         stateProps.showVoiceTime = true;
        //                                         stateProps.showBottomDeleteButton = voiceInfo.index != this.state.selectIndex; //选中的是当前的铃声，则不显示删除按钮
        //                                         timeValueCopy = voiceInfo.time;
        //                     }
        //                 }
        //             }
        //         } else {
        //             // 无声光报警自定义音频数据
        //             stateProps.customVoiceList = [];
        //         }
        //     } else {
        //         // 无声光报警自定义音频数据
        //         stateProps.customVoiceList = [];
        //     }
        //
        //     // 统一设置从设备端获取的值
        //     this.setState(stateProps);
        //     console.log('statprops---', stateProps);
        // }).catch(error => {
        //     console.log('错误哈哈哈--', JSON.stringify(error));
        //     showToast(I18n.t('commLoadingFailText'));
        //     showLoading(false);
        //     console.log(JSON.stringify(error))
        // });
    }

    getAudioAuthorize() {
        AudioRecorder.requestAuthorization()
            .then(isAuthor => {
                // showToast('是否授权: ' + isAuthor);

                this.setState({hasPermission: isAuthor});
                if (!isAuthor) {

                    showToast(I18n.t('audio_permission_denied'));
                    return;
                }
                //强制从speaker输出
                // Sound.setCategory("Playback",false);
                // Sound.setActive(true);
                // Sound.setCategory("PlayAndRecord",false);
                // 需要使用扬声器和听筒切换场景，用
                // AVAudioSessionCategoryPlayAndRecord

                // if(Platform.OS === 'ios'){
                //     this.audioPath =
                //         AudioUtils.CachesDirectoryPath + `${new Date().getTime()}.aac`;
                // }else {
                //     this.audioPath = AudioUtils.DocumentDirectoryPath + `${new Date().getTime()}.aac`;
                // }
                // showToast(this.state.audioPath);

                // this.prepareRecordingPath(this.state.audioPath);
                // 录音进展


                AudioRecorder.onProgress = (data) => {

                    /*if (data.currentTime > 10) {
                        this.setState({
                            showVoiceTime: true,
                            recordTime: 10,
                            recordTimeValue: 10,
                            tempAudioPath: this.state.audioPath,
                            tempAudioTime: 10,
                            tempShowVoiceTime: false,
                            isNotChangeAudio: false
                        });
                        this.stopRecordAudio();
                        return;
                    }
                    this.setState({
                        recordTime: parseInt(data.currentTime),
                        recordTimeValue: parseInt(data.currentTime)
                    });*/

                };
                // 完成录音
                AudioRecorder.onFinished = (data) => {
                    this.timeCounter && clearInterval(this.timeCounter);
                    countTime = 0;
                    // data 录音数据，可以在此存储需要传给接口的路径数据
                    // console.log(this.state.recordTime)
                    //console.log('录音数据--', data);
                    // showToast(this.state.recordTime);
                };
            })
    }

    // 自定义导航栏
    _renderNavView() {
        return (
            <View style={{width: "100%", height: 90, backgroundColor: 'white', top: 0}}>
                <View style={{height: 90, flexDirection: 'row', width: '100%',}}>
                    <View style={{color: 'white', left: 14, top: 20, width: 80, height: 70,}}>
                        <TouchableOpacity
                            onPress={() => {
                                if (this.state.audioPlaying) {
                                    this.handlePauseAudio();
                                }
                                this._onPressBack();
                                // this.props.navigation.pop()
                            }}>
                            <Text style={{
                                width: 80,
                                left: 0,
                                lineHeight: 66,
                                alignItems: 'center',
                                // width:Utils.getScreenWidth()-50,
                                justifyContent: 'center',
                                textAlign: 'left',
                                textAlignVertical: 'center',
                                color: '#333333',
                                backgroundColor: 'white',
                                fontSize: 15,
                            }}>{stringsTo('cancel')}</Text>
                        </TouchableOpacity>
                    </View>
                    <Text style={{
                        position: 'absolute',
                        width: screen_width - 190,
                        lineHeight: 68,
                        left: 100,
                        top: 20,
                        alignItems: 'center',
                        // width:Utils.getScreenWidth()-50,
                        justifyContent: 'center',
                        textAlign: 'center',
                        textAlignVertical: 'center',
                        color: 'black',
                        backgroundColor: 'white',
                        fontSize: 18,
                        fontWeight: 'bold',
                    }}>{this.state.isVoiceAdd ? stringsTo('voice_for_add') : stringsTo('voice_for_edit')}</Text>

                    <View style={{color: 'white', position: 'absolute', right: 14, top: 20, width: 70, height: 70,}}>
                        <TouchableOpacity
                            disabled={this.state.recordTime == 0 || this.state.audioPlaying}
                            onPress={()=>{
                                console.log('保存');
                                if(!this._isClickValid()){
                                    return;
                                }

                                if (this.state.audioPlaying) {
                                    this.handlePauseAudio();
                                }

                                if (!this.state.isVoiceAdd && this.state.isNotChangeAudio) {
                                    // 编辑状态下且音频未修改  名称可改可不改
                                    console.log('未修改音频---');
                                    this.saveNotChangeAudio();
                                } else {
                                    this.saveAudio();
                                }
                            }}>
                            <Text style={{
                                width: 70,
                                left: 0,
                                lineHeight: 68,
                                alignItems: 'center',
                                // width:Utils.getScreenWidth()-50,
                                justifyContent: 'center',
                                textAlign: 'right',
                                textAlignVertical: 'center',
                                color: this.state.recordTime == 0 || this.state.audioPlaying ? '#B2B2B2' : '#4A70A5',
                                backgroundColor: 'white',
                                fontSize: 15,
                            }}>{stringsTo('imi_save')}</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>);
    }

    saveAudio() { //录制的是aac格式的音频，需要aac转pcm,pcm再转成设备可播放的opus格式
        // let timestamp = Date.parse(new Date());
        if (this.state.recordTimeValue <= 0) {
            return
        }
        showLoading(stringsTo('commWaitText'), true);
        this.getAACUrl();
    }


    getAACUrl() {
        const params = {
            Path: 'api/app_file/device/upload_by_user',
            Method: 'POST',
            ParamMap: {
                storeType:2,
                iotId: LetDevice.deviceID,
                productKey:LetDevice.model,
                folder: 'customize_audio',
                suffix: 'aac',
                timer: 600000,
            }
        };
        console.log('自定义音频录制-------获取aac文件上传的url参数', params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // showLoading(false);
            console.log('自定义音频录制-------获取aac文件上传的url成功', data);
            this.uploadAACUrl(JSON.parse(data));
        }).catch((error) => {
            console.log('获取文件上传失败--', error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    uploadAACUrl(dataStr) {
        console.log('aac-上传url---', dataStr,this.state.audioPath);
        IMIIotRequest.uploadFileToIMICloud(dataStr.uploadUrl, this.state.audioPath, dataStr.path, '').then((data) => {
            console.log('自定义音频录制-------上传aac文件成功', data);
            // this.getDeviceDownloadUrl(dataStr);
            console.log('本地音频路径',this.state.audioPath);
            this.uploadData(dataStr);
        }).catch((error) => {
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('上传失败', error)
        })
    }

    getDeviceDownloadUrl(dataStr){
        const params = {
            Path: 'api/app_file/device/download_by_user',
            Method: 'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path: dataStr.path,
                timer: 300000,
            }
        };
        console.log('获取下载文件参数', params);
        showLoading(stringsTo('commWaitText'), true);
        LetIMIIotRequest.sendUserServerRequest(params).then((result) => {
            // showLoading(false);
            this.uploadData(dataStr,JSON.parse(result));
        }).catch((error) => {
            console.log('下载文件失败--', error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    uploadData(data) {
        console.log('上传物模型',  this.state.typeStr,this.state.isVoiceAdd);
        let paramsData =
            {
                "index": this.state.location,
                "name": this.state.nameValue,
                "time": this.state.recordTimeValue,
                "url": data.path,
            };
        if (this.state.isVoiceAdd) {//新增自定义铃声
            this.state.customVoiceList.push(JSON.stringify(paramsData));
            console.log('新增数据---', this.state.customVoiceList);
        } else {//编辑提示音
            let index;
            for (let i = 0; i < this.state.customVoiceList.length; i++) {
                let voiceItem = JSON.parse(this.state.customVoiceList[i]);
                if (voiceItem.index == this.state.location) {
                    index = i
                }
            }
            this.state.customVoiceList.splice(index, 1, JSON.stringify(paramsData));
        }
        let params = {SoundLightCustomAudioList: this.state.customVoiceList};
        console.log('上传自定义音频参数',params);
        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            if (!this.state.isVoiceAdd && IMIPackage.minApiLevel>=10007) { //如果是重新编辑铃声，下载的铃声要清除掉
                try {
                    //删除服务器上的文件
                    this._deleteUrl(false);

                    IMIFile.deleteFileByPath(IMIFile.storageBasePath + `/${this.state.typeStr}_download_audio_${this._getTimestampOfUrl(this.state.downTempAudioPath)}_${this.state.location}.aac`)
                        .then((vo) => {
                            console.log("删除结果---"+vo);
                        }).catch(() => {
                        console.log("删除出错");
                    });
                } catch (e) {
                    console.log("删除崩了");
                }

            }

            this.timer = setTimeout(
                () => {
                    showToast(I18n.t('settings_set_success'));
                    showLoading(false);
                    this.timer && clearTimeout(this.timer);
                    this._onPressBack(1);
                },
                3500
            );
        }).catch((error) => {
            console.log('失败----', error);
            console.log('失败----', error);
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    // 不修改音频，修改名称，但是需要更新给固件下载音频的url
    saveNotChangeAudio() {
        showLoading(stringsTo('commWaitText'), true);
        let paramsData =
            {
                "index": this.state.location,
                "name": this.state.nameValue,
                "time": this.state.recordTimeValue,
                "url": this.state.downTempAudioPath,
            };

        //编辑提示音
        let index;
        console.log("只改名称-----customVoiceList", this.state.location, JSON.parse(JSON.stringify(this.state.customVoiceList)));
        for (let i = 0; i < this.state.customVoiceList.length; i++) {
            // let voiceItem = this.state.customVoiceList[i];
            let voiceItem = JSON.parse(this.state.customVoiceList[i]);
            console.log('voiceItm---', voiceItem.index, this.state.location);
            if (voiceItem.index == this.state.location) {
                index = i;
            }
        }
        console.log('只改名称,当前第几个---', index);
        this.state.customVoiceList.splice(index, 1, JSON.stringify(paramsData));
        console.log('只改名称----- 当前修改后的customVoiceList', this.state.customVoiceList);
        let params = {SoundLightCustomAudioList: this.state.customVoiceList};
        console.log("只改名称-----", params);
        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            this.timer = setTimeout(
                () => {
                    showToast(I18n.t('settings_set_success'));
                    showLoading(false);
                    this.timer && clearTimeout(this.timer);
                    this._onPressBack(1);
                },
                3500
            );
        }).catch((error) => {
            // console.log('失败----',error);
            // console.log('失败----',error);
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });

    }



    downAudioUrl() {
        const params = {
            Path: 'api/app_file/device/download_by_user',
            Method: 'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path: this.state.downTempAudioPath,
                timer: 600000,
            }
        };
        console.log('获取下载文件参数', params);
        showLoading(stringsTo('commWaitText'), true);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            // showLoading(false);
            let tempStr = JSON.parse(data);
            console.log('获取下载文件数据--', tempStr); //TODO测试每个服务器下，path前拼接的前缀
            this.getDownAudioPath(tempStr);
        }).catch((error) => {
            console.log('下载文件失败--', error);
            showLoading(false);
            showToast(I18n.t('operationFailed'));
            console.log('sendUserServerRequest error ' + error)
        });
    }

    getDownAudioPath(dataStr) {
        console.log('下载data', dataStr,IMIFile.storageBasePath);
        let fileName = `${this.state.typeStr}_download_audio_${this._getTimestampOfUrl(this.state.downTempAudioPath)}_${this.state.location}.aac`;
        IMIDownload.downloadToPath(EVENT_NAME, dataStr.downloadUrl, IMIFile.storageBasePath, fileName);

        this.listener = DeviceEventEmitter.addListener(EVENT_NAME, (event) => {
            if (event.status === IMIDownload.STATUS_START) {
                console.log('开始下载');
                this.setState({isCanPress: false});
            }
            if (event.status === IMIDownload.STATUS_DOWNLOADING) {
                console.log('正在下载');
                this.setState({isCanPress: false});
            }

            if (event.status === IMIDownload.STATUS_ERROR) {
                // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
                // console.log(EVENT_NAME + " download error mataInfo : " + mataInfo)
                showLoading(false);
                showToast(I18n.t('waitFailedTip'));
                this.setState({isCanPress: true});
                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
            if (event.status === IMIDownload.STATUS_CANCEL) {
                // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
                //用过一次必须释放
                this.setState({isCanPress: true});
                this.listener && this.listener.remove();
                this.listener = null;
            }

            if (event.status === IMIDownload.STATUS_SUCCESS) {
                const path = `${event.downloadPath}/${fileName}`;
                this.setState({
                    isDownUrl: true,
                    audioPath: path,
                    // tempAudioPath: path,
                    stop: true,
                    recordTime: this.state.editTempTime,
                    recordTimeValue: this.state.editTempTime
                }, callback => {
                    console.log('下载成功');
                    console.log('下载成功后文件路径-常量---', path);
                    console.log('下载成功路径---', this.state.audioPath);
                    showLoading(false);
                    this.handlePlayAudio(path);
                    this.editDownTimer && clearInterval(this.editDownTimer);
                    timeValueCopy = this.state.recordTimeValue;
                    this.editDownTimer = setInterval(() => { //从10秒开始倒计时
                        let time = this.state.recordTimeValue - 1;
                        if (time <= 0) { //倒计时结束，停止视频播放
                            this.editDownTimer && clearInterval(this.editDownTimer);
                            this.setState({
                                recordTimeValue: this.state.recordTime,
                                isCanPress: true,
                            });
                        } else {
                            this.setState({
                                recordTimeValue: time,
                                isCanPress: false,
                            });
                        }
                    }, 1000);
                });

                //用过一次必须释放
                this.listener && this.listener.remove();
                this.listener = null;
            }
        });
    }

    /**
     * 先删除云端音频文件
     * @param afterBack 删除成功后，是否返回上级页面
     */
    _deleteUrl(afterBack = true) {
        const params = {
            Path: 'api/app_file/device/delete_by_user',
            Method: 'POST',
            ParamMap: {
                iotId: LetDevice.deviceID,
                path: this.state.downTempAudioPath,
            }
        };
        console.log('删除aac文件参数', params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            if (afterBack){
                this.waitToBack();
            }
        }).catch((error) => {
            if (afterBack){
                this.waitToBack();
            }
        });
    }

    //再更新云端物模型
    deleteData() {
        showLoading(stringsTo('commWaitText'), true);
        let allData = [...this.state.customVoiceList]; //做个备份，不直接操作customizeBellList
        console.log("删除前-------",this.state.customVoiceList);
        if (allData.length != 0) { //没有自定义音频数据
            let index;
            for (let i = 0; i < allData.length; i++) {
                let tempStr = JSON.parse(allData[i]);
                if (tempStr.index == this.state.location) {
                    index = i;
                }
            }
            allData.splice(index, 1);
            console.log("删除后-------",allData);
        }
        let params = {SoundLightCustomAudioList: allData};

        LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
            this.setState({customVoiceList:allData});
            let audioPath = IMIFile.storageBasePath + `/${this.state.typeStr}_quick_audio_${this.state.location}.aac`;
            let audioPath2 = IMIFile.storageBasePath + `/${this.state.typeStr}_quick_audio_${this.state.location}_rerecord.aac`;
            let downloadAudio = IMIFile.storageBasePath + `/${this.state.typeStr}_download_audio_${this._getTimestampOfUrl(this.state.downTempAudioPath)}_${this.state.location}.aac`;
            let toDeleteArr = [audioPath,audioPath2,downloadAudio];
            //删除铃声需要清除此铃声相关的录制、下载的铃声
            Promise.all(toDeleteArr.map((path) => {
                //出入的是秒，不是毫秒
                return this.deleteLocalFile(path);
            })).then((res) => {
                //nothing to do
            }).catch(err => {
                //nothing to do
            });
            //物模型删除成功后，我们再去删除云端数据，
            //云端数据不管删除成功与否，我们认为这个自定义音频都删除成功了
            this._deleteUrl();
        }).catch((error) => {
            showToast(I18n.t('waitFailedTip'));
            showLoading(false);
        });
    }

    deleteLocalFile(path){
        return new Promise((resolve, reject) => {
            //暂时060项目的SDK level是一定会大于10007的
            IMIFile.deleteFileByPath(path).then(()=>resolve('success')).catch(()=>reject('fail'))
        });
    }

    waitToBack(){
        this.timer = setTimeout(
            () => {
                showToast(I18n.t('settings_set_success'));
                showLoading(false);
                this._onPressBack(1);
                this.timer  && clearTimeout(this.timer);
            }, 3500);
    }

    render() {
        StatusBar.setBarStyle('dark-content');//状态栏字体刷黑色
        StatusBar.setBackgroundColor('white');//状态栏背景颜色
        return (<View style={styles.container}>
                {/*{this._renderNavView()}*/}
                <NavigationBar
                    title={this.state.isVoiceAdd ? stringsTo('voice_for_add') : stringsTo('voice_for_edit')}
                    left={[{
                        key: NavigationBar.ICON.CUSTOM,
                        n_source:require('../../../resources/images/newLive/icon_angel_del.png'),
                        onPress: () => {
                            if (this.state.audioPlaying) {
                                this.handlePauseAudio();
                            }
                            // this._onPressBack(0);
                            this.props.navigation.pop();
                        },
                        accessibilityLabel: this.state.isVoiceAdd ? 'voice_for_add' : 'voice_for_edit'
                    }]}
                    right={[{
                        key: NavigationBar.ICON.CUSTOM,
                        n_source:require('../../../resources/images/newLive/icon_angel_save.png'),
                        onPress: () => {
                            if(!this._isClickValid()){
                                return;
                            }
                            if (this.state.recordTime == 0 || this.state.audioPlaying){
                                return;
                            }
                            if (this.state.audioPlaying) {
                                this.handlePauseAudio();
                            }

                            if (!this.state.isVoiceAdd && this.state.isNotChangeAudio) {
                                // 编辑状态下且音频未修改  名称可改可不改
                                console.log('未修改音频---');
                                this.saveNotChangeAudio();
                            } else {
                                this.saveAudio();
                            }
                        },
                        accessibilityLabel: 'voice_save'
                        }]}
                />

                <ListItem titleStyle={{color: '#333333', fontWeight: 'bold'}} accessibilityLabel={"voice_for_name"} title={stringsTo('voice_for_name')}
                          value={this.state.nameValue} onPress={() => {
                    // this.props.navigation.push("ImageSetting");
                    this.setState({nameDiaLogVisible: true})
                }}/>
                {this._renderTipVoiceView()}

                {this.state.isVoiceAdd || !this.state.showBottomDeleteButton? null :
                    <View style={{width: '100%', height: 75, position: 'absolute', bottom: 0}}>
                        <RoundedButtonView buttonText={stringsTo('delete_title')}
                                           disabled={false}
                                           buttonStyle={{backgroundColor: "white", margin: 15,}}
                                           buttonTextStyle={{color: "#E74D4D"}}
                                           accessibilityLabel={"delete"}
                                           onPress={() => {
                                               console.log('删除');
                                               this.setState({isShowDeleteAlert: true});
                                           }}/>
                    </View>}

                {Platform.OS == 'ios' ? this._showiOSNameDialogView() : this._showNameDialogView()}

                {this._showRecordAlertView()}

                {this._showDelertArt()}
            </View>
        )
    }

    _showDelertArt() {
        if (!this.state.isShowDeleteAlert) {
            return;
        }
        return (
            <MessageDialog
                title={''}
                showTitle={false}
                message={stringsTo('delete_alert')}
                visible={this.state.isShowDeleteAlert}
                canDismiss={true}
                onDismiss={() => {
                    this.setState({isShowDeleteAlert: false})
                }}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({isShowDeleteAlert: false});
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            if(!this._isClickValid()){
                                return;
                            }
                            if (this.state.audioPlaying) {
                                this.handlePauseAudio();
                            }
                            this.setState({isShowDeleteAlert: false});
                            //20220905@byh 修改
                            //首先去删除物模型数据
                            //如果先删除图片数据，成功后再去删除物模型数据，如果图片删除成功了，物模型删除失败了，
                            //再次去删除图片会一直提示删除失败，这个自定义音频，再也无法删除了
                            this.deleteData();
                        }
                    },
                ]}
            >
            </MessageDialog>
        )
    }

    _showiOSNameDialogView() {
        if (!this.state.nameDiaLogVisible) {
            return null;
        }
        return (
            <Modal
                animationType="none"
                transparent={true}
                visible={this.state.nameDiaLogVisible}
                onRequestClose={() => {
                    console.log('onRequestClose------');
                    this.setState({nameDiaLogVisible: false})
                }}

                // onShow={() => {
                //     this.setState({playingItemId: '', recordTime: 0 });
                // }}
                onDismiss={() => {
                    console.log('onDismiss------');
                    this.setState({nameDiaLogVisible: false})
                }}>

                <View style={{
                    flexDirection: 'column',
                    flex: 1,
                    justifyContent: 'flex-end',
                    backgroundColor: '#00000099'
                }}>
                    <TouchableWithoutFeedback onPress={() => {
                        this.setState({nameDiaLogVisible: false})
                    }}>
                        <View style={{position: 'absolute', left: 0, right: 0, top: 0, bottom: 0}}/>
                    </TouchableWithoutFeedback>

                    <View style={{
                        flexDirection: 'column',
                        marginLeft: 14,
                        marginRight: 14,
                        borderRadius: 20,
                        position: 'absolute',
                        top: 150,
                        // height:200,
                        width: screen_width - 28,
                        borderTopLeftRadius: 20,
                        borderTopRightRadius: 20,
                        backgroundColor: '#FFFFFF', zIndex: 1
                    }}>
                        <Text style={{
                            fontSize: 16,
                            color: '#333333',
                            textAlign: 'center',
                            textAlignVertical: 'center',
                            lineHeight: 60,
                            // marginTop:25,
                            // marginBottom:10,
                        }}>{stringsTo('voice_for_name')}</Text>

                        <View style={{backgroundColor: 'white'}}>
                            <TextInput
                                ref="textInput"
                                style={styles.textInputStyle}
                                placeholder={stringsTo('voice_for_enter_name')}
                                placeholderTextColor={'#7F7F7F'}
                                onChangeText={(text) => this._onChangeText(text)}
                                // value={this.state.nameValue}
                                // onBlur={this._reset.bind(this)}
                                // onFocus={this._onFocus.bind(this, 'textInput')}
                            >
                            </TextInput>
                        </View>

                        <View style={{
                            flexDirection: 'row',
                            height: 60,
                            lineHeight: 60,
                            marginTop: 14,
                            borderBottomLeftRadius: 20,
                            borderBottomRightRadius: 20,
                            backgroundColor: '#FFFFFF'
                        }}>
                            <TouchableWithoutFeedback
                                onPress={() => {
                                    this.setState({nameValue: this.state.nameCancleTit})
                                    this.setState({nameDiaLogVisible: false})
                                }}>
                                <View style={{
                                    width: (screen_width - 14 * 5) / 2,
                                    alignItems: 'center',
                                    marginLeft: 14,
                                    marginRight: 14,
                                    marginBottom: 14,
                                    justifyContent: 'center',
                                    borderRadius: 22.5,
                                    height: 45,
                                    backgroundColor: '#F2F3F5',
                                }}>
                                    <Text style={{
                                        width: '100%',
                                        height: '100%',
                                        lineHeight: 45,
                                        textAlign: 'center',
                                        textAlignVertical: 'center',
                                        color: '#7F7F7F',
                                        fontSize: 15,
                                        fontWeight: 'bold'
                                    }}>{stringsTo('cancel')}</Text>
                                </View>
                            </TouchableWithoutFeedback>

                            <TouchableWithoutFeedback
                                onPress={() => {
                                    let tempStr = this.state.nameValue;
                                    let regStr = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
                                    let unStr = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g;
                                    if (tempStr.match(regStr) || (tempStr.match(unStr))) {
                                        console.log('输入确认走判断');
                                        showToast(I18n.t('imi_input_text_tip'));
                                        this.setState({nameValue: this.state.nameCancleTit, nameDiaLogVisible: false});
                                        return;
                                    }
                                    this.setState({nameDiaLogVisible: false, nameCancleTit: this.state.nameValue});
                                }}>
                                <View style={{
                                    width: (screen_width - 14 * 5) / 2,
                                    alignItems: 'center',
                                    marginRight: 14,
                                    marginBottom: 14,
                                    justifyContent: 'center',
                                    borderRadius: 22.5,
                                    height: 45,
                                    backgroundColor: '#4A70A5',
                                }}>
                                    <Text style={{
                                        width: '100%',
                                        height: '100%',
                                        lineHeight: 45,
                                        textAlign: 'center',
                                        textAlignVertical: 'center',
                                        color: '#FFFFFF',
                                        fontSize: 15,
                                        fontWeight: 'bold'
                                    }}>{stringsTo('ok_button')}</Text>
                                </View>
                            </TouchableWithoutFeedback>

                        </View>

                    </View>
                </View>
            </Modal>
        );
    }


    _showNameDialogView() {
        if (!this.state.nameDiaLogVisible) {
            return null;
        }
        return (<View>
            <MessageDialog
                title={I18n.t('voice_for_name')}
                visible={this.state.nameDiaLogVisible}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({nameValue: this.state.nameCancleTit})
                            this.setState({nameDiaLogVisible: false});
                            // this.setState(tempRecordTimeSetting);
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            // if (this.state.nameValue.length === 0) { return; }
                            // this.confirmRenameWithMac(this.state.nameValue);
                            // this.setState({nameCancleTit:this.state.nameValue})

                            let tempStr = this.state.nameValue;
                            let regStr = /(?:[\u2700-\u27bf]|(?:\ud83c[\udde6-\uddff]){2}|[\ud800-\udbff][\udc00-\udfff]|[\u0023-\u0039]\ufe0f?\u20e3|\u3299|\u3297|\u303d|\u3030|\u24c2|\ud83c[\udd70-\udd71]|\ud83c[\udd7e-\udd7f]|\ud83c\udd8e|\ud83c[\udd91-\udd9a]|\ud83c[\udde6-\uddff]|\ud83c[\ude01-\ude02]|\ud83c\ude1a|\ud83c\ude2f|\ud83c[\ude32-\ude3a]|\ud83c[\ude50-\ude51]|\u203c|\u2049|[\u25aa-\u25ab]|\u25b6|\u25c0|[\u25fb-\u25fe]|\u00a9|\u00ae|\u2122|\u2139|\ud83c\udc04|[\u2600-\u26FF]|\u2b05|\u2b06|\u2b07|\u2b1b|\u2b1c|\u2b50|\u2b55|\u231a|\u231b|\u2328|\u23cf|[\u23e9-\u23f3]|[\u23f8-\u23fa]|\ud83c\udccf|\u2934|\u2935|[\u2190-\u21ff])/g;
                            let unStr = /[^\a-\z\A-\Z0-9\u4E00-\u9FA5]/g;
                            if (tempStr.match(regStr) || (tempStr.match(unStr))) {
                                showToast(I18n.t('imi_input_text_tip'));
                                this.setState({nameValue: this.state.nameCancleTit, nameDiaLogVisible: false});
                                return;
                            }
                            this.setState({nameDiaLogVisible: false, nameCancleTit: this.state.nameValue});
                            // this.setState({nameDiaLogVisible:false});
                            // this.updateRecordTime();
                        }
                    },
                ]}
            >
                <View style={{backgroundColor: 'white'}}>
                    <TextInput
                        ref="textInput"
                        style={styles.textInputStyle}
                        placeholder={stringsTo('voice_for_enter_name')}
                        placeholderTextColor={'#7F7F7F'}
                        onChangeText={(text) => this._onChangeText(text)}
                        // value={this.state.nameValue}
                        // onBlur={this._reset.bind(this)}
                        // onFocus={this._onFocus.bind(this, 'textInput')}
                    >
                    </TextInput>
                </View>
            </MessageDialog>
        </View>)
    }

    _onChangeText(textStr) {
        // console.log('hhh输入值--value---',textStr);
        this.setState({nameValue: textStr});
        // this.setState({nameValue:textStr,nameCancleTit:this.state.nameValue});

        // let tempText = textStr.replace(regStr, '');
        // let tempTextNew = tempText.replace(unStr,'');
        // console.log('tempText---',tempText);
        // console.log('new---',tempTextNew);
        //  // this.setState({ inputTextString: inputText });
        //  this.setState({nameValue:tempTextNew});

    }

    _reset() {

        this.refs.scrollView.scrollTo({y: 0});

    }

    _onFocus(refName) {

        this.scrollResponderTimer = setTimeout(() => {

            let scrollResponder = this.refs.scrollView.getScrollResponder();
            scrollResponder.scrollResponderScrollNativeHandleToKeyboard(
                ReactNative.findNodeHandle(this.refs[refName]), 0, true);
            this.scrollResponderTimer && clearTimeout(this.scrollResponderTimer);
        }, 100);
    }


    //录音时长显示和确定取消弹窗
    _showRecordAlertView() {
        return (<View>
            <MessageDialog
                title={""}
                showTitle={false}
                visible={this.state.showRecordAlert}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            if (this.state.showVoiceTime) {
                                this.stopRecordAudio();
                                if (this.state.isVoiceAdd == false) {
                                    //编辑提示音时重新录音 点击取消，重新赋值原来的录音时间
                                    this.setState({
                                        /*recordTime: this.state.editTempTime,
                                        recordTimeValue: this.state.editTempTime,*/
                                        isNotChangeAudio: true
                                    });
                                }
                                console.log('取消录音时间p---', this.state.recordTime, this.state.recordTimeValue, this.state.editTempTime);
                                console.log('获取上次录音确定时间-', this.state.tempAudioTime, this.state.recordTime);
                                this.setState({
                                    showRecordAlert: false,
                                    isRecording: false,
                                    //  tempShowVoiceTime: true,
                                    recordTime: timeValueCopy,
                                    recordTimeValue: timeValueCopy
                                }, callback => {
                                    console.log('添加重新录制取消--' + this.state.recordTime);
                                });
                            } else {
                                this.stopRecordAudio();
                                // this.handleDelAudio()
                                this.setState({
                                    showRecordAlert: false,
                                    isRecording: false,
                                    recordTime: 0,
                                    showVoiceTime: false
                                });
                            }

                            let pathV1 = IMIFile.storageBasePath + `/${this.state.typeStr}_quick_audio_${this.state.location}.aac`;
                            let pathV2 = IMIFile.storageBasePath + `/${this.state.typeStr}_quick_audio_${this.state.location}_rerecord.aac`;
                            currentRecordPath = currentRecordPath == pathV1 ? pathV2 : pathV1;
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            let totalTimeVaue = this.state.recordTime;
                            console.log('点击确定--录音时间---', this.state.recordTime, totalTimeVaue);
                            this.stopRecordAudio();
                            timeValueCopy = totalTimeVaue;
                            this.setState({
                                showRecordAlert: false,
                                isRecording: false,
                                showVoiceTime: true,
                                recordTimeValue: totalTimeVaue,
                                //  tempAudioPath: this.state.audioPath,
                                tempAudioTime: totalTimeVaue,
                                audioPath: currentRecordPath, //点击确定时，播放的路径才是录制的路径
                                //  tempShowVoiceTime: false,
                                isNotChangeAudio: false
                            }, callback => {
                                console.log('确定录音时间---' + totalTimeVaue + '临时确定时间' + this.state.tempAudioTime,)
                            });
                            tryListen = true; //点击确定，可以试听了
                        }
                    },
                ]}
            >
                <Text style={{
                    fontSize: 24,
                    color: '#4A70A5',
                    textAlign: 'center',
                    textAlignVertical: 'center',
                    marginTop: 25,
                    marginBottom: 10,
                }}>{`00:00:${this.state.recordTime > 9 ? this.state.recordTime : (`0${this.state.recordTime}`)}`}</Text>
                {this._renderWaveViewLayout()}

            </MessageDialog>
        </View>)
    }

    //录音波浪
    _renderWaveViewLayout() {
        return (<View style={{backgroundColor: 'white', width: screen_width, top: 20, height: 56}}>
            <WaveView
                waveHeight={36}
                waveWidth={screen_width}/>
        </View>)
    }

    _renderTipVoiceView() {
        return (<View style={styles.viewContainerNew}>
            <View style={{flex: 0.5,justifyContent:'center'}}>
                <Text ellipsizeMode="tail"
                      numberOfLines={2}
                      style={styles.titStyle}>{stringsTo('voice_for_tip_tit_time')}</Text>
            </View>

            <View style={{flex: 0.5, justifyContent: 'flex-end', flexDirection: 'row',}}>
                {this.state.showVoiceTime ?
                    <View style={{
                        height: 30,
                        width: 70,
                        marginTop: 20,
                        marginRight: 10,
                        flexDirection: 'row',
                        backgroundColor: '#EBF0FB',
                        borderRadius: 15,
                        opacity: this.state.recordTimeValue == this.state.recordTime ? 1 : 0.5
                    }}>
                        <TouchableOpacity
                            disabled={this.state.isCanPress ? false : true}
                            style={{marginBottom: 0, paddingBottom: 0, flexDirection: 'row'}}
                            accessibilityLabel={"listen_custom_voice"}
                            onPress={() => {
                                console.log('recordTimeValue---recordTime', this.state.recordTimeValue, this.state.recordTime)
                                if (this.state.isCanPress) {
                                    // console.log('播放时不可点击');
                                    // return;
                                    if (this.state.isVoiceAdd) {
                                        // 添加
                                        this.handlePlayAudio();
                                        this.countdownTimer && clearInterval(this.countdownTimer);
                                        timeValueCopy = this.state.recordTimeValue;
                                        this.countdownTimer = setInterval(() => { //从10秒开始倒计时
                                            let time = this.state.recordTimeValue - 1;
                                            if (time <= 0) { //倒计时结束，停止视频播放
                                                this.countdownTimer && clearInterval(this.countdownTimer);
                                                this.setState({
                                                    recordTimeValue: this.state.recordTime,
                                                    isCanPress: true,
                                                });
                                            } else {
                                                this.setState({
                                                    recordTimeValue: time,
                                                    isCanPress: false,
                                                });
                                            }
                                        }, 1000);

                                    } else {
                                        // 编辑
                                        if(IMIPackage.minApiLevel<10007){
                                            if (this.state.isDownUrl) { //已下载
                                                console.log('编辑---');
                                                console.log('编辑下载后recordTimeValue---recordTime', this.state.recordTimeValue, this.state.recordTime);
                                                this.handlePlayAudio();
                                                //重新录制后播放声音
                                                this.editDownTimer && clearInterval(this.editDownTimer);
                                                timeValueCopy = this.state.recordTimeValue;
                                                this.editDownTimer = setInterval(() => { //从10秒开始倒计时
                                                    let time = this.state.recordTimeValue - 1;
                                                    if (time <= 0) { //倒计时结束，停止视频播放
                                                        this.editDownTimer && clearInterval(this.editDownTimer);
                                                        this.setState({
                                                            recordTimeValue: this.state.recordTime,
                                                            isCanPress: true,
                                                        });
                                                    } else {
                                                        this.setState({
                                                            recordTimeValue: time,
                                                            isCanPress: false,
                                                        });
                                                    }
                                                }, 1000);
                                            } else {
                                                this.downAudioUrl();
                                            }
                                        }else{ //SDK10007及以上的处理逻辑
                                            if(tryListen){ //如果是录制后点击那就是试听
                                                this.handlePlayAudio();
                                                this.startTimeCountdownInterval();
                                            }else{ //如果不是那就播放,分下载的文件存在存不存在两种情况
                                                let audioPath = IMIFile.storageBasePath  + `/${this.state.typeStr}_download_audio_${this._getTimestampOfUrl(this.state.downTempAudioPath)}_${this.state.location}.aac`;
                                                IMIFile.fileExists(audioPath).then((result)=>{
                                                    if (result) {
                                                        this.handlePlayAudio(audioPath);
                                                        timeValueCopy = this.state.recordTimeValue;
                                                        this.startTimeCountdownInterval();
                                                    } else {
                                                        //先下载，后播放，然后设置
                                                        this.downAudioUrl();
                                                    }

                                                }).catch(() => {

                                                });
                                            }

                                        }
                                    }
                                } else {
                                    // console.log('时间不等走这里',this.state.recordTimeValue,this.state.recordTime,this.state.isCanPress);
                                }
                                this.setState({isCanPress: !this.state.isCanPress}, callback => {
                                    console.log('当前是否可点击---', this.state.isCanPress);
                                });

                            }}>
                            <Image style={{width: 20, height: 20, marginLeft: 12, marginTop: 5}}
                                   source={require('../../../resources/images/newLive/icon_voice_sound.png')}></Image>
                            <Text style={{
                                lineHeight: 30,
                                color: '#4A70A5',
                                width: 25,
                                fontSize: 12,
                                textAlign: 'right',
                                marginRight: 15,
                            }}>{this.state.recordTimeValue + "s"}</Text>
                        </TouchableOpacity>
                    </View>
                    : null}

                <View style={{marginRight: 14,  height: 70}}>
                    <TouchableOpacity
                        disabled={false}
                        style={{marginBottom: 0, paddingBottom: 0, flexDirection: 'row'}}
                        accessibilityLabel={"record_voice"}
                        onPress={() => {
                            // console.log('点击录制');
                            if (!this.state.hasPermission) {
                                showToast(I18n.t('audio_permission_denied'));
                                return;
                            }

                            if (this.state.showVoiceTime) {
                                this.handlePauseAudio();
                            }

                            // this.setState({showRecordAlert: true, isRecording: true, recordTime: 0,});
                            this._startRecordButtonClicked();
                        }}>
                        <Text
                            ellipsizeMode="tail"
                            numberOfLines={1}
                            style={{
                                lineHeight: 66,
                                color: '#4A70A5',
                                fontSize: 15,
                                textAlign: 'right',
                            }}>{this.state.showVoiceTime ? stringsTo('voice_for_re_record') : stringsTo('voice_for_click_record')}</Text>
                    </TouchableOpacity>
                </View>
            </View>

        </View>)
    }

    startTimeCountdownInterval(){
        this.editDownTimer && clearInterval(this.editDownTimer);
        this.editDownTimer = setInterval(() => { //从10秒开始倒计时
            let time = this.state.recordTimeValue - 1;
            if (time <= 0) { //倒计时结束，停止视频播放
                this.editDownTimer && clearInterval(this.editDownTimer);
                this.setState({
                    recordTimeValue: this.state.recordTime,
                    isCanPress: true,
                });
            } else {
                this.setState({
                    recordTimeValue: time,
                    isCanPress: false,
                });
            }
        }, 1000);
    }

    // 开始录音
    _startRecordButtonClicked() {
        if (this.state.isRecording) {
            return;
        }

        // let settings = {// only worked for android
        //     RecordType: "audioRecord",
        //     AVFormatIDKey: 'G711',
        //     AVSampleRateKey: 16000,
        //     AVNumberOfChannelsKey: 1, // mono 单声道
        //     AVLinearPCMBitDepthKey: 16// 位深16
        // };
        // fileName = 'hhhhh.alaw';

        isCheckingPermission = true;
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
            console.log('当前音频状态---', status);
            if (status == 0) {
                this.startRecordAudio();
                isCheckingPermission = false;
            } else if (status == -1) {
                showToast(stringsTo('audio_permission_denied'));
                isCheckingPermission = false;
            }
        })
    }



    /**
     * AudioRecorder.prepareRecordingAtPath(path,option)
     * 录制路径
     * path 路径
     * option 参数
     */
    prepareRecordingPath = (path) => {
        const option = {
            SampleRate: 16000.0, //采样率
            Channels: 1, //通道
            AudioQuality: 'High', //音质
            AudioEncoding: 'aac', //音频编码 aac
            OutputFormat: 'aac_adts', //输出格式
            MeteringEnabled: false, //是否计量
            MeasurementMode: false, //测量模式
            AudioEncodingBitRate: 32000, //音频编码比特率
            IncludeBase64: true, //是否是base64格式
            AudioSource: 0, //音频源
        };
        AudioRecorder.prepareRecordingAtPath(path, option)
    };

    // 开始录音
    async startRecordAudio() {

        this.setState({showRecordAlert: true, isRecording: true, recordTime: 0,});
        try {
            //用来计算录音时长的interval逻辑
            countTime = 0;
            this.timeCounter = setInterval(()=>{
                if (countTime >= 10) {
                    this.setState({
                        showVoiceTime: true,
                        recordTime: 10,
                        recordTimeValue: 10,
                        audioPath: currentRecordPath, //点击确定时，播放的路径才是录制的路径,
                        tempAudioTime: 10,
                        isNotChangeAudio: false
                    });
                    this.stopRecordAudio();
                    this.timeCounter && clearInterval(this.timeCounter);
                    countTime = 0;
                    tryListen = true; //点击确定，可以试听了
                    return;

                }
                countTime++;
                this.setState({
                    recordTime: countTime,
                    recordTimeValue: countTime
                });


            },1000);

            // showVoiceTime  需要做个备份
            let pathV1 = IMIFile.storageBasePath + `/${this.state.typeStr}_quick_audio_${this.state.location}.aac`;
            let pathV2 = IMIFile.storageBasePath + `/${this.state.typeStr}_quick_audio_${this.state.location}_rerecord.aac`;
            let audioPathNew = currentRecordPath==""||currentRecordPath==pathV2 ? pathV1 : pathV2;
            console.log("坑爹---开始录音", audioPathNew);
            // tryListen = true;
            currentRecordPath = audioPathNew;
            //this.setState({audioPath: audioPathNew});
            this.prepareRecordingPath(audioPathNew);
            await AudioRecorder.startRecording();

            /*if(this.state.isVoiceAdd){ //新增铃声
                if(this.state.showVoiceTime){ //录制成功至少一次了，有个存根
                    audioPathNew = IMIFile.storageBasePath + `/${this.state.typeStr}_quick_audio_${this.state.location}_rerecord.aac`;
                    console.log("坑爹---开始录音",audioPathNew);
                    tryListen = true;
                    //this.setState({audioPath: audioPathNew});
                    this.prepareRecordingPath(audioPathNew);
                    await AudioRecorder.startRecording();
                }else{

                }



            }else{

            }*/



        } catch (err) {
            this.timeCounter && clearInterval(this.timeCounter);
            countTime = 0;
            console.error(err)
        }
    }

    // 停止录音
    async stopRecordAudio() {
        try {
            await AudioRecorder.stopRecording();
            // this.setState({ stop: true, recording: false });
            this.setState({isRecording: false, showRecordAlert: false, stop: true});
        } catch (error) {
            // console.error(error);
            this.setState({isRecording: false, showRecordAlert: false, stop: true});
            // 可能录制失败
            // showToast('error',error);
        }
    }

    //播放录音
    async handlePlayAudio(audioPath = null) {

        let playPath = '';
        let audioPathNew = '';
        /*if (this.state.tempShowVoiceTime) {
            audioPathNew = this.state.tempAudioPath;
        } else {
            audioPathNew = this.state.audioPath;
        }*/

        playPath = audioPath?audioPath: this.state.audioPath;

        this.setState({audioPlaying:true});
        whoosh = new Sound(playPath, '', (err) => {
            if (err) {
                // showToast('加载音频失败',err);
                showToast(I18n.t('waitFailedTip'));
                return console.log(err)
            }
            whoosh.setVolume(1); // 设置音量
            whoosh.play(success => {
                if (success) {
                    this.setState({audioPlaying:false});
                    console.log('播放完毕');
                } else {
                    this.setState({audioPlaying:false});
                    console.log('播放失败');
                }
            })
        })
    }

    async handlePauseAudio() {
        whoosh.pause();
        this.setState({audioPlaying:false});
    }

    // 删除录音
    async handleDelAudio() {
        // 初始化录音
        this.prepareRecordingPath(this.state.audioPath);
        let {listOptionData} = this.state
        listOptionData[11].value = ''
        this.setState({
            recordTime: 0,
            stop: false,
            listOptionData
        })
    }


    componentWillUnmount() {
        // 音频相关
        this.countdownTimer && clearInterval(this.countdownTimer);
        this.editDownTimer && clearInterval(this.editDownTimer);
        this.tempCancleTimer && clearInterval(this.tempCancleTimer);
        this.scrollResponderTimer && clearTimeout(this.scrollResponderTimer);
        this.timeCounter && clearInterval(this.timeCounter);
        this._enterBackground && this._enterBackground.remove();
        this._subscribe_blur && this._subscribe_blur();
        if (isAndroid()) {
            BackHandler.removeEventListener('hardwareBackPress', this.onBackHandler);
        }
        tryListen = false;
        countTime = 0;
        currentRecordPath = "";
        lastClickTime = 0;
    }

    onBackHandler = () => {
        console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
        if (this.state.showVoiceTime) {
            this.handlePauseAudio();
        }
        return false;
    };
    _onPressBack = (value=0) => {
        if (this.props.route.params.callback) {
            this.props.route.params.callback(this.state.customVoiceList,new Date().getTime());
        }
        this.props.navigation.pop();
    };

    _getTimestampOfUrl(url){
        if(!url || url == ""){
            return new Date().getTime().toString();
        }
        // 'oss|a1o26TMbbCU/customize_audio/Zu23O3LAyChP0UFAzSzI000000/20220813/1660386674224_bsuabo.aac';
        // return url.split("customize_audio/")[1].split("/")[2].split("_")[0]
        try {
            let urlArr = url.split('/');
            return urlArr[urlArr.length-1].split('_')[0]
        }catch (e){
            return new Date().getTime().toString();
        }

    }

    //判断是否重复快速点击了，如果是，点击无效
    _isClickValid(){
        let nowTime = new Date().getTime();
        let isValid = nowTime - lastClickTime > 1000;
        if(isValid){
            lastClickTime = nowTime;
        }
        return isValid
    }

}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F1F1F1',
    },
    viewContainerNew: {
        width:'100%',
        height: 70,
        flexDirection: 'row',
        backgroundColor: 'white',
        display:"flex"
    },
    titStyle: {
        color: '#333333',
        textAlign: 'left',
        fontWeight: 'bold',
        fontSize: 15,
        marginLeft: 14,
    },
    textInputStyle: {
        // 设置尺寸
        // width: '100%',
        height: 45,
        margin: 14,
        paddingLeft: 14,
        borderRadius: 15,
        // 设置背景颜色
        backgroundColor: '#F1F1F1',
        color: '#000000'
    },
    dialogModal: { // 弹窗
        position: 'absolute',
        bottom: 50, // 距离屏幕底部的边距
        width: screen_width, // 宽度
        borderTopLeftRadius: 20, // 圆角
        borderTopRightRadius: 20, // 圆角
        backgroundColor: '#FFFFFF', // 内容背景色
    },
    dialogBackground: { // 蒙层背景
        flex: 1,
        backgroundColor: 'rgba(0,0,0,0.4)', // 蒙层背景色
    },
});
