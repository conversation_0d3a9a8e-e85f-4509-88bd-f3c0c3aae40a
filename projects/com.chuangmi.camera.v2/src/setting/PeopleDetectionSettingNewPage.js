import React from 'react';

import {StyleSheet, View, BackHandler} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui'

import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import ListItmeWithSwitch from "../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {LetDevice, letDevice} from "../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {showToast} from "../../../../imilab-design-ui";
import {showLoading} from "../../../../imilab-design-ui";
import {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import {isAndroid} from "../../../../imilab-rn-sdk/utils/Utils";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import {imiAlarmEventCloudApi, LetIMIIotRequest} from "../../../../imilab-rn-sdk";
import {EVENT_NOTICE_TYPE} from "../../../../imilab-rn-sdk/components/camera/alarm/api/IMIAlarmEventCloudApi";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";



/**
 * 人形侦测
 */
export  default  class  PeopleDetectionSettingNewPage extends BaseDeviceComponent {
    static propTypes = {};

    constructor(props) {
        super(props);
        this.state = {
            peopleSwitchValue:false,//人形侦测开关
            peopleSwitchEffectValue:false,//人形定时生效开关
            peopleSwitchPushValue:false,//人形侦测推送开关
            trackSwitchValue:false,//人形追踪开关
            effectTimeData:'[0,0,0,0,0,0,0]',//生效时间
            repeat_time:"",//重复时间
            repeat_week:"",//重复星期
            sound_alarm:JSON.stringify({switch:0,name:'1-alarm'}),//智能语音提示
            voiceSwitch:false,//智能语音开关
            voiceUrl:"1-alarm",//智能语音url
            //声光报警
            soundLightSwitchValue: false,//声光报警开关
            lightSwitch: false,//聚光灯开关
            lightMode: 1,//聚光灯模式
            containTime: 10,//聚光灯模式
        }
    }

    UNSAFE_componentWillMount() {
        if (isAndroid()) {
            BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
        }
    }
    componentWillUnmount() {
        if (isAndroid()) {
            BackHandler.removeEventListener('hardwareBackPress',this.onBackHandler);
        }
    }

    onBackHandler = () => {
        console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
        if (navigation.isFocused()) {
            this._onPressBack();
            return true;
        } else {
        }
        return false;
    };

    // 返回上一页
    _onPressBack = () => {
        if (this.props.route.params.callback) {
            console.log('返回传值',this.state.peopleSwitchValue)
            let tempStr;
            if (this.state.peopleSwitchValue){
                tempStr = "1"; // 已开启
            }else {
                tempStr = "0"; // 未开启
            }
            console.log('返回值---',tempStr)
            this.props.route.params.callback(tempStr);
        }
        this.props.navigation.pop();
    };

    componentDidMount() {
        this.getAllValue()
    }
    /**
     * 获取MoveDetectionAttr移动事件属性
     * 参数名称：switch 开关
     * 参数名称：push_flag 推送信息开关
     * 参数名称：active_time_switch 定时生效开关
     * 参数名称：active_time 生效时间
     * 参数名称：repeat_time 重复时间
     * 参数名称：repeat_week 重复周期
     * 参数名称：sound_alarm 智能语音提示
     */
    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.getAssignedPropertyCloud('PeopleDetectionAttr').then((data) => {
            showLoading(false);
            let dataObject = JSON.parse(data);
            let stateProps = {};
            stateProps.peopleSwitchValue = dataObject.switch === 1;
            stateProps.peopleSwitchEffectValue = dataObject.active_time_switch === 1;
            stateProps.peopleSwitchPushValue = dataObject.push_flag === 1;
            console.log('statprops--'+ data);
            this.opPushNotice(stateProps.peopleSwitchValue,stateProps.peopleSwitchPushValue);
            if(dataObject.active_time) {
                stateProps.effectTimeData = dataObject.active_time;
            }

            if(dataObject.repeat_time){
                stateProps.repeat_time = dataObject.repeat_time;
            }
            if(dataObject.repeat_week){
                stateProps.repeat_week = dataObject.repeat_week;
            }
            if(dataObject.sound_alarm){
                stateProps.sound_alarm = dataObject.sound_alarm;
                let voice = JSON.parse(dataObject.sound_alarm);
                console.log("jeff getAllValue voice = ",voice);
                stateProps.voiceSwitch = voice.switch !=0;
                stateProps.voiceUrl = voice.name;
                stateProps.soundLightSwitchValue = voice.switch != 0;
            }
            if (dataObject.light_alarm) {
                //声光的值
                stateProps.light_alarm = dataObject.light_alarm;
                let light_alarm = JSON.parse(dataObject.light_alarm);
                let lightParam = typeof (light_alarm.param) == "string" ? JSON.parse(light_alarm.param) : light_alarm.param;
                stateProps.lightMode = lightParam[0];
                stateProps.containTime = lightParam[1];
            }
            // 统一设置从设备端获取的值
            this.setState(stateProps);
        }).catch(error => {
            showToast(I18n.t('commLoadingFailText'));
            showLoading(false);
            console.log(JSON.stringify(error))
        });

        LetDevice.getPropertyCloud('TrackSwitch').then((data) =>{ //
            console.log('TrackSwitch--------' + data);
            if(data=="0"|| data==0){
                this.setState({trackSwitchValue:false});
            }else {
                this.setState({trackSwitchValue:true});
            }
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    render() {
        global.navigation = this.props.navigation;
        let {showSoundBroadcast,showTrackSwitch,showSoundLightAlarm} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        return (<View style={styles.container}>
            <NavigationBar
                title={stringsTo('people_event')}
                left={[{key: NavigationBar.ICON.BACK, onPress: ()=>this._onPressBack(),accessibilityLabel:"people_detection_go_back"}]}
                right={[]}
            />
                {/*人形侦测开关*/}
                <ListItmeWithSwitch title={stringsTo('people_event')} value={this.state.peopleSwitchValue}
                                    accessibilityLabel={["people_detection_switch_off","people_detection_switch_on"]}
                                    onValueChange={(value) => {
                                        this.updatePeopleMsg(value,this.state.peopleSwitchPushValue,this.state.peopleSwitchEffectValue,
                                            this.state.effectTimeData,this.state.repeat_time,this.state.repeat_week,this.state.sound_alarm,this.state.light_alarm, 0);
                                    }}/>
                {/*人形追踪*/}
            {this.state.peopleSwitchValue && showTrackSwitch ?
            <ListItmeWithSwitch title={stringsTo('settings_alarm_human_track_title')} value={this.state.trackSwitchValue}
                                                              onValueChange={(value) => {
                                                                  if(value){
                                                                      LetDevice.propertyOn("TrackSwitch").then(()=>{
                                                                          this.setState({
                                                                              trackSwitchValue:value
                                                                          });
                                                                      }).catch(err=>{
                                                                          this.setState({
                                                                              trackSwitchValue:!value
                                                                          });
                                                                          showToast(I18n.t('operationFailed'));
                                                                      });
                                                                  }else{
                                                                      LetDevice.propertyOff("TrackSwitch").then(()=>{
                                                                          this.setState({
                                                                              trackSwitchValue:value
                                                                          });
                                                                      }).catch(err=>{
                                                                          this.setState({
                                                                              trackSwitchValue:!value
                                                                          });
                                                                          showToast(I18n.t('operationFailed'));
                                                                      });
                                                                  }
                                                              }}
                                                              accessibilityLabel={["housekeeping_assistant_human_off","housekeeping_assistant_human_on"]}
            /> :null}
            {this.state.peopleSwitchValue && showSoundBroadcast?(<ListItem title={stringsTo('intelligent_voice_tip')} accessibilityLabel={"intelligent_voice_tip"} value={this.state.voiceSwitch ? stringsTo('preset_opened'):stringsTo('preset_closed')} onPress={() => {
                this.props.navigation.navigate('IntelligentVoiceSetting', {
                    type: 'peopleDetectVoice',
                    detectSwitch:this.state.peopleSwitchValue,
                    push_flag:this.state.peopleSwitchPushValue,
                    active_time_switch:this.state.peopleSwitchEffectValue,
                    active_time:this.state.effectTimeData,
                    repeat_time:this.state.repeat_time,
                    repeat_week:this.state.repeat_week,
                    voiceSwitch:this.state.voiceSwitch,
                    voiceUrl:this.state.voiceUrl,
                    callback: ((voiceUrl,voiceSwitch) => {
                        console.log('返回获取提示音--', voiceUrl,voiceSwitch);
                        this.setState({voiceSwitch:voiceSwitch,voiceUrl:voiceUrl})
                    })
                })
            }}/>):null}
            {this.state.peopleSwitchValue && showSoundLightAlarm ?
                (<ListItem title={stringsTo('soundLightAlarm')}
                           value={this.state.voiceSwitch ? stringsTo('preset_opened') : stringsTo('preset_closed')}
                           accessibilityLabel={"soundLightAlarm"}
                           onPress={() => {
                               this.props.navigation.navigate('AudioAlarmSetPageV2', {
                                   location: 1,
                                   type: 'peopleDetection',
                                   soundLightSwitchValue: this.state.soundLightSwitchValue,
                                   voiceSwitch: this.state.voiceSwitch,
                                   voiceUrl: this.state.voiceUrl,
                                   lightSwitch: this.state.lightSwitch,
                                   lightMode: this.state.lightMode,
                                   containTime: this.state.containTime,
                                   sound_alarm: this.state.sound_alarm,
                                   light_alarm: this.state.light_alarm,
                                   //这块数据声光报警页面不用动，只是设置的时候有用到
                                   repeat_time: this.state.repeat_time,
                                   repeat_week: this.state.repeat_week,
                                   detectSwitch: this.state.peopleSwitchValue,
                                   push_flag: this.state.peopleSwitchPushValue,
                                   active_time_switch: this.state.peopleSwitchEffectValue,
                                   callback: ((backParams) => {
                                       console.log('返回声光报警--', backParams);
                                       //声光报警的总开关
                                       let totalSwitch = backParams.totalSwitch;
                                       let tempVoiceSwitch = false;
                                       let tempLightSwitch = false;
                                       if (totalSwitch) {
                                           //声光报警的开关打开了
                                           tempVoiceSwitch = true;
                                           tempLightSwitch = true;
                                       }
                                       this.setState({
                                           soundLightSwitchValue: totalSwitch,
                                           voiceSwitch: tempVoiceSwitch,
                                           voiceUrl: backParams.voiceUrl,
                                           lightSwitch: tempLightSwitch,
                                           lightMode: backParams.spotlightMode,
                                           containTime: backParams.containTime,
                                           sound_alarm: backParams.sound_alarm,
                                           light_alarm: backParams.light_alarm,
                                       })

                                   })
                               })
                           }}/>) : null}
            {this.state.peopleSwitchValue?
                <ListItem title={stringsTo('timed_effective')} accessibilityLabel={"time_effective"} value={this.state.peopleSwitchEffectValue ? stringsTo('preset_opened'):stringsTo('preset_closed')} onPress={() => {
                    this._gotoEffectiveTimeSetting();
                }}/>:null
            }

            {/*移动侦测推送开关*/}
            {this.state.peopleSwitchValue?
                <ListItmeWithSwitch title={stringsTo('message_push')} value={this.state.peopleSwitchPushValue}
                                    onValueChange={(value) => {
                                        this.updatePeopleMsg(this.state.peopleSwitchValue,value,this.state.peopleSwitchEffectValue,
                                            this.state.effectTimeData,this.state.repeat_time,this.state.repeat_week,this.state.sound_alarm,this.state.light_alarm, 1);
                                    }}
                                    accessibilityLabel={["housekeeping_assistant_motion_detecting_off","housekeeping_assistant_motion_detecting_on"]}
                />:null
            }


        </View>);
    }

    /**
     * 进入定时生效时间设置界面
     * @private
     */
    _gotoEffectiveTimeSetting(){
        let areaAllArr = [];
        let startTime = 0;
        let endTime = 0;
        if(this.state.repeat_time){
            let timeArr = this.state.repeat_time.split(',');
            startTime = timeArr[0];
            endTime = timeArr[1];
        }
        let dataArr = {
            detectSwitch:this.state.peopleSwitchValue,
            push_flag:this.state.peopleSwitchPushValue,
            active_time_switch:this.state.peopleSwitchEffectValue,
            active_time:this.state.effectTimeData,
            startTime:startTime,
            endTime:endTime,
            repeatTime:this.state.repeat_week,
            sound_alarm:this.state.sound_alarm,
            light_alarm:this.state.light_alarm,
        };
        areaAllArr.push(dataArr);

        let isEdit = false;
        if (this.state.effectTimeData == '[0,0,0,0,0,0,0]'){
            isEdit = false;
        }else {
            isEdit = true;
        }
        this.props.navigation.navigate('AngelTimeSetting',{
            type:'peopleEffectTime',
            location:1,
            isEdit:isEdit,
            timeSwitch:this.state.peopleSwitchEffectValue,
            areaAllArr:areaAllArr,
            callback:((timeData) => {
                let valueData = JSON.parse(timeData);
                this.setState({peopleSwitchEffectValue:valueData.timeSwitch,
                    effectTimeData:'['+valueData.active_time+']',
                    repeat_time:valueData.startTime +","+valueData.endTime,
                    repeat_week:valueData.repeatTime,
                });
            })
        })
    }

    /**
     * 获取PeopleDetectionAttr人形事件属性
     * 参数名称：switch 开关
     * 参数名称：push_flag 推送信息开关
     * 参数名称：active_time_switch 定时生效开关
     * 参数名称：active_time 生效时间
     * 参数名称：repeat_time 重复时间
     * 参数名称：repeat_week 重复周期
     * 参数名称：sound_alarm 智能语音提示
     * 参数名称：position 设置参数的位置
     * 参数名称：light_alarm 声光的值，有声光报警的项目有这个值
     */
    updatePeopleMsg(switch_on,push_flag,active_time_switch,active_time,repeat_time,repeat_week,sound_alarm,light_alarm,position) {
        showLoading(stringsTo('commWaitText'), true);
        let value = {
            switch: switch_on ? 1 : 0, push_flag: push_flag ? 1 : 0, active_time_switch: active_time_switch ? 1 : 0,
            active_time: active_time, repeat_time: repeat_time, repeat_week: repeat_week, sound_alarm: sound_alarm
        };
        let {showSoundLightAlarm} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        if (showSoundLightAlarm) {
            //声光报警，声光
            value['light_alarm'] = light_alarm;
        }
        let params = {PeopleDetectionAttr: value};
        LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
            this.opPushNotice(switch_on,push_flag);
            showToast(I18n.t('settings_set_success'));
            showLoading(false);
            if(position == 0){
                this.setState({peopleSwitchValue: switch_on});
            }else if(position == 1){
                this.setState({peopleSwitchPushValue: push_flag});
            }else if(position == 2){
                this.setState({peopleSwitchEffectValue: active_time_switch});
            }
        }).catch((error) => {
            console.log('jeff 失败----',error);
            showToast(I18n.t('waitFailedTip'));
            if(position == 0){
                this.setState({peopleSwitchValue: !switch_on});
            }else if(position == 1){
                this.setState({peopleSwitchPushValue: !push_flag});
            }else if(position == 2){
                this.setState({peopleSwitchEffectValue: !active_time_switch});
            }
            showLoading(false);
        });
        let jsonParams = {PeopleDetectSwitch:switch_on ? 1 : 0,PeopleScheduledSwitch:active_time_switch ? 1 : 0,PeoplePushSwitch:push_flag ? 1 : 0};
        try {
            if (showSoundLightAlarm && light_alarm) {
                //声光报警，声光
                let jsonObject = JSON.parse(light_alarm);
                jsonParams['PeopleLightSoundSwitch'] = jsonObject.switch;
            }
        }catch (e) {

        }

        IMILogUtil.uploadClickEventValue(jsonParams);
    }

    /**
     * 推送打开的条件，总开关打开，并且消息推送开关也打开
     * @param switch_on 总开关
     * @param push_flag 消息推送开关
     */
    opPushNotice(switch_on,push_flag){
        let pushNotice = false;
        if (switch_on && push_flag){
            //开关全部开的情况下，才会有推送
            pushNotice = true;
        }
        imiAlarmEventCloudApi.setEventPushNotice(LetDevice.deviceID,LetDevice.model,EVENT_NOTICE_TYPE.people,pushNotice)
            .then(res=>{}).catch(err=>{});
    }
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: imiThemeManager.theme.pageBg,
    }
});
