/**
 * HomePageLivePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 * @property {string}  pageBackgroundColor 直播播放器下方空白页面的颜色
 *
 * 示例:
 * <HomePageLivePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </HomePageLivePlayerComponent>
 *
 * @author: shenyonggang
 * @date: 2021/02/01
 */

import React, {Component} from 'react';
import {
    View, Text, BackHandler, ActivityIndicator, ImageBackground, TouchableOpacity, Image, StyleSheet
} from 'react-native';
import {IMIGotoPage, LetDevice} from "../../../imilab-rn-sdk";
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from "../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIFile from "../../../imilab-rn-sdk/native/local-kit/IMIFile";
import I18n, {stringsTo} from "../../../globalization/Localize";
import LivePlayerToolBarView from "../../com.chuangmi.camera.IPC031/src/CommonView/PlayerToolBarView/LivePlayerToolBarView";
import NavigationBar from "../../com.chuangmi.camera.IPC031/src/CommonView/NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';
import TouchableOpacityText from "../../com.chuangmi.camera.IPC031/src/CommonView/TouchableOpacityText/TouchableOpacityText";
import ModalView from "../../com.chuangmi.camera.IPC031/src/CommonView/ModalView/ModalView";

import PropTypes from 'prop-types';
import Utils, {getScreenWidth, isIos} from "../../../imilab-rn-sdk/utils/Utils";
import {timeFilter} from "../../../imilab-rn-sdk/utils/DateUtils";

import IMIToast from "../../../imilab-design-ui/src/widgets/IMIToast";
import {XText} from "react-native-easy-app";
import {colors, CONST, RoundedButtonView} from "../../../imilab-design-ui";
import ImageButton from "../../com.chuangmi.camera.IPC031/src/CommonView/ImageButton/ImageButton";
import {PLAYER_EVENT_CODE} from "../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import {showToast} from "../../../imilab-design-ui/src/widgets/Loading";
import TextImageButton from "../../com.chuangmi.camera.IPC031/src/CommonView/TextImageButton/TextImageButton";
import FastReplyView from "./FastReplyView";
import IMIPermission from "../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import AlertDialog from "../../../imilab-design-ui/src/widgets/settingUI/AlertDialog";
import Sound from "react-native-sound";
import Toast from "react-native-root-toast";
import {IMINativeLifeCycleEvent} from "../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import moment from "moment";
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;

const LIVE_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading',
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};
Object.freeze(LIVE_PLAYER_STATUS);
const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});
let isCheckingPermission = false;
let lastClickSnapPhoto = 0; //上一次点击截图的时间
let time = 0;

export default class VoiceCallPlayerComponent extends Component {
    static LIVE_PLAYER_STATUS = LIVE_PLAYER_STATUS;

    constructor(props, context) {
        super(props, context);
        this.state = {
            qualityIndex: 1,
            qualityVisible: false,
            bps: -1,
            isFullScreen: false,
            mute: true,
            recording: false,
            recordDuration: 0,
            showFullScreenTools: false,
            sound:null,
            isLoading: false,
            isPlaying: false,
            showErrorView: false,
            showPauseView: false,
            errorCode: null,
            alarmSwitch: false, //报警开关
            snapshotVisible: false,
            screenShotPath: null,
            isCalling: false,
            isMicOpen: false, //麦克风是否打开
            showFastReply: false,
            hasNewReply: false, // 控制快捷回复数据是否刷新
            alarmSwitchDialogVisible: false, //报警按钮提示对话框的可见性标志位
            startCallTime: 0,
            callDuration: 0,
            isMicOpenState:false,// 控制快速点击麦克风
        }
    }

    static propTypes = {
        navBar: PropTypes.func,
        videoRef: PropTypes.func,
        navBarRight: PropTypes.array,

        toolBarMoreItems: PropTypes.array,
        videoSubView: PropTypes.func,

        coverView: PropTypes.func,
        loadingView: PropTypes.func,
        pauseView: PropTypes.func,
        errorView: PropTypes.func,
        isSleepStatus: PropTypes.bool,//休眠状态
        isMove: PropTypes.bool,//全屏触摸转动云台
        isOnLine: PropTypes.bool,//在线状态

        onLivePlayerStatusChange: PropTypes.func,

        fullScreenToolBarMoreItems: PropTypes.array,
        lensCorrect: PropTypes.shape({
            use: PropTypes.bool.isRequired,
            x: PropTypes.number,
            y: PropTypes.number
        }),
        onVideoClick: PropTypes.func,
        pageBackgroundColor: PropTypes.string,
        qualityData: PropTypes.array,//清晰度数据
    };

    static defaultProps = {
        navBar: undefined,
        navBarRight: [],
        toolBarMoreItems: [],
        fullScreenToolBarMoreItems: [],
        lensCorrect: {use: false, x: 0, y: 0},
        isSleepStatus:false,
        isOnLine:true,
        isMove:false,
        qualityData: [
            {title:stringsTo("quality_sd"),index:0},
            {title:stringsTo("quality_25K"),index:1}
        ],
    };


    UNSAFE_componentWillMount() {

        Orientation.lockToPortrait();
        Orientation.addOrientationListener(this._orientationDidChange);
        LetDevice.getPropertyCloud('StreamVideoQuality').then(data => {
            this.setState({qualityIndex: parseInt(data)>1?1:parseInt(data)});
        }).catch(error => {
        });
        this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onPressBack());
        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            if (!isCheckingPermission&&this.state.isCalling) {
                this.IMIVideoView.stop();//音视频单通道，关闭视频流，通话随即关闭。没有这句，退出后台，声音没法及时关闭
                this._onPressOutStopSpeak();
            }
        });
    }

    componentDidMount() {

        //30秒超时未接听，退出响铃接听页面
        this.waitTimer=setTimeout(() => this._onPressOutStopSpeak(), 40000);

        // 加载声音文件
        let musicPath = require('../../com.chuangmi.camera.IPC031/resources/sounds/1.mp3');       // 匹配的格式挺多的....
        this.sound = new Sound(musicPath,(error)=>{
            if(error){
                console.log("+++++++++++++++++",error)
            }else{
                this.sound.setVolume(0.5);
                this.sound.setCurrentTime(0);
                this.sound.setNumberOfLoops(-1);
                this.sound.play();
            }
        })
    }

    _stopMusicPlay(){
        this.sound && this.sound.stop();
        this.sound && this.sound.release();
    }

    componentWillUnmount() {
        this._stopMusicPlay();
        Orientation.removeOrientationListener(this._orientationDidChange);
        this.IMIVideoView && this.IMIVideoView.stop();
        this.IMIVideoView && this.IMIVideoView.destroy();
        this.IMIVideoView = null;
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.backHandler && this.backHandler.remove();
        this._enterBackground&&this._enterBackground.remove();
    }


    _orientationDidChange = (orientation) => {
        if (orientation === 'LANDSCAPE') {
        } else {
            // do something with portrait layout
        }
    }


    _onPressFullScreen = () => {
        Orientation.lockToLandscape();
        this.setState({isFullScreen: true});
        NavigationBar.setStatusBarHidden(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
    };

    _exitFullScreen = () => {
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
    }

    _onPressBack = () => {
        if (this.state.isFullScreen) {
            this._exitFullScreen();
        } else {
            IMIGotoPage.exit();
        }
    };


    _onPressFullScreenTools = () => {
        this.setState({showFullScreenTools: true});
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.fullScreenTooltsTimer = setTimeout(() => {
            this._onCloseFullScreenTools();
        }, 5000);
    }

    _onCloseFullScreenTools() {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.setState({showFullScreenTools: false});
    }

    _onPressMute = () => {
        if(!this._canStepIn())  return;
        this.setState({mute: !this.state.mute})
    };

    getMute() {
        return this.state.mute;
    }

    setMute(mute) {
        this.setState({mute: mute});
    }

    //点击截屏按钮
    _onPressScreenShot = () => {
        if(!this._canStepIn())  return;
        if(new Date().getTime()-lastClickSnapPhoto<1000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
            return;
        }
        lastClickSnapPhoto = new Date().getTime();
        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);


        if(this.state.snapshotVisible){
            this.setState({ snapshotVisible: false});
        }
        isCheckingPermission = true;
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
            if (status === 0) {
                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                    if (status2 === 0) {
                        let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                        this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                            IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, this.props.albumName).then(_ => {
                                this.setState({screenShotPath:currentSnapshotPath,snapshotVisible: true});
                                IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
                                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                                    this.setState({ snapshotVisible: false});
                                }, 3000);
                            });
                        });
                    } else if (status2 === -1) {
                        showToast(stringsTo('storage_permission_denied'));
                    }
                    isCheckingPermission = false;
                });
            } else if (status === -1) {
                isCheckingPermission = false;
                showToast(stringsTo('storage_permission_denied'))
            }
        })


    };

    //点击录屏按钮
    _onPressRecord = () => {
        console.log("点击录屏");
        console.log("排查录屏卡死问题------录屏-----点击录屏按钮",this.state.recording);
        if(!this._canStepIn())  return;
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        } else {
            isCheckingPermission = true;
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
                if (status === 0) {
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                        if (status2 === 0) {
                            time=moment(new Date().getTime()).format('yyyyMMDD')+"_"+new Date().getTime();
                            let pathUrl = VEDIO_RECORD_PATH;
                            if (isIos()){
                                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
                            }
                            this.IMIVideoView.startRecord(pathUrl).then(_ => {
                                console.log("排查录屏卡死问题------录屏-----开始录屏");
                                this.setState({recording: true, recordDuration: 0});
                            });
                        } else if (status2 === -1) {
                            showToast(stringsTo('storage_permission_denied'));
                        }
                        isCheckingPermission = false;
                    })
                } else if (status === -1) {
                    showToast(stringsTo('storage_permission_denied'));
                    isCheckingPermission = false;
                }
            })

        }
    };

    //停止录像并保存在相册
    _stopRecord(forSave=true){
        if(this.state.recordDuration<6){
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            forSave = false;
        }
        this.IMIVideoView.stopRecord().then(_ => { //停止录制
            if(!forSave){ //只停止，不保存
                // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                return;
            }
            let pathUrl=VEDIO_RECORD_PATH;
            if (isIos()){
                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
            }

            IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName).then(_ => { //转存视频
                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                    this.setState({screenShotPath: currentSnapshotPath, snapshotVisible: true});
                    this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                        this.setState({snapshotVisible: false});
                    }, 3000);
                });
                IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
            });
        });
        this.setState({recording: false, recordDuration: 0});
    }

    //IOS在视频流暂停时,将录制的视频保存到相册
    _saveVideoToPhotosAlbum() {
        console.log("排查页面卡死问题------录屏因为实时流停止------",this.state.recordDuration,this.state.isPlaying);
        if (this.state.recordDuration < 6&&this.state.recordDuration>0) { //Android因为直播流停止recordDuration会直接边为0
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            this.setState({recording: false, recordDuration: 0});
            return;
        }

        let pathUrl = VEDIO_RECORD_PATH;
        if (isIos()){
            pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
        }
        IMIFile.saveVideoToPhotosAlbum(pathUrl, this.props.albumName).then(_ => { //转存视频
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            let tempShotPath = `${IMIFile.storageBasePath}/tmp/snapshot.jpg`;
            this.IMIVideoView.screenShot(tempShotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                this.setState({screenShotPath: tempShotPath, snapshotVisible: true});
                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                    this.setState({snapshotVisible: false});
                }, 3000);
            });
            IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        });
        this.setState({recording: false, recordDuration: 0});
    }

    _onPressInStartSpeak = () => {
        this.waitTimer && clearTimeout(this.waitTimer);
        isCheckingPermission = true;
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
            if (status === 0) {
                isCheckingPermission = false;
                this.IMIVideoView && this.IMIVideoView.startSpeak();
                this.setState({isCalling: true, isMicOpen: true, callDuration: 0,mute:false})
                this.callingTimer && clearInterval(this.callingTimer);
                this.callingTimer = setInterval(() => { //3秒后截屏缩略图自动隐藏
                    let curduration = this.state.callDuration;
                    curduration = curduration++;
                    this.setState({callDuration: curduration + 1});
                }, 1000);

            } else if (status === -1) {
                isCheckingPermission = false;
                showToast(stringsTo('audio_permission_denied'))
            }
        })
    }

    /*打开或关闭麦克风*/
    _onPressMic = () => {
        if (this.state.isMicOpen) {
            // this.setState(
            //     {
            //         isMicOpen: false,
            //         //mute: true
            //     });
            this.IMIVideoView && this.IMIVideoView.stopSpeak();
            /*this.pressMicTimer = setTimeout(() => {
                this.setMute(false);
            }, 100);*/

        } else {
            // this.setState({isMicOpen: true});
            this.IMIVideoView && this.IMIVideoView.startSpeak();
            //this.pressMicTimer && clearTimeout(this.pressMicTimer);
        }
        this.setState({isMicOpen:!this.state.isMicOpen,isMicOpenState:true});
        this.t && clearTimeout(this.t);
        this.t = setTimeout(() => {
            this.setState({isMicOpenState:false});
            this.t && clearTimeout(this.t);
        }, 1000);
    }

    _onPressOutStopSpeak = () => {
        this.callingTimer && clearInterval(this.callingTimer);
        this.waitTimer && clearTimeout(this.waitTimer);
        if (this.state.isCalling) {
            this.IMIVideoView && this.IMIVideoView.stopSpeak();
        }
        this.IMIVideoView && this.IMIVideoView.stop();
        this.IMIVideoView && this.IMIVideoView.destroy();
        this.IMIVideoView = null;
        if(this.props.navigation.canGoBack()){
            this.props.navigation.pop();
        }else{
            IMIGotoPage.exit();
        }
    }

    //点击报警按钮
    _onClickWarnButton = () => {
        if(!this._canStepIn())  return;
        this.autoTurnOffAlarmTimer && clearTimeout(this.autoTurnOffAlarmTimer);
        if (this.state.alarmSwitch) { //正在报警中,直接停止
            LetDevice.sendDeviceServerRequest("StreamingAlarm", JSON.stringify({StreamingAlarmSwitch: 0})).then((data) => {
                console.log(' sendDeviceServerRequest  then-> stopAlarmSwitch' + data);
                this.setState({alarmSwitch: false});
            }).catch((error) => {
                console.log('sendDeviceServerRequest error ' + error)
            });
            return;
        }
        this.setState({alarmSwitchDialogVisible: true});
    };

    _onPressQuality(index) {
        if (this.state.qualityIndex === index) return;
        LetDevice.setPropertyCloud(JSON.stringify({"StreamVideoQuality": index})).then(data => {
            this.setState({qualityIndex: index});
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }


    /* _onEventChange = (event) => {
         if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
             console.log("直播流----_onEventChange,回调网速值");
             this.setState({bps: event.extra.bps})
         } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
             console.log("直播流----_onEventChange,开始启用");
             this.setState({isLoading: true, isPlaying: false, showPauseView: false, showErrorView: false});
             this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
         } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
             console.log("直播流----_onEventChange,出现关键帧");
             this.setState({isLoading: false, isPlaying: true});
             this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
         } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
             console.log("直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
             this.setState({isLoading: false, isPlaying: false, showPauseView: this.state.isPlaying});
             this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);
         } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
             console.log("直播流----_onEventChange,开始播放");
             // this.setState({isLoading:false});
         }
     };*/

    _onEventChange = (event) => {
        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            console.log("直播流----_onEventChange,回调网速值",event.extra.bps);
            // this.setState({bps: this._getAverageBps(event.extra.bps)});
            event.extra.bps>0&&this.state.isLoading&&!this.state.isPlaying&&this.setState({isLoading:false,isPlaying:true});//此句为了规避正常播放时突然出现loading框的问题

        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            console.log("直播流----_onEventChange,开始启用");
            this.setState({isLoading: true, isPlaying: false, showPauseView: false, showErrorView: false});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            console.log("直播流----_onEventChange,出现关键帧");
            this.setState({isLoading: false, isPlaying: true});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
            console.log("直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            this.setState({isLoading: false, isPlaying: false, showPauseView: true}); //Android会调用两次，所以停止了也没有暂停按钮
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);

            if(this.state.recording){ //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
                if(CONST.isAndroid&&this.state.isPlaying){
                    this._stopRecord();
                }else{ //录像停止IOS会自动停止视频录制，所以直接转存即可。Android因为直播流异常也只需直接转存视频即可
                    this._saveVideoToPhotosAlbum();
                }

            }

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            console.log("直播流----_onEventChange,开始播放");
            //this.setState({isLoading:false});
        }
    };

    /*优化网速值有较大跳变的问题，取平均值*/
    /* _getAverageBps(currentBps){
         let average = 0;
         if(bpsArray.length<5){
             bpsArray.push(currentBps);
         }else{
             bpsArray.shift();
             bpsArray.push(currentBps);
         }
         let total = bpsArray.reduce((a,b)=>{
             return a+b;
         });
         return  parseInt(total/bpsArray.length);
     }*/


    _onRecordTimeChange = (event) => {
        this.setState({recordDuration: event.extra})
    }

    /**
     * 竖屏状态视屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenVideoViewArea() {
        return (
            this.props.navBar ? this.props.navBar(this.state.bps, this.state.isFullScreen) : (
                <NavigationBar
                    type={NavigationBar.TYPE.DARK} backgroundColor={"transparent"}
                    // title={LetDevice.devNickName}
                    // subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
                />)
        );
    }

    /**
     * 清晰度选择器
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenQualityPopView() {
        return (
            <ModalView visible={this.state.qualityVisible}
                       onClose={_ => this.setState({qualityVisible: false})}>
                <View style={{flex: 1, flexDirection: "column", justifyContent: "center", alignItems: "center"}}>
                    {
                        this.props.qualityData.map((item, index) => {
                            let qualityData = this.props.qualityData[index];
                            return (
                                <TouchableOpacityText
                                    key={`qualityItem_${index}`}
                                    title={qualityData.title}
                                    style={{
                                        width: 120,
                                        height: 40,
                                        borderRadius: 20,
                                        backgroundColor: index == this.state.qualityIndex ? "#496EE0" : "rgba(255,255,255,0.3)",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        marginVertical: 10
                                    }}
                                    textStyle={{color: 'rgba(255,255,255,0.9)', fontSize: 15, fontWeight: "bold"}}
                                    onPress={_ => {
                                        this.setState({qualityVisible: false});
                                        this._onPressQuality(qualityData.index);
                                    }}
                                />
                            );
                        })
                    }
                </View>
            </ModalView>
        );
    }

    _renderRecordingView() {
        let duration = this.state.recordDuration > 0 ?  `${timeFilter(this.state.recordDuration)}` : "00:00";
        return (
            <View style={{
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: "center",
                alignItems: "center",
                marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
            }}>
                <View style={{backgroundColor: "#E74D4D", opacity: 0.9, width: 6, height: 6, borderRadius: 3}}/>
                <Text style={{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}}>{duration}</Text>
            </View>
        );
    }

    _loadingView() {
        if (!this.state.isLoading) return;
        return (<View pointerEvents="box-none"
                      style={{
                          position: "absolute",
                          width: "100%",
                          height: "100%",
                          display: "flex",
                          justifyContent: "center",
                          alignItems: "center"
                      }}
        >
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.state.showErrorView) return;
        return (
            <View pointerEvents="box-none"
                  style={{
                      position: "absolute",
                      width: "100%",
                      height: "100%",
                      display: "flex",
                      justifyContent: "center",
                      alignItems: "center"
                  }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
                />

                <RoundedButtonView buttonText={stringsTo('errorClickCommonRetry')}
                                   buttonStyle={{
                                       margin: 14,
                                       paddingHorizontal: 15,
                                       height: 40
                                   }}
                                   onPress={() => {
                                       this.IMIVideoView.start();
                                   }}/>
            </View>
        );
    }


    _renderSnapshotView() {
        if (!this.state.snapshotVisible) {
            return null;
        }
        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                bottom: 19,
                left: 14,
                width: 140,
                height: 80,
                zIndex: 999,
            }}>
                <ImageButton
                    style={{
                        width: "100%",
                        height: "100%",
                        borderWidth: 2,
                        borderColor: 'white',
                        borderRadius: 10
                    }}
                    source={{uri: 'file://' + this.state.screenShotPath}}
                    onPress={(_) => { //TODO 跳转到相册预览？产品要求，不可跳转
                        // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                    }}
                />
            </View>
        )
    }

    /*中间tab功能栏*/
    _renderMiddleLayout() {
        let qualityData = this.props.qualityData[this.state.qualityIndex];
        return (
            <View style={{flexDirection: "column",height:50}}>
                <View style={{flexDirection: "row", width: '95%', marginTop: 12, marginHorizontal: 14}}>

                    <View style={{flexDirection: "row", justifyContent: "flex-start"}}>
                        {/*麦克*/}
                        <TextImageButton style={{ marginLeft: 6}}
                                         source={this.state.isMicOpen ? require("./res/icon_mic_open.png") : require("./res/icon_mic_close.png")}
                                         imageStyle={{width: 30, height: 30}}
                                         onPress={() => {
                                             if (this.state.isMicOpenState){
                                                 return;
                                             }
                                             this._onPressMic()
                                         }}
                        />

                        <TextImageButton style={{ marginLeft: 20}}
                                         source={this.state.mute ? require("./res/icon_voice_close.png") : require("./res/icon_voice_open.png")}
                                         imageStyle={{width: 30, height: 30}}
                                         onPress={() => {
                                             this._onPressMute()
                                         }}
                        />
                    </View>

                    {/*清晰度切换按钮,录屏时置灰禁用*/}
                    <View style={{flex: 1, flexDirection: "row", justifyContent: "flex-end"}}>
                        <View style={{
                            borderColor: this.state.recording?"#FFFFFF66":"#FFFFFFCC",
                            borderRadius: 3,
                            borderWidth: 1,
                            minWidth: 40,
                            height: 30,
                            textAlign: 'center',
                            textAlignVertical: 'center',
                            marginRight: 20,
                            flexWrap: 'nowrap',justifyContent:'center',
                        }}>
                            <Text style={{
                                color: this.state.recording?"#FFFFFF55":"#FFFFFF",
                                textAlign: 'center',
                                textAlignVertical: 'center',
                                flexWrap: 'nowrap'
                            }}
                                  onPress={_ => {
                                      if (!this.state.recording){
                                          this.setState({qualityVisible: true});
                                      }
                                  }}
                            >
                                {qualityData.title}
                            </Text>
                        </View>

                    </View>

                </View>
            </View>
        )
    }

    /*底部tab功能栏*/
    _renderBottomLayout() {
        // justifyContent: 'flex-end'
        return (
            <View style={{flexDirection: "column",top:45}}>
                <View style={{
                    flexDirection: "row",
                    height: 110,
                    // marginBottom: 30,
                    marginHorizontal: 14,
                    alignItems: 'flex-start',
                }}>
                    {/*截屏*/}
                    <TextImageButton style={{flex: 1}}
                                     title={stringsTo("bottom_snapshot_string")}
                                     source={require("./res/icon_screenshot_new.png")}
                                     imageStyle={{width: 60, height: 60}}
                                     textStyle={styles.bottomButtonText}
                                     onPress={() => {
                                         this._onPressScreenShot()
                                     }}
                    />
                    {/*录像*/}
                    <TextImageButton style={{flex: 1}}
                                     title={stringsTo("bottom_record_string")}
                                     source={this.state.recording ? require("./res/icon_recoding_new.png") : require("./res/icon_record_new.png")}
                                     imageStyle={{width: 60, height: 60}}
                                     textStyle={styles.bottomButtonText}
                                     onPress={() => {
                                         this._onPressRecord()
                                     }}
                    />
                    {/*挂断*/}
                    <TextImageButton
                        // style={{flex: 1.5}}
                        style={{flex: 1}}
                        title={stringsTo("call_hangup")}
                        source={require("./res/call_icon_hangup.png")}
                        imageStyle={{width: 60, height: 60}}
                        textStyle={styles.bottomButtonText}
                        onPress={() => {
                            this._onPressOutStopSpeak();
                        }}
                    />
                    {/*快捷回复*/}
                    <TextImageButton style={{flex: 1}}
                                     title={stringsTo("call_reply_str")}
                                     source={require("./res/icon_reply_new.png")}
                                     imageStyle={{width: 60, height: 60}}
                                     textStyle={styles.bottomButtonText}
                                     onPress={() => {
                                         if(!this._canStepIn())  return;
                                         this.setState({showFastReply: true, hasNewReply: !this.state.hasNewReply})
                                     }}
                    />

                    {/*/!*报警*!/*/}
                    <TextImageButton
                        // style={{flex: 1, marginLeft: 12}}
                        style={{flex: 1}}
                        title={stringsTo("call_warning_str")}
                        source={this.state.alarmSwitch ? require("./res/icon_alarm_on_new.png") : require("./res/icon_alarm_off_new.png")}
                        imageStyle={{width: 60, height: 60}}
                        textStyle={styles.bottomButtonText}
                        onPress={this._onClickWarnButton}
                    />
                </View>
            </View>
        )
    }

    /*底部tab功能栏*/
    _renderBottomCallLayout() {
        return (
            <View style={{flexDirection: "column", justifyContent: 'flex-end',bottom:60}}>
                <View style={{flexDirection: "row", height: 110, margin: 30, marginHorizontal: 14}}>

                    <TextImageButton style={{flex: 1}}
                                     title={stringsTo("call_hangup")}
                                     source={require("./res/call_icon_hangup.png")}
                                     imageStyle={{width: 60, height: 60}}
                                     textStyle={styles.bottomButtonText}
                                     onPress={() => {
                                         this._stopMusicPlay();
                                         this._onPressOutStopSpeak()
                                     }}
                    />
                    <TextImageButton style={{flex: 1}}
                                     title={stringsTo("call_answer_str")}
                                     source={require("./res/call_icon_answer.png")}
                                     imageStyle={{width: 60, height: 60}}
                                     textStyle={styles.bottomButtonText}
                                     onPress={() => {
                                         this._stopMusicPlay();
                                         this._onPressInStartSpeak()
                                     }}
                    />
                </View>
            </View>
        )
    }

    _timeSecondToStr(second) {
        let hour = parseInt(second / 3600);
        let hourStr = '';
        if (hour > 0) {
            hourStr = hour > 9 ? hour + ":" : '0' + hour + ":"
        }
        let min = parseInt((second % 3600) / 60);
        let minstr = min > 9 ? min + ":" : '0' + min + ":";
        let sec = parseInt(second % 60);
        let secStr = sec > 9 ? sec : '0' + sec
        return hourStr + minstr + secStr;
    }

    //判断当前是否可以操作
    _canStepIn(){
        if (!this.props.isOnLine){
            showToast(stringsTo('device_offline'));
            return false;
        }
        if (this.props.isSleepStatus){
            showToast(stringsTo('power_off'));
            return false;
        }
        if(!this.state.isPlaying){
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }

    //点击报警按钮后的提醒dialog
    _renderAlarmHintDialog() {
        return (
            <AlertDialog
                title={I18n.t("alarmDoorbellDialogTitle")}
                visible={this.state.alarmSwitchDialogVisible}
                message={I18n.t("alarmDoorbellDialogMessage")}
                messageStyle={{
                    marginBottom: 14,
                    marginHorizontal: 28,
                    fontSize: 12,
                    fontWeight: '500',
                    color: "#7F7F7F"
                }}
                canDismiss={true}
                onDismiss={() => {
                    this.setState({alarmSwitchDialogVisible: false});
                }}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({alarmSwitchDialogVisible: false});
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            let paramsJSON = {StreamingAlarmSwitch: 1};
                            LetDevice.sendDeviceServerRequest("StreamingAlarm", JSON.stringify(paramsJSON)).then((data) => {
                                console.log(' sendDeviceServerRequest  then-> startAlarmSwitch' + data);
                                this.setState({alarmSwitchDialogVisible: false, alarmSwitch: true});
                                this.autoTurnOffAlarmTimer = setTimeout(() => {
                                    paramsJSON.StreamingAlarmSwitch = 0;
                                    LetDevice.sendDeviceServerRequest("StreamingAlarm", JSON.stringify(paramsJSON)).then((data) => {
                                        console.log(' sendDeviceServerRequest  then-> stopAlarmSwitch' + data);
                                       this.autoTurnOffAlarmTimer = setTimeout(()=>{
                                           this.setState({alarmSwitch: false});
                                           clearTimeout(this.autoTurnOffAlarmTimer);
                                       },2500);
                                    }).catch((error) => {
                                        console.log('sendDeviceServerRequest error ' + error)
                                    });
                                }, 7500);
                            }).catch((error) => {
                                console.log('sendDeviceServerRequest error ' + error)
                            });


                        }
                    },
                ]}
            />
        );
    }

    render() {
        return (
            <View style={{
                flex: 1,
                backgroundColor: this.props.pageBackgroundColor ? this.props.pageBackgroundColor : "#17171A",
                flexDirection: "column"
            }}>
                <View style={{
                    height:200,
                    alignItems: 'center', justifyContent: 'center'}}>
                    <Text style={{
                        fontSize: 17,
                        fontWeight:"bold" ,
                        color: "#ffffff",fontFamily:isIos()?null:''
                    }}>{this.state.isCalling ? "" : stringsTo('call_ringing_str')}</Text>
                </View>
                <View style={{
                    height:getScreenWidth()*9/16,
                }}>
                    <IMICameraVideoView

                        style={{flex: 1}}
                        ref={ref => {
                            this.IMIVideoView = ref;
                            this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                        }}
                        mute={this.state.mute}
                        playerClass={IMICameraVideoView.PlayerClass.LIVE}
                        dataSource={{
                            playerClass: IMICameraVideoView.PlayerClass.LIVE,
                            did: LetDevice.deviceID,
                            audioParams:3
                        }}
                        onPrepared={() => {
                            console.log("直播流-----onPrepared------");
                            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PREPARED);
                        }}
                        onEventChange={this._onEventChange}

                        onErrorChange={(event) => {
                            console.log("直播流-----onErrorChange------   event.code " + event.code, event);
                            // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示
                            do {
                                //判断如果是通话报错则此处进行判断是否为占线
                                if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                                    continue
                                }
                                //判断是否为对讲模式
                                if (event.extra.arg1 !== CAMERA_PLAYER_MODE.MODE_SPEAK) {
                                    continue
                                }


                                //暂时阿里SDK无法返回指定错误码，此处暂时使用info 对比
                                if (event.extra.arg2 === "voice intercom existed") {
                                    showToast(stringsTo('call_busy_tips'));
                                } else {
                                    showToast(stringsTo('call_connect_error'));
                                }
                                setTimeout(() => {
                                    console.log('报错后要退出当前页面');
                                    this._onPressOutStopSpeak();
                                }, 2000);
                                return;

                            } while (false);

                            if (isIos()){
                                if(event.code==12||event.code==15||event.code==16||event.code==19||event.code==-88002||event.code==14){
                                    if (event.code == 12){
                                        showToast(stringsTo('call_busy_tips'));
                                    }else {
                                        showToast(stringsTo('call_connect_error'));
                                    }

                                    setTimeout(() => {
                                        console.log('报错后要退出当前页面');
                                        this._onPressOutStopSpeak();
                                    }, 2000);
                                    return;
                                }
                            }else {
                                if (event.code == 1005){ // TODO
                               /*     showToast(stringsTo('call_busy_tips'));
                                }else {*/
                                    showToast(stringsTo('call_connect_error'));
                                }
                            }



                            this.setState({
                                isPlaying: false,
                                showErrorView: true,
                                showPauseView: false,
                                errorCode: event.code,
                            });
                            setTimeout(() => {
                                console.log('报错后要退出当前页面');
                                this._onPressOutStopSpeak();
                            }, 2000);
                            return;

                            // this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.ERROR);
                        }}
                        onRecordTimeChange={this._onRecordTimeChange}
                        lensCorrect={{
                            use: false,
                            x: 685 / 1920,
                            y: 44 / 1080,
                            r: 0.8
                        }}
                        onVideoViewClick={() => {
                            this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                            this.props.onVideoClick && this.props.onVideoClick();
                        }}
                    />
                    {/*全屏?横屏UI:竖屏UI(navBar)*/}
                    <View pointerEvents="box-none" style={{
                        position: "absolute",
                        width: "100%",
                        height: "100%",
                        flexDirection: "column",
                        alignItems: "center",
                        pageBackgroundColor: '#17171A'
                    }}>
                        {
                            this._loadingView()
                        }
                        {
                            this._errorView()}
                        {
                            this._renderSnapshotView()
                        }
                        {
                            this._renderAlarmHintDialog()
                        }
                        {
                            this._renderPortraitScreenVideoViewArea()
                        }
                        {
                            this.state.recording ? this._renderRecordingView() : null
                        }
                    </View>
                </View>

                {/*全屏?null:渲染外层传入的UI*/}
                {this.state.isCalling ? this._renderMiddleLayout() : <View style={{flex: 1, flexDirection: "column"}}/>}
                {/*//justifyContent: 'flex-end',*/}
                <View style={{height: 20, top:25,alignItems: 'center'}}>
                    <Text style={{
                        fontSize: 14,
                        fontWeight:"bold" ,
                        color: "#ffffff"
                    }}>{this.state.isCalling ? this._timeSecondToStr(this.state.callDuration) : ""}</Text>
                </View>

                {/*屏幕底部功能按键*/}
                {this.state.isCalling ? this._renderBottomLayout() : this._renderBottomCallLayout()}

                {/*渲染清晰度选择器，不占UI空间的*/}
                {this._renderLandscapeScreenQualityPopView()}
                <FastReplyView visible={this.state.showFastReply} did={this.state.did}
                               isRefreshData={this.state.hasNewReply}
                               title={stringsTo('call_reply_str')}
                               closePage={() => {
                                   this.setState({showFastReply: false});
                                   this.props.navigation.goBack();
                               }}
                               onDismiss={() => {
                                   this.setState({showFastReply: false});
                               }}/>
            </View>
        );
    }
}

const styles = StyleSheet.create({

    bottomLayout: {
        display: "flex",
        width: "100%",
        height: 110,
        flexDirection: "row",
        flexWrap: 'nowrap',
        alignItems: "center"
    },
    bottomButtonText: {
        fontSize: 12,
        marginTop: 8,
        color: "#ffffff",
        textAlign:"center",
    },
    bottomLayoutItem: {
        flex: 1,
        height: "100%",
        marginTop: 15,
        display: "flex",
        justifyContent: "center",
        alignItems: "center"
    },
    middleLayoutItem: {
        height: "100%",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        marginLeft: 15
    },
});
