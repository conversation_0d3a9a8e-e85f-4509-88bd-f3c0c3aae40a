import React from 'react';

import {StyleSheet, View, ScrollView, Dimensions, Modal,Text, TextInput, TouchableOpacity, Keyboard} from 'react-native';

import { MessageDialog, showToast} from 'imilab-design-ui'
import NavigationBar from "../../../com.chuangmi.door/src/CommonView/NavigationBar/NavigationBar";
import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import I18n, {stringsTo} from "../../../../globalization/Localize";
import {XText, XView} from "react-native-easy-app";
import {IMIGotoPage, LetDevice, LetIMIIotRequest} from "../../../../imilab-rn-sdk";

import {METHOD} from "../../../../imilab-rn-sdk/native/iot-kit/IMIIotRequest";
import {IMINativeLifeCycleEvent} from "../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import {isAndroid, isIos, isIphoneXSeries} from "../../../../imilab-rn-sdk/utils/Utils";

const {width} = Dimensions.get('window');
/**
 * 通用设置页面
 */
let CHECK_UPDATE_URL = "link://app/pages/toDeviceUpgradePage";//检查更新
let DEVICE_SHARE_URL = "link://app/pages/toDeviceSharePage";//共享
let COMMON_HELP_URL = "link://feedback/pages/CommonHelp";//帮助与反馈
export default class CommonSettingPage extends BaseDeviceComponent {

    componentDidMount() {
        LetDevice.registerInfoChangeListener(data => {
            console.log(`_doDevInfoText eventData : ${data} + data ${JSON.stringify(data)}`);
        });
        this._subscribe_blur = this.props.navigation.addListener('blur', () => { //防止033接到弹出按铃呼叫时，对话框浮于最上层
            this.state.modalVisible && this.setState({modalVisible: false});
            this.state.modalDeleteVisible && this.setState({modalDeleteVisible: false});
        });
    }

    constructor(props, context) {
        super(props, context);
        this.state = {
            keyBoardHeight: 0,//软键盘高度
            modalVisible: false,//弹出聊天输入
            modalDeleteVisible: false,//是否删除
            deviceName: LetDevice.devNickName,
            showCircle: false,//是否显示升级
            doorbellIsOnline: this.props.route.params.doorbellIsOnline,
            batteryValue: this.props.route.params.batteryValue
        }
        this.inputData = "";//输入框内容
        this._onChangeText = this._onChangeText.bind(this);//监听输入变化
    }

    componentWillUnmount() {
        this.keyboardDidShowListener.remove();
        this.keyboardDidHideListener.remove();
        this._subscribe_blur && this._subscribe_blur();
        this._onResumeListener && this._onResumeListener.remove();
    }

    UNSAFE_componentWillMount() {
        this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow.bind(this));
        this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide.bind(this));
        this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
            //app页面回到插件
            NavigationBar.setBarStyle('dark-content'); //修改从云存购买界面返回状态栏显示白色字体问题

            //有新的升级版本，从升级页面回来需要重新检查是否升级到最新固件
            if (this.state.showCircle) {
                this.getFirmwareInfo();
            }
        });
        this.getFirmwareInfo();
    }

    renderBottomDeleteBtn() {
        return (<XView style={{
            position: "absolute",
            height: 48,
            alignItems: "center",
            backgroundColor: "#EEEEEE",
            justifyContent: 'center',
            left: 20,
            right: 20,
            bottom: isIphoneXSeries() ? 23 : 14,
            borderRadius: 6.67
        }} onPress={() => {
            this.setState({
                modalDeleteVisible: true
            })
        }}>
            <XText style={{fontSize: 15, textAlign: 'center', color: "#EB614B", fontWeight: 'bold',fontFamily:isIos()?null:''}}
                   text={stringsTo("comm_setting_remove_device")}
                   accessibilityLabel={"comm_setting_remove_device"}
            />
        </XView>);
    }

    render() {
        return (<XView style={styles.container}>
            <NavigationBar
                title={stringsTo('commTitleSettingText')}
                left={[{
                    key: NavigationBar.ICON.BACK, onPress: () => this.props.navigation.pop(),
                    accessibilityLabel: "comm_setting_go_back",
                }]}
                right={[]}
            />
            {this.renderBottomDeleteBtn()}
            <ScrollView showsVerticalScrollIndicator={false} style={{backgroundColor: "#FAFAFA", marginBottom: isIphoneXSeries()?85:65}}>
                {/*        <Separator/>*/}
                <Text style={{
                    color: "#00000080",
                    fontSize: 12,
                    paddingLeft: 15,
                    paddingTop: 5,
                    width: "100%",
                    backgroundColor: "#ffffff"
                }}>{stringsTo("feature_set")}</Text>

                <ListItem title={LetDevice.category == "doorbell"?stringsTo('doorbellSetting'):stringsTo('popo_setting_camera_text')}
                          onPress={() => {
                              LetDevice.category == "doorbell"?this.props.navigation.push("DoorbellSettingPage"):this.props.navigation.push("CameraSettingPage");
                          }}
                          disabled={!this.state.doorbellIsOnline || !LetDevice.isOnline}
                />

                <ListItem title={stringsTo('alarmSettingText')}
                          onPress={() => {
                              this.props.navigation.push("HomeKeepSetting");
                          }}
                          disabled={!this.state.doorbellIsOnline || !LetDevice.isOnline}
                />

                <ListItem title={stringsTo('playBackText')}
                          onPress={() => {
                              this.props.navigation.push('PlayBackVideoPage');
                          }}
                          disabled={!LetDevice.isOnline}
                />

                <ListItem title={stringsTo('cameraTestTipsTitle2')}
                          onPress={() => {
                              this.props.navigation.push('SignalTestPage', {pageOnSettings: true});
                          }}
                          disabled={!this.state.doorbellIsOnline || !LetDevice.isOnline}
                />

                {/*033的SD卡管理页，031的在网关，不在这里*/}
                <ListItem title={stringsTo('record_files_sdcard')}
                          onPress={() => {
                              this.props.navigation.push('SdCardManagePage');
                          }}
                          disabled={!LetDevice.isOnline}
                          hide={LetDevice.category != "doorbell"}
                />

                <View style={{backgroundColor: "#FAFAFA", height: 14, width: "100%"}}></View>
                <Text style={{
                    color: "#00000080",
                    fontSize: 12,
                    paddingLeft: 15,
                    paddingTop: 5,
                    width: "100%",
                    backgroundColor: "#ffffff"
                }}>{stringsTo("comm_setting_title")}</Text>

                <ListItem title={stringsTo('device_name')} value={this.state.deviceName}
                          onPress={() => {
                              this.setState({
                                  modalVisible: true
                              })
                          }}
                          accessibilityLabel={"device_name"}
                />
                <ListItem title={stringsTo('check_update')} showCircle={this.state.showCircle}
                          onPress={() => {
                              if (LetDevice.isShareUser) {
                                  showToast(stringsTo('shareUser_tip'));
                                  return;
                              }
                              if (this.state.showCircle && (this.state.batteryValue == null || this.state.batteryValue < 20)) {
                                  showToast(stringsTo('lowPowerUpdateSubTitle'));
                                  return;
                              }
                              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, CHECK_UPDATE_URL);
                          }}
                          accessibilityLabel={"check_update"}
                />
                <ListItem title={stringsTo('shared_setting')}
                          onPress={() => {
                              if (LetDevice.isShareUser) {
                                  showToast(stringsTo('shareUser_tip'));
                                  return;
                              }
                              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, DEVICE_SHARE_URL);
                          }}
                          accessibilityLabel={"shared_setting"}
                />
                <ListItem title={stringsTo('help_callback')}
                          onPress={() => {
                              //this.props.navigation.push('WebViewPage', {url: "https://api.imilab.com/service/app/faq/index?model=${LetDevice.model}&locale=en"})
                              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_HELP_URL);
                          }}
                          accessibilityLabel={"help_callback"}
                />


                <MessageDialog
                    title={stringsTo('confirm_deletion_device')}
                    visible={this.state.modalDeleteVisible}
                    canDismiss={false}
                    buttons={[
                        {
                            text: I18n.t("cancel"),
                            callback: _ => {
                                this.setState({modalDeleteVisible: false});
                            }
                        },
                        {
                            text: I18n.t("ok_button"),
                            callback: _ => {
                                this.setState({modalDeleteVisible: false});
                                this.DeleteDevice();
                            }
                        },
                    ]}
                />
            </ScrollView>
            {this._updateDeviceNameDialog()}
        </XView>);
    }

    /**
     * 修改设备名称的弹框
     * @private
     */
    _updateDeviceNameDialog() {
        return (<View>
            <MessageDialog
                title={stringsTo('update_device_name')}
                visible={this.state.modalVisible}
                style={{bottom: isAndroid() ? 0 : this.state.keyBoardHeight}}
                canDismiss={true}
                onDismiss={() => {
                    this.setState({modalVisible: false});
                }}
                buttons={[
                    {
                        text: stringsTo("cancel"),
                        callback: _ => {
                            this.setState({modalVisible: false});
                        }
                    },
                    {
                        text: stringsTo("ok_button"),
                        callback: _ => {
                            this.setState({modalVisible: false});
                            this.updateDeviceName();
                        }
                    },
                ]}
            >
                <View style={{
                    width: "100%", alignItems: "center",
                    justifyContent: "center", flexDirection: 'column', paddingVertical: 10
                }}>
                    <TextInput style={{
                        fontSize: 15,
                        borderRadius: 7,
                        height: 50,
                        width: width * 0.8,
                        backgroundColor: '#F1F1F1',
                        paddingLeft: 20
                    }}
                               placeholderTextColor={"#B2B2B2"}
                               placeholder={this.state.deviceName}
                               returnKeyType='done'
                               clearButtonMode='while-editing'
                               enablesReturnKeyAutomatically={true}
                               editable={true}
                               autoFocus={true}
                               maxLength={100}
                               keyboardType='default'
                               onChangeText={this._onChangeText}
                    >
                    </TextInput>
                </View>
            </MessageDialog>
        </View>)
    }

    _onChangeText(inputData) {
        //把获取到的内容，设置给showValue
        this.inputData = inputData;
    }

    _keyboardDidShow(e) {
        this.setState({
            keyBoardHeight: e.endCoordinates.height
        });
    }

    _keyboardDidHide() {
        this.setState({
            keyBoardHeight: 0,
            // modalVisible:false
        });
    }

    onRequestClose() {
        this.setState({
            modalVisible: false
        })
    }

    updateDeviceName() {
        if (this.inputData.length == 0) {
            showToast(stringsTo('input_name'));
            return
        }
        const params = {
            Path: 'api/app_device/nickname',
            Method: "PUT",
            ParamMap: {
                iotId: LetDevice.deviceID,
                isAli: true,
                nickName: this.inputData ? this.inputData : LetDevice.devNickName
            }
        };
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {

            LetDevice.setDevNickName(this.inputData)
            this.setState({
                deviceName: this.inputData
            })
            this.inputData = "";
        }).catch((error) => {
            this.inputData = "";
        });
    }

    getFirmwareInfo() {
        const params = {
            Path: '/thing/ota/info/queryByUser',
            APIVersion: '1.0.2',
            ParamMap: {
                iotId: LetDevice.deviceID,
                productKey: LetDevice.model
            }
        };
        LetIMIIotRequest.sendIotServerRequest(params).then((data) => {
            console.log(' sendIotServerRequest  then->' + data)
            data = JSON.parse(data);
            let versionOnServer = data.version;
            let currentVersion = data.currentVersion;
            let log = data.desc;

            /*let versionOnServer = '031201_1.1.10_0202';
            let currentVersion = '031201_1.1.8_0129';*/

            this.setState({
                showCircle: this.compareVersion(currentVersion, versionOnServer)
            });
        }).catch((error) => {
            console.log('sendIotServerRequest error ' + error)
        });

    }

    compareVersion(curVersion, newVersion) {

        if (this.isEmpty(newVersion) || this.isEmpty(curVersion)) {
            return false;
        } else if (newVersion.equals(curVersion)) {
            return false;
        }

        let newSpilUnderLine = newVersion.split("_");
        let curSpilUnderLine = curVersion.split("_");
        if (newSpilUnderLine.length < 3 || newSpilUnderLine.length < 3) {
            return false;
        }
        let newSpilComma = newSpilUnderLine[1].split(".");
        let curSpilComma = curSpilUnderLine[1].split(".");
        if (newSpilComma.length < 3 || curSpilComma.length < 3) {
            return false;
        }

        let newNum = parseInt(newSpilComma[0]) * 1000 + parseInt(newSpilComma[1]) * 300 + parseInt(newSpilComma[2]) * 10 + parseInt(newSpilUnderLine[2]);
        let curNum = parseInt(curSpilComma[0]) * 1000 + parseInt(curSpilComma[1]) * 300 + parseInt(curSpilComma[2]) * 10 + parseInt(curSpilUnderLine[2]);
        if (newNum > curNum) {
            return true;
        } else {
            return false;
        }

    }

    isEmpty(obj) {
        if (typeof obj === 'undefined' || obj == null || obj === '') {
            return true;
        } else {
            return false;
        }
    }

    DeleteDevice() {
        //分享账号调用此接口，是删除分享设备，不会使设备解绑
        const params = {
            Path: 'api/app_device/unbind',
            Method: METHOD.POST,
            ParamMap: [{
                iotId: LetDevice.deviceID,
                isAli: true,
            }]
        };
        console.log('传值params--', params);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            IMIGotoPage.exit()
        }).catch((error) => {

        });
    }

}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#FAFAFA",
    },
    textInputContainer: {
        position: 'absolute', height: 45,
        backgroundColor: "#ffffff", width: "100%", alignItems: "center", justifyContent: "center", flexDirection: "row"
    },
    dialogButtons: { // 按钮容器
        height: 40, // 底部按钮的高度
        flexDirection: 'row',
        backgroundColor: '#ffffff',
        borderRadius: 55,
        justifyContent: 'center',
        alignItems: "center",
        marginTop: 20,
        width: width * 0.9
    },
});
