import React from 'react';

import {StyleSheet, View, Image,ImageBackground, Text, Platform, ScrollView, Modal,TouchableWithoutFeedback,Dimensions,Picker,TouchableOpacity} from 'react-native';


import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import ChoiceItem from "../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem";
import Separator from "../../../../imilab-design-ui/src/widgets/settingUI/Separator"
import SlideGear from "../../../../imilab-design-ui/src/widgets/settingUI/SlideGear"
import MessageDialog from "../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog"
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {XText, XView} from "react-native-easy-app";
import DetectionTime from "../../../com.chuangmi.camera.IPC031/src/setting/HouseKeepSetting/DetectionTime"
import FocusAreaDetectionSetting from "./HouseKeepSetting/FocusAreaDetectionSetting";
import {LetDevice, letDevice} from "../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {CameraMethod, LetIMIIotRequest} from "../../../../imilab-rn-sdk";
import {showToast} from "../../../../imilab-design-ui";
import {showLoading} from "../../../../imilab-design-ui";
import {stringsTo} from "../../../../globalization/Localize";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";

import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import {
    convertToTimeString,
    convertUtcToLocalTime,
    convertUtcToLocalTimeStamp
} from "../../../../imilab-rn-sdk/utils/Utils";
import IMILogUtil from "../../../../imilab-rn-sdk/native/local-kit/IMILogUtil";

const BACKGROUNDCOLOR = '#4A6EE019';
const UNCHECKED_BACKGROUNDCOLOR = '#F2F3F5';
let tempStayTimeSetting = {};
let tempRecordTimeSetting = {};
let tempRecordInterValSetting = {};
let tempDistanceSetting = {};

let isSupportNewLogic = LetDevice.model !="a1MZkl613tF"; //是否支持新版的侦测时间处理逻辑,031默认不支持


/**
 * 看家助手功能设置页面
 */
export  default  class  HomeKeepSetting extends BaseDeviceComponent {
    static propTypes = {};

    constructor(props) {
        super(props);
        this.state = {
            detectTimeValue: I18n.t('detectAllDayStr'),//侦测时间Value值
            detectTimeMode:'0',//侦测时间模式
            stayTimeValue: '20s',//逗留时长Value
            recordTimeValue: I18n.t('recordTimeAutoModeTimeStr'),//录像时长
            recordTimeInteravlValue: '0',//录像间隔
            detectionDistanceValue: 4,//侦测距离Value
            alarmMsgPushValue: false,//报警消息推送
            keyAreaValue: I18n.t('settings_switch_off'),//重点区域侦测
            keyAreaValueAlarmSwitch: false,
            isSelectedTwo: true,
            isSelectedFour: false,
            isSelectedSix: false,
            isSelectedEight: false,
            selectedImgStr:'twoStr',


            showStayTimeModal: false,//逗留时长弹窗
            showRecordTimeModal: false,//录像时长弹窗
            showRecordInterValModal: false,//录像间隔弹窗
            showDetectionDistanceModal: false,//侦测距离
            /*逗留时长选中*/
            isCheckPowerMore: true,
            isCheckPowerCommon: false,
            isCheckPowerSmall: false,
            /*录像时长选中*/
            isCheckRecordTime:true,
            isCheckRecordTimeDIY:false,
            recordTimeModalColor:UNCHECKED_BACKGROUNDCOLOR,
            staySelectArr:[],//逗留时长时间停留选中数组
            staySelectIndex:0,//逗留时长时间停留数组选中

            timeZone:'',//获取当前时区
            beginUTCTime:'',//获取上传时间的utcBeginTime 8:00
            endUTCTime:'',//获取上传时间的utcEndTime 20:00
            zeroUTCTime:'',//获取上传时间的utcEndTime 0:00
            lastUTCTime:'',//获取上传时间的utcEndTime 23:59:59
        }
    }

    componentDidMount() {
        /*//旧固件在超时16秒后才走catch，不能在catch里获取所有物模型，太慢。因为超时700ms给服务发送和返回已足够。
        // 所以设置超时700ms走直接走getAllValue，此时isSupportNewLogic为false
        this.forOldFirmware = setTimeout(()=>this.getAllValue(),700);
        //通过此服务来判断固件是新还是旧，旧固件无返回值，直接走catch
        LetDevice.sendDeviceServerRequest("PluginCompatible", JSON.stringify({})).then((data) => {
            this.forOldFirmware && clearTimeout(this.forOldFirmware);
            isSupportNewLogic = true;

        }).catch((err) => {
            isSupportNewLogic = false;
        });*/

        this.getAllValue();

        this._subscribe_blur = this.props.navigation.addListener('blur', () => { //防止033接到弹出按铃呼叫时，下面四种之一的对话框浮于最上层
            this.state.showStayTimeModal && this.setState({showStayTimeModal: false});
            this.state.showRecordTimeModal && this.setState({showRecordTimeModal: false});
            this.state.showRecordInterValModal && this.setState({showRecordInterValModal: false});
            this.state.showDetectionDistanceModal && this.setState({showDetectionDistanceModal: false});
        });
    }

    componentWillUnmount() {
        super.componentWillUnmount();
        // this.setState = () => false;// 组件销毁的时候将异步方法撤销
        if(this.state.showDetectionDistanceModal){this.updatePirTestValue(false);}

        this._subscribe_blur && this._subscribe_blur();
    }


    getAllValue() {
        showLoading(stringsTo('commWaitText'), true);
        LetDevice.updateAllPropertyCloud().then((data) => {
            showLoading(false);
            console.log('yyyyyyyyyyyy',JSON.parse(data));
            let dataObject = JSON.parse(data);
            let stateProps = {};

            if(LetDevice.model =="a1MZkl613tF"&&dataObject.VersionCompatible){
               isSupportNewLogic = true;
            }


            // 当前时区
            if (dataObject.DeviceTimeZone){
                stateProps.timeZone = dataObject.DeviceTimeZone.value;
                console.log('当前时区---',dataObject.DeviceTimeZone.value,stateProps.timeZone);
                if (this.state.timeZone < 13){
                    stateProps.beginUTCTime = Math.abs(((8-stateProps.timeZone+24)%24)*3600);
                    stateProps.endUTCTime = Math.abs(((20-stateProps.timeZone+24)%24)*3600);
                    stateProps.zeroUTCTime = Math.abs(((0-stateProps.timeZone+24)%24)*3600);
                    stateProps.lastUTCTime = Math.abs((((24-stateProps.timeZone+24)%24)*3600-1));
                    console.log('k--end',stateProps.zeroUTCTime,stateProps.lastUTCTime,stateProps.beginUTCTime,stateProps.endUTCTime);
                }else if (this.state.timeZone >12 && this.state.timeZone < 24){
                    stateProps.beginUTCTime = Math.abs(((8+24-stateProps.timeZone+24)%24)*3600);
                    stateProps.endUTCTime = Math.abs(((20+24-stateProps.timeZone+24)%24)*3600);
                    stateProps.zeroUTCTime = Math.abs(((0+24-stateProps.timeZone+24)%24)*3600);
                    stateProps.lastUTCTime = Math.abs((((24+24-stateProps.timeZone+24)%24)*3600-1));
                }else if (this.state.timeZone === 24){
                    stateProps.beginUTCTime = Math.abs((((8-3+24)%24)*3600-30*60));
                    stateProps.endUTCTime = Math.abs((((20-3+24)%24)*3600-30*60));
                    stateProps.zeroUTCTime = Math.abs((((0-3+24)%24)*3600-30*60));
                    stateProps.lastUTCTime = Math.abs(((((24-3+24)%24)*3600-30*60)-1));
                }else if (this.state.timeZone === 25){
                    stateProps.beginUTCTime = Math.abs((((8-4+24)%24)*3600-30*60));
                    stateProps.endUTCTime = Math.abs((((20-4+24)%24)*3600-30*60));
                    stateProps.zeroUTCTime = Math.abs((((0-4+24)%24)*3600-30*60));
                    stateProps.lastUTCTime = Math.abs(((((24-4+24)%24)*3600-30*60)-1));
                }else if (this.state.timeZone === 26){
                    stateProps.beginUTCTime = Math.abs((((8-5+24)%24)*3600-30*60));
                    stateProps.endUTCTime = Math.abs((((20-5+24)%24)*3600-30*60));
                    stateProps.zeroUTCTime = Math.abs((((0-5+24)%24)*3600-30*60));
                    stateProps.lastUTCTime = Math.abs(((((24-5+24)%24)*3600-30*60)-1));
                }else if (this.state.timeZone === 27){
                    stateProps.beginUTCTime = Math.abs((((8-5+24)%24)*3600-45*60));
                    stateProps.endUTCTime = Math.abs((((20-5+24)%24)*3600-45*60));
                    stateProps.zeroUTCTime = Math.abs((((0-5+24)%24)*3600-45*60));
                    stateProps.lastUTCTime = Math.abs(((((24-5+24)%24)*3600-45*60)-1));
                }else if (this.state.timeZone === 28){
                    stateProps.beginUTCTime = Math.abs((((8-6+24)%24)*3600-30*60));
                    stateProps.endUTCTime = Math.abs((((20-6+24)%24)*3600-30*60));
                    stateProps.zeroUTCTime = Math.abs((((0-6+24)%24)*3600-30*60));
                    stateProps.lastUTCTime = Math.abs(((((24-6+24)%24)*3600-30*60)-1));
                }else if (this.state.timeZone === 29){
                    stateProps.beginUTCTime = Math.abs((((8-8+24)%24)*3600-30*60));
                    stateProps.endUTCTime = Math.abs((((20-8+24)%24)*3600-30*60));
                    stateProps.zeroUTCTime = Math.abs((((0-8+24)%24)*3600-30*60));
                    stateProps.lastUTCTime = Math.abs(((((24-8+24)%24)*3600-30*60)-1));
                }else if (this.state.timeZone === 30){
                    stateProps.beginUTCTime = Math.abs((((8-8+24)%24)*3600-45*60));
                    stateProps.endUTCTime = Math.abs((((20-8+24)%24)*3600-45*60));
                    stateProps.zeroUTCTime = Math.abs((((0-8+24)%24)*3600-45*60));
                    stateProps.lastUTCTime = Math.abs(((((24-8+24)%24)*3600-45*60)-1));
                }else if (this.state.timeZone === 31){
                    stateProps.beginUTCTime = Math.abs((((8-9+24)%24)*3600-30*60));
                    stateProps.endUTCTime = Math.abs((((20-9+24)%24)*3600-30*60));
                    stateProps.zeroUTCTime = Math.abs((((0-9+24)%24)*3600-30*60));
                    stateProps.lastUTCTime = Math.abs(((((24-9+24)%24)*3600-30*60)-1));
                }else if (this.state.timeZone === 32){
                    stateProps.beginUTCTime = Math.abs((((8-10+24)%24)*3600-30*60));
                    stateProps.endUTCTime = Math.abs((((20-10+24)%24)*3600-30*60));
                    stateProps.zeroUTCTime = Math.abs((((0-10+24)%24)*3600-30*60));
                    stateProps.lastUTCTime = Math.abs(((((24-10+24)%24)*3600-30*60)-1));
                }else if (this.state.timeZone === 33){
                    stateProps.beginUTCTime = Math.abs((((8-12+24)%24)*3600-45*60));
                    stateProps.endUTCTime = Math.abs((((20-12+24)%24)*3600-45*60));
                    stateProps.zeroUTCTime = Math.abs((((0-12+24)%24)*3600-45*60));
                    stateProps.lastUTCTime = Math.abs(((((24-12+24)%24)*3600-45*60)-1));
                }else if (this.state.timeZone === 34){
                    stateProps.beginUTCTime = Math.abs((((8-13+24)%24)*3600-45*60));
                    stateProps.endUTCTime = Math.abs((((20-13+24)%24)*3600-45*60));
                    stateProps.zeroUTCTime = Math.abs((((0-13+24)%24)*3600-45*60));
                    stateProps.lastUTCTime = Math.abs(((((24-13+24)%24)*3600-45*60)-1));
                }else if (this.state.timeZone === 35){
                    stateProps.beginUTCTime = Math.abs((((8+2+24)%24)*3600+30*60));
                    stateProps.endUTCTime = Math.abs((((20+2+24)%24)*3600+30*60));
                    stateProps.zeroUTCTime = Math.abs((((0+2+24)%24)*3600+30*60));
                    stateProps.lastUTCTime = Math.abs(((((24+2+24)%24)*3600+30*60)-1));
                }else if (this.state.timeZone === 36){
                    stateProps.beginUTCTime = Math.abs((((8+3+24)%24)*3600+30*60));
                    stateProps.endUTCTime = Math.abs((((20+3+24)%24)*3600+30*60));
                    stateProps.zeroUTCTime = Math.abs((((0+3+24)%24)*3600+30*60));
                    stateProps.lastUTCTime = Math.abs(((((24+3+24)%24)*3600+30*60)-1));
                }else if (this.state.timeZone === 37){
                    stateProps.beginUTCTime = Math.abs((((8+9+24)%24)*3600+30*60));
                    stateProps.endUTCTime = Math.abs((((20+9+24)%24)*3600+30*60));
                    stateProps.zeroUTCTime = Math.abs((((0+9+24)%24)*3600+30*60));
                    stateProps.lastUTCTime = Math.abs(((((24+9+24)%24)*3600+30*60)-1));
                }

                console.log('beginUTC-endUTC',stateProps.beginUTCTime,stateProps.endUTCTime,stateProps.zeroUTCTime,stateProps.lastUTCTime);
            }
            //侦测时间模式
            if (dataObject.AlarmNotifyMode){
                stateProps.detectTimeMode = dataObject.AlarmNotifyMode.value;
                console.log('报警提醒模式---',stateProps.detectTimeMode);
            }else {
                stateProps.detectTimeMode = 0;
            }

            //侦测时间
            if (dataObject.AlarmNotifyPlan) {
                var timeArr = dataObject.AlarmNotifyPlan.value;
                console.log('侦测时间--',dataObject.AlarmNotifyPlan.value);
                console.log('时间数组--',timeArr);
                if (timeArr.length>0){
                    let endTime = timeArr[0].EndTime;
                    let startTime = timeArr[0].BeginTime;
                    if ((startTime === 0) && (endTime === 86399) && (stateProps.detectTimeMode === 0)) {
                        //全天
                        stateProps.detectTimeValue = I18n.t('detectAllDayStr');
                    } else if ((!isSupportNewLogic && startTime === stateProps.beginUTCTime && endTime === stateProps.endUTCTime) ||
                        (isSupportNewLogic && startTime == 28800 && endTime == 72000)) {
                        //白天侦测
                        stateProps.detectTimeValue = I18n.t('detectDayValue');
                    } else if ((!isSupportNewLogic && startTime === stateProps.endUTCTime && endTime === stateProps.beginUTCTime) ||
                        (isSupportNewLogic && startTime == 72000 && endTime == 28800)) {
                        //夜晚侦测
                        stateProps.detectTimeValue = I18n.t('detectNightDayValue');
                    } else {
                        //自定义
                        // stateProps.detectTimeValue = '自定义';
                        if((startTime === 0) && (endTime === 86399) && (stateProps.detectTimeMode === 3)){
                            console.log('自定义全天');
                            stateProps.detectTimeValue = '00:00'+'-'+'23:59';
                        }else {

                           // let moment = require("moment");
                            let crossDay = this._getIsCrossDay(startTime,endTime);
                            /*if(moment().isDST()){ //避免夏令时问题，需要单独处理，美国安克雷奇差2小时，梅特拉卡特拉和洛杉矶差一小时
                                let dstHourOffset = moment().utcOffset()*-1<=420?1:2;
                                startTime = startTime+3600*dstHourOffset;
                                endTime = endTime+3600*dstHourOffset;
                            }*/
                            /*let startTimeStr = moment.unix(startTime).format('HH:mm'); //这种转化在IOS14.4以上新加坡会少30分钟
                            let tempEndTimeStr = moment.unix(endTime).format('HH:mm:ss');
                            let tempEndArr = tempEndTimeStr.split(':');
                            let endTimeStr = tempEndArr[0]+':'+tempEndArr[1];*/
                            let startTimeStr = isSupportNewLogic?convertToTimeString(startTime):convertUtcToLocalTime(startTime);
                            let endTimeStr = isSupportNewLogic?convertToTimeString(endTime):convertUtcToLocalTime(endTime);
                           // console.log('endStr--tempEndArr', tempEndTimeStr, tempEndArr, endTimeStr);
                            stateProps.detectTimeValue = startTimeStr + '-' + (crossDay ? stringsTo('next_day') : "") + endTimeStr;
                            console.log('UTC转为本地-begin---end', startTime, endTime, startTimeStr, endTimeStr);
                            console.log('时间字符串----', stateProps.detectTimeValue);
                        }

                    }
                }
            }else {
                stateProps.detectTimeValue = I18n.t('detectAllDayStr');
            }



            // 逗留时长
            if (dataObject.PirDetectionMechanism) {
                if (dataObject.PirDetectionMechanism.value == 0) {
                    //非常耗电
                    stateProps.stayTimeValue = 0;
                    stateProps.isCheckPowerMore = true;
                    stateProps.isCheckPowerCommon = false;
                    stateProps.isCheckPowerSmall = false;
                    stateProps.staySelectArr = [0, 0, 0, 0];


                    tempStayTimeSetting.stayTimeValue = 0;
                    tempStayTimeSetting.isCheckPowerMore = true;
                    tempStayTimeSetting.isCheckPowerCommon = false;
                    tempStayTimeSetting.isCheckPowerSmall = false;
                    tempStayTimeSetting.staySelectArr = [0, 0, 0, 0];
                    console.log('逗留时长立即录像-----' + dataObject.PirDetectionMechanism.value);
                } else if (dataObject.PirDetectionMechanism.value == -1) {
                    //关闭逗留侦测
                    stateProps.stayTimeValue = -1;
                    stateProps.isCheckPowerMore = false;
                    stateProps.isCheckPowerCommon = false;
                    stateProps.isCheckPowerSmall = true;
                    stateProps.staySelectArr = [0, 0, 0, 0];

                    tempStayTimeSetting.stayTimeValue = -1;
                    tempStayTimeSetting.isCheckPowerMore = false;
                    tempStayTimeSetting.isCheckPowerCommon = false;
                    tempStayTimeSetting.isCheckPowerSmall = true;
                    tempStayTimeSetting.staySelectArr = [0, 0, 0, 0];

                    console.log('逗留时长关闭录像-----99' + dataObject.PirDetectionMechanism.value);
                } else {
                    //停留录像
                    stateProps.stayTimeValue = dataObject.PirDetectionMechanism.value;
                    stateProps.isCheckPowerMore = false;
                    stateProps.isCheckPowerCommon = true;
                    stateProps.isCheckPowerSmall = false;

                    tempStayTimeSetting.stayTimeValue = dataObject.PirDetectionMechanism.value;
                    tempStayTimeSetting.isCheckPowerMore = false;
                    tempStayTimeSetting.isCheckPowerCommon = true;
                    tempStayTimeSetting.isCheckPowerSmall = false;
                    console.log('逗留时长停留录像-----' + dataObject.PirDetectionMechanism.value);
                    if (stateProps.stayTimeValue == 5) {
                        stateProps.staySelectArr = [1, 0, 0, 0];
                        stateProps.staySelectIndex = 0;
                    } else if (stateProps.stayTimeValue == 10) {
                        stateProps.staySelectArr = [0, 1, 0, 0];
                        stateProps.staySelectIndex = 1;
                    } else if (stateProps.stayTimeValue == 15) {
                        stateProps.staySelectArr = [0, 0, 1, 0];
                        stateProps.staySelectIndex = 2;
                    } else if (stateProps.stayTimeValue == 20) {
                        stateProps.staySelectArr = [0, 0, 0, 1];
                        stateProps.staySelectIndex = 3;
                    }

                    if (tempStayTimeSetting.stayTimeValue == 5) {
                        tempStayTimeSetting.staySelectArr = [1, 0, 0, 0];
                        tempStayTimeSetting.staySelectIndex = 0;
                    } else if (tempStayTimeSetting.stayTimeValue == 10) {
                        tempStayTimeSetting.staySelectArr = [0, 1, 0, 0];
                        tempStayTimeSetting.staySelectIndex = 1;
                    } else if (tempStayTimeSetting.stayTimeValue == 15) {
                        tempStayTimeSetting.staySelectArr = [0, 0, 1, 0];
                        tempStayTimeSetting.staySelectIndex = 2;
                    } else if (tempStayTimeSetting.stayTimeValue == 20) {
                        tempStayTimeSetting.staySelectArr = [0, 0, 0, 1];
                        tempStayTimeSetting.staySelectIndex = 3;
                    }
                }
            }

            //录像时长
            if (dataObject.PirRecordingTime) {
                if (dataObject.PirRecordingTime.value < 6) {  //自定义模式的取值是6-60秒，如果是0秒，则是自动模式
                    //自动模式
                    stateProps.isCheckRecordTime = true;
                    stateProps.isCheckRecordTimeDIY = false;
                    stateProps.recordTimeValue = 0;
                    stateProps.recordTimeModalColor = UNCHECKED_BACKGROUNDCOLOR;


                    tempRecordTimeSetting.isCheckRecordTime = true;
                    tempRecordTimeSetting.isCheckRecordTimeDIY = false;
                    tempRecordTimeSetting.recordTimeValue = 0;
                    tempRecordTimeSetting.recordTimeModalColor = UNCHECKED_BACKGROUNDCOLOR;

                    console.log('自动--录像时长--' + stateProps.recordTimeValue);
                } else {
                    //自定义
                    stateProps.isCheckRecordTime = false;
                    stateProps.isCheckRecordTimeDIY = true;
                    stateProps.recordTimeValue = dataObject.PirRecordingTime.value;
                    stateProps.recordTimeModalColor = BACKGROUNDCOLOR;

                    tempRecordTimeSetting.isCheckRecordTime = false;
                    tempRecordTimeSetting.isCheckRecordTimeDIY = true;
                    tempRecordTimeSetting.recordTimeValue = dataObject.PirRecordingTime.value;
                    tempRecordTimeSetting.recordTimeModalColor = BACKGROUNDCOLOR;

                    console.log('自定义--录像时长--' + stateProps.recordTimeValue);
                }
            }else {
                stateProps.isCheckRecordTime = true;
                stateProps.isCheckRecordTimeDIY = false;
                stateProps.recordTimeValue = 0;
                stateProps.recordTimeModalColor = UNCHECKED_BACKGROUNDCOLOR;


                tempRecordTimeSetting.isCheckRecordTime = true;
                tempRecordTimeSetting.isCheckRecordTimeDIY = false;
                tempRecordTimeSetting.recordTimeValue = 0;
                tempRecordTimeSetting.recordTimeModalColor = UNCHECKED_BACKGROUNDCOLOR;
            }

            // 录像间隔
            if (dataObject.PirRecordingInterval) {
                console.log('录像--' + dataObject.PirRecordingInterval.value);
               /* if (dataObject.PirRecordingInterval.value == 0) {
                    stateProps.recordTimeInteravlValue = 30;
                    tempRecordInterValSetting.recordTimeInteravlValue = 30;
                    console.log('录像间隔失败timeInVal--' + dataObject.PirRecordingInterval.value);
                }else {*/
                    stateProps.recordTimeInteravlValue = dataObject.PirRecordingInterval.value;
                    tempRecordInterValSetting.recordTimeInteravlValue = dataObject.PirRecordingInterval.value;
                    console.log('录像间隔timeInVal----' + dataObject.PirRecordingInterval.value);
                /*}*/
            }else {
                stateProps.recordTimeInteravlValue = 30;
                tempRecordInterValSetting.recordTimeInteravlValue = 30;
            }


            //侦测距离
            if (dataObject.PirDetectDistance) {
                console.log('侦测距离--' + dataObject.PirDetectDistance.value);
                if (dataObject.PirDetectDistance.value == 0) {
                    stateProps.detectionDistanceValue = 4;
                    tempDistanceSetting.detectionDistanceValue = 4;
                } else {
                    stateProps.detectionDistanceValue = dataObject.PirDetectDistance.value/2;
                    tempDistanceSetting.detectionDistanceValue = dataObject.PirDetectDistance.value/2;
                    console.log('侦测距离成功-----' + dataObject.PirDetectDistance.value);
                }
            }

            //IPC031重点区域侦测的开关取自聚光灯的设置，IPC031无AreaDetectionAlarm
            if(dataObject.SpotlightSetting){
                let spotlightSetting = dataObject.SpotlightSetting.value;
                if(spotlightSetting){
                    stateProps.keyAreaValueAlarmSwitch = spotlightSetting.spotlight_switch == 1 ? true : false;
                }
            }
            //IPC033重点区域侦测的开关取自AreaDetectionAlarm的值，IPC033无SpotlightSetting
            if (dataObject.AreaDetectionAlarm) {
                stateProps.keyAreaValueAlarmSwitch = dataObject.AreaDetectionAlarm.value == 1;
            }


            if (dataObject.AlarmDetectionArea) {
                stateProps.areaArr = dataObject.AlarmDetectionArea.value;
                if (stateProps.areaArr.length > 0 && stateProps.keyAreaValueAlarmSwitch){
                    stateProps.keyAreaValue = I18n.t('alreadyOpenStr');
                }else {
                    stateProps.keyAreaValue = I18n.t('settings_switch_off');

                }
            } else {
                stateProps.keyAreaValue = I18n.t('settings_switch_off');
            }


            console.log('statprops--'+ JSON.stringify(stateProps));
            // 统一设置从设备端获取的值
            this.setState(stateProps);
          /*  this.setState(tempStayTimeSetting);
            this.setState(tempRecordTimeSetting);
            this.setState(tempRecordInterValSetting);
            this.setState(tempDistanceSetting);*/
            console.log('statprops----000--'+ JSON.stringify(stateProps));
        }).catch(error => {
            showToast(I18n.t('commLoadingFailText'));
            showLoading(false);
            console.log(JSON.stringify(error));
        });
    }

    //判断是否跨天
    _getIsCrossDay(startTimeValue, endTimeValue) {
        let startTimeLocal = isSupportNewLogic ? startTimeValue : convertUtcToLocalTimeStamp(startTimeValue);
        let endTimeLocal = isSupportNewLogic ? endTimeValue : convertUtcToLocalTimeStamp(endTimeValue);
        if (startTimeLocal > endTimeLocal) {
            return true
        }
        return false;
    }

    render() {
        global.navigation = this.props.navigation;
        console.log('pushValue--' + this.state.alarmMsgPushValue);
        return (<View style={styles.container}>
            <NavigationBar
                type={NavigationBar.TYPE.LIGHT} backgroundColor={"#FFFFFF"}
                title={I18n.t('alarmSettingText')}
                left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                right={[]}
            />

            <ScrollView showsVerticalScrollIndicator={false}>
             {/*   <XText style={styles.functionSettingStyle} allowFontScaling={false} text={I18n.t('stayDetectStr')}
                       numberOfLines={1}/>
                逗留侦测*/}
                <Separator/>
                {/*侦测时间*/}
                <ListItem title={I18n.t('detectTimeStr')}
                         /* subtitle={I18n.t('detectTimeSubStr')}*/
                          value={this.state.detectTimeValue} hideArrow={false}
                          onPress={() => {
                              // this.setState({alarmMsgPushValue: true});
                             this.props.navigation.navigate('DetectionTime',{
                                   callback:((timeStr) => {
                                       console.log('返回修改侦测时间--',timeStr);
                                       if (timeStr != 'failed'){
                                           this.setState({
                                               detectTimeValue:timeStr
                                           })
                                       }
                              })
                             })
                              // navigation.push('DetectionTime');
                          }}/>
                {/*逗留时长*/}
                <ListItem title={I18n.t('stayTimeStr')} value={this.state.isCheckPowerCommon === true ? this.state.stayTimeValue+I18n.t("cruise_seconds"):null} hideArrow={false}
                          onPress={() => {
                              this.setState({showStayTimeModal: true});
                          }}/>
                          {/*录像时长*/}
                <ListItem title={I18n.t('recordTimeStr')} value={this.state.isCheckRecordTime == true ? I18n.t('recordTimeAutoModeTimeStr') : this.state.recordTimeValue+I18n.t('cruise_seconds')} hideArrow={false}
                          onPress={() => {
                              this.setState({showRecordTimeModal: true});
                          }}/>
                {/*录像间隔*/}
                <ListItem title={I18n.t('recordTimeInteravlStr')}
                          value={this.state.recordTimeInteravlValue+I18n.t('cruise_seconds')} hideArrow={false}
                          onPress={() => {
                              this.setState({showRecordInterValModal: true});
                          }}/>
                {/*侦测灵敏度*/}
                <ListItem title={I18n.t('detectDistanceStr')}
                          value={this.state.detectionDistanceValue+""} hideArrow={false}
                          onPress={() => {
                              this.updatePirTestValue(true);
                              this.setState({showDetectionDistanceModal: true});
                          }}/>

                <View style={{height:14,backgroundColor: "#F1F1F1"}} />
                {/*重点区域侦测*/}
                <ListItem title={I18n.t('keyAreaDetectStr')} value={this.state.keyAreaValue} hideArrow={false}
                          onPress={() => {
                              // navigation.push('FocusAreaDetectionSetting');
                              this.props.navigation.push(LetDevice.model == "a1MZkl613tF" ? 'FocusAreaDetectionSetting' : 'MonitoringAlarmSetting'
                                  , {
                                      callback: ((areaValue) => {
                                          console.log('返回修改重点侦测区域值--', areaValue);
                                          if (areaValue) {
                                              this.setState({
                                                  keyAreaValue: I18n.t('alreadyOpenStr')
                                              })
                                          } else {
                                              this.setState({
                                                  keyAreaValue: I18n.t('settings_switch_off')
                                              })
                                          }
                                          //上传重点侦测区域的开启情况
                                          IMILogUtil.uploadClickEventValue({FocusAreaAlarmSwitch:areaValue?1:0})
                                      })
                                  })
                          }}/>
                <View style={{height:14}} />
                {/*消息推送管理*/}
                <ListItem title={I18n.t('pushMsgManager')}
                          onPress={()=>{
                              this.props.navigation.push('PushMessageManager');
                          }}/>
                {/*逗留时长弹窗*/}
                {this._stayTimeModal()}

                {/*录像时长弹窗*/}
                {this._recordTimeModal()}

                {/*录像间隔*/}
                {this._recordTimeIntervalModal()}
                {/*侦测灵敏度*/}
                {this._detectionDistanceModal()}
            </ScrollView>
        </View>);
    }
    //返回上一页
    _onPressBack = () => {
        this.props.navigation.pop();
    };

    //为了让用户看清楚测试效果，在打开侦测距离设置时开启此设置，退出时关闭此开关
    updatePirTestValue(value,hasHint=false) {
        let params = {PirTestSwitch:value == true ? 1 : 0};
        console.log('pir测试值=-hhhh--',JSON.stringify(params));
        LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
            if (value===true){
                this.setState({showDetectionDistanceModal: true});
                //通知固件，如果没有发送停止测试模式指令，指示灯闪烁应该停止的时间
                IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify({PirTestTimeout:parseInt(new Date().getTime()/1000)+20*60}))
                    .then(()=>{}).catch();
            }else if(hasHint){
                showLoading(false);
                showToast(I18n.t('settings_set_success'));
            }
        }).catch((error) => {
            if(!value&&hasHint){
                showToast(I18n.t('waitFailedTip'));
                showLoading(false);
            }
        });
    }

    //逗留时长间隔
    _stayTimeModal() {
        return( <View>
            <MessageDialog
            title={I18n.t('stayTimeStr')}
            visible={this.state.showStayTimeModal}
            canDismiss={false}
            buttons={[
                {
                    text: I18n.t("cancel"),
                    callback: _ => {
                        this.setState({showStayTimeModal: false});
                        this.setState(tempStayTimeSetting);
                    }
                },
                {
                    text: I18n.t("ok_button"),
                    callback: _ => {
                        this.setState({showStayTimeModal:false});
                        this.updateStayTime();
                    }
                },
            ]}
            ><ScrollView style={{flex:1}}>
                <XText style={styles.modalSubTit} text={I18n.t('houseDetectedPeopleStr')}/>
                <ChoiceItem title={I18n.t('peopleRecordPowerMoreStr')} subtitle={I18n.t('peopleRecordPowerMoreSubStr')}
                            headIcon={require("../../resources/images/icon_person.png")}
                            containerStyle={{margin: 14}}
                            checked={this.state.isCheckPowerMore}
                            onlyChecked={true}
                            // checked={this.state.StayTimeSelectedIndexArray[0]}
                            onValueChange={(value) => {
                                console.log("ChoiceItem------checked", value);
                                if (value == true) {
                                    this.setState({
                                        isCheckPowerMore: true,
                                        isCheckPowerCommon: false,
                                        isCheckPowerSmall: false,
                                        stayTimeValue:0,
                                        staySelectArr:[0,0,0,0]
                                    })
                                }
                            }}/>

                <ChoiceItem title={I18n.t('peopleRecordPowerCommonStr')} subtitle={I18n.t('peopleRecordPowerCommonSubStr')}
                            headIcon={require("../../resources/images/icon_stay.png")}
                            containerStyle={{marginHorizontal: 14}}
                            checked={this.state.isCheckPowerCommon}
                            onlyChecked={true}
                            showChoiceArray={true}
                            selectIndexArray={this.state.staySelectArr}
                            choiceArrayDefaultIndex={LetDevice.model == "a1EVaCvJ43g"?1:2} //033默认选中10秒，031默认选15秒
                            onValueChange={(value) => {
                                if (value == true) {
                                    this.setState({
                                        isCheckPowerMore: false,
                                        isCheckPowerCommon: true,
                                        isCheckPowerSmall: false,
                                    })
                                }
                            }}
                            onSelectValueChange={(index) => {
                                if (index == 0){
                                    this.setState({
                                        staySelectArr:[1,0,0,0],
                                        stayTimeValue:5,
                                    });
                                }else  if (index == 1){
                                    this.setState({
                                        staySelectArr:[0,1,0,0],
                                        stayTimeValue:10
                                    });
                                }else  if (index == 2){
                                    this.setState({
                                        staySelectArr:[0,0,1,0],
                                        stayTimeValue:15
                                    });
                                }else  if (index == 3){
                                    this.setState({
                                        staySelectArr:[0,0,0,1],
                                        stayTimeValue:20
                                    });
                                }
                                console.log('hhhh--ChoiceItem------indexSelected--000' + index);
                            }}/>
                <ChoiceItem title={I18n.t('peopleRecordPowerLessStr')} subtitle={I18n.t('peopleRecordPowerLessSubStr')}
                            headIcon={require("../../resources/images/icon_close.png")}
                            containerStyle={{margin: 14,marginBottom:0}}
                            checked={this.state.isCheckPowerSmall}
                            onlyChecked={true}
                            // checked={this.state.StayTimeSelectedIndexArray[2]}
                            onValueChange={(value) => {
                                console.log("ChoiceItem------checked", value);
                                if (value == true) {
                                    this.setState({
                                        isCheckPowerMore: false,
                                        isCheckPowerCommon: false,
                                        isCheckPowerSmall: true,
                                        stayTimeValue:-1,
                                        staySelectArr:[0,0,0,0]
                                    })
                                }
                            }}/></ScrollView>
            </MessageDialog>
        </View>)
    }

    //设置推送消息
    updatePushMsg(value) {
        console.log('消息--',value);
        const params = {
            Path: 'api/app/notice/event/set',
            Method:'PUT',
            ParamMap: {
                iotId: LetDevice.deviceID,
                productKey: LetDevice.model,
                event:'STAY',
                noticeEnabled:value,
            }
        };
        console.log('开关推送传值params--',params);
        showLoading(stringsTo('commWaitText'), true);
        LetIMIIotRequest.sendUserServerRequest(params).then((data) => {
            showToast(I18n.t('settings_set_success'));
            showLoading(false);
        }).catch((error) => {
            console.log('设置推送数据错误--',error);
            this.setState({
                alarmMsgPushValue:!value
            });
            showToast(I18n.t('operationFailed'));
            showLoading(false);
            console.log('sendUserServerRequest error ' + error)
        });
    }


    //设置逗留时长
    updateStayTime() {
        console.log('非常耗电--一般耗电---少耗电',this.state.isCheckPowerMore,this.state.isCheckPowerCommon,this.state.isCheckPowerSmall);
        console.log('逗留时长--==',this.state.stayTimeValue);

        showLoading(stringsTo('commWaitText'), true);
        let params = {PirDetectionMechanism:this.state.stayTimeValue};
        console.log('逗留时长-hhhh--',JSON.stringify(params));
        IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params)).then( ()=> {
            this.setState({
                PirDetectionMechanism: this.state.stayTimeValue,
            });

            if (this.state.stayTimeValue === 0){
                //非常耗电
                tempStayTimeSetting.stayTimeValue = this.state.stayTimeValue;
                tempStayTimeSetting.isCheckPowerMore = true;
                tempStayTimeSetting.isCheckPowerCommon = false;
                tempStayTimeSetting.isCheckPowerSmall = false;
                tempStayTimeSetting.staySelectArr = [0, 0, 0, 0];
            }else if (this.state.stayTimeValue === -1){
                //关闭
                tempStayTimeSetting.stayTimeValue = this.state.stayTimeValue;
                tempStayTimeSetting.isCheckPowerMore = false;
                tempStayTimeSetting.isCheckPowerCommon = false;
                tempStayTimeSetting.isCheckPowerSmall = true;
                tempStayTimeSetting.staySelectArr = [0, 0, 0, 0];
            }else {
                tempStayTimeSetting.stayTimeValue = this.state.stayTimeValue;
                tempStayTimeSetting.isCheckPowerMore = false;
                tempStayTimeSetting.isCheckPowerCommon = true;
                tempStayTimeSetting.isCheckPowerSmall = false;

                if (tempStayTimeSetting.stayTimeValue === 5) {
                    tempStayTimeSetting.staySelectArr = [1, 0, 0, 0];
                    tempStayTimeSetting.staySelectIndex = 0;
                } else if (tempStayTimeSetting.stayTimeValue === 10) {
                    tempStayTimeSetting.staySelectArr = [0, 1, 0, 0];
                    tempStayTimeSetting.staySelectIndex = 1;
                } else if (tempStayTimeSetting.stayTimeValue === 15) {
                    tempStayTimeSetting.staySelectArr = [0, 0, 1, 0];
                    tempStayTimeSetting.staySelectIndex = 2;
                } else if (tempStayTimeSetting.stayTimeValue === 20) {
                    tempStayTimeSetting.staySelectArr = [0, 0, 0, 1];
                    tempStayTimeSetting.staySelectIndex = 3;
                }
            }

            console.log('临时逗留时长设置---',tempStayTimeSetting.stayTimeValue);
            this.setState(tempStayTimeSetting);
            showToast(I18n.t('settings_set_success'));
            showLoading(false);
            console.log('设置逗留时长---'+this.state.stayTimeValue);
        }).catch(() => {
            this.setState(tempStayTimeSetting);
            showToast(I18n.t('operationFailed'));
            showLoading(false);
        });
    }

    //录像时长
    _recordTimeModal() {
        return(<View>
            <MessageDialog
                title={I18n.t('recordTimeStr')}
                visible={this.state.showRecordTimeModal}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({showRecordTimeModal: false});
                            this.setState(tempRecordTimeSetting);
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            this.setState({showRecordTimeModal:false});
                            this.updateRecordTime();
                        }
                    },
                ]}
            >
                <ChoiceItem title={I18n.t('recordTimeAutoModeTimeStr')}
                            subtitle={I18n.t('recordTimeAutoModeSubtitle60')}
                            containerStyle={{margin: 14}}
                            checked={this.state.isCheckRecordTime}
                            onValueChange={(value) => {
                                if (value==true) {
                                    this.setState({
                                        isCheckRecordTime:true,
                                        isCheckRecordTimeDIY:false,
                                        recordTimeValue:0,
                                        recordTimeModalColor:UNCHECKED_BACKGROUNDCOLOR,
                                    })
                                }else{
                                    this.setState({
                                        isCheckRecordTime:false,
                                        isCheckRecordTimeDIY:true,
                                        recordTimeValue:6,
                                        recordTimeModalColor:BACKGROUNDCOLOR,
                                    })
                                }
                            }}/>
                <View style={{
                    flexDirection: "column",
                    margin: 14,
                    marginTop:0,
                    marginBottom:0,
                    borderRadius: 10,
                    backgroundColor:this.state.recordTimeModalColor,
                }}>
                    <ChoiceItem title={I18n.t('recordTimeCustomModeStr')+(this.state.recordTimeValue<6?"":"("+this.state.recordTimeValue+I18n.t('cruise_seconds')+")")}
                                containerStyle={{marginTop: 0,borderBottomLeftRadius:0,borderBottomRightRadius:0}}
                                checked={this.state.isCheckRecordTimeDIY}
                                onValueChange={(value) => {
                                    if (value==true) {
                                        this.setState({
                                            isCheckRecordTimeDIY:true,
                                            isCheckRecordTime:false,
                                            recordTimeValue:6,
                                            recordTimeModalColor:BACKGROUNDCOLOR,
                                        })
                                    }else {
                                        this.setState({
                                            isCheckRecordTimeDIY:false,
                                            isCheckRecordTime:true,
                                            recordTimeValue:0,
                                            recordTimeModalColor:UNCHECKED_BACKGROUNDCOLOR,
                                        })
                                    }
                                }}/>
                    <View style={{
                        flexDirection: "column",
                        height: 60,
                        borderBottomLeftRadius: 10,
                        borderBottomRightRadius: 10,
                        // backgroundColor:this.state.recordTimeModalColor,
                    }}>
                        <SlideGear
                            options={Array.from({ length: 55 }, (v, i) => i + 1)}
                            indicatorTextArray={[6,10,20,30,40,50,60]}
                            value={this.state.isCheckRecordTimeDIY === true ? this.state.recordTimeValue-6 : 0}
                            onValueChange={(index) => {

                               /* if(index==0){
                                    this.setState({
                                        isCheckRecordTimeDIY:false,
                                        isCheckRecordTime:true,
                                        recordTimeValue:0,
                                        recordTimeModalColor:UNCHECKED_BACKGROUNDCOLOR,
                                    });
                                }else{*/
                                    this.setState({
                                        isCheckRecordTimeDIY:true,
                                        isCheckRecordTime:false,
                                        recordTimeValue:index+6,
                                        recordTimeModalColor:BACKGROUNDCOLOR,

                                    });
                                /*}*/

                            }}
                            onSlidingComplete={()=>{}}
                           /* onSlidingComplete={(value) => {
                                if (value <= 0) {
                                    value = 1;
                                } else if (value > 59) {
                                    value = 60;
                                }
                                this.setState({
                                    recordTimeValue:value
                                });
                            }}*/
                        />
                    </View>
                </View>
            </MessageDialog>
        </View>)
    }

    //设置录像时长
    updateRecordTime(){
        console.log('录像时长--',this.state.recordTimeValue);
        showLoading(stringsTo('commWaitText'), true);
        let params = {PirRecordingTime:this.state.recordTimeValue};
        IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params),{PirRecordingTime:this._getRecordTimeLogValue(this.state.recordTimeValue)}).then( ()=> {
            this.setState({
                PirRecordingTime: this.state.recordTimeValue
            });
            if (this.state.isCheckRecordTime === true){
                tempRecordTimeSetting.isCheckRecordTime = true;
                tempRecordTimeSetting.isCheckRecordTimeDIY = false;
                tempRecordTimeSetting.recordTimeValue = 0;
                tempRecordTimeSetting.recordTimeModalColor = UNCHECKED_BACKGROUNDCOLOR;
            }else {
                tempRecordTimeSetting.isCheckRecordTime = false;
                tempRecordTimeSetting.isCheckRecordTimeDIY = true;
                tempRecordTimeSetting.recordTimeValue = this.state.recordTimeValue;
                tempRecordTimeSetting.recordTimeModalColor = BACKGROUNDCOLOR;
            }
            this.setState(tempRecordTimeSetting);
            showToast(I18n.t('settings_set_success'));
            showLoading(false);
            console.log('设置录像时长---'+this.state.recordTimeValue);
        }).catch(() => {
            this.setState(tempRecordTimeSetting);
            showToast(I18n.t('operationFailed'));
            showLoading(false);
        });
    }

    //录像时间打点需要统计时长的设置区间
    _getRecordTimeLogValue(recordTimeValue){
        let result = "0";
        if(recordTimeValue<6){
            result = "0";
        }else if(recordTimeValue>=6&&recordTimeValue<10){
            result = "1";
        }else if(recordTimeValue>=10&&recordTimeValue<20){
            result = "2";
        }else if(recordTimeValue>=20&&recordTimeValue<30){
            result = "3";
        }else if(recordTimeValue>=30&&recordTimeValue<40){
            result = "4";
        }else if(recordTimeValue>=40&&recordTimeValue<50){
            result = "5";
        }else if(recordTimeValue>=50&&recordTimeValue<=60){
            result = "6";
        }
        return result;
    }

    //录像间隔
    _recordTimeIntervalModal() {
        return(<View>
            <MessageDialog
                title={I18n.t('recordTimeInteravlStr')+'('+this.state.recordTimeInteravlValue+I18n.t('cruise_seconds')+')'}
                visible={this.state.showRecordInterValModal}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({showRecordInterValModal: false});
                            this.setState(tempRecordInterValSetting);
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            this.setState({showRecordInterValModal:false});
                            this.updateRecordTimeInteravl();
                        }
                    },
                ]}
            >
                <View style ={{marginTop:14}}>
                    <SlideGear
                        options={Array.from({ length: 121 }, (v, i) => i + 1)}
                        indicatorTextArray={[0,20,40,60,80,100,120]}
                        value={this.state.recordTimeInteravlValue}
                        onValueChange={(index) => {
                            console.log('滑动距离===',index);
                            if (index <= 0) {
                                index = 0;
                            } else if (index > 119) {
                                index = 120;
                            }
                            this.setState({
                                recordTimeInteravlValue:index
                            });
                        }}
                        onSlidingComplete={()=>{}}
                        // onSlidingComplete={(value) => {
                        //     if (value <= 0) {
                        //         value = 1;
                        //     } else if (value > 119) {
                        //         value = 120;
                        //     }
                        //     this.setState({
                        //         recordTimeInteravlValue:value
                        //     });
                        // }}
                    />
                </View>
            </MessageDialog>
        </View>)
    }

    //设置录像间隔
    updateRecordTimeInteravl(){
        console.log('distance-',this.state.recordTimeInteravlValue);

        showLoading(stringsTo('commWaitText'), true);
        let params = {PirRecordingInterval:this.state.recordTimeInteravlValue};
        IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params),{PirRecordingInterval:this._getRecordIntervalLogValue(this.state.recordTimeInteravlValue)}).then( ()=> {
            this.setState({
                PirRecordingInterval: this.state.recordTimeInteravlValue
            });
            tempRecordInterValSetting.recordTimeInteravlValue = this.state.recordTimeInteravlValue;
            this.setState(tempRecordInterValSetting);
            showToast(I18n.t('settings_set_success'));
            showLoading(false);
            console.log('设置录像间隔成功---'+this.state.recordTimeInteravlValue);
        }).catch(() => {
            this.setState(tempRecordInterValSetting);
            showToast(I18n.t('operationFailed'));
            showLoading(false);
        });
    }

    //录像间隔需要打点统计选择区间内占比
    _getRecordIntervalLogValue(recordInterval){
        let result = "0"
        if(recordInterval==0){
            result = "0";
        }else if(recordInterval>=1&&recordInterval<10){
            result = "1";
        }else if(recordInterval>=10&&recordInterval<20){
            result = "2";
        }else if(recordInterval>=20&&recordInterval<30){
            result = "3";
        }else if(recordInterval>=30&&recordInterval<60){
            result = "4";
        }else if(recordInterval>=60&&recordInterval<90){
            result = "5";
        }else if(recordInterval>=90&&recordInterval<=120){
            result = "6";
        }
        return result;
    }


    //侦测灵敏度设置对话框
    _detectionDistanceModal() {
        let defaultSource = require('../../../com.chuangmi.camera.IPC031/resources/images/icon_distance.png');
        let defaultSubtitle = I18n.t('detectDistanceModalSubStr');
        let configProject = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        let distanceImageSource = configProject.specProjectConfig?configProject.specProjectConfig.distanceImageSource:defaultSource;
        let distanceSubtitle = configProject.specProjectConfig?configProject.specProjectConfig.distanceSubtitle:defaultSubtitle;
        let distanceOrangeLightImageSource = require('../../../com.chuangmi.camera.IPC031/resources/images/icon_distance_orange_light.png');
        let detectionDistanceValueArray = [1,2,3,4]; //拖动条组件上显示的数字
        return(<View>
            <MessageDialog
                title={I18n.t('detectDistanceStr')}
                visible={this.state.showDetectionDistanceModal}
                canDismiss={false}
                buttons={[
                    {
                        text: I18n.t("cancel"),
                        callback: _ => {
                            this.setState({showDetectionDistanceModal: false});
                            this.setState(tempDistanceSetting);
                            this.updatePirTestValue(false);
                        }
                    },
                    {
                        text: I18n.t("ok_button"),
                        callback: _ => {
                            this.setState({showDetectionDistanceModal:false});
                            this.updateDetectDistance();
                        }
                    },
                ]}
            >
                <XText numoflines={3} style={styles.distanceModalSubTit} text={distanceSubtitle}/>
                <View style={{
                    flexDirection: 'row',
                    height:100,
                    width:"100%",
                    alignItems:'center',
                    justifyContent:'center',
                    marginBottom:14,
                    flex: 0,
                }}>
                    <ImageBackground style = {{height:100,width:100,}} source={distanceImageSource}>
                        <Image style = {{height:100,width:100,}} source={distanceOrangeLightImageSource}/>
                    </ImageBackground>
                </View>
                <SlideGear
                    options={[0,1,2,3]}
                    containerStyle={{marginBottom:14}}
                    indicatorTextArray={[1,2,3,4]}
                    value={detectionDistanceValueArray.indexOf(this.state.detectionDistanceValue)}
                    stopAtIndicatorText={true}
                    onValueChange={(index) => {
                    }}
                    onSlidingComplete={(index) => {
                        this.setState({
                            detectionDistanceValue: detectionDistanceValueArray[index]
                        });

                    }}
                />
            </MessageDialog>
        </View>)
    }


//设置侦测距离
    updateDetectDistance() {
        showLoading(stringsTo('commWaitText'), true);
        if (this.state.detectionDistanceValue === 0) {
            showToast(I18n.t('operationFailed'));
        }

        let params = {PirDetectDistance: this.state.detectionDistanceValue * 2};
        console.log('设置侦测距离--' + JSON.stringify(params));
        IMILogUtil.setPropertyCloudWithUploadLog(JSON.stringify(params),{PirDetectDistance:this.state.detectionDistanceValue.toString()}).then(() => {
            tempDistanceSetting.detectionDistanceValue = this.state.detectionDistanceValue;

            this.setState(tempDistanceSetting);
            this.updatePirTestValue(false,true);

            // showToast(I18n.t('settings_set_success'));
            // showLoading(false);
            // this.setState({
            //               PirDetectDistance: this.state.detectionDistanceValue
            //           });
            console.log('PirDetectDistance---' + this.state.detectionDistanceValue);
        }).catch(() => {
            this.setState(tempDistanceSetting);
            showToast(I18n.t('operationFailed'));
            showLoading(false);
        });
 }

}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#F1F1F1',
    },
    functionSettingStyle: {
        color: '#0000007F',
        fontSize: 12,
        marginTop: 23,
        marginLeft: 14
    },
    modalTit: {
        fontSize: 16,
        color: '#333333',
        lineHeight:60,
        alignItems:'center',
        borderTopLeftRadius:20,
        borderTopRightRadius:20,
        textAlign:'center',
        // backgroundColor: 'red',
        fontWeight:'bold',
        justifyContent:'center',
        backgroundColor:'red',
    },
    modalSubTit: {
        fontSize: 12,
        color: '#7F7F7F',
        lineHeight:40,
        paddingLeft:14,
        // backgroundColor: 'green',
        alignItems:'center',
        justifyContent: 'center',
        // backgroundColor:'blue',
    },
    distanceModalTit: {
        alignItems:'center',
        justifyContent:'center',
        textAlign:'center',
        fontSize:16,
        fontWeight: 'bold',
        color: '#333333',
        // backgroundColor:'green',
        marginTop: 16,
    },
    distanceModalSubTit: {
        alignItems:'center',
        justifyContent:'center',
        textAlign:'center',
        fontSize:12,
        color: '#7F7F7F',
        // backgroundColor:'red',
        marginTop:-10,
        marginBottom:14,
    },

});
