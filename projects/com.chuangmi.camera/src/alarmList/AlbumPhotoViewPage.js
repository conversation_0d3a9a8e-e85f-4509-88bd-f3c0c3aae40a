import React from 'react';

import I18n, {stringsTo} from '../../../../globalization/Localize';
import {View, Dimensions, ActivityIndicator, BackHandler, Platform, DeviceEventEmitter} from 'react-native';

import ImageViewer from 'react-native-image-zoom-viewer';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import Orientation from 'react-native-orientation';
import {showLoading, showToast, MessageDialog, IMIImageView2} from '../../../../imilab-design-ui';
import {
  aliAlarmEventCloudApi,
  imiAlarmEventCloudApi,
  IMIDownload,
  IMIGotoPage,
  IMIPackage,
  LetDevice,
} from '../../../../imilab-rn-sdk';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import moment from 'moment';
import {XText} from 'react-native-easy-app';
import {convertUtcToLocalTimeStamp} from '../../../../imilab-rn-sdk/utils/Utils';
import IMIPermission from '../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import {cloudDeviceService} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDeviceCloudService';
import NewGiftDialog from '../../../../imilab-design-ui/src/widgets/settingUI/NewGiftDialog';
import {customJsonParse} from '../utils/GenericUtils';
import {GotoPageInNative} from '../../../../imilab-rn-sdk';

const iconSize = 40; // 图标尺寸
const EVENT_NAME = 'IMIDownloadImageScheduler1 - ';

export default class AlbumPhotoViewPage extends React.Component {
  constructor(props) {
    super(props);
    this.receiverMap = new Map();
    this.receiveUrlArray = [];
    this.title = null;
    this.state = {
      imageUrl: '',
      imageUrlArray: [],
      eventTime: '',
      eventType: 0,
      segmentId: null,
      // showLoadingDialog: true,
      deleteDialogVisible: false,
      navigationBarVisible: true,
      vipState: -1,
      item: {},
      imageLoadError: false, //图片加载失败
      giftDialog: false, //免费 vip 弹窗
    };
  }

  componentDidMount() {
    if (!this.props.navigation.canGoBack()) {
      GotoPageInNative.addStarNativeGoToPage(this.props.navigation);
    }
    Orientation.lockToPortrait(); // 切换回竖屏
    //未开云存，iOS上的route.params中的storageType为null,之前判断不为null才执行逻辑，会导致iOS加载不出图片
    if (
      this.props.route.params != null &&
      this.props.route.params.item != null
    ) {
      let {eventType, eventStartTime, segmentId} = this.props.route.params.item;
      let eventTime = moment(eventStartTime).format('YYYY-MM-DD HH:mm:ss');
      this.setState({
        vipState: this.props.route.params.vipState,
        eventTime: eventTime.substr(10),
        eventType: eventType,
        segmentId,
        item: this.props.route.params.item,
      });
      this.getImgUrl();
    }

    showLoading(stringsTo('commLoadingText'), true);
    imiAlarmEventCloudApi
      .getVipState(LetDevice.deviceID)
      .then(res => {
        let data = JSON.parse(res);

        //showLoading(false);
        this.setState({vipState: data.status});
      })
      .catch(error => {
        //showLoading(false);
        //获取VIP状态失败，给定为VIP状态，会导致非VIP获取失败，不展示购买提示
        //改为-1吧，与看家页面逻辑保持一致，看家获取VIP失败，状态标识为非VIP
        this.setState({vipState: -1});
      });
    // this.loadFiles();
    if (Platform.OS === 'android') {
      this.backHandler = BackHandler.addEventListener('hardwareBackPress', this.onBack);
    }
  }

  componentWillUnmount() {
    showLoading(false);
    this.backHandler && this.backHandler.remove()
    if (!this.props.navigation.canGoBack()) {
      GotoPageInNative.removeStarNativeGoToPage();
    }
    this.setState = (state, callback) => {
      return;
    };
  }
  // 获取封面图片
  getImgUrl = async () => {
    let {imgSignedDownloadUrl} = this.props.route.params.item;

   
    const {segmentId, publicKeyVersion, imgPath} = imgSignedDownloadUrl;
    const data = await cloudDeviceService
      .downLoadImage(LetDevice.deviceID, segmentId, publicKeyVersion, imgPath)
      .catch(e => {
        showToast(stringsTo('commLoadingFailText'))
      });
    if (data) {
      this.setState({
        imageUrl: data,
      });
    }
  };

  onBack = () => {
    this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
    return true;
  };

  _onChange(index) {
    console.log('index1111', index);
  }

  // 领取免费套餐
  async getFreeCloudStorageInfo() {
    if (LetDevice.isShareUser) {
      return;
    }
    showLoading(stringsTo('commLoadingText'), true);
    const data = await cloudDeviceService.getStorageFree(LetDevice.deviceID).catch(e => {
      console.log(e);
      showLoading(false);
    });
    if (data) {
      const res = customJsonParse(data);
      if (res?.isAllowReceiveFreePackage) {
        this.setState({giftDialog: true});
        showLoading(false);
      } else {
        showLoading(false);
        // showToast('没有次数');
        IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,"link://feedback/pages/toCloudBuyBrowserPage");
      }
    }
  }

  render() {
    let screenWidth =
      Dimensions.get('window').width > Dimensions.get('window').height
        ? Dimensions.get('window').height
        : Dimensions.get('window').width;
    return (
      <View style={{display: 'flex', width: '100%', height: '100%', flexWrap: 'nowrap', backgroundColor: 'black'}}>
        {this.state.navigationBarVisible ? (
          <NavigationBar
            type={NavigationBar.TYPE.DARK}
            backgroundColor={'transparent'}
            title={this.state.eventTime}
            left={[
              {
                key: NavigationBar.ICON.BACK,
                onPress: () => {
                  this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
                },
              },
            ]}
            right={[
              {
                key: NavigationBar.ICON.CUSTOM,
                disable: this.state.imageLoadError,
                n_source: require('../../resources/images/icon_download_white.png'),
                onPress: _ => {
                  // showToast('暂时无法下载');
                  IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status => {
                    if (status === 0) {
                      showLoading(stringsTo('alarm_download_downloading'), true);
                      this.downloadFile(this.props.route.params.item);
                    } else if (status === -1) {
                      showToast(stringsTo('storage_permission_denied'));
                    }
                  });
                },
              },
              {
                key: NavigationBar.ICON.CUSTOM,
                n_source: require('../../resources/images/icon_album_delete_white.png'),
                onPress: _ => {
                  if (this.state.segmentId != null) {
                    this.setState({showDeleteTip: true});
                  }
                },
              },
            ]}
          />
        ) : null}

        <View style={{marginTop: 110, alignItems: 'flex-end', height: 40}}>
          <View
            style={{
              minWidth: '50%',
              maxWidth: '85%',
              height: 40,
              borderBottomLeftRadius: 20,
              borderTopLeftRadius: 20,
              backgroundColor: '#4A70A5',
              justifyContent: 'center',
              display: this.state?.vipState !== 1 && !IMIPackage.closeCloudStorage ? '' : 'none'
            }}>
            <XText
              raw={true}
              text={stringsTo('buy_cloud_for_info')}
              style={{paddingLeft: 20, textAlign: 'center', fontSize: 12, color: 'white'}}
              onPress={() => {
                if (LetDevice.isShareUser) {
                  showToast(stringsTo('shareUser_tip'));
                  return;
                }
                if (this.state?.vipState !== 1) {
                  // this.setState({giftDialog: true});
                  this.getFreeCloudStorageInfo()
                } else {
                  showToast('已经是会员');
                }
              }}
            />
          </View>
        </View>
        <View style={{marginTop: 50, width: screenWidth, height: (screenWidth * 9) / 16.0}}>
          {/*backgroundColor:'blue',*/}
          {console.log('王 试测试测试render试---imageUrl--', this.state.imageUrl)}
          {this.state.imageUrl && <ImageViewer
            /*  backgroundColor={'black'}*/
            imageUrls={[
              {
                url: this.state.imageUrl,
                width: screenWidth,
                height: (screenWidth * 9) / 16.0,
                path: undefined,
                createTime: 1602641239,
              },
            ]}
            renderIndicator={() => {
              return null;
            }}
            saveToLocalByLongPress={false}
            enableImageZoom={false} //056产品定义不能展开
            loadingRender={() => {
              return <ActivityIndicator size="large" color={'red'} />;
            }}
            onChange={this._onChange.bind(this)}
            renderImage={prop => {
              return (
                <IMIImageView2
                  {...prop}
                  imageUrl={`file://${this.state.imageUrl}`}
                  onLoadEnd={success => {
                    showLoading(false);
                    this.setState({imageLoadError: !success});
                  }}
                  resizeMode={'contain'}
                />
              );
            }}
          />}
        </View>
        <MessageDialog
          title={stringsTo('delete_alert')}
          visible={this.state.showDeleteTip}
          canDismiss={true}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelDeleteAlertPhoto',
              callback: _ => {
                this.setState({showDeleteTip: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okDeleteAlertPhoto',
              callback: _ => {
                showLoading(stringsTo('delete_title_loading'), true);
                this.setState({showDeleteTip: false});
                aliAlarmEventCloudApi
                  .deleteSingleFileBySegment(LetDevice.deviceID, this.state.segmentId)
                  .then(data => {
                    showLoading(stringsTo('delete_title_loading'), false);
                    showToast(stringsTo('delete_success'));
                    this.props.route?.params?.refreshList && this.props.route.params.refreshList()
                    this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
                  })
                  .catch(error => {
                    showLoading(stringsTo('delete_title_loading'), false);
                    showToast(stringsTo('delete_failed'));
                    console.log('deleteAliCloudImage--error---', error);
                  });
                /*  aliAlarmEventCloudApi
                  .deleteAliPictureCloudImage(LetDevice.deviceID, [this.state.segmentId])
                  .then(data => {
                    showLoading(stringsTo('delete_title_loading'), false);
                    showToast(stringsTo('delete_success'));
                    this.props.navigation.pop();
                  })
                  .catch(error => {
                    showLoading(stringsTo('delete_title_loading'), false);
                    showToast(stringsTo('delete_failed'));
                    console.log('deleteAliCloudImage--error---', error);
                  }); */
              },
            },
          ]}
        />
        {!IMIPackage.closeCloudStorage && <NewGiftDialog
          visible={this.state.giftDialog}
          resource={require('../../resources/images/newFreeCloudBg.png')}
          /*   giftName={this.state.giftName}
          giftPrice={this.state.giftPrice}
          currency={this.state.currency} */
          canDismiss={false}
          onDismiss={() => {
            this.setState({giftDialog: false});
          }}
          onPress={() => {
            //快速点击，会出现多次领用云存的问题，
            //改为giftDialog状态成功后再去更新云端值,降低出错
            //this.gainFreeCloudStorage();
            this.setState({giftDialog: false}, () => {
              // this.getFreeCloudStorageInfo();
              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,"link://feedback/pages/toCloudBuyBrowserPage");
            });
          }}
          onPressCloseDialog={() => {
            this.setState({giftDialog: false});
          }}
        />}
      </View>
    );
  }

  downloadFile() {
    IMIFile.saveImageToPhotosAlbum(this.state.imageUrl, LetDevice.deviceID)
      .then(data => {
        showLoading(false);
        showToast(stringsTo('download_system_album'));
        console.log('保存相册' + data);
      })
      .catch(error => {
        showLoading(false);
        console.log('保存相册error--', this.state.imageUrl, JSON.stringify(error));
        showToast(stringsTo('video_download_fail'));
      });
  }
}
