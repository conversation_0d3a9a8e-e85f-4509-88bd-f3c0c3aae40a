/*
 * 作者：sunhongda
 * 文件：AlarmListPlayerComponent.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React, {useEffect, useState} from 'react';

import {StyleSheet, View, Dimensions, DeviceEventEmitter, Text, Image, TouchableOpacity, StatusBar} from 'react-native';

import {XFlatList, XImage, XText, XView} from 'react-native-easy-app';

import {
  colors,
  IMIDesignEmptyView,
  IMIDesignEmptyViewNew,
  IMIImageView,
  showLoading,
  showToast,
  MessageDialog,
  imiThemeManager,
} from '../../../../imilab-design-ui';

import {
  AlarmType,
  BaseDeviceComponent,
  DateUtils,
  aliAlarmEventCloudApi,
  LetDevice,
  LetIMIIotRequest,
  IMILog,
  imiAlarmEventCloudApi
} from '../../../../imilab-rn-sdk';

import AlarmTopSelectBar from '../../../../imilab-modules/com.chuangmi.camera.moudle/components/camera/alarm/AlarmTopSelectBar';
import iconEmpty from '../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/police_pic_empty.png';
import iconEmptyNew from '../../resources/images/police_pic_empty_new.png';
import Orientation from 'react-native-orientation';
import PropTypes from 'prop-types';
import {isAndroid, isEmpty} from '../../../../imilab-rn-sdk/utils/Utils';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import {IMIDownload, IMIPackage, IMIGotoPage} from '../../../../imilab-rn-sdk';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import {PLAYER_EVENT_CODE} from '../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView';

import moment from 'moment';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import {cloudDeviceService} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDeviceCloudService';
import {assemblyDate, customJsonParse} from '../utils/GenericUtils';
import {IMINativeLifeCycleEvent} from '../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';

/**
 * @Description: 摄像机看家模块
 * @Author:   sunhongda
 * @CreateDate: 2020/9/28 18:02
 * @UpdateUser:     更新者
 * @UpdateDate:  2020/9/28 18:02
 * @UpdateRemark:   更新说明
 */
const defaultEventCurrentTitles = [
  [stringsTo('all_events_str'), AlarmType.ALL],
  [stringsTo('people_event'), AlarmType.PEOPLE],
  [stringsTo('move_event'), AlarmType.MOVE],
  // [stringsTo('alarm_loud_switch'), AlarmType.SOUND],
];
const {width, height} = Dimensions.get('window');
let windowWidth = height > width ? width : height;
let isGoBack = false; //判断数组是否返回父视图

const EVENT_NAME = 'IMIDownloadImageScheduler - ';
const LIST_LIMIT = 20; //每次拉取的视频个数 getEventList
const EVENT_LIST_LIMIT = 10; //每次拉取事件视频数

const TAG = ' AlarmListPlayerComponent- ';

export default class AlarmListPlayerComponent extends BaseDeviceComponent {
  static propTypes = {
    /* 事件选择title 数组 */
    eventCurrentTitles: PropTypes.array,
    onClickCancel: PropTypes.func,
    isEdit: PropTypes.bool,
    onClickSelect: PropTypes.func,
    isSelectedAll: PropTypes.bool,
    dataArray: PropTypes.array,
    vipState: PropTypes.number,
    // 传入navigation用于返回事件
    navigation: PropTypes.object,
    getDataLength: PropTypes.func,
    lifecycleDay: PropTypes.number,
  };
  static defaultProps = {
    eventCurrentTitles: null,
    onClickCancel: null,
    isEdit: false,
    onClickSelect: null,
    isSelectedAll: false,
    dataArray: [],
    vipState: -1,
    getDataLength: null,
  };

  constructor(props) {
    super(props);
    this.state = {
      /* 数据集合  */
      dataList: [],
      muted: false,
      videoCover: undefined,
      flatListHeight: 0,
      isDataEmpty: true,
      fullScreen: Orientation.getInitialOrientation() !== 'PORTRAIT',
      isPlaying: false,
      dataSource: {},
      eventCurrentIndex: 0,
      timeEnd: 0, //用于加载更多的结束时间，不要和每次查询的结束时间弄混淆。 因为现在的查询时间为倒叙，所以查询结束时间为每次查询结果的最后一个视频的时间 ，但是开始的时间为当天的凌晨时间
      /** 某个日期下日历小圆点 **/
      dateTimeDotSelect: this.props.dateTimeDotSelect,
      showDeleteTip: false,
      vipState: -1,
    };
    this.reqTime = this._getDataFormatTime();
    this.reqIntelligentTypeArray = [];

    this.pageIndex = 1; //页码
    this.topSelectBarRoot = undefined;

    this.mCurItem = null;
    this.mCurIndex = 0;
    this.num = 0;
    this.keepLoad = false; //标记是否继续加载后面的数据，true继续，false不继续
    this.isListLoading = false; // 列表加载状态
    this.selectedMonth = new Date().getMonth() + 1;
    this.selectedDay = '';
    this.itemClick = false;
  }

  _getDataFormatTime() {
    return `${DateUtils.doHandleYear()}-${DateUtils.doHandleMonth()}-${DateUtils.doHandleDay2()}`;
  }

  getDay(day) {
    var today = new Date();
    var targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
    today.setTime(targetday_milliseconds); //注意，这行是关键代码
    var tYear = today.getFullYear();
    var tMonth = today.getMonth();
    var tDate = today.getDate();
    tMonth = this.doHandleMonth(tMonth + 1);
    tDate = this.doHandleMonth(tDate);
    return tYear + '-' + tMonth + '-' + tDate;
  }

  async getCurrentMonthEventDays(month, year) {
    const currentMonth = month || this.selectedMonth;
    const currentYear = year || new Date().getFullYear();
    const params = {
      Path: `v1.0/imilab-01/app/cloudstorage/getMonthCloudStorageVideoDays?iotId=${LetDevice.deviceID}&month=${currentMonth}&year=${currentYear}`,
      //   ParamMap: {
      //     iotId: LetDevice.deviceID,
      //     month: new Date().getMonth() + 1,
      //   },
      ParamMap: {},
      Method: 'GET',
    };

    const data = await LetIMIIotRequest.sendUserServerRequest(params).catch(e => {
      console.log(JSON.stringify(e), 'getCurrentMonthEventDays', params);
    });
    if (data) {
      const dateTime = {...this.state.dateTimeDotSelect};

      customJsonParse(data)?.forEach(item => {
        const key = currentYear + '-' + assemblyDate(currentMonth) + '-' + assemblyDate(item);
        dateTime[key] = {
          marked: true,
          selectedColor: imiThemeManager.theme.primaryColor,
        };
      });
      //今天的状态
      const today =
        new Date().getFullYear() + '-' + assemblyDate(this.selectedMonth) + '-' + assemblyDate(new Date().getDate());
      // const todayStatus = dateTime[today];
      // if (todayStatus) {
      //   todayStatus.selected = true;
      // } else {
      //   dateTime[today] = {
      //     selected: true,
      //     selectedColor: imiThemeManager.theme.primaryColor,
      //   };
      // }
      if (!month) {
        this.selectedDay = today;
      }
      if (dateTime[this.selectedDay]) {
        dateTime[this.selectedDay].selected = true;
      } else {
        dateTime[this.selectedDay] = {
          selected: true,
        };
      }

      this.setState({dateTimeDotSelect: dateTime});
    }
  }

  doHandleMonth(month) {
    var m = month;
    if (month.toString().length == 1) {
      m = '0' + month;
    }
    return m;
  }

  render() {
    // console.log(TAG + "render  - > props", this.props);
    // console.log(TAG + "render  - > state", this.state);
    let {showHideSelect} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    // let minDay = this.getDay(this.props.vipState == 1 ? -this.props.lifecycleDay : -30); //显示30天的
    let minDay = this.getDay(-30); //显示30天的
    // console.log('前3天日期---',minDay);
    let m = DateUtils.doHandleMonth();
    let d = DateUtils.doHandleDay2();
    let dayTitle = stringsTo('day') != '日' ? m + '/' + d : `${m}${stringsTo('month')}${d}${stringsTo('day')}`;

    // 检测是否为双摄设备
    const cameraNumber = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1';
    const isDualCamera = cameraNumber === '2';

    // 构建初始化数据，顺序要与AlarmTopSelectBar中data数组保持一致
    let initData = [dayTitle]; // 第一个：时间筛选

    if (isDualCamera) {
      initData.push(stringsTo('dual_camera_view')); // 第二个：摄像头类型筛选（默认选中双摄画面）
    }

    initData.push(stringsTo('all_events_str')); // 最后一个：事件筛选

    console.log('initData yyh: ', this.props.eventCurrentTitles);

    return (
      <XView raw={true} style={styles.container}>
        <AlarmTopSelectBar
          initData={initData}
          dateTimeDotSelect={this.state.dateTimeDotSelect}
          minDate={minDay}
          // minDate={timeStr}
          showTodayBackGroundColor={true}
          isListLoading={!this.state.loadEnd}
          eventCurrentTitles={this.props.eventCurrentTitles ? this.props.eventCurrentTitles : defaultEventCurrentTitles}
          onEventItemPress={(currentTitles, index) => {
            console.log('-------------onEventItemPress-------------------' + currentTitles[index][1]);

            let typeParams = aliAlarmEventCloudApi._eventType2Info(currentTitles[index][1]);
            this.reqIntelligentTypeArray = typeParams ? [typeParams] : [];
            console.log('-------------reqIntelligentTypeArray-------------------' + typeParams);
            this.setState({eventCurrentIndex: index}, () => {
              this.keepLoad = false;
              this._getAlarmListData();
            });
          }}
          hideTitleSelect={showHideSelect ? this.props.isEdit : false}
          // hideTitleSelect={this.props.isEdit}
          onDayPress={date => {
            console.log('---------------onDayPress-----------------' + date.toString());
            if (this.state.dateTimeDotSelect && this.state.dateTimeDotSelect[this.selectedDay]) {
              this.state.dateTimeDotSelect[this.selectedDay].selected = false;
            }
            const todayStatus = this.state.dateTimeDotSelect && this.state.dateTimeDotSelect[date.toString()];
            if (todayStatus) {
              todayStatus.selected = true;
            } else {
              this.state.dateTimeDotSelect[date.toString()] = {
                selected: true,
              };
            }
            this.selectedDay = date.toString();
            this.setState({dateTimeDotSelect: {...this.state.dateTimeDotSelect}});
            this.reqTime = date;

            this._getAlarmListData();
          }}
          onVisibleMonthsChange={data => {
            if (data && data[0]) {
              this.getCurrentMonthEventDays(data[0].month, data[0].year);
            }
          }}
          onCameraTypePress={(cameraType, index) => {
            // TODO: 实现摄像头类型筛选逻辑
            console.log('摄像头类型筛选回调 - 需要实现数据筛选逻辑:', cameraType, index);
            // 这里可以根据选择的摄像头类型来筛选视频数据
            // 例如：
            // - index 0: 双摄画面 (显示所有视频)
            // - index 1: 固定画面 (只显示枪机视频)
            // - index 2: 云台画面 (只显示球机视频)

            // 重新获取数据
            // this.setState({cameraTypeIndex: index}, () => {
            //   this.keepLoad = false;
            //   this._getAlarmListData();
            // });
          }}
          renderView={this._renderView.bind(this)}
        />
        <MessageDialog
          title={stringsTo('delete_alert')}
          visible={this.state.showDeleteTip}
          canDismiss={true}
          onDismiss={() => {
            this.setState({showDeleteTip: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelDeleteAlert',
              callback: _ => {
                this.setState({showDeleteTip: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okDeleteAlert',
              callback: _ => {
                if (this.isListLoading) {
                  showToast('加载中不能删除');
                  return;
                }
                showLoading(stringsTo('delete_title_loading'), true);
                this.setState({showDeleteTip: false});
                this.deletImageArray(0);
              },
            },
          ]}
        />
      </XView>
    );
  }

  /**
   * 获取当前数据           **  可以进行子类重写 **
   * @param isPullDown
   * @private
   */
  _queryDataList = isPullDown => {
    //每次重新加载的时候把持续加载制为false
    if (isPullDown) {
      this.keepLoad = false;
    }
    this.refreshList && this.refreshList.refreshPreLoad(isPullDown);
    let paramsToken = isPullDown ? '' : this.reqToken;
    isPullDown && this.checkVipState()

    this._changeEventTime(this.reqTime, paramsToken, this.reqIntelligentTypeArray, isPullDown);
  };

  // 检查是否有云存
  checkVipState() {
    console.log('查看云存状态')
      imiAlarmEventCloudApi
        .getVipState(LetDevice.deviceID)
        .then(res => {
          let data = JSON.parse(res);
          this.setState({vipState: data.status});
        })
        .catch(error => {
          console.log(error)
        });
    }

  /**
   *  切换事件的时间点，
   * @param time  格式化过后的 yyy/mm/dd 格式时间
   * @param paramsToken
   * @param intelligentTypeArray 需要查询的事件类型
   * @param isPullDown
   * @private
   */
  _changeEventTime(time, paramsToken, intelligentTypeArray, isPullDown) {
    if (this.isListLoading) {
      console.log('加载中不再加载')
      return; // 防止重复加载
    }
    let dataList = this.state.dataList;
    //根据选择类型获取数据，获取后不需要额外处理
    let curStartTime = DateUtils.startUnix(time);
    let curEndTime = this.keepLoad ? this.state.timeEnd : DateUtils.endUnix(time);
    // 因为获取视频的接口和根据事件获取视频的接口不一样，所以要增加对应的区分
    // eventCurrentIndex===0说明是所有的事件
    this.isListLoading = true;
    this.setState({
      loadEnd: false
    })
    const eventType = this.props.eventCurrentTitles[this.state.eventCurrentIndex];
    aliAlarmEventCloudApi.getEventDataListByParams(LetDevice.deviceID, curStartTime, curEndTime, EVENT_LIST_LIMIT, '', '-1')
      .then(res => {
        console.log("yyhdata111 :" + JSON.parse(res))
      })
      .catch(error => {
        console.log('yyhdata111 error1111', JSON.stringify(error));
      });
    const requestList =
      this.state.eventCurrentIndex !== 0
        ? aliAlarmEventCloudApi.getEventList(
            LetDevice.deviceID,
            curStartTime,
            curEndTime,
            EVENT_LIST_LIMIT,
            eventType?.eventType,
          )
        : aliAlarmEventCloudApi.getEventList(LetDevice.deviceID, curStartTime, curEndTime, EVENT_LIST_LIMIT, '');
    console.log(
      ' eventStorageSessions ' +
        this.state.eventCurrentIndex +
        '---' +
        ' curStartTime ' +
        curStartTime +
        ' curEndTime ' +
        curEndTime +
        ' time  ' +
        time +
        ' intelligentTypeArray ' +
        intelligentTypeArray,
      'keepLoad ' + this.keepLoad,
      'eventType' + eventType?.eventType,
      LetDevice.deviceID,
    );

    const time2 = new Date().getTime();
    requestList
      .then(data => {
        console.log(
          '--------------',
          new Date().getTime() - time2,
          LetDevice.deviceID,
          curStartTime,
          curEndTime,
          EVENT_LIST_LIMIT,
        );
        IMILog.logD(
          '看家时间',
          'params' +
            JSON.stringify({
              time1: time2,
              time2: new Date().getTime(),
              timeGap: new Date().getTime() - time2,
              deviceID: LetDevice.deviceID,
              curStartTime,
              curEndTime,
              EVENT_LIST_LIMIT,
            }),
        );
        // console.log('开始的数据 ' + '-----', data);
        let result = JSON.parse(data);
        //isContinue 代表是否有更多的数据

        let {cloudStoragePlayList, isContinue, cloudStorageEventList} = result;
        //console.log('getAliPictureList  then-> json ' + '-----' + JSON.stringify(cloudStoragePlayList[0]));
        if (!cloudStoragePlayList && cloudStorageEventList) {
          // 全部事件和单一事件的取值不一样
          const list = [];
          cloudStorageEventList?.forEach(item => {
            const imgSignedDownloadUrl = {
              imgSignedDownloadUrl: item?.imgSignedDownloadUrl,
              publicKeyVersion: item?.publicKeyVersion,
              segmentId: item?.segmentId,
              imgPath: item?.imgSignedDownloadUrl,
            };
            item.imgSignedDownloadUrl = imgSignedDownloadUrl;
            list.push(item);
          });
          cloudStoragePlayList = list;
        }

        // this.reqToken = token;

        let isSuccess = true;
        let isDataEmpty = !isContinue; //!objHasKey(pictureList);

        //如果此时没有数据，不认为没有更多数据
        console.log('getAliPictureList  dataList  isDataEmpty   -> ' + isDataEmpty + '----', isContinue);
        /*  if (event != 0) {
          //筛选数组
          cloudStoragePlayList = cloudStoragePlayList.filter(item => item.eventType == event);
        } */

        //如果是下拉刷新，并且不是继续加载数据
        let itemList = isPullDown && !this.keepLoad ? cloudStoragePlayList : [...dataList, ...cloudStoragePlayList];
        // console.log(
        //   'isPullDown',
        //   isPullDown,
        //   'keepLoad',
        //   this.keepLoad,
        //   dataList.length,
        //   cloudStoragePlayList.length,
        //   itemList.length,
        // );

        /* //去重
            let resultList = [];
            let obj = {};
            for (let i = 0; i < itemList.length; i++) {
                if (!obj[itemList[i].eventId]) {
                        resultList.push(itemList[i]);
                        obj[itemList[i].eventId] = true;
                }
            }
            itemList = resultList;
            console.log('eventStorageSessions  dataList  itemList.length   -> ' + itemList.length );
            /!**
             * 判断第一加载为都为无效数据,并且还有数据加载
             *!/
            if (itemList.length==0&&nextValid&&isPullDown){
                console.log('eventStorageSessions  dataList  nextValid   -> ' + isPullDown );
                let page =this.state.pageStart+1;
                this.setState({pageStart:this.state.pageStart+1});
                let paramsToken = isPullDown ? "" : this.reqToken;
                this._changeEventTime(this.reqTime, paramsToken, this.reqIntelligentTypeArray, isPullDown,page);
            }
	    //返回都是无效数据处理
            if (this.state.dataList.length>0&&itemList.length==this.state.dataList.length){  //数据是否相等
                if (nextValid == true&&isPullDown!=true){ //判断是否加载完毕
                    console.log('eventStorageSessions  dataList  isPullDown   -> ' + isPullDown );
                    let page =this.state.pageStart+1;
                    this.setState({pageStart:this.state.pageStart+1});
                    let paramsToken = isPullDown ? "" : this.reqToken;
                    this._changeEventTime(this.reqTime, paramsToken, this.reqIntelligentTypeArray, isPullDown,page);
                }
            }*/
        console.log('getAliFileList  dataList.length:', itemList.length, this.state.dataList.length);
        console.log("getAlarmListData：" + JSON.stringify(itemList))
        this.setState(
          {
            dataList: itemList,
            loadEnd: true,
          },
          () => {
            if (this.props.isSelectedAll) {
              if (isGoBack == false) {
                if (this.props.onClickSelect) {
                  let indexAry = [];
                  if (this.props.isSelectedAll) {
                    for (let m = 0; m < this.state.dataList.length; m++) {
                      indexAry.push(m);
                    }
                  }
                  isGoBack = true;
                  // console.log('isGoBack---------',indexAry);
                  // console.log('累加了',indexAry);
                  this.props.onClickSelect(undefined, indexAry);
                }
              }
            } else {
              isGoBack = false;
            }
            this.keepLoad = isContinue;

            if (isDataEmpty) {
              //isDataEmpty加载完毕
              if (isDataEmpty && isPullDown) {
                this.refreshList && this.refreshList.refreshLoaded(isSuccess, false, cloudStoragePlayList.length > 0 ? isDataEmpty : false, false);
                return;
              }
              this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown, isDataEmpty, false);
              return;
            } else {
              // 如果还有数据，那么记录下每次查询结果的最后一条数据的时间,isContinue为true
              //
              console.log('isContinue为true', cloudStoragePlayList[cloudStoragePlayList.length - 1]?.segmentStartTime);
              if (cloudStoragePlayList?.length && cloudStoragePlayList[cloudStoragePlayList.length - 1]) {
                const timeEnd =
                  this.state.eventCurrentIndex !== 0
                    ? cloudStoragePlayList[cloudStoragePlayList.length - 1]?.eventStartTime - 1
                    : cloudStoragePlayList[cloudStoragePlayList.length - 1]?.eventStartTime - 1;
                this.setState({timeEnd});
              }
            }

            this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown, isDataEmpty, false);
          },
        );
        if (this.props.getDataLength) {
          this.props.getDataLength(this.state.dataList.length);
        }
      })
      .catch(error => {
        console.log('error:1', JSON.stringify(error));
        // this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown, token === undefined, netWorkException(error));
        let isSuccess = false;
        this.refreshList && this.refreshList.refreshLoaded(isSuccess, isPullDown);
        this.setState({
          dataList: [],
          loadEnd: true,
        });
        if (this.props.getDataLength) {
          this.props.getDataLength(this.state.dataList.length);
        }
      })
      .finally(() => {
        this.setState({
          loadEnd: true
        }, () => {
          setTimeout(() => {
            this.isListLoading = false;
          }, 100)
        })
      });
  }

  _renderView() {
    console.log(' _renderView  fullScreen ' + this.state.fullScreen);

    return (
      <View style={{flex: 1}}>
        {/*渲染播放列表*/}
        {/*{!this.state.fullScreen ? this._renderListView() : null}*/}
        {this.state.vipState !== 1 && this.state.vipState !== -1 && !IMIPackage.closeCloudStorage &&  !LetDevice.isShareUser && this._renderCloud()}
        {this._renderListView()}
        {/*编辑底部*/}
        {this._renderEditView()}
      </View>
    );
  }

  // _renderPlayerVideoViewComponents() {
  //     return (
  //         <View>
  //
  //             {!this.state.fullScreen ? <RNLine/> : null}
  //
  //             {this.getPlayerVideoView()}
  //
  //             {!this.state.fullScreen ? this._renderVideoToolBar() : null}
  //
  //             {!this.state.fullScreen ? <RNLine/> : null}
  //
  //         </View>
  //     )
  // }

  // getPlayerVideoView() {
  //
  //     /** ! 此处暂时备注 这样写法有风险，猜测：多个地方引用会导致共享变量 !*/
  //     CoverVideoImage.defaultProps = {
  //         videoCover: this.state.videoCover
  //     };
  //
  //     return (
  //         <VideoPlayerContainer
  //             ref={ref => this.videoPlayer = ref}
  //             muted={this.state.muted}
  //             PlayerElement={RNVideoImiCloudEx}
  //             isPlaying={this.state.isPlaying}
  //             dataSource={this.state.dataSource}
  //             cover={[CoveLoading, CoverVideoImage, CoverVideoToolProgress, CoveError]}
  //             videoCover={this.state.videoCover}
  //             onEventChange={this.doHandleOnEventChange.bind(this)}
  //             onCompletion={() => {
  //                 showToast('播放完成');
  //             }}
  //             onUpdateOrientation={(orientation) => {
  //                 this.setState({fullScreen: orientation !== 'PORTRAIT'});
  //                 this.props.onClickFullScreen && this.props.onClickFullScreen(orientation !== 'PORTRAIT');
  //             }
  //          }/>
  //     )
  // }

  onBackAndroid() {
    if (this.state.fullScreen) {
      this._toggleOrientation(!this.state.fullScreen);
      return true;
    }
    return super.onBackAndroid();
  }

  /**
   * 视频功能工具栏
   * @private
   */
  //   _renderVideoToolBar() {
  //     console.log('    _renderVideoToolBar() ' + JSON.stringify(this.state.isPlaying));

  //     return (
  //       <View
  //         style={{
  //           backgroundColor: colors.white,
  //           flexDirection: 'row',
  //           paddingVertical: 10,
  //           justifyContent: 'space-between',
  //           paddingHorizontal: 15,
  //         }}>
  //         <CheckBoxButton
  //           checked={this.state.isPlaying}
  //           buttonStyle={styles.videoToolBtn}
  //           buttonImage={[iconPlay, iconPause]}
  //           onValueChange={checkValue => {
  //             console.log('CheckBoxButton 暂停/播放  checkValue  ' + +checkValue);
  //             if (checkValue && this.mCurItem == null) {
  //               this.mCurItem == null && (this.mCurItem = this.state.dataList.length > 0 ? this.state.dataList[0] : null);
  //               this.mCurIndex = 0;
  //               this._onItemPress(this.mCurItem, this.mCurIndex);
  //             } else {
  //               this.videoPlayer.togglePlayPause();
  //             }
  //           }}
  //         />

  //         <CheckBoxButton
  //           buttonStyle={styles.videoToolBtn}
  //           buttonImage={[iconVoiceOpen, iconVoiceClose]}
  //           onValueChange={checkValue => {
  //             this.videoPlayer.muted(checkValue);
  //           }}
  //         />

  //         <CheckBoxButton
  //           buttonStyle={styles.videoToolBtn}
  //           buttonImage={[iconSave]}
  //           onValueChange={() => {
  //             this._saveVideoToLocal();
  //           }}
  //         />

  //         <CheckBoxButton
  //           buttonStyle={styles.videoToolBtn}
  //           buttonImage={[iconFull]}
  //           onValueChange={checkValue => {
  //             this._toggleOrientation(checkValue);
  //           }}
  //         />
  //       </View>
  //     );
  //   }

  _toggleOrientation(fullScreen) {
    this.videoPlayer.toggleOrientation();

    console.log('_toggleOrientation   ' + this.state.fullScreen + ' checkValue ' + fullScreen);
    this.setState({fullScreen: fullScreen});
    this.props.onClickFullScreen && this.props.onClickFullScreen(fullScreen);
  }

  // 开通云存
  _renderCloud() {
    return (
      <XView style={{
        backgroundColor: imiThemeManager.theme.primaryColor+'1A',
        marginLeft: 20, 
        marginRight: 20,
        borderRadius: 10,
        flexDirection: 'row', 
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingLeft: 20,
        paddingRight: 10
      }} onPress={() => {
        IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID,"link://feedback/pages/toCloudBuyBrowserPage");
      }}>
        <Text style={{color: imiThemeManager.theme.primaryColor,textAlign: 'center', paddingTop: 14, paddingBottom: 14}}>{stringsTo('cloudTip')}</Text>
        <Image style={{width: 26, height: 26}}  source={require('../../resources/images/right_arrow_cloud.png')}/>
      </XView>
    )
  }
  /**
   * 事件展示列表
   * @returns {*}
   * @private
   */
  _renderListView() {
    let dataList = this.state.dataList;
    // console.log(" _renderListView dataList  -> " + JSON.stringify(dataList));
    return (
      <XFlatList
        raw={true}
        style={{
          backgroundColor: colors.page_bg,
          marginTop: 10
        }}
        data={dataList}
        onRefresh={() => this._queryDataList(true)}
        onLoadMore={() => {
          console.log(' this._queryDataList(false)', this.state.timeEnd);
          IMILog.logI('加载更多', JSON.stringify({isListLoading: this.isListLoading, loadEnd: this.state.loadEnd}));
          if (this.isListLoading) {
            return; // 防止重复加载
          }
          this._queryDataList(false);
        }}
        refreshStatus={{
          // RefreshingData: {text: I18n.t('commLoadingText')},
          // NoData: {text: I18n.t('housekeeping_no_event')},
          // LoadingMoreData: {text: I18n.t('commLoadingMoreDataText')},
          // LoadFailure: {text: I18n.t('commLoadingFailText')},
          // LoadMoreFailure: {text: I18n.t('commLoadingClickText')},
          PreLoading: {text: I18n.t('housekeeping_no_event')}, //不传值默认是first loading，在当天视频为空且续费成功回来后必现
          RefreshingData: {text: I18n.t('commLoadingText')},
          NoData: {text: I18n.t('housekeeping_no_event')},
          LoadFailure: {text: I18n.t('commLoadingFailText')},
          //列表底部提示文案
          LoadingMoreData: {moreText: I18n.t('commLoadingMoreDataText')},
          NoMoreData: {moreText: I18n.t('x_flat_list_no_more_data')},
          LoadMoreFailure: {moreText: I18n.t('commLoadingClickText')},
          NetException: {moreText: I18n.t('commLoadingFailText')},
        }}
        onEndReachedThreshold={0.1}
        numColumns={1}
        renderEmptyViewFunc={(status, isEmpty) =>
          LetDevice.model == 'a1Godgpvr3D'
            ? this._renderEmptyNewView(status, isEmpty)
            : this._renderEmptyView(status, isEmpty)
        }
        onLayout={e => {
          let height = e.nativeEvent.layout.height;
          if (this.state.flatListHeight < height) {
            this.setState({flatListHeight: height});
          }
        }}
        ref={refreshList => (this.refreshList = refreshList)}
        renderItem={({item, index}) => this._renderItem(item, index)}
      />
    );
  }

  _renderEmptyView(status, isEmpty) {
    console.log(' _renderEmptyView  isEmpty' + isEmpty + ' status ' + JSON.stringify(status));
    let icon = isEmpty ? iconEmpty : null;
    return (
      // <IMIDesignEmptyView rootStyle={{height: this.state.flatListHeight}} defaultIcon={icon}
      //                     defaultText={status.text} />
      this.isListLoading ? null : <IMIDesignEmptyViewNew
        rootStyle={{height: this.state.flatListHeight}}
        defaultIcon={icon}
        defaultText={stringsTo('housekeeping_no_event')}
      />
    );
  }
  _renderEmptyNewView(status, isEmpty) {
    let icon = isEmpty ? iconEmptyNew : null;
    return (
      this.isListLoading ? null : <IMIDesignEmptyView
        rootStyle={{height: this.state.flatListHeight}}
        defaultIcon={icon}
        defaultText={status.text}
      />
    );
  }

  _assignTopSelectBarRoot = component => {
    this.topSelectBarRoot = component;
  };

  /**
   * 获取看护列表数据
   * @private
   */
  _getAlarmListData() {
    this.keepLoad = false;
    this.itemClick = false;
    this._queryDataList(true);
  }

  resetStatus() {
    this.itemClick = false;
  }
  /**
   * 绘制item view    **  可以进行子类重写 **
   * @param item
   * @param index
   * @returns {*}
   * @private
   */
  _renderItem = (item, index) => {
    //console.log('_renderItem' + JSON.stringify(item));

    // 检测是否为双摄设备
    const cameraNumber = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1';
    const isDualCamera = cameraNumber === '2';

    let {displayEventType, segmentStartTime, eventStartTime} = item;
    //console.log('当前imgUrl---',eventPicThumbUrl);
    let isSelected = false;
    if (this.props.dataArray.length > 0) {
      isGoBack = false;
      for (let m = 0; m < this.props.dataArray.length; m++) {
        // console.log('index-----------',index,this.props.dataArray[m]);
        if (index == this.props.dataArray[m]) {
          // console.log('index-----------',index,this.props.dataArray[m]);
          isSelected = true;
        }
      }
    } else {
      if (this.props.isSelectedAll) {
        if (isGoBack == false) {
          if (this.props.onClickSelect) {
            let indexAry = [];
            if (this.props.isSelectedAll) {
              for (let m = 0; m < this.state.dataList.length; m++) {
                indexAry.push(m);
              }
            }
            isGoBack = true;
            console.log('isGoBack---------', indexAry);
            this.props.onClickSelect(undefined, indexAry);
          }
        }
      } else {
        isGoBack = false;
      }

      isSelected = this.props.isSelectedAll;
    }
    // let formatStarTime = dateFormat(new Date(startTime), 'mm:ss');
    // console.log('_renderItem isSelected' + item.isSelected,item);

    // let formatStarTime = eventTime.split(" ");
    let time = moment(segmentStartTime || eventStartTime).format('YYYY-MM-DD HH:mm:ss');
    const formatStarTime = time.substr(11);
    const iconDotMove = require('../../resources/images/pic_move.png');
    const iconDotPeople = require('../../resources/images/pic_person.png');
    const iconDotSound = require('../../resources/images/pic_sound.png');

    const iconDotFenceEnter = require('../../resources/images/pic_enter_fence.png');
    const iconDotFenceLeave = require('../../resources/images/pic_leave_fence.png');
    const iconDotVehicleDetect = require('../../resources/images/pic_detect_vehicle.png');
    const iconDotApproachVehicle = require('../../resources/images/pic_approach_vehicle.png');
    const iconDotBikeDetect = require('../../resources/images/pic_detect_bike.png');
    const iconDotApproachBike = require('../../resources/images/pic_approach_bike.png');
    const eventTypeList = displayEventType && displayEventType.split(',');
    const iconDot = {
      ObjectMotion: iconDotMove,
      PeopleMotion: iconDotPeople,
      AbnormalSound: iconDotSound,
      FenceIn: iconDotFenceEnter,
      FenceOut: iconDotFenceLeave,
      VEHICLE_DETECT: iconDotVehicleDetect,
      VEHICLE_Approach: iconDotApproachVehicle,
      BIKE_DETECT: iconDotBikeDetect,
      BIKE_Approach: iconDotApproachBike,
    };
    const textList = {
      ObjectMotion: '画面变动', // 修改文案：移动侦测 -> 画面变动
      PeopleMotion: '有人移动', // 修改文案：人形侦测 -> 有人移动
      AbnormalSound: stringsTo('soundEvent'),
      FenceIn: '有人进入围栏',
      FenceOut: '有人离开围栏',
      VEHICLE_DETECT: '车辆检测',
      VEHICLE_Approach: '车辆检测',
      BIKE_DETECT: '非机动车检测',
      BIKE_Approach: '非机动车检测',
    };
    const text =
      Array.isArray(eventTypeList) &&
      eventTypeList.map(res => {
        return textList[res];
      });
    return (
      <XView
        key={item.segmentId+time}
        style={styles.cardList}
        onPress={this._onItemPress.bind(this, item, index)}
        accessibilityLabel={'housekeeping_item_view_' + time}>
        <View>
          {Array.isArray(eventTypeList) && eventTypeList.map((item,index) => {
            return <Image style={{width: 18, height: 18, marginRight: 8, marginBottom: (eventTypeList.length === 1 || index == eventTypeList.length - 1) ? 0 : 2}} source={iconDot[eventTypeList[index]]} />
          })}
        </View>
        <View style={styles.leftComponent}>
          {Array.isArray(text) && text.map(res => {
            return <Text style={styles.itemTitle}>{res}</Text>
          })}
        </View>
        <View style={styles.leftComponent}>
          <Text style={styles.itemTime}>{formatStarTime}</Text>
        </View>
        <View style={{position: 'relative'}}>
          <PathImage key={item.segmentId+time} imgSignedDownloadUrl={item.imgSignedDownloadUrl} deviceID={LetDevice.deviceID} />
          {/* 双摄设备显示摄像头类型图标 */}
          {isDualCamera && item.lensNumber !== undefined && (
            <Image
              style={{
                position: 'absolute',
                bottom: 0,
                right: 0,
                width: 22,
                height: 22
              }}
              source={
                item.lensNumber === 0
                  ? require('../../resources/images/ic_bolt_camera.png')
                  : item.lensNumber === 1
                  ? require('../../resources/images/ic_ptz_camera.png')
                  : null
              }
            />
          )}
        </View>
      </XView>
    );
  };

  _renderEditView() {
    if (this.props.isEdit) {
      // return(<XView style={{position: "absolute",bottom:14,left:14,right:14,height:64,borderRadius:10,backgroundColor:'#496EE0', alignItems: 'center',justifyContent: 'center',flexDirection: 'row'}}>
      return (
        <View style={{bottom: 0, height: 64, width: '100%', backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
          <View style={{height: 2, backgroundColor: '#F1F1F1'}} />
          <View
            style={{
              height: 62,
              width: '100%',
              backgroundColor: '#FFFFFF',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            {/*<XText style={{fontSize:12,color:'#333333'}} iconSize={30} icon={require('../../resources/images/icon_alarm_down.png')} text={stringsTo('downLoadTitle')} onPress={()=>{*/}
            {/*    if (this.props.dataArray.length>0){*/}

            {/*        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {*/}
            {/*            if (status === 0) {*/}
            {/*                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {*/}
            {/*                    if (status2 === 0) {*/}

            {/*                        let dataAry = [];*/}
            {/*                        for (let m = 0; m < this.props.dataArray.length;m ++){*/}
            {/*                            let data = this.props.dataArray[m];*/}
            {/*                            let item = this.state.dataList[data];*/}
            {/*                            dataAry.push(item);*/}
            {/*                        }*/}
            {/*                        showLoading(stringsTo('alarm_download_downloading'),true);*/}
            {/*                        this.props.onClickCancel&&this.props.onClickCancel(false);*/}
            {/*                        this.downloadImageArray(0,dataAry);*/}
            {/*                    } else if (status2 === -1) {*/}
            {/*                        showToast(stringsTo('storage_permission_denied'));*/}
            {/*                    }*/}
            {/*                });*/}
            {/*            } else if (status === -1) {*/}
            {/*                showToast(stringsTo('storage_permission_denied'))*/}
            {/*            }*/}
            {/*        })*/}

            {/*    }else {*/}
            {/*        showToast(stringsTo('select_tip'));*/}
            {/*    }*/}

            {/*}}*/}
            {/* accessibilityLabel={"housekeeping_download"}*/}
            {/*/>*/}

            {/*<XText raw={true} style={{fontSize:12,color:'#E74D4D'}} iconSize={30} icon={require('../../resources/images/play_back_delete.png')} text={stringsTo('delete_title')} */}
            {/*       onPress={()=>{*/}
            {/*    if (this.props.dataArray.length>0){*/}
            {/*        // opacity:LetDevice.isShareUser?0.2:1,*/}
            {/*        // if (LetDevice.isShareUser){*/}
            {/*        //     showToast(stringsTo('shareUser_tip'));*/}
            {/*        //     return;*/}
            {/*        // }*/}
            {/*        this.setState({showDeleteTip: true});*/}
            {/*    }else{*/}
            {/*        showToast(stringsTo('select_tip'));*/}
            {/*    }*/}
            {/*}}*/}
            {/*  accessibilityLabel={"housekeeping_delete"}*/}
            {/*/>*/}

            <TouchableOpacity
              onPress={() => {
                if (this.props.dataArray.length > 0) {
                  // opacity:LetDevice.isShareUser?0.2:1,
                  // if (LetDevice.isShareUser){
                  //     showToast(stringsTo('shareUser_tip'));
                  //     return;
                  // }
                  this.setState({showDeleteTip: true});
                } else {
                  showToast(stringsTo('select_tip'));
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center'}}
              accessibilityLabel={'housekeeping_delete'}>
              <Image style={{width: 30, height: 30}} source={require('../../resources/images/play_back_delete.png')} />
              <Text
                style={{fontSize: 16, color: '#E74D4D'}} //设计稿改动
              >
                {stringsTo('delete_title')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  }

  deletImageArray(page) {
    const dataAry = [];
    /*   let tempLen = (page + 1) * LIMIT;
    let length = tempLen >= this.props.dataArray.length ? this.props.dataArray.length : tempLen;
    for (let m = page * LIMIT; m < length; m++) {
      let data = this.props.dataArray[m];
      let item = this.state.dataList[data];
      dataAry.push(item.pictureId);
    } */
    const map = new Map();
    this.props.dataArray?.forEach(element => {
      const segmentId = this.state.dataList[element]?.segmentId;
      if (!map.has(segmentId)) {
        dataAry.push(segmentId);
      }
      map.set(segmentId, segmentId);
    });

    aliAlarmEventCloudApi
      .deleteFileBySegment(LetDevice.deviceID, dataAry)
      .then(data => {
        showLoading(stringsTo('delete_title_loading'), false);
        showToast(stringsTo('delete_success'));
        this.props.onClickCancel && this.props.onClickCancel(false);
        this._getAlarmListData();
        console.log('deleteAliCloudImage-----', data);
      })
      .catch(error => {
        showLoading(stringsTo('delete_title_loading'), false);
        showToast(stringsTo('delete_failed'));
        this.props.onClickCancel && this.props.onClickCancel(false);
        this._getAlarmListData();
        console.log('deleteAliCloudImage--error---', JSON.stringify(error));
      });
  }

  downloadImageArray(page, list) {
    this.downloadFile(page, list)
      .then((start, data) => {
        if (start == list.length - 1) {
          showLoading(stringsTo('alarm_download_downloading'), false);
          showToast(stringsTo('save_success'));
        } else {
          console.log('start--', start);
          this.downloadImageArray(start + 1, list);
        }
      })
      .catch((start, error) => {
        if (start == list.length - 1) {
          console.log('失败了走这里');
          showLoading(stringsTo('alarm_download_downloading'), false);
          showToast(stringsTo('save_success'));
        } else {
          console.log('失败了走这里下载其他的');
          this.downloadImageArray(start + 1, list);
        }
      });
  }

  downloadFile(downStart, dataList) {
    console.log(TAG + 'start downloadFile  ===================  ');
    return new Promise((resolve, reject) => {
      let item = dataList[downStart];
      let {pictureTime, pictureUrl} = item;
      let fileName = `${pictureTime}.jpg`;

      IMIDownload.downloadToPath(EVENT_NAME, pictureUrl, LetDevice.deviceID, fileName);

      this.listener = DeviceEventEmitter.addListener(EVENT_NAME, event => {
        if (event.status === IMIDownload.STATUS_START) {
        }
        if (event.status === IMIDownload.STATUS_DOWNLOADING) {
        }

        if (event.status === IMIDownload.STATUS_ERROR) {
          reject(downStart, event.status); //IOS在错误时会一直显示加载

          // reject(EVENT_NAME + " download error mataInfo : " + mataInfo)
          //用过一次必须释放
          this.listener && this.listener.remove();
          this.listener = null;
        }
        if (event.status === IMIDownload.STATUS_CANCEL) {
          // reject(EVENT_NAME + " download cancel  mataInfo :" + mataInfo)
          //用过一次必须释放
          this.listener && this.listener.remove();
          this.listener = null;
        }

        if (event.status === IMIDownload.STATUS_SUCCESS) {
          const path = `${event.downloadPath}/${fileName}`;

          IMIFile.saveImageToPhotosAlbum(path, LetDevice.deviceID)
            .then(data => {
              resolve(downStart, data);
            })
            .catch(error => {
              reject(downStart, error);
            });
          //用过一次必须释放
          this.listener && this.listener.remove();
          this.listener = null;
        }
      });
    }).then((start, data) => {
      console.log(TAG + 'start downloadFile ++++++++++++++++ ', data);
      return start;
    });
  }

  /**
   * 事件选择监听按下
   * @param item
   * @param index
   */
  _onItemPress(item, index) {
    if (isEmpty(item)) {
      return;
    }
    if (this.itemClick) {
      return;
    }
    this.itemClick = true;

    if (!item.isExistVideo) {
      //  IMILog.logD("王 看家缩略图点击事件 _onItemPress: ",item.toString())
      this.props.navigation.push('AlbumPhotoViewPage', {
        item: item,
        vipState: this.props.vipState,
        refreshList: () => {
          // 处理回调数据
          this._getAlarmListData();
          const dateTimeDotSelect = this.state.dateTimeDotSelect;
          dateTimeDotSelect[this.selectedDay].marked = false
          this.setState({
            dateTimeDotSelect
          }, () => {
            this.getCurrentMonthEventDays(moment(this.selectedDay).format('MM'), moment(this.selectedDay).format('YYYY'))
          })
        },
      });
    } else {
      const list = [];
      Array.isArray(this.props.eventCurrentTitles) && this.props.eventCurrentTitles.map(res => {
        if (res.eventType == 'housekeeping_event_all') {
          list.push('')
        } else {
          list.push(res.eventType)
        }
      })
      this.props.navigation.push('AlbumVideoViewDual', {
        item: item,
        eventCurrentIndex: this.state.eventCurrentIndex,
        eventCurrentTitles: list,
        refreshList: () => {
          // 处理回调数据
          this._getAlarmListData();
          const dateTimeDotSelect = this.state.dateTimeDotSelect;
          dateTimeDotSelect[this.selectedDay].marked = false
          this.setState({
            dateTimeDotSelect
          }, () => {
            this.getCurrentMonthEventDays(moment(this.selectedDay).format('MM'), moment(this.selectedDay).format('YYYY'))
          })
        },
      });
    }
  }

  onSelectAllChanged(isSelectedAll) {
    let selectedCount = 0;
    for (let file of this.state.dataList) {
      file.isSelected = isSelectedAll;
      if (file.isSelected) {
        selectedCount++;
      }
    }
    this.num = selectedCount;
    this.setState({dataList: this.state.dataList}); // 刷新页面 状态不要保留在ui控件里
    if (this.props.onClickSelect) {
      let isSelect;
      if (this.num == 0) {
        isSelect = false;
      }
      if (this.num == this.state.dataList.length) {
        isSelect = true;
      }
      this.props.onClickSelect(this.num, isSelect);
    }
  }

  /**
   * 保存当前播放视频到本地相册中
   * @private
   */
  _saveVideoToLocal() {
    this.waitingSaveVideo = true;

    showLoading(stringsTo('commWaitText'), true);

    console.log('_saveVideoToLocal', this.mCurItemLocal);
    if (this.mCurItemLocal != null) {
      console.log('_saveVideoToLocal22222222222222222222', this.mCurItemLocal);
      // IMIVideoUtils.downloadToPath(pathList)
    }
  }

  /**
   * 处理当前播放器事件
   */
  doHandleOnEventChange(data) {
    console.log(TAG + 'doHandleOnEventChange ', data);

    if (!data.hasOwnProperty('extra')) {
      return;
    }

    let {extra = {}, code = undefined} = data;
    //如果缓存完毕
    if (code === PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFER_FINISH) {
      this.mCurItemLocal = extra;

      //判断 此处下载完成后 用户是否需要保存视频
      if (this.waitingSaveVideo) {
        this._saveVideoToLocal();
      }
    }
  }

  /***           系統函數   START    ****/

  componentWillUnmount() {
    super.componentWillUnmount();
    this._enterForeground && this._enterForeground.remove();
  }

  componentDidMount() {
    this.getCurrentMonthEventDays();
    // this._getAlarmListData();
    Orientation.lockToPortrait();
    this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
      this.checkVipState()
    })
  }

  /***           系統函數   END    ****/
}

const PathImage = ({imgSignedDownloadUrl, deviceID}) => {
  const [path, setPath] = useState('');

  //需要照顾数据格式的不同
  const {segmentId, publicKeyVersion, imgPath} = customJsonParse(imgSignedDownloadUrl);
  useEffect(() => {
    const getImageUrl = async () => {
      const data = await cloudDeviceService.downLoadImage(deviceID, segmentId, publicKeyVersion, imgPath).catch(e => {
        console.log(11, e);
      });
      if (data) {
        setPath(data);
      }
    };
    getImageUrl();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <IMIImageView style={styles.itemIcon} source={{uri: isAndroid() ? `file://${path}` : isEmpty(path) ? require('../../resources/images/play_back_comm_bg.png') :`file://${path}`}} />;
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    //position: 'absolute',
    backgroundColor: colors.page_bg,
  },

  itemParent: {
    // alignItems: 'center',
    paddingLeft: 14,
    paddingTop: 14,
    // flexDirection: 'row',
    backgroundColor: colors.white,
  },

  itemIcon: {
    borderRadius: 4,
    width: (windowWidth - 4 * 14) / 4,
    height: ((windowWidth - 4 * 14) / 4 / 16) * 9,
  },

  itemTitle: {
    fontSize: 12,
    lineHeight: 18,
    fontWeight: 'bold',
    color: colors.gray,
    // paddingLeft: 14
  },

  itemTime: {
    fontSize: 10,
    color: colors.gray,
    // marginTop: 2,
    fontWeight: 'bold',
  },
  itemDesc: {
    fontSize: 12,
    paddingLeft: 14,
    // marginTop: 10,
    color: colors.black,
  },
  videoToolBtn: {
    marginHorizontal: 15,
    width: 30,
    height: 30,
  },
  cardList: {
    flex: 1,
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    // height: 80,
    paddingTop: 12,
    paddingBottom: 12,
    paddingLeft: 16,
    paddingRight: 16,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  leftComponent: {
    flex: 1,
    justifyContent: 'center',
  },
});
