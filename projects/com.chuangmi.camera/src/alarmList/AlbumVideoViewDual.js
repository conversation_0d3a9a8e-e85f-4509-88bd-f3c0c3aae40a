import React, {useEffect, useState} from 'react';
import {StyleSheet, View, ActivityIndicator, Text, Dimensions, Image, BackHandler} from 'react-native';
import {XText, XView} from 'react-native-easy-app';
import IMCloudServerVideoView from '../../../../imilab-rn-sdk/native/camera-kit/IMCloudServerVideoView';
import IMICameraVideoView from '../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView';
import VideoProgressView from '../cloudStorage/videoProgressView/VideoProgressView';
import ImageButton from '../../../../imi-rn-commonView/ImageButton/ImageButton';
import PlayBackToolBarView from '../../../../imi-rn-commonView/PlayerToolBarView/PlayBackToolBarView';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import NetInfo from '@react-native-community/netinfo';
import IMIFile from '../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import IMIPermission from '../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import IMIToast from '../../../../imilab-design-ui/src/widgets/IMIToast';
import PlayBackFullScreenToolBarView from '../../../../imi-rn-commonView/PlayerToolBarView/PlayBackFullScreenToolBarView';
import {
  colors,
  IMIImageView,
  showLoading,
  showToast,
  MessageDialog,
  RoundedButtonView,
} from '../../../../imilab-design-ui';
import {AlarmType, aliAlarmEventCloudApi, LetDevice, LetIMIIotRequest, IMIGotoPage, GotoPageInNative, IMILog } from '../../../../imilab-rn-sdk';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import Orientation from 'react-native-orientation';
import {IMINativeLifeCycleEvent} from '../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {isAndroid, isIos} from '../../../../imilab-rn-sdk/utils/Utils';
import {cloudDeviceService} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDeviceCloudService';
import moment from 'moment';
import {ScrollView} from 'react-native-gesture-handler';
import {isEmpty} from '../../../../imilab-rn-sdk/utils/Utils';
const {width, height} = Dimensions.get('window');
let windowWidth = height > width ? width : height;
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
let isCheckingPermission = false;

let lists = [];

export default class AlbumVideoViewDual extends React.Component {
  constructor(props) {
    super(props);
    this.state = {
      currentSegmentId: this.props.route.params.item,
      switchVideoItemIng: false, // 正在切换视频中 ，切换中进度条不能变
      isLoading: true, //加载中,进入显示加载中
      showErrorView: false, //错误view
      showPauseView: false, //暂停view
      isPlayFinish: false,
      mute: true,
      isFullScreen: false,
      netConnected: true,
      dataList: [],
      showDeleteTip: false,
      eventCurrentIndex: this.props.route.params?.eventCurrentIndex || 0,
    };
    this.first = true;
    this.finish = false;
    this.downloadFlag = false;
    lists = this.props.route.params?.eventCurrentTitles
  }

  componentDidMount() {
    this._addListener();
    this._queryDataList();
    if (!this.props.navigation.canGoBack()) {
      GotoPageInNative.addStarNativeGoToPage(this.props.navigation);
    }
  }

  componentWillUnmount() {
    this.backHandler && this.backHandler.remove()
    if (!this.props.navigation.canGoBack()) {
      GotoPageInNative.removeStarNativeGoToPage();
    }
  }

  // 监听
  _addListener() {
    this.unsubscribe = NetInfo.addEventListener(state => {
      this.setState({
        netConnected: state.isConnected,
      });

      console.log('Is connected?', state.isConnected);
    });

    this.backHandler = BackHandler.addEventListener('hardwareBackPress', this._onPressBack);

    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      console.log('CloudStoragePage _subscribe_focus' + this.camera);
      this.isForegroundPage = true;
      console.log('进入当前云存页面全屏状态');
      // if (this.leaveCloudFull){
      // this.cloudStorgeComponent && this.cloudStorgeComponent.quitFullScreen();
      NavigationBar.setBarStyle('light-content');

      // setTimeout(()=>{
      //     console.log('延时1S退出全屏');
      //     this.cloudStorgeComponent&&this.cloudStorgeComponent.quitFullScreen();
      // },1500);

      // }
    });

    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      console.log('CloudStoragePage _subscribe_blur' + this.camera);
      this.isForegroundPage = false;
      this.IMIVideoView && this.IMIVideoView.stop();
      // this.leaveCloudFull = this.cloudStorgeComponent.getFullscreen();
      console.log('离开当前云存页面全屏状态');
    });
    this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(() => {
      // console.log("_onPauseListener")
      if (this.playStatus && !isCheckingPermission) {
        this.IMIVideoView && this.IMIVideoView.pause();
      }
    });

    this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      // console.log("_onResumeListener"+this.isFcous)
      NavigationBar.setBarStyle('light-content'); // 修改从云存购买界面返回状态栏显示黑色字体问题
      if (this.playStatus && !isCheckingPermission) {
        this.IMIVideoView && this.IMIVideoView.resume();
      }
    });

    this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
      if (this.state.isPlay && !isCheckingPermission) {
        this.setState({isPlay: false, showPauseView: true, isClickPause: true}, () => {
          this.IMIVideoView && this.IMIVideoView.pause();
        });
      }
    });
    //回到前台
    this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
      //启动
      console.log('addListener');
      // 只有不是初始化的的时候才能在进入播放时调用静音与否
      this.setState({mute: true});
    });
  }

  /**
   * 获取当前数据           **  可以进行子类重写 **
   * @param isPullDown
   * @private
   */
  _queryDataList = () => {
    const params = {
      Path: '/v1.0/imilab-01/app/cloudstorage/getEventListBySegmentid',
      ParamMap: {
        iotId: LetDevice.deviceID,
        segmentId: this.state.currentSegmentId.segmentId,
        productId: LetDevice.model
      },
      Method: 'GET',
    };
    if(this.state.eventCurrentIndex !== 0) {
      params['ParamMap']['eventType'] = lists[this.state.eventCurrentIndex]
    }
    console.log('yyhparams', params);
    showLoading(stringsTo('commLoadingText'), true);
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        let newLists = [];
        // 筛选由插件改为接口增加字段
        // if (this.state.eventCurrentIndex !== 0) {
        //   item.cloudStorageEventList.map(res => {
        //     const eventTypeList = res.displayEventType && res.displayEventType.split(',');
        //     if (eventTypeList.includes(lists[this.state.eventCurrentIndex])) {
        //       newLists.push(res);
        //     }
        //   });
        // } else {
        //   newLists = item.cloudStorageEventList;
        // }
        newLists = item.cloudStorageEventList;
        this.setState({
          dataList: newLists,
        });
      })
      .catch(e => {
        console.log(JSON.stringify(e));
      })
      .finally(() => {
        showLoading(false);
      });
  };

  // 监听事件
  _onEventChange = event => {
    if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
      //this.setState({bps: event.extra.bps})
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
      this.finish = false;
      if (this.first && this.state.currentSegmentId?.offsetDuration) {
        this.first = false;
        // this.setState({seekTo: Math.floor(this.state.currentSegmentId?.offsetDuration)});
        this.IMIVideoView && this.IMIVideoView.seekTo(Math.floor(this.state.currentSegmentId?.offsetDuration));
        return;
      }
      //console.log(event.extra.currentTime);
      // let scrollTime = currentPlayTime-1 >= event.extra.currentTime/1000.0 ? currentPlayTime : event.extra.currentTime/1000;

      if (this.state.isLoading) {
        // Iphone 12 bug: http://************:10083/issues/9393
        this.setState({
          isLoading: false,
          showPauseView: false,
          isPlay: true,
          isPlayFinish: false,
          showErrorView: false,
        });
      }
      if (this.state.noVideo) {
        this.progressView && this.progressView.onSliderValueChanged(0);
        this.state.homePictureTime = 0;
      } else {
        if (this.tempScrollPause) {
          //修改暂停逻辑 pause  需要resume后再seekto 防止回复在加载
          // 暂停状态下滑动时间轴.seekTo(Math.round(currentTime)*1000.0);
          console.log(Math.round(this.tempScrollTime * 1000));
          this.tempScrollPause = false;
          //this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime * 1000));
        } else {
          //202200302@byh ios最后返回的时间会比，视频时长少一秒，增加0.5修正，增加返回的视频时长与实际视频时长判断
          //Android不能添加,添加播放结束后可能在00：01秒位置
          let scrollTime = event.extra.currentTime;
          //20220318@byh 在播放完成后，监听播放完成，把进度拉满，所以这里不在需要修正
          // if (isIos()){
          //     scrollTime = scrollTime+0.5;
          // }
          let secondSTime = Math.round(scrollTime);
          let second = secondSTime > this.state.durationProgress ? this.state.durationProgress : secondSTime;

          if (second > 0) {
            this.progressView && this.progressView.onSliderValueChanged(second);
          }
        }
      }
    } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) {
      //调用video.start()后的第一个回调
      this.finish = false;
      this.setState({
        isLoading: this.state.netConnected ? true : false,
        isPlay: false,
        showPauseView: false,
        isPlayFinish: false,
        showErrorView: false,
      });
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
      IMILog.logI('获取时长', JSON.stringify(event));
      // 第一帧
      this.finish = false;
      this.setState({
        isLoading: false,
        showPauseView: false,
        isPlay: true,
        isPlayFinish: false,
        showErrorView: false,
        durationProgress: event?.extra?.duration && event.extra.duration,
      });
      if (this.selectBeginTime > 0) {
        //如果这个偏移时间比实际视频时长大，Android端是无法发生偏移的，需要这里做个修正
        if (this.state.durationProgress > 0 && this.selectBeginTime > this.state.durationProgress) {
          //如果大于进度时间，可能没找到视频，找了最近的播放，那么我们从头开始播放不进行偏移
          // this.selectBeginTime = this.state.durationProgress * 1000;
          this.selectBeginTime = 0;
          return;
        }
        //  this.IMIVideoView && this.IMIVideoView.seekTo(this.selectBeginTime);
        this.selectBeginTime = 0;
      }
    } else if (
      event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP ||
      event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_RESET
    ) {
      console.log(' 直播流----_onEventChange,出现停止');
      // IMILog.logD("王 错误 PayBack _onEventChange PLAYER_EVENT_ON_STOP>",event.toString())
      this.setState({isLoading: false, isPlay: false, showPauseView: true, isPlayFinish: true});
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
      // console.log(" 直播流----_onEventChange,开始播放");
      //this.setState({isLoading:false});
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE) {
      this.setState({isLoading: false, isPlay: false, showPauseView: true});
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PAUSE_IOS) {
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_END) {
      // 此处逻辑同播放器onPlayCompletion
      // console.log('播放结束了----------');
      // this.finish = true;
      // this.progressView && this.progressView.resetStatus();
      // bug879，播放结束后全屏未全屏展示
      // this.IMIVideoView?.reset();
    }
  };

  _onPressPlay = () => {
    if (!this.state.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }

    if (this.state.showErrorView && !this.state.isPlay) {
      this.progressView && this.progressView.resetStatus();
      //this.IMIVideoView && this.IMIVideoView.prepare();
      return;
    }
    // this.setState({isClickPause:true});

    if (this.tempScrollPause) {
      if (this.state.isClickPause) {
        // 暂停状态下滑动时间轴
        this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
          this.IMIVideoView && this.IMIVideoView.resume();
        });
      } else {
        this.progressView && this.progressView.resetStatus();
        //this.IMIVideoView && this.IMIVideoView.prepare();
        //this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(this.tempScrollTime*1000));
      }
      return;
    }

    if (!this.state.isPlay) {
      if (this.state.isPlayFinish) {
        this.progressView && this.progressView.resetStatus();
        this.IMIVideoView?.reset();
        this.IMIVideoView?.prepare();
        // if (isIos()) {
        //   this.setState({isLoading: true, showPauseView: false});
        // }
        this.setState({isPlay: true, showPauseView: false, isPlayFinish: false});
      } else {
        if (this.state.isClickPause) {
          this.IMIVideoView.resume();
        } else {
          this.progressView && this.progressView.resetStatus();
          //this.IMIVideoView.prepare();
        }
      }

      if (this.state.isClickPause) {
        this.setState({isPlay: true, showPauseView: false, isClickPause: false});
      } else {
        this.setState({isClickPause: false});
      }
      return;
    }

    this.setState({isLoading: false, isClickPause: true}, () => {
      this.IMIVideoView && this.IMIVideoView.pause();
    });
  };

  // 全屏工具
  _onPressFullScreenTools = () => {
    this.setState({showFullScreenTools: true});
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.fullScreenTooltsTimer = setTimeout(() => {
      this._onCloseFullScreenTools();
    }, 5000);
  };

  // 退出全屏工具
  _onCloseFullScreenTools() {
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.setState({showFullScreenTools: false});
  }

  // 全屏
  _onPressFullScreen = () => {
    // if(!this._canStepIn())  return;
    this.setState({isFullScreen: true}, () => {
      isAndroid() ? Orientation.lockToLandscape() : Orientation.lockToLandscapeRight();
      NavigationBar.setStatusBarHidden(true);
      this.props.navigation.setOptions({tabBarVisible: false});
    });
    this._onPressFullScreenTools();
  };

  // 退出全屏
  _exitFullScreen = () => {
    // if(!this._canStepIn())  return;
    this.setState({isFullScreen: false}, () => {
      Orientation.lockToPortrait();
      NavigationBar.setStatusBarHidden(false);
      this.props.navigation.setOptions({tabBarVisible: true});
    });
    this._onCloseFullScreenTools();
  };

  // 静音
  _onPressMute = () => {
    if (!this.state.netConnected) {
      showToast(stringsTo('network_not_connected'));
      return;
    }
    if (!this._canStepIn()) {
      return;
    }
    this.setState({mute: !this.state.mute});
  };

  //判断当前是否可以操作
  _canStepIn() {
    if (!this.state.isPlay) {
      showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
      return false;
    }
    return true;
  }

  onProgressChanged = currentTime => {
    console.log('currentTime', currentTime + ':::' + this.state.isPlay);

    // console.log('当前iOS播放状态---',this.state.isPlay);
    if (!this.state.isPlay) {
      // 暂停状态
      this.setState({isPlay: true, showPauseView: false, isClickPause: false}, () => {
        this.tempScrollPause = true;
        this.tempScrollTime = currentTime;
        this.IMIVideoView && this.IMIVideoView.resume();
      });
    }
    // this.setState({seekTo: Math.round(currentTime)});
    this.IMIVideoView && this.IMIVideoView.seekTo(Math.round(currentTime));
  };

  // 进度条
  renderVideoProgressView() {
    if (this.state.snapshotVisible) {
      return null;
    }
    if (this.state.noVideo) {
      return null;
    }
    if (this.state.todayIsNoData) {
      return null;
    }
    return (
      <View
        style={{
          backgroundColor: 'transparent',
          position: 'absolute',
          width: '98%', //NKGT-127
          bottom: 1,
          height: 80,
          zIndex: 999,
          opacity: this.state.isFullScreen && !this.state.showFullScreenTools ? 0 : 1,
        }}>
        <VideoProgressView
          ref={ref => (this.progressView = ref)}
          isPlayFromBeginning={this.state.isPlayFromBeginning}
          duration={this.state.durationProgress}
          isPlayFinish={this.state.isPlayFinish}
          onProgressValueChanged={this.onProgressChanged}
          switchVideoItemIng={this.state.switchVideoItemIng}
          isAlarm={true}
        />
      </View>
    );
  }

  // 加载中
  _loadingView() {
    if (this.state.showPauseView) {
      return;
    }
    if (!this.state.isLoading) {
      return;
    }
    if (this.state.noVideo) {
      return;
    }
    if (this.state.todayIsNoData) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator style={{width: 54, height: 54}} color={'#ffffff'} size={'large'} />
        <Text
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}>
          {stringsTo('commLoadingText')}
        </Text>
      </View>
    );
  }

  // 出错了
  _errorView() {
    if (!this.state.showErrorView) {
      return;
    }
    if (this.state.todayIsNoData) {
      return null;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
        />

        <RoundedButtonView
          buttonText={stringsTo('error_code_common_retry')}
          buttonStyle={{
            margin: 14,
            paddingHorizontal: 15,
            height: 40,
          }}
          buttonTextStyle={{textAlign: 'center'}}
          onPress={() => {
            // ;
            console.log('视频名称:' + this.state.deleteSegmentId);
            // this.timeLine && this.timeLine.scrollToTimestamp(scrollTime);
            // console.log('播放失败---',playEndTime);
            if (!this.state.netConnected) {
              showToast(stringsTo('network_not_connected'));
              return;
            }
            this.tempScrollPause = false;
            this.errPrepare = true;
            this.progressView && this.progressView.resetStatus();
            this.IMIVideoView?.reset();
            this.IMIVideoView?.prepare();
            this.setState({showErrorView: false});
          }}
        />
      </View>
    );
  }
  // 暂停
  _pauseView() {
    if (!this.state.showPauseView) {
      return null;
    }
    if (this.state.showErrorView) {
      return;
    }
    if (this.state.noVideo) {
      return;
    }
    if (this.state.todayIsNoData) {
      return null;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ImageButton
          style={{width: 52, height: 52}}
          source={require('../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          highlightedSource={require('../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          onPress={() => {
            this._onPressPlay();
          }}
        />
      </View>
    );
  }

  // 下载
  _downloadPress = () => {
    if (!this.state.currentSegmentId?.segmentId) {
      //可以做个提示
      return;
    }
    //需要检测权限
    isCheckingPermission = true;
    this.downloadFlag = true;
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            this.getDownloadUrl();
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
            this.downloadFlag = false;
          }
          isCheckingPermission = false;
        });
      } else if (status === -1) {
        showToast(stringsTo('storage_permission_denied'));
        isCheckingPermission = false;
        this.downloadFlag = false;
      }
    });
  };

  getDownloadUrl() {
    showLoading(stringsTo('alarm_download_downloading'), true);
    this.intervalTimer && clearInterval(this.intervalTimer);
    const saveFileName = `VIDEO_IMI_${new Date().getTime()}`;
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${saveFileName}.mp4`;
    const {segmentId, publicKeyVersion} = this.state.currentSegmentId;
    aliAlarmEventCloudApi
      .downloadVideo(LetDevice.deviceID, segmentId, publicKeyVersion, saveFilePath, saveFileName)
      .then(_ => {
        var path = saveFilePath;
        if (isAndroid()) {
          path = `${saveFilePath}/concat.mp4`;
        }
        // 推送到相册
        IMIFile.saveVideoToPhotosAlbum(path, LetDevice.deviceID)
          .then(_ => {
            IMIToast.showToast(stringsTo('download_system_album'), IMIToast.TYPE.BOTTOM);
            showLoading(false);
            this.downloadFlag = false;
          })
          .catch(error => {
            showToast(stringsTo('video_download_fail'));
            showLoading(false);
            this.downloadFlag = false;
          });
      })
      .catch(error => {
        showToast(stringsTo('video_download_fail'));
        showLoading(false);
        this.downloadFlag = false;
      });
  }

  // 返回
  _onPressBack = () => {
    this.isView = false;
    if (this.downloadFlag) {
      return true;
    }
    if (this.state.isFullScreen) {
      this._exitFullScreen();
    } else {
      this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
    }
    return true;
  };

  deletImageArray() {
    aliAlarmEventCloudApi
      .deleteFileBySegment(LetDevice.deviceID, [this.state.currentSegmentId.segmentId])
      .then(data => {
        showLoading(stringsTo('delete_title_loading'), false);
        showToast(stringsTo('delete_success'));
        this.props.route?.params?.refreshList && this.props.route.params.refreshList();
        this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
      })
      .catch(error => {
        console.log('删除失败', error)
        IMILog.logI('删除失败', JSON.stringify(error));
        showLoading(stringsTo('delete_title_loading'), false);
        showToast(stringsTo('delete_failed'));
      });
  }

  // 操作栏
  _renderPortraitScreenPlayerToolBarArea(isFullScreen) {
    let item = {
      isText: false,
      data: [require('../../resources/images/icon_alarm_down.png')],
      onPress: this._downloadPress,
      disabled: !this.state.currentSegmentId?.segmentId || !(this.state.dataList.length > 0),
      dataIndex: 0,
      accessibilityLabel: ['play_back_full_screen'],
    };
    let {cloudDownload} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    let moreItems = [];
    if (cloudDownload) {
      moreItems = [{item: item, insertIndex: 5}];
    }
    return (
      <View style={{flex: 1, flexDirection: 'column', display: isFullScreen ? 'none' : ''}}>
        <PlayBackToolBarView
          playDisabled={this.state.noVideo || this.state.todayIsNoData}
          speedTitle={['1X', '2X']}
          speedPress={this._onPressSpeed}
          speedDisabled={true}
          speedIndex={this.state.speedIndex}
          fullscreenPress={this._onPressFullScreen}
          mutePress={this._onPressMute}
          mute={this.state.mute}
          muteDisabled={!this.state.isPlay}
          // screenshotPress={this._onPressScreenShot}
          screenshotDisabled={!this.state.isPlay}
          // recordPress={this._onPressRecord}
          recordDisabled={!this.state.isPlay}
          fullscreenDisabled={this.state.noVideo || this.state.todayIsNoData || !(this.state.dataList.length > 0)}
          playPress={this._onPressPlay}
          play={this.state.isPlay}
          moreItems={moreItems}
          isShowSpeedItem={false}
          // 隐藏录屏按钮
          ignoreRecord={true}
          // 隐藏截图
          ignoreShot={true}
        />
        {this._renderListView()}
        <MessageDialog
          title={stringsTo('delete_alert_tip') + this.state.dataList.length + stringsTo('delete_alert_tip1')}
          visible={this.state.showDeleteTip}
          canDismiss={true}
          onDismiss={() => {
            this.setState({showDeleteTip: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelDeleteAlertVideo',
              callback: _ => {
                this.setState({showDeleteTip: false});
              },
            },
            {
              text: I18n.t('delete_title'),
              accessibilityLabel: 'okDeleteAlertVideo',
              callback: _ => {
                showLoading(stringsTo('delete_title_loading'), true);
                this.setState({showDeleteTip: false});
                this.deletImageArray(0);
              },
            },
          ]}
        />
      </View>
    );
  }

  /**
   * 竖屏状态视屏区域填充UI
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenVideoViewArea() {
    return (
      <NavigationBar
        type={NavigationBar.TYPE.DARK}
        backgroundColor={'transparent'}
        title={stringsTo('bottom_house_video_keeping')}
        left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
        right={[
          {
            key: NavigationBar.ICON.CUSTOM,
            n_source: !(this.state.dataList.length > 0) ? require('../../resources/images/houseKeepingV2/icon_angel_set_del.png') 
            : require('../../resources/images/icon_album_delete_white.png'),
            onPress: _ => {
              if(!(this.state.dataList.length > 0)) {
                return
              }
              this.setState({showDeleteTip: true});
            },
            accessibilityLabel: 'homedelete',
          },
        ]}
      />
    );
  }

  /**
   * 全屏状态videoView区域填充UI
   * @returns {Element}
   * @private
   */
  _renderLandscapeScreenVideoViewArea() {
    let item = {
      isText: false,
      data: [require('../../resources/images/icon_download_white.png')],
      onPress: this._downloadPress,
      disabled: !this.state.currentSegmentId?.segmentId || !(this.state.dataList.length > 0),
      dataIndex: 0,
      accessibilityLabel: ['play_back_full_screen'],
    };
    let {cloudDownload} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    let moreItems = [];
    if (cloudDownload) {
      moreItems = [{item: item, insertIndex: 5}];
    }

    return (
      <View
        pointerEvents="box-none"
        style={{width: '100%', height: '100%', display: this.state.showFullScreenTools ? 'flex' : 'none'}}>
        <PlayBackFullScreenToolBarView
          exitPress={this._exitFullScreen}
          playPress={this._onPressPlay}
          isPlay={this.state.isPlay}
          mutePress={this._onPressMute} //静音
          mute={this.state.mute}
          muteDisabled={!this.state.isPlay}
          speedDisabled={true}
          // screenshotPress={this._onPressScreenShot}
          screenshotDisabled={!this.state.isPlay}
          // recordPress={this._onPressRecord}
          recording={this.state.recording}
          recordDisabled={!this.state.isPlay}
          fullScreenPress={this._exitFullScreen}
          moreItems={moreItems}
          isShowSpeedItem={false}
          // 隐藏录屏按钮
          ignoreRecord={true}
          ignoreShot={true}
        />

        <View
          style={{
            position: 'absolute',
            left: 0,
            bottom: 10,
            right: 0,
          }}
        />
      </View>
    );
  }

  _renderListView() {
    let dataList = this.state.dataList || [];
    return (
      <ScrollView
        raw={true}
        style={{
          backgroundColor: colors.page_bg,
          paddingTop: 10,
        }}
        data={dataList}
        numColumns={1}>
        {Array.isArray(dataList) &&
          dataList.map((item, index) => {
            return this._renderItem(item, index);
          })}
      </ScrollView>
    );
  }

  handleCurrent = item => {
    if (this.state.currentSegmentId.eventStartTime == item.eventStartTime) {
      return;
    }
    console.log('handleCurrent---', item);
    if (this.finish) {
      this.setState(
        {
          currentSegmentId: item,
        },
        () => {
          this.first = true;
          // 播完后必须reset才能prepare到流
          this.IMIVideoView?.reset()
          this.IMIVideoView?.prepare();
        },
      );
      return;
    }
    if (!this.state.isPlay) {
      // 暂停状态
      this.setState({isPlay: true, showPauseView: false}, () => {
        this.IMIVideoView && this.IMIVideoView.resume();
      });
    }
    this.setState({
      currentSegmentId: item,
      // seekTo: Math.floor(item.offsetDuration || 0),
    });
    this.IMIVideoView && this.IMIVideoView.seekTo(Math.floor(item.offsetDuration || 0));
  };

  _renderItem = (item, index) => {
    let {displayEventType, segmentStartTime, eventStartTime} = item;
    let time = moment(segmentStartTime || eventStartTime).format('YYYY-MM-DD HH:mm:ss');
    let formatStarTime = time.substr(11);
    const iconDotMove = require('../../resources/images/pic_move.png');
    const iconDotPeople = require('../../resources/images/pic_person.png');
    const iconDotSound = require('../../resources/images/pic_sound.png');
    const eventTypeList = displayEventType && displayEventType.split(',');
    const iconDot = {
      ObjectMotion: iconDotMove,
      PeopleMotion: iconDotPeople,
      AbnormalSound: iconDotSound,
    };
    const textList = {
      ObjectMotion: stringsTo('moveEvent'),
      PeopleMotion: stringsTo('peopleEvent'),
      AbnormalSound: stringsTo('soundEvent'),
    };
    const text = [];
    const newEventTypeList = [];
    Array.isArray(eventTypeList) &&
      eventTypeList.map(res => {
        // 筛选由插件改为接口增加字段
        // if (this.state.eventCurrentIndex === 0) {
        //   text.push(textList[res]);
        //   newEventTypeList.push(res);
        // } else if (lists[this.state.eventCurrentIndex] === res) {
        //   text.push(textList[res]);
        //   newEventTypeList.push(res);
        // }
          text.push(textList[res]);
          newEventTypeList.push(res);
      });
    const isCurrent = this.state.currentSegmentId.eventStartTime === item.eventStartTime;

    return (
      <XView
        key={time}
        style={styles.cardList}
        onPress={() => this.handleCurrent(item)}
        accessibilityLabel={'housekeeping_item_view_' + time}>
        <View>
        {Array.isArray(newEventTypeList) && newEventTypeList.map((item,index) => {
            return <Image style={{width: 18, height: 18, marginRight: 8, marginBottom: (newEventTypeList.length === 1 || index == newEventTypeList.length - 1) ? 0 : 2}} source={iconDot[newEventTypeList[index]]} />
          })}
        </View>
        <View style={styles.leftComponent}>
          {Array.isArray(text) && text.map(res => {
            return <Text style={styles.itemTitle} >{res}</Text>
          })}
        </View>
        <View style={styles.leftComponent}>
          <Text style={styles.itemTime} >{formatStarTime}</Text>
        </View>
        <View style={styles.imgWrap}>
          {isCurrent && <XText style={styles.currentPlay} text={stringsTo('currentPlay')} />}
          <PathImage key={time} item={item} deviceID={LetDevice.deviceID} />
        </View>
      </XView>
    );
  };

  render() {
    return (
      <View
        ref={ref => (this.componentContainer = ref)}
        pointerEvents="box-none"
        style={{flex: 1, backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
        <XView
          activeOpacity={1}
          style={{flex: 1}}
          onPress={() => {
            this.state.isFullScreen && this.state.showFullScreenTools
              ? this._onCloseFullScreenTools()
              : this._onPressFullScreenTools();
          }}>
          <IMCloudServerVideoView
            style={{flex: 1}}
            ref={ref => {
              if (!this.IMIVideoView) {
                this.IMIVideoView = ref;
                this.IMIVideoView?.prepare();
              }
            }}
            mute={this.state.mute} // 初始化必须为undefined
            // speed={this.state.speed} // 初始化必须为undefined
            // seekTo={this.state.seekTo} // 初始化必须为undefined
            playerClass={IMICameraVideoView.PlayerClass.HLS}
            dataSource={{
              productId: LetDevice.model,
              iotId: LetDevice.deviceID,
              segmentId: this.state.currentSegmentId?.segmentId,
              playerClass: IMICameraVideoView.PlayerClass.HLS,
              publicKeyVersion: this.state.currentSegmentId?.publicKeyVersion,
            }}
            onPrepared={() => {
              this.progressView && this.progressView.resetStatus();
            }}
            onEventChange={this._onEventChange}
            onPlayCompletion={() => {
              // 播放完成
              this.finish = true;
              this.progressView && this.progressView.resetStatus();
            }}
            onErrorChange={event => {
              this.setState({
                isPlay: false,
                showErrorView: true,
                showPauseView: false,
                isLoading: false,
                errorCode: event.code,
              });
            }}
          />

          {/*全屏?横屏UI:竖屏UI(navBar)*/}
          <View
            pointerEvents="box-none"
            style={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              flexDirection: 'column',
              alignItems: 'center',
            }}>
            {this.renderVideoProgressView()}
            {this.state.isFullScreen
              ? this._renderLandscapeScreenVideoViewArea()
              : this._renderPortraitScreenVideoViewArea()}
            {this._loadingView()}
            {this._errorView()}
            {this._pauseView()}
          </View>
        </XView>
        {this._renderPortraitScreenPlayerToolBarArea(this.state.isFullScreen)}
      </View>
    );
  }
}

const PathImage = ({item, deviceID}) => {
  const [path, setPath] = useState('');

  //需要照顾数据格式的不同
  const {segmentId, publicKeyVersion, imgSignedDownloadUrl} = item;
  useEffect(() => {
    const getImageUrl = async () => {
      const data = await cloudDeviceService
        .downLoadImage(deviceID, segmentId, publicKeyVersion, imgSignedDownloadUrl)
        .catch(e => {
          console.log(11, e);
        });
      if (data) {
        setPath(data);
      }
    };
    getImageUrl();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // if (isEmpty(path)) {
  //   return <IMIImageView style={styles.itemIcon} source={require('../../resources/images/play_back_comm_bg.png')} />;
  // }
  return <IMIImageView style={styles.itemIcon} source={{uri: isIos() ? isEmpty(path) ? require('../../resources/images/play_back_comm_bg.png') : `file://${path}` : `file://${path}`}} />;
};

const styles = StyleSheet.create({
  cardList: {
    flex: 1,
    flexDirection: 'row',
    marginLeft: 15,
    marginRight: 15,
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    // height: 80,
    paddingTop: 12,
    paddingBottom: 12,
    paddingLeft: 16,
    paddingRight: 16,
    backgroundColor: colors.white,
    borderRadius: 10,
  },
  leftComponent: {
    flex: 1,
    justifyContent: 'center',
  },
  itemIcon: {
    borderRadius: 4,
    width: (windowWidth - 4 * 14) / 4,
    height: ((windowWidth - 4 * 14) / 4 / 16) * 9,
  },
  itemTitle: {
    fontSize: 12,
    lineHeight: 18,
    fontWeight: 'bold',
    color: colors.gray,
  },

  itemTime: {
    fontSize: 10,
    color: colors.gray,
    // marginTop: 2,
    fontWeight: 'bold',
  },
  imgWrap: {
    width: (windowWidth - 4 * 14) / 4,
    height: ((windowWidth - 4 * 14) / 4 / 16) * 9,
    position: 'relative',
    alignItems: 'center',
  },
  currentPlay: {
    position: 'absolute',
    zIndex: 99,
    fontSize: 10,
    color: '#fff',
    backgroundColor: colors.defaultThemeColor,
    borderWidth: 1.5,
    borderColor: '#fff',
    borderStyle: 'solid',
    borderRadius: 12,
    paddingLeft: 10,
    paddingRight: 10,
    paddingTop: 2,
    paddingBottom: 2,
    top: 11,
  },
});
