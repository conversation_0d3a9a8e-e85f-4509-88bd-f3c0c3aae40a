/*
 * 作者：sunhongda
 * 文件：IPC031MainPage.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */
import { stringsTo } from '../../../../globalization/Localize';
import { showLoading, showToast } from '../../../../imilab-design-ui/src/widgets/Loading';
import {
  LetDevice,
  BaseDeviceComponent
} from '../../../../imilab-rn-sdk';
import {LetIMIIotRequest} from '../../../../imilab-rn-sdk';
import Orientation from 'react-native-orientation';

export default class MsgTrans extends BaseDeviceComponent {
  constructor(props) {
    super(props);
    this.state = {
    };
  }
  componentDidMount() {
    Orientation.lockToPortrait();
    showLoading(stringsTo('loadMsgInfo'), true)
    this.getDetail()
  }
  // 获取信息
  getDetail() {
    const {segmentId, offset} = JSON.parse(JSON.parse(this.props?.route?.params?.extra)?.pushInfo);
    const params = {
      Path: '/v1.0/imilab-01/app/cloudstorage/getEventListBySegmentid',
      ParamMap: {
        iotId: LetDevice.deviceID,
        segmentId: segmentId,
        productId: LetDevice.model
      },
      Method: 'GET',
    };
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        const lists = []
          item.cloudStorageEventList.map(res => {
            if (res.offset - offset <= 0) {
              lists.push({...res, leftNum: res.offset - offset})
            };
          })
          lists.sort((a, b) => {
            return b.leftNum - a.leftNum
          })
          if (lists.length === 0) {
            showLoading(false);
            showToast(stringsTo('loadMsgInfoError'));
            this.props.navigation.replace('AlarmListPage');
            return
          }
          getCurrentSegment = lists[0]
          showLoading(false)
          if (!getCurrentSegment.isExistVideo) {
            const imgSignedDownloadUrl = {
              imgSignedDownloadUrl: getCurrentSegment?.imgSignedDownloadUrl,
              publicKeyVersion: getCurrentSegment?.publicKeyVersion,
              segmentId: getCurrentSegment?.segmentId,
              imgPath: getCurrentSegment?.imgSignedDownloadUrl,
            };
            //  IMILog.logD("王 看家缩略图点击事件 _onItemPress: ",item.toString())
            this.props.navigation.replace('AlbumPhotoViewPage', {
              item: {
                ... getCurrentSegment,
                imgSignedDownloadUrl,
              },
              refreshList: () => {
                // 处理回调数据
              }
            });
          } else {
            this.props.navigation.replace('AlbumVideoView', {
              item: getCurrentSegment,
              refreshList: () => {
                // 处理回调数据
              }
            });
          }
      })
      .catch(e => {
        showLoading(false);
        showToast(stringsTo('loadMsgInfoError'));
        this.props.navigation.replace('AlarmListPage');
        console.log('出错了', JSON.stringify(e));
      })
  }

  render(){
    return null
  }
}