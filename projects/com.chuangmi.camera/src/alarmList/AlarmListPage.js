/*
 * 作者：sunhongda
 * 文件：IPC031MainPage.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */
import React from 'react';

import {StyleSheet, TouchableOpacity, View, SafeAreaView, Dimensions, Text, Image, StatusBar} from 'react-native';

import {XText, XView} from 'react-native-easy-app';

import {RNLine, colors, imiThemeManager, showLoading, showToast} from '../../../../imilab-design-ui';
import AlertDialog from '../../../../imilab-design-ui/src/widgets/settingUI/AlertDialog';

import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {
  LetDevice,
  IMIGotoPage,
  BaseDeviceComponent,
  IMIStorageBean,
  imiAlarmEventCloudApi,
  aliAlarmEventCloudApi,
} from '../../../../imilab-rn-sdk';
import {LetIMIIotRequest} from '../../../../imilab-rn-sdk';
import AlarmListPlayerComponent from './AlarmListPlayerComponent';
import Orientation from 'react-native-orientation';
import I18n, {stringsTo} from '../../../../globalization/Localize';
import {AlarmType, IMIStorage} from '../../../../imilab-rn-sdk';
import {isPhoneX} from '../../../../imilab-rn-sdk/utils/Utils';
import {isAndroid} from '../../../../imilab-rn-sdk/utils/Utils';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import {IMINativeLifeCycleEvent} from '../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';

const {width, height} = Dimensions.get('window');
let SCREEN_WIDTH = height > width ? width : height;

/**
 * @Description:   IPC031配置模块
 * @Author:   sunhongda
 * @CreateDate: 2020/9/28 18:06
 * @UpdateUser:     更新者
 * @UpdateDate:  2020/9/28 18:06
 * @UpdateRemark:   更新说明
 */

//const ALL = [stringsTo('all_events_str'), 'housekeeping_event_all'];
const ALL = {eventTypeName: stringsTo('all_events_str'), eventType: 'housekeeping_event_all'};
const People = [stringsTo('people_event'), AlarmType.PEOPLE, 'housekeeping_human_detection'];
const MOVE = [stringsTo('move_event'), AlarmType.MOVE, 'housekeeping_motion_detecting'];
const SOUND = [stringsTo('alarm_loud_switch'), AlarmType.ABNORMAL_SOUND, 'housekeeping_sound_detection'];
//阿里没有无人，这边先用笑声代替LAUGHTER，现在029C01使用的是笑声
const NOBODY = [stringsTo('no_human_event'), AlarmType.LAUGHTER, 'housekeeping_nobody_detection'];
const FENCE = [stringsTo('fence_detect_switch'), AlarmType.CROSSING, 'fence_detect_detection'];
// 026 哭声侦测
const CRY = [stringsTo('cry_event'), AlarmType.CRY, 'housekeeping_cry_detection'];
const KEY_AREA = [stringsTo('keyAreaDetectStr'), AlarmType.KEY_AREA, 'housekeeping_key_area_detection'];

export default class AlarmListPage extends BaseDeviceComponent {
  constructor(props) {
    super(props);
    this.state = {
      fullScreen: Orientation.getInitialOrientation() !== 'PORTRAIT',
      isEdit: false,
      numAry: [],
      isSelectAll: false,
      vipState: -1,
      alarmDialog: false,
      length: 0,
      lifecycleDay: 3, //选中日期
      eventTypeList: [], //事件类型列表
    };
  }

  componentWillUnmount() {
    // NavigationBar.setBarStyle('dark-content'); // 修改从云存界面返回状态栏显示白色字体问题
    this._subscribe_focus && this._subscribe_focus();
    this._onResumeListener && this._onResumeListener.remove();
  }

  componentDidMount() {
    this.alarmListPlayerComponent._getAlarmListData();
    this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      //app页面回到插件
      NavigationBar.setBarStyle('dark-content'); // 修改从云存界面返回状态栏显示白色字体问题
    });

    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      Orientation.lockToPortrait();
      console.log('进入前台页面了');
      this.alarmListPlayerComponent.resetStatus()
      this.alarmListPlayerComponent.checkVipState()
    });
    showLoading(true);
    imiAlarmEventCloudApi
      .getVipState(LetDevice.deviceID)
      .then(res => {
        console.log('[[[[[[[[[[', res);
        let data = JSON.parse(res);
        showLoading(true);
        this.setState({vipState: data.status});
      })
      .catch(error => {
        console.log('error111111', error);

        showLoading(true);
        // showToast(JSON.stringify(error));
      });

    this.getOrders();
    this.getHouseKeep();
    this.getEventTypeByIotId();
    // LetDevice.updateAllPropertyCloud().then((data) => {
    //     showLoading(false);
    //     let dataObject = JSON.parse(data);
    //     //侦测时间
    //     if (dataObject.AlarmSwitch) {
    //         if (!dataObject.AlarmSwitch.value){
    //             this.setState({alarmDialog: true});
    //         }
    //     }
    //     console.log('statprops--'+ JSON.stringify(data));
    // }).catch(error => {
    //     console.log(JSON.stringify(error))
    // });
  }

  /**
   * 获取看家助手总开关的状态
   * @returns {JSX.Element}
   */
  getHouseKeep() {
    const params = {
      Path: 'v1.0/imilab-01/app/cloudstorage/get-video-plan',
      ParamMap: {
        iotId: LetDevice.deviceID,
      },
      Method: 'GET',
    };
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        console.log('getHouseKeep', item);
        if (!item?.switchOn) {
          //看家开关未打开
          this.getIMIStorageAlarm();
        } else {
          this.setSaveTime(); //防止关闭再打开后弹出
        }
      })
      .catch(error => {
        //未正确请求到看家开关，默认就不展示弹框，这里不做处理，不会展示弹框
        this.getIMIStorageAlarm();
      });
  }

  /**
   * 获取本地保存的上次展示看家设置弹框的时间
   * 如果没有存入时间，则展示看家设置弹框
   * 24小时内，只展示一次看家弹框（每次APP卸载重新安装并且看家开关未打开时，会展示设置看家弹框）
   */
  getIMIStorageAlarm() {
    IMIStorage.load({
      key: LetDevice.deviceID + 'alarmDialog',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        let oldTime = res.alarmDialogStartTime;
        let newTime = Date.parse(new Date());
        if (!res.alarmDialogStartTime) {
          this.setState({alarmDialog: true});
        } else {
          if (newTime >= oldTime) {
            this.setState({alarmDialog: true});
          } else {
            this.setState({alarmDialog: false});
          }
        }
      })
      .catch(err => {
        console.log('及时err:', err);
        //没有对应的key会走这里，那么就显示弹框吧
        this.setState({alarmDialog: true});
      });
  }
  /* 根据iotid获取事件类型 */
  getEventTypeByIotId() {
    aliAlarmEventCloudApi
      .getEventTypeListByIotId(LetDevice.deviceID)
      .then(res => {
        const eventAry = [ALL];
        const textList = {
          ObjectMotion: stringsTo('picture_change'),
          PeopleMotion: stringsTo('people_move'),
          AbnormalSound: stringsTo('soundEvent'),
          FenceOut: stringsTo('person_leave_fence'),
          FenceIn: stringsTo('person_enter_fence'),
          PersonApproaching: stringsTo('vehicle_people_near'),
          CarDetected: stringsTo('vehicle_detection'),
          PersonApproachingNonMotor: stringsTo('non_vehicle_people_near'),
          NonMotorDetected: stringsTo('non_vehicle_detection'),

        }
        JSON.parse(res)?.forEach(element => {
          console.log('yyhelement', element);
          eventAry.push({eventTypeName: textList[element?.eventType] || element.eventTypeName, eventType: element?.eventType});
        });
        this.setState({eventTypeList: eventAry});
      })
      .catch(error => {
        console.log('error1111', JSON.stringify(error));
      });
  }
  render() {
    /* let {
      showPeopleEvent,
      showMoveEvent,
      showAlarmLoudSwitch,
      showNoHuman,
      showFencesSwitch,
      showCrySwitch,
      showKeyAreaEvent = false,
    } = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');

    let eventAry = [ALL];
    if (showPeopleEvent) {
      eventAry.push(People);
    }
    if (showMoveEvent) {
      eventAry.push(MOVE);
    }
    if (showAlarmLoudSwitch) {
      eventAry.push(SOUND);
    }
    if (showNoHuman) {
      eventAry.push(NOBODY);
    }
    if (showFencesSwitch) {
      eventAry.push(FENCE);
    }
    if (showCrySwitch) {
      eventAry.push(CRY);
    }
    if (showKeyAreaEvent) {
      eventAry.push(KEY_AREA);
    }
    console.log('eventAry', eventAry); */

    //解决其他页面横屏时点击push消息进入看家页面，状态栏不显示问题
    NavigationBar.setStatusBarHidden(false);
    return (
      <>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={'transparent'}
          title={stringsTo('bottom_house_keeping')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'housekeeping_back',
            },
          ]}
          right={[
            // {
            //   key: NavigationBar.ICON.CUSTOM,
            //   n_source:
            //     this.state.length == 0
            //       ? require('../../resources/images/icon_edit_disabled.png')
            //       : require('../../resources/images/icon_edit.png'),
            //   onPress: _ => {
            //     if (this._isShareUser()) {
            //       return;
            //     }
            //     if (this.state.length) {
            //       this.setState({isEdit: true, numAry: [], isSelectAll: false});
            //     }
            //   },
            //   accessibilityLabel: 'housekeeping_editor',
            // },
            {
              key: NavigationBar.ICON.CUSTOM,
              n_source: require('../../resources/images/icon_set.png'),
              onPress: _ => {
                if (this._isShareUser()) {
                  return;
                }
                if (!LetDevice.isOnline) {
                  showToast(stringsTo('device_offline'));
                  return;
                }
                this.props.navigation.push('HouseKeepSetting');
                /* if (LetDevice.model == 'a1MSKK9lmbs') {
                  this.props.navigation.push('HouseKeepSettingV2');
                } else if (
                  LetDevice.model == 'a1FKrifIRwH' ||
                  LetDevice.model == 'a1Ikkj5vsiK' ||
                  LetDevice.model == 'a1znn6t1et8'
                ) {
                  // 026 021E01 036 进入新的看家界面 原生项目转RN
                  this.props.navigation.push('HouseKeepOldNativeSetting');
                } else {
                  this.props.navigation.push('HouseKeepSetting');
                } */
              },
              accessibilityLabel: 'housekeeping_setting',
            },
          ]}
        />

        <AlarmListPlayerComponent
          ref={component => (this.alarmListPlayerComponent = component)}
          onClickCancel={checkFullScreen => {
            this.setState({isEdit: checkFullScreen});
          }}
          isEdit={this.state.isEdit}
          isSelectedAll={this.state.isSelectAll}
          dataArray={this.state.numAry}
          vipState={this.state.vipState}
          lifecycleDay={this.state.lifecycleDay}
          navigation={this.props.navigation}
          eventCurrentTitles={this.state.eventTypeList}
          onClickSelect={(index, indexAry) => {
            let stateParams = {};
            if (index != undefined) {
              let dataAry = this.state.numAry;
              if (dataAry.indexOf(index) < 0) {
                dataAry.push(index);
              } else {
                let num = dataAry.indexOf(index);
                dataAry.splice(num, 1);
              }
              stateParams.numAry = dataAry;
            } else {
              stateParams.numAry = indexAry;
            }
            //逐个取消勾选，当无勾选时，左上角变为"全选"
            if (stateParams.numAry.length == 0 || this.state.length > stateParams.numAry.length) {
              stateParams.isSelectAll = false;
            }
            //逐个勾选，当全部勾选时，左上角变为"全不选"
            if (this.state.length == stateParams.numAry.length) {
              stateParams.isSelectAll = true;
            }
            this.setState(stateParams);
          }}
          getDataLength={length => {
            this.setState({length: length});
          }}
        />

        {!this.state.fullScreen ? <RNLine style={{height: 1}} /> : null}
        {/*编辑选择头部*/}
        {this._renderSelectView()}
        {LetDevice.isShareUser ? null : this._renderAlarmDialog()}
      </>
    );
  }
  /**
   * 判断是否为分享
   * @returns {boolean}
   * @private
   */
  _isShareUser() {
    if (LetDevice.isShareUser) {
      showToast(stringsTo('shareUser_tip'));
      return true;
    }
    return false;
  }
  /**
   * 获取云存
   */
  getOrders() {
    ///api/app/storage/cut/orders
    const now = new Date();
    const midnight = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const midnightMilliseconds = midnight.getTime();
    imiAlarmEventCloudApi
      .getPlayList(LetDevice.deviceID, `${midnightMilliseconds}`, `${now.getTime()}`, '20')
      .then(data => {
        let result = JSON.parse(data).cloudStoragePlayList;
        for (let item of result) {
          if (item.stateDescribe == 1) {
            this.setState({
              lifecycleDay: item.lifecycle,
            });
            break;
          }
        }
      })
      .catch(error => {
        console.log('error1111', JSON.stringify(error));
      });
  }
  /**
   * 全选-全不选 操作框
   */

  _renderSelectView() {
    if (this.state.isEdit) {
      // return(<XView style={{position: "absolute",height: isPhoneX()?52+34:52,top:isPhoneX()?34:20,backgroundColor:'#FFF',width:'100%', alignItems: 'center',justifyContent: 'space-between',flexDirection: 'row'}}>
      return (
        <View
          raw={true}
          style={{
            position: 'absolute',
            height: isPhoneX() ? 54 : isAndroid() ? 42 : 35,
            top: isPhoneX() ? 42 : isAndroid() ? 36 : 20,
            backgroundColor: '#efefef',
            width: SCREEN_WIDTH,
            alignItems: 'center',
            justifyContent: 'space-between',
            flexDirection: 'row',
          }}>
          {/*<XText raw={true} style={[styles.barText,{minWidth:90,maxWidth:120, lineHeight:19,}]} text={this.state.isSelectAll?stringsTo('unselect_all'):stringsTo('select_all')}*/}
          {/*       onPress={()=>{*/}
          {/*           this.setState({isSelectAll:!this.state.isSelectAll,numAry:[]});*/}
          {/*       }}*/}
          {/*       accessibilityLabel = {this.state.isSelectAll?"housekeeping_unselect_all":"housekeeping_select_all"}*/}
          {/*/>*/}
          {/*  <XText raw={true} style={[styles.barText,{fontWeight:'bold'}]} text={I18n.t('select_title_3', {code: this.state.numAry.length})}/>*/}
          {/*  <XText raw={true} style={[styles.barText,{minWidth:80,maxWidth:110}]} text={stringsTo('cancel')} onPress={()=>this.setState({isEdit:false})}*/}
          {/*         accessibilityLabel = {"housekeeping_cancel"}*/}
          {/*  />*/}
          <TouchableOpacity
            style={{minWidth: 90, maxWidth: 120, lineHeight: 19}}
            onPress={() => {
              this.setState({isSelectAll: !this.state.isSelectAll, numAry: []});
            }}
            accessibilityLabel={'isSelectAll'}>
            <Text
              style={{color: '#333333', fontSize: 17, textAlign: 'center'}} //设计稿改动
            >
              {this.state.isSelectAll ? stringsTo('unselect_all') : stringsTo('select_all')}
            </Text>
          </TouchableOpacity>

          <Text
            style={{color: '#333333', fontSize: 17, fontWeight: 'bold', textAlign: 'center'}} //设计稿改动
          >
            {I18n.t('select_title_3', {code: this.state.numAry.length})}
          </Text>

          <TouchableOpacity
            style={{minWidth: 80, maxWidth: 110, lineHeight: 19}}
            onPress={() => {
              this.setState({isEdit: false});
            }}
            accessibilityLabel={'cancle_edit'}>
            <Text
              style={{color: '#333333', fontSize: 17, textAlign: 'center'}} //设计稿改动
            >
              {stringsTo('cancel')}
            </Text>
          </TouchableOpacity>
        </View>
      );
    }
    return null;
  }

  _renderAlarmDialog() {
    if (LetDevice.isShareUser) {
      return null;
    } //分享用于无法进入看家设置，也不弹打开看家的提示框

    //默认有人、画面变化
    let str_message = `${stringsTo('str_housekeeping_tip_guide_people')}\n${stringsTo(
      'str_housekeeping_tip_guide_move',
    )}`;
    // let str_message="str_housekeeping_tip_value_nosound";
    if (LetDevice.model == 'a1Godgpvr3D') {
      // str_message="str_housekeeping_tip_value";
      str_message = `${stringsTo('str_housekeeping_tip_guide_people')}\n${stringsTo(
        'str_housekeeping_tip_guide_move',
      )}\n${stringsTo('str_housekeeping_tip_guide_sound')}`;
    } else if (LetDevice.model == 'a1zcQKoHQ83') {
      // str_message="str_housekeeping_tip_guide";
      str_message = `${stringsTo('str_housekeeping_tip_guide_people')}\n${stringsTo(
        'str_housekeeping_tip_guide_move',
      )}\n${stringsTo('str_housekeeping_tip_guide_sound')}\n${stringsTo('str_housekeeping_tip_guide_fence')}`;
    } else if (LetDevice.model == 'a1QRbHvcYBd') {
      // str_message = "str_housekeeping_tip_guide_029";
      str_message = `${stringsTo('str_housekeeping_tip_guide_people')}\n${stringsTo(
        'str_housekeeping_tip_guide_move',
      )}\n${stringsTo('str_housekeeping_tip_guide_sound')}\n${stringsTo('str_housekeeping_tip_guide_nobody')}`;
    } else if (LetDevice.model == 'a1FKrifIRwH') {
      // 026
      str_message = `${stringsTo('str_housekeeping_tip_guide_move')}\n${stringsTo('str_housekeeping_tip_guide_cry')}`;
    } else if (LetDevice.model == 'a1Ikkj5vsiK') {
      // 021e01
      str_message = `${stringsTo('str_housekeeping_tip_guide_people')}\n${stringsTo(
        'str_housekeeping_tip_guide_move',
      )}\n${stringsTo('str_housekeeping_tip_guide_sound')}\n${stringsTo('str_housekeeping_tip_guide_nobody')}`;
    } else if (LetDevice.model == 'a1znn6t1et8') {
      // 036a01
      str_message = `${stringsTo('str_housekeeping_tip_guide_people')}\n${stringsTo(
        'str_housekeeping_tip_guide_move',
      )}\n${stringsTo('str_housekeeping_tip_guide_sound')}`;
    } else if (LetDevice.model == 'a1MSKK9lmbs') {
      //062
      str_message = `${stringsTo('str_housekeeping_tip_guide_people')}\n${stringsTo(
        'str_housekeeping_tip_guide_move',
      )}\n${stringsTo('str_housekeeping_tip_guide_sound')}\n${stringsTo('str_housekeeping_tip_guide_important')}`;
    }

    return (
      <AlertDialog
        title={stringsTo('str_housekeeping_tip_title')}
        visible={this.state.alarmDialog}
        resource={require('../../resources/images/police_pic_empty.png')}
        message={str_message}
        messageStyle={{marginVertical: 5, fontSize: 12, fontWeight: '500', color: '#7F7F7F', textAlign: 'left'}}
        titleStyle={{fontSize: 14}}
        canDismiss={true}
        onDismiss={() => {
          this.setState({alarmDialog: false}, () => {
            this.setSaveTime();
          });
        }}
        buttons={[
          {
            text: stringsTo('cancel'),
            callback: _ => {
              this.setState({alarmDialog: false}, () => {
                this.setSaveTime();
              });
            },
          },
          {
            text: stringsTo('go_to_open'),
            callback: _ => {
              this.setState({alarmDialog: false}, () => {
                this.setSaveTime();
                if (LetDevice.model == 'a1MSKK9lmbs') {
                  this.props.navigation.push('HouseKeepSettingV2');
                } else if (
                  LetDevice.model == 'a1FKrifIRwH' ||
                  LetDevice.model == 'a1Ikkj5vsiK' ||
                  LetDevice.model == 'a1znn6t1et8'
                ) {
                  // 026 021E01 036 进入新的看家界面 原生项目转RN
                  this.props.navigation.push('HouseKeepOldNativeSetting');
                } else {
                  this.props.navigation.push('HouseKeepSetting');
                }
              });
            },
          },
        ]}
      />
    );
  }
  //保存第一次进入时间
  setSaveTime() {
    let newDate = new Date(new Date().getTime() + 1000 * 60 * 60 * 24);
    let tempNewDate = new Date(newDate.getFullYear(), newDate.getMonth(), newDate.getDate(), 0, 0, 0);
    let startTime = parseInt(tempNewDate.getTime());
    IMIStorage.save({
      key: LetDevice.deviceID + 'alarmDialog',
      data: {
        alarmDialogStartTime: startTime,
      },
      expires: null,
    });
  }
}

const styles = StyleSheet.create({
  tab: {
    flex: 1,
    textAlign: 'center',
    color: colors.gray,
    fontSize: 15,
    textShadowColor: imiThemeManager.theme.primaryColor,
  },
  videoPlayer: {
    position: 'absolute',
    top: 44,
    left: 0,
    bottom: 0,
    right: 0,
  },
  barText: {
    color: '#333333',
    fontSize: 17,
    textAlign: 'center',
  },
});
