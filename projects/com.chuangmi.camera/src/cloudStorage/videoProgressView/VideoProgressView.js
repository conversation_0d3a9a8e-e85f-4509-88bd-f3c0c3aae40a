/*
 * 作者：sunhongda
 * 文件：CoverVideoToolProgress.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React from 'react';
import {ImageBackground, StyleSheet} from 'react-native';
import PropTypes from 'prop-types';
import {CheckBoxButton, colors, imiThemeManager, Slider} from '../../../../../imilab-design-ui';
import {XImage, XText, XView} from 'react-native-easy-app';
import {appendZero, timeFilter} from '../../../../../imilab-rn-sdk/utils/DateUtils';
import * as Typography from '../../../../../imilab-design-ui/src/style/Typography';
import {ViewPropTypes} from 'deprecated-react-native-prop-types';
import I18n, {stringsTo} from '../../../../../globalization/Localize';
import moment from 'moment';

/**
 * 视频播放进度条工具
 */

const TAG = 'VideoProgressView';
let isUserDraging = false; //是否在滑动进度条
let tempCurrentTime = 0;

export default class VideoProgressView extends React.Component {
  static propTypes = {
    /**  videoCover |string|  覆蓋圖片 */
    videoCover: PropTypes.string,
    duration: PropTypes.number,
    isSdVideo: PropTypes.bool,
    isPlayFromBeginning: PropTypes.bool,
    onProgressValueChanged: PropTypes.func, //手动拖动
    recording: PropTypes.bool,
    isPlayFinish: PropTypes.bool,
    leftText: PropTypes.string,
    rightText: PropTypes.string,
    switchVideoItemIng: PropTypes.bool,
    showImageBg: PropTypes.bool,
    ...ViewPropTypes,
  };
  static defaultProps = {
    isSdVideo: false,
    isPlayFromBeginning: false,
    recording: false,
    isPlayFinish: false,
    leftText: '',
    rightText: '',
    showImageBg: true,
  };

  constructor(props) {
    super(props);
    this.state = {
      currentTime: 0,
      showVideoControl: true, // 是否显示视频控制组件
      duration: this.props.duration ? this.props.duration : 0,
      isSdVideo: this.props.isSdVideo ? this.props.isSdVideo : false,
      isPlayFromBeginning: this.props.isPlayFromBeginning ? this.props.isPlayFromBeginning : true,
      showImageBg: this.props.showImageBg ? this.props.showImageBg : true,
    };
  }

  render() {
    let currentTimeSlider = 0;
    if (this.props.duration != 0 && this.state.currentTime - this.props.duration > 0) {
      currentTimeSlider = this.props.duration;
    } else {
      // if (this.state.currentTime == 0 && tempCurrentTime != 0){
      //     currentTimeSlider=tempCurrentTime+3;
      // }else {
      //     currentTimeSlider=this.state.currentTime;
      // }
      currentTimeSlider = this.state.currentTime;
    }
    let leftText = ''
    if (this.props.leftText) {
      leftText = moment((this.props.leftText + this.state.currentTime) * 1000).format('mm:ss');
    }
    return (
      <XView
        raw={true}
        activeOpacity={1}
        style={[styles.container, this.props.style]}
        onPress={() => {
          //this.hideControl()
        }}>
        {/*视频工具条*/}
        {this.state.showVideoControl ? (
          <ImageBackground
            style={[styles.control, {width: '100%', bottom: 0}]}
            source={this.props.showImageBg ? require('../../../../../imilab-modules/com.chuangmi.camera.moudle/resources/images/masking_bg.png') : null}>
            {/*开始时间*/}
            <XText raw={true} style={[styles.time, Typography.textFix]} text={this.props.leftText ? leftText : formatTime(currentTimeSlider)} />

            {/*拖动进度条*/}
            <Slider
              disabled={this.props.recording || this.props.isPlayFinish}
              style={{flex: 1}}
              maximumTrackTintColor={'#FFFFFFB2'}
              minimumTrackTintColor={imiThemeManager.theme.primaryColor}
              thumbTintColor={'#FFFFFF'}
              value={this.props.duration > 0 ? currentTimeSlider : 0}
              minimumValue={0}
              maximumValue={this.props.duration} //this.props.duration
              onSlidingStart={() => this.onSlidingProgressStart()}
              onValueChange={value => {
                this.setState({
                  currentTime: Math.round(value),
                });
              }}
              onSlidingComplete={currentTime => {
                this.isUserDragingDelay();
                console.log('完成Time---', currentTime);
                tempCurrentTime = Math.round(currentTime);
                this.setState({currentTime: Math.round(currentTime)});
                this.props.onProgressValueChanged && this.props.onProgressValueChanged(Math.round(currentTime));
              }}
            />

            {/*结束时间*/}
            <XText raw={true} style={[styles.time, Typography.textFix]} text={this.props.rightText ? this.props.rightText : formatTime(this.props.duration)} />
          </ImageBackground>
        ) : null}
      </XView>
    );
  }

  isUserDragingDelay() {
    this.isUserDragingTimer && clearTimeout(this.isUserDragingTimer);
    this.isUserDragingTimer = setTimeout(() => {
      isUserDraging = false;
    }, 1000);
  }

  isUserDragingDelayClean() {
    this.isUserDragingTimer && clearTimeout(this.isUserDragingTimer);
  }

  onBuffering() {
    // alert(str+"onBuffering");
    console.log(TAG + 'onBuffering...');
  }

  //开始拖动进度条
  onSlidingProgressStart() {
    console.log('开始');
    //现在进度条在暂停时是无法拖动的，但是只会走onSlidingProgressStart，不走onSlidingComplete
    //没有这个return，会导致暂停尝试操作进度条后，isUserDraging一直为true，所以播放进度条不动
    this.isUserDragingDelayClean();
    tempCurrentTime = 0;
    if (this.props.isPlayFinish || this.props.recording) {
      isUserDraging = false;
    } else {
      isUserDraging = true;
    }
  }

  /// 进度条值改变
  onSliderValueChanged(currentTime) {
    //如果不处于播放中忽略进度拖动变化
    //switchVideoItemIng 表示正在切换视频，这时候进度条不让动，不然显示会有问题
    if (isUserDraging || (!this.props.isAlarm && currentTime <= tempCurrentTime) || this.props.switchVideoItemIng) {
      return;
    }

    tempCurrentTime = 0;
    this.setState({
      currentTime: currentTime,
    });
  }

  resetStatus() {
    console.log('========resetStatus========');
    isUserDraging = false;
    tempCurrentTime = 0;
    this.setState({
      currentTime: 0,
    });
  }

  componentWillUnmount() {
    tempCurrentTime = 0;
    isUserDraging = false;
    this.isUserDragingDelayClean();
    this.timer && clearTimeout(this.timer);
  }
}

function formatTime(value) {
  return value > 0 ? `${timeFilter(value)}` : '00:00';
}

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    position: 'absolute',
  },
  container_sub: {
    width: '100%',
    height: '100%',
  },
  control: {
    width: '100%',
    flexDirection: 'row',
    height: 44,
    alignItems: 'center',
    backgroundColor: colors.transparent,
    position: 'absolute',
    bottom: 15,
    left: 0,
  },
  videoToolBtn: {
    marginHorizontal: 15,
    width: 30,
    height: 30,
  },
  time: {
    width: 50,
    textAlign: 'center',
    fontSize: 12,
    color: 'white',
  },
});
