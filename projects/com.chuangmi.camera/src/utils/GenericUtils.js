const customJsonParse = value => {
  let obj = {};
  try {
    obj = JSON.parse(value);
  } catch (error) {
    obj = value;
    // console.log(obj);
  }
  return obj;
};

const assemblyDate = value => {
  let data = value;
  if (`${data}`.length > 1) {
    return `${data}`;
  }
  data = '0' + data;
  return data;
};

const strToArrayBuffer = message => {
  const blobData = new Blob(message, {type: 'text/plain'});
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsArrayBuffer(blobData);
    reader.onload = function (e) {
      //console.log(e.target.result, 'm_direction11111');
      resolve(e.target.result);
    };
    reader.onerror = e => {
      reject(e);
    };
  });
};

const stringToBinaryUTF8 = str => {
  let binaryString = '';

  for (let i = 0; i < str.length; i++) {
    const codePoint = str.codePointAt(i);

    if (codePoint <= 0x7f) {
      // 单字节字符
      binaryString += codePoint.toString(2).padStart(8, '0') + ' ';
    } else if (codePoint <= 0x7ff) {
      // 二字节字符
      binaryString +=
        (0xc0 | (codePoint >> 6)).toString(2).padStart(8, '0') +
        ' ' +
        ((0x80 | (codePoint & 0x3f)).toString(2).padStart(8, '0') + ' ');
    } else if (codePoint <= 0xffff) {
      // 三字节字符
      binaryString +=
        (0xe0 | (codePoint >> 12)).toString(2).padStart(8, '0') +
        ' ' +
        ((0x80 | ((codePoint >> 6) & 0x3f)).toString(2).padStart(8, '0') + ' ') +
        ((0x80 | (codePoint & 0x3f)).toString(2).padStart(8, '0') + ' ');
    } else if (codePoint <= 0x10ffff) {
      // 四字节字符
      binaryString +=
        (0xf0 | (codePoint >> 18)).toString(2).padStart(8, '0') +
        ' ' +
        ((0x80 | ((codePoint >> 12) & 0x3f)).toString(2).padStart(8, '0') + ' ') +
        ((0x80 | ((codePoint >> 6) & 0x3f)).toString(2).padStart(8, '0') + ' ') +
        ((0x80 | (codePoint & 0x3f)).toString(2).padStart(8, '0') + ' ');
    }
  }

  return binaryString.trim(); // 去掉最后的空格
};

const getDay = day => {
  const doHandleMonth = month => {
    var m = month;
    if (month.toString().length == 1) {
      m = '0' + month;
    }
    return m;
  };
  const today = new Date();
  const targetday_milliseconds = today.getTime() + 1000 * 60 * 60 * 24 * day;
  today.setTime(targetday_milliseconds); //注意，这行是关键代码
  const tYear = today.getFullYear();
  let tMonth = today.getMonth();
  let tDate = today.getDate();
  tMonth = doHandleMonth(tMonth + 1);
  tDate = doHandleMonth(tDate);
  return tYear + '-' + tMonth + '-' + tDate;
};
const byteArrayToInt = (data, position) => {
  return 0xff & data[position];
};
const byteArrayToInt4 = (data, position) => {
  return (
    (0xff & data[position]) |
    ((0xff & data[position + 1]) << 8) |
    ((0xff & data[position + 2]) << 16) |
    ((0xff & data[position + 3]) << 24)
  );
};
const byteArrayToLong8 = (data, position) => {
  return (
    (0xff & data[position]) |
    ((0xff & data[position + 1]) << 8) |
    ((0xff & data[position + 2]) << 16) |
    ((0xff & data[position + 3]) << 24) |
    ((0xff & data[position + 4]) << 32) |
    ((0xff & data[position + 5]) << 40) |
    ((0xff & data[position + 6]) << 48) |
    ((0xff & data[position + 7]) << 56)
  );
};

function calculateDaysDifference(date1, date2) {
  // 将两个日期都转换为毫秒值
  const milliseconds1 = date1.getTime();
  const milliseconds2 = date2.getTime();

  const earlyTime = new Date(date1.setHours(0, 0, 0, 0)).getTime();

  // 获取凌晨时间戳（毫秒）

  if (milliseconds2 > earlyTime) {
    return 0;
  }
  // 计算相差的毫秒数
  const millisecondsDiff = Math.abs(milliseconds1 - milliseconds2);

  // 将毫秒数转换为天数
  const daysDiff = millisecondsDiff / (1000 * 60 * 60 * 24);

  return Math.floor(daysDiff);
}

const findStartTimeOld = (list, currentTime) => {
  const currentTimeStamp = currentTime;
  let blackIndex = 0;
  // if (list?.length === 0) {
  //   return {blackIndex: 0, currentTimeStamp: 0};
  // }
  // if (list?.length === 1) {
  //   return {blackIndex: 0, currentTimeStamp};
  // }
  let hasData = false;
  for (let i = 0; i < list.length - 1; i++) {
    // 顺序逆序
    if ((currentTimeStamp <= list[i].timestamp && currentTimeStamp >= list[i+1].timestamp) || (currentTimeStamp >= list[i].timestamp && currentTimeStamp <= list[i+1].timestamp)) {
      if (currentTimeStamp <= list[i].timestamp && currentTimeStamp >= list[i+1].timestamp) {
        blackIndex = i;
      }
      else {
        blackIndex = i + 1;
      }
      hasData = true;
    }
  }
  if (!hasData) {
    if (Math.abs(currentTimeStamp - list[0].timestamp) > Math.abs(currentTimeStamp - list[list.length - 1].timestamp)) {
      blackIndex = list.length - 1;
    } else {
      blackIndex = 0;
    }
  }
  return {blackIndex, currentTimeStamp};
};

const findStartTime =  (list, currentTime) => {
  const currentTimeStamp = currentTime;
  let blackIndex = 0;
  if (list?.length === 0) {
    return {blackIndex: 0, currentTimeStamp: 0};
  }
  if (list?.length === 1) {
    return {blackIndex: 0, currentTimeStamp};
  }
  let hasData = false;
  for (let i = 0; i < list.length - 1; i++) {
    if (currentTimeStamp >= list[i].startTime / 1000 && currentTimeStamp <= list[i].endTime / 1000) {
      blackIndex = i;
      hasData = true;
    }
  }
  if (!hasData) {
    const oldTime = findStartTimeOld(list, currentTime);
    blackIndex = oldTime?.blackIndex;
  }
  return {blackIndex, currentTimeStamp};
};


const findOneMinuteIndex = backList => {
  let findIndex = 0;
  if (backList?.length < 2) {
    return 0;
  }
  const fiveminite = backList[0].timestamp - 300;
  for (let index = 0; index < backList.length; index++) {
    const element = backList[index];
    if (element.timestamp <= fiveminite) {
      findIndex = index;
      break;
    }
  }
  return findIndex;
};

export {
  customJsonParse,
  assemblyDate,
  strToArrayBuffer,
  stringToBinaryUTF8,
  getDay,
  byteArrayToInt,
  byteArrayToInt4,
  byteArrayToLong8,
  calculateDaysDifference,
  findStartTime,
  findOneMinuteIndex,
};
