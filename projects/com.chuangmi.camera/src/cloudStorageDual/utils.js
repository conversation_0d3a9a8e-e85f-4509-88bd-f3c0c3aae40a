import { LetDevice, LetIMIIotRequest } from "../../../../imilab-rn-sdk"

const getCloudList = (iotId, beginTime, endTime,limit=50, lensNumber,) => {
    const params = {
        Path: `/v1.0/imilab-01/app/cloudstorage/play-list`,
        ParamMap: {
            beginTime,
            endTime,
            iotId,
            lensNumber,
             limit,
        },
        Method: 'GET'
    };
    return LetIMIIotRequest.sendUserServerRequest(params)
}
export { getCloudList }