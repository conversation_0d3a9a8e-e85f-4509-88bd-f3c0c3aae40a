import React, {Component} from 'react';
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  Dimensions,
  SafeAreaView,
  Image,
  Button,
  ActivityIndicator,
} from 'react-native';
import {imiThemeManager, RoundedButtonView} from '../../../../imilab-design-ui';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import {LetIMIIotRequest, IMIGotoPage} from '../../../../imilab-rn-sdk';
import {showToast} from '../../../../imilab-design-ui';
import {showLoading} from '../../../../imilab-design-ui';
import {stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {customJsonParse} from '../utils/GenericUtils';
import CircleProgress from './CircleProgress';
import Toast from 'react-native-root-toast';
const {width} = Dimensions.get('window');
// import ProgressView from '../view/ProgressView';
/**
 * 固件更新页面
 */
// 0:未开始升级 1:已下发APP消息给用户 2:已下发升级消息给设备 3:下载中 4:安装中 5:升级完成 6:升级失败 7:已取消
export default class UpgradePage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props) {
    super(props);
    this.state = {
      firmwareInfo: {},
      pollingIntervalId: null,
      progress: 0,
      fw_ver: '0',
      errorStatus: false,
      endLoading: false,
    };
    this.uploading = false;
    this.isFirst = true;
    this.loading1=false;
    this.loading2=false;
  }

  componentWillUnmount() {
    showLoading(false);
    this.stopPolling();
  }

  componentDidMount() {
    this.setState({
      endLoading: true
    })
    this.getFirmwareInfo();
    // 使用接口获取哦
    // if (LetDevice.isOnline) {
    this.getVersion();
    // }
  }

  getVersion = () => {
    this.loading1 = true;
    const params = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        // thingId: 500,
        thingId: '0',
        method: 'sync',
      },
      Method: 'POST',
    };
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(data => {
        // console.log('getSingleProperty---', data.value?.otaChannel);
        // this.setState({fw_ver: data?.value?.otaChannel[0]?.version_name, errorStatus: false});
        this.setState({fw_ver: data?.value?.fw_ver, errorStatus: false});
      })
      .catch(error => {
        this.setState({errorStatus: true});
        console.log('getSinglePropertyerror111', JSON.stringify(error));
      }).finally(() => {
        this.loading1 = false;
        if(!this.loading1 && !this.loading2) {
          showLoading(false);
          this.setState({
            endLoading: false
          })
        }
      });
  };

  startPolling = () => {
    const intervalId = setInterval(() => {
      this.getFirmwareUpdateInfo();
    }, 2000);
    this.setState({pollingIntervalId: intervalId});
  };

  stopPolling = () => {
    if (this.state.pollingIntervalId) {
      clearInterval(this.state.pollingIntervalId);
      this.setState({pollingIntervalId: null});
    }
  };

  //   获取设备是否需要升级
  getFirmwareInfo() {
    const params = {
      Path: 'v1.0/imilab-01/ota/getUpgradeFirmware',
      ParamMap: {
        iotId: LetDevice.deviceID,
      },
      Method: 'GET',
    };
    this.loading2 = true;
    showLoading(stringsTo('commLoadingText'), true);
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        if (item.length > 0) {
          console.log('item==123==', item);
          this.setState({firmwareInfo: customJsonParse(item[0])});
          console.log('item==123==', this.state.firmwareInfo?.copywriting);
          if (
            this.state.firmwareInfo?.status !== 0 &&
            this.state.firmwareInfo?.status !== 6 &&
            this.state.firmwareInfo?.status !== 7 &&
            this.state.firmwareInfo?.status !== 8
          ) {
            this.getFirmwareUpdateInfo();
          } else {
            if (this.isFirst && this.props.route.params.type === 1) {
              this.setFirmwareUpdateInfo();
            }
          }
        }
        this.isFirst = false;
      })
      .catch(e => {
        console.log(JSON.stringify(e));
      })
      .finally(() => {
        
        this.loading2 = false;
        if(!this.loading1 && !this.loading2) {
          showLoading(false);
          this.setState({
            endLoading: false
          })
        }
      });
  }

  //   升级指令
  setFirmwareUpdateInfo() {
    if (this.uploading) {
      console.log('正在升级中---');
      return;
    }
    this.uploading = true;
    const params = {
      Path: '/v1.0/imilab-01/ota/upgradeConfirm',
      ParamMap: {
        iotId: LetDevice.deviceID,
        channel: this.state.firmwareInfo?.channel,
        versionName: this.state.firmwareInfo?.versionName,
      },
      Method: 'POST',
    };
    showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params)
      .then(item => {
        console.log('item --==123==', item);
        this.startPolling();
        let info = this.state.firmwareInfo;
        info.status = 1;
        this.setState({firmwareInfo: info});
      })
      .catch(e => {
        console.log('err --item==123==', e);
        console.log(JSON.stringify(e));
      })
      .finally(() => {
        showLoading(false);
        this.uploading = false;
      });
  }
  // 循环获取升级信息
  getFirmwareUpdateInfo() {
    const params = {
      Path: '/v1.0/imilab-01/ota/getProgress',
      ParamMap: {
        iotId: LetDevice.deviceID,
        channel: JSON.stringify(this.state.firmwareInfo?.channel),
        versionName: this.state.firmwareInfo?.versionName,
      },
      Method: 'GET',
    };
    // showLoading(stringsTo('commWaitText'), true);
    LetIMIIotRequest.sendUserServerRequest(params, true)
      .then(item => {
        console.log('res item==123==---', this.state.pollingIntervalId);
        if (
          !this.state.pollingIntervalId &&
          item.status !== 0 &&
          item.status !== 5 &&
          item.status !== 6 &&
          item.status !== 7 &&
          item.status !== 8
        ) {
          console.log('res item==123==', this.state.pollingIntervalId);
          this.startPolling();
        }
        if (this.state.pollingIntervalId) {
          const lastestVersion = this.state.firmwareInfo?.versionName;
          this.setState({firmwareInfo: item});
          if (item.status === 0 || item.status === 5 || item.status === 6 || item.status === 7 || item.status === 8) {
            if (item.status === 5) {
              this.setState({
                fw_ver: lastestVersion,
              });
              this.getVersion();
            }
            this.stopPolling();
          }
        }
      })
      .catch(e => {
        console.log('err item==123==', e);
        console.log(JSON.stringify(e));
      })
      .finally(() => {
        // showLoading(false);
      });
  }
  render() {
    global.navigation = this.props.navigation;

    return (
      <View style={styles.container}>
        <NavigationBar
          title={I18n.t('check_update')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'housekeeping_assistant_back',
            },
          ]}
          right={[]}
        />

        {/*看家开关*/}
        <View style={{...styles.container1, display: this.state.endLoading ? 'none' : ''}}>
          {this.renderFirmLatestUI()}
          {this.renderFirmUpdateUI()}
          {this.renderFirmUpdatingUI()}
          {this.renderFirmUpdateFailUI()}
          {/* {this.renderFirmUpdateSuccessUI()} */}
        </View>
        <View style={{height: '10%'}} />
      </View>
    );
  }
  //   展示当前为最新版本
  renderFirmLatestUI() {
    if (Object.keys(this.state.firmwareInfo).length === 0 || this.state.firmwareInfo?.status === 5) {
      return (
        <View style={{alignItems: 'center', justifyContent: 'center', height: '100%'}}>
          <Image
            source={
              this.state.firmwareInfo?.status === 5
                ? require('../../resources/images/houseKeepingV2/uploadFinish.png')
                : require('../../resources/images/houseKeepingV2/upgradeSuccess.png')
            }
            style={{height: 60, resizeMode: 'contain'}}
          />
          {this.state.firmwareInfo?.status === 5 && (
            <Text style={{...styles.modalSubTit, marginTop: 20, marginBottom: 0}}>
              {I18n.t('upload_grade_success')}
            </Text>
          )}
          {!this.state.errorStatus && <Text style={styles.modalSubTit}>{I18n.t('list_item_curr_version') + ':' + this.state.fw_ver}</Text>}
          {!this.state.errorStatus && <Text style={{...styles.modalTit}}>{I18n.t('list_item_latest_version_now')}</Text>}
          {this.state.errorStatus && this.state.firmwareInfo?.status !== 5 && <Text style={{...styles.modalTit}}>{I18n.t('getError')}</Text>}
          {/* <Text style={styles.titleStyle}>{stringsTo('list_item_latest_version_now')}</Text>
          <Text style={styles.subTitleStyle}>{stringsTo('list_item_curr_version') + '：' + this.state.fw_ver}</Text> */}
        </View>
      );
    }
  }
  //   展示升级页面
  renderFirmUpdateUI() {
    if (this.state.firmwareInfo?.status !== 0) {
      return;
    }
    return (
      <View style={{height: '100%'}}>
        {/* <Text style={styles.modalTit}>
          {I18n.t('upgrade_dialog_title', {code: this.state.firmwareInfo?.versionName})}
        </Text> */}
        <View style={{height: '10%'}} />
        {this.renderUpdateLog()}
        <View style={{alignItems: 'center', marginTop: 0}}>
          <RoundedButtonView
            buttonText={stringsTo('upgrade_button')}
            buttonStyle={[
              {
                width: width - 40,
                backgroundColor: '#12AA9C',
                borderRadius: 8,
                margin: 14,
              },
            ]}
            buttonTextStyle={[{color: '#FFFFFFE5'}]}
            onPress={() => {
              this.setFirmwareUpdateInfo();
            }}
          />
        </View>
      </View>
    );
  }

  //   展示升级中页面
  renderFirmUpdatingUI() {
    if (
      this.state.firmwareInfo?.status === 0 ||
      this.state.firmwareInfo?.status === 6 ||
      this.state.firmwareInfo?.status === 5 ||
      this.state.firmwareInfo?.status === 7 ||
      this.state.firmwareInfo?.status === 8 ||
      !this.state.firmwareInfo?.status
    ) {
      return;
    }
    return (
      <View>
        <View style={{alignItems: 'center'}}>
          <Text style={styles.uploadTit} />
          <CircleProgress
            size={90}
            progress={this.state.firmwareInfo?.percentage ?? 0}
            strokeWidth={4}
            strokeColor="#12AA9C"
            backgroundColor="#e5e5e5"
            text={
              this.state.firmwareInfo?.status !== 4
                ? `${this.state.firmwareInfo?.percentage || 0}%`
                : stringsTo('list_item_version_status_4')
            }
            fontSize={this.state.firmwareInfo?.status !== 4 ? 20 : 18}
          />
          <View>
            <Text style={{...styles.modalTit, marginTop: 14}}>
              {stringsTo('list_item_latest_version_uploading_title')}
            </Text>
            <Text style={styles.modalTittip}>
              {this.state.firmwareInfo?.status !== 4
                ? stringsTo('list_item_latest_version_uploading')
                : stringsTo('list_item_latest_version_upload_finish')}
            </Text>
          </View>
        </View>

        {this.renderUpdateLog()}
      </View>
    );
  }
  //   展示升级失败页面
  renderFirmUpdateFailUI() {
    if (this.state.firmwareInfo?.status === 6 || this.state.firmwareInfo?.status === 8) {
      return (
        <View>
          <View style={{alignItems: 'center', justifyContent: 'center', marginTop: '30%'}}>
            <Image
              source={require('../../resources/images/houseKeepingV2/uploadError.png')}
              style={{width: 60, height: 60}}
            />
            <Text style={{...styles.modalSubTit, marginTop: 20, marginBottom: 0, color: 'red'}}>
              {this.state.firmwareInfo?.status === 8 ? I18n.t('upload_grade_timeout') : I18n.t('upload_grade_error')}
            </Text>
            <Text style={styles.modalSubTit}>
              {I18n.t('list_item_curr_version') + ':' + this.state.firmwareInfo?.currentVersionName}
            </Text>
            <Text style={styles.modalTit}>
              {I18n.t('list_item_latest_version') + ':' + this.state.firmwareInfo?.versionName}
            </Text>
          </View>
          {this.renderUpdateLog()}
        </View>
      );
    }
  }
  //   展示升级成功页面
  // renderFirmUpdateSuccessUI() {
  //   if (this.state.firmwareInfo?.status === 5) {
  //     return (
  //       this.renderFirmLatestUI()
  //       // <View>
  //       //   <View style={{height: '60%'}} />
  //       //   <View style={{alignItems: 'center'}}>
  //       //     <RoundedButtonView
  //       //       buttonText={stringsTo('light_blue_orange_mix_flash_success')}
  //       //       buttonStyle={[
  //       //         {
  //       //           width: 200,
  //       //           backgroundColor: '#4A70A5',
  //       //           margin: 14,
  //       //         },
  //       //       ]}
  //       //       buttonTextStyle={[{color: '#FFFFFFE5'}]}
  //       //       onPress={() => {
  //       //         this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
  //       //       }}
  //       //     />
  //       //   </View>
  //       // </View>
  //     );
  //   }
  // }
  // 更新日志
  renderUpdateLog() {
    const showImg = this.state.firmwareInfo.status === 0;
    return (
      <View style={{height: '78%'}}>
        <View style={{height: showImg ? 200 : 50, textAlign: 'center', alignItems: 'center'}}>
          {showImg && (
            <>
              <Image
                source={require('../../resources/images/houseKeepingV2/upgrade.png')}
                style={{width: 60, height: 60}}
              />
              <Text style={styles.modalSubTit}>
                {I18n.t('list_item_curr_version') + ':' + this.state.firmwareInfo?.currentVersionName}
              </Text>
              <Text style={styles.modalTit}>
                {I18n.t('list_item_latest_version') + ':' + this.state.firmwareInfo?.versionName}
              </Text>
            </>
          )}
        </View>
        <ScrollView style={{paddingLeft: 14}}>
          <Text style={styles.titleStyle}>{I18n.t('list_item_latest_version_log')}</Text>
          <Text style={styles.functionSettingStyle}>{this.state.firmwareInfo?.copywriting}</Text>
        </ScrollView>
      </View>
    );
  }
  //重写安卓物理返回键
  onBackAndroid() {
    if (this.state.showCalibrationModal) {
      this.setState({showCalibrationModal: false});
      return true;
    }
    return super.onBackAndroid();
  }

  getFirmwareStatus(status) {
    return stringsTo('list_item_version_status_' + status);
    //   if (status === 0) {
    //     return stringsTo('list_item_version_status_' + status);
    //   } else if (status === 1) {
    //     return '已下发APP消息给用户';
    //   } else if (status === 2) {
    //     return '已下发升级消息给设备';
    //   } else if (status === 3) {
    //     return '下载中';
    //   } else if (status === 4) {
    //     return '安装中';
    //   } else if (status === 5) {
    //     return '升级完成';
    //   } else if (status === 6) {
    //     return '升级失败';
    //   } else if (status === 7) {
    //     return '已取消';
    //   } else if (status === 8) {
    //     return '升级超时';
    //   }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  container1: {
    flex: 1,
    // justifyContent: 'center',
    // alignItems: 'center',
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  titleStyle: {
    fontSize: 16,
    color: '#333333',
    lineHeight: 30,
    paddingLeft: 14,
    fontWeight: 'bold',
  },
  functionSettingStyle: {
    color: '#666666',
    fontSize: 14,
    paddingLeft: 14,
    fontWeight: 'bold',
  },
  uploadTit: {
    fontSize: 65,
    color: '#4A70A5',
    lineHeight: 120,
    paddingLeft: 14,
    fontWeight: 'bold',
    justifyContent: 'center',
    textAlign: 'center',
  },
  modalTit: {
    fontSize: 16,
    color: '#000',
    lineHeight: 30,
    // paddingLeft: 14,
    fontWeight: 'bold',
    justifyContent: 'center',
    textAlign: 'center',
    marginTop: 6,
  },
  modalSubTit: {
    fontSize: 16,
    color: '#12AA9C',
    lineHeight: 30,
    // paddingLeft: 14,
    fontWeight: 'bold',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    marginTop: 14,
  },
  modalTittip: {
    fontSize: 12,
    color: '#7f7f7f',
    marginTop: 14,
  },
  distanceModalTit: {
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333333',
    // backgroundColor:'green',
    marginTop: 16,
  },
  distanceModalSubTit: {
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
    fontSize: 12,
    color: '#7F7F7F',
    // backgroundColor:'red',
    marginTop: -10,
    marginBottom: 14,
  },
  circleContainer: {
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  circleBackground: {
    width: 80,
    height: 80,
    borderWidth: 4,
    borderColor: 'gray',
    borderRadius: 40,
  },
  circleProgress: {
    // borderLeftColor will be the progress color
  },
  animatedPercentage: {
    marginTop: 10,
    fontSize: 18,
  },
  card: {
    backgroundColor: 'white',
    padding: 50,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
});
