import React from 'react';
import {
  ScrollView,
  View,
  Text,
  Image,
  Dimensions,
  StyleSheet,
} from 'react-native';

import {imiThemeManager} from '../../../../imilab-design-ui';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import I18n from '../../../../globalization/Localize';
import BaseDeviceComponent from '../../../../imilab-rn-sdk/components/BaseDeviceComponent';
import {LetDevice} from '../../../../imilab-rn-sdk/native/iot-kit/IMIDevice';
import {LetIMIIotRequest, IMIGotoPage} from '../../../../imilab-rn-sdk';
import {showToast, showLoading} from '../../../../imilab-design-ui';
import {stringsTo} from '../../../../globalization/Localize';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {CAMERA_CONTROL} from "../../constants/Spec";
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';

const ScreenOptions = Dimensions.get('window');
const viewWidth = ScreenOptions.width - 24 * 2;
const viewHeight = viewWidth * 9 / 16;
// 将直播流播放器分成8*4个矩形块
const itemWidth = viewWidth / 8;
const itemHeight = viewHeight / 4;
const REACT_MARGIN = 3; // 矩形线框到画面边缘的外边距

const tag = 'DetectionAreaSetting';

export default class DetectionAreaSetting extends BaseDeviceComponent {
  constructor(props) {
    super(props);
    this.state = {
      // 检测区域相关状态
      detectionAreaSwitch: false, // 区域检测开关（总开关）UI状态
      detectionAreaActualEnabled: false, // 真实设置状态，用于控制子组件显示
      detectionAreaDisplay: false, // 检测区域直播展示开关
      detectionAreaData: null, // 检测区域坐标数据

      // UI状态
      areaType: 0, // 区域样式类型 0-3
      showAIDialog: false,

      // 其他AI功能状态（用于提醒冲突）
      fenceSwitch: false,
      peopleSwitch: false,
      motionSwitch: false,
    };
    this.cameraNumber = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber || '1';

    // 矩形线框的左上角和右下键的x、y坐标轴
    this.rectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN];

    // 双摄像头支持：为sensor0(球机)和sensor1(枪机)分别存储坐标数据
    if (this.cameraNumber === '2') {
      this.sensor0RectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]; // 球机区域坐标
      this.sensor1RectDatas = [REACT_MARGIN, REACT_MARGIN, viewWidth - REACT_MARGIN, viewHeight - REACT_MARGIN]; // 枪机区域坐标
      console.log('初始化双摄像头检测区域支持');
    }
  }

  componentDidMount() {
    this._getAllData();
    this._subscribeFocus = this.props.navigation.addListener('focus', () => {
      // 从编辑页面返回时刷新数据
      this._getAllData();
    });
  }

  componentWillUnmount() {
    this._subscribeFocus && this._subscribeFocus();
    showLoading(false);
  }

  // 获取检测区域相关数据
  async _getAllData() {
    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log('开始获取检测区域设置数据...');

      // 使用Promise.allSettled等待所有请求完成
      const promises = [
        LetDevice.getSingleProperty(CAMERA_CONTROL.DETECTION_AREA_SWITCH.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.DETECTION_AREA_DISPLAY_SWITCH.PIID),
        LetDevice.getSingleProperty(CAMERA_CONTROL.DETECTION_AREA_ATTR.PIID),
      ];

      const results = await Promise.allSettled(promises);

      let newStates = {
        detectionAreaSwitch: false,
        detectionAreaActualEnabled: false,
        detectionAreaDisplay: false,
        detectionAreaData: null,
        areaType: 0,
        motionSwitch: false,
        peopleSwitch: false,
        fenceSwitch: false
      };

      // 处理区域检测开关 (100008) - 主开关
      if (results[0].status === 'fulfilled') {
        const data = results[0].value;
        console.log('区域检测开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          newStates.detectionAreaSwitch = isEnabled;
          newStates.detectionAreaActualEnabled = isEnabled; // 同时设置真实状态
          console.log('区域检测开关:', data.value.value);
        }
      } else {
        console.error('获取区域检测开关失败:', results[0].reason);
      }

      // 处理检测区域直播展示开关 (100009)
      if (results[1].status === 'fulfilled') {
        const data = results[1].value;
        console.log('检测区域直播展示开关--------' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const isEnabled = data.value.value || false;
          newStates.detectionAreaDisplay = isEnabled;
          console.log('检测区域直播展示开关:', data.value.value);
        }
      } else {
        console.error('获取检测区域直播展示开关失败:', results[1].reason);
      }

      // 处理检测区域坐标数据 (100010)
      if (results[2].status === 'fulfilled') {
        const data = results[2].value;
        console.log('检测区域坐标数据--------' + JSON.stringify(data));
        if (data?.value?.code == 0 && data.value.value) {
          newStates.detectionAreaData = data.value.value;
          console.log('检测区域坐标数据:', data.value.value);
        }
      } else {
        console.error('获取检测区域坐标数据失败:', results[2].reason);
      }

      // 检查是否有请求失败
      const hasFailures = results.some(result => result.status === 'rejected');
      if (hasFailures) {
        console.error('部分检测区域设置获取失败');
        showToast(stringsTo('commLoadingFailText'));
      }

      this.setState(newStates, () => {
        // 处理检测区域数据
        if (this.state.detectionAreaData) {
          // 解析检测区域数据，提取pos字符串
          try {
            const parsedData = JSON.parse(this.state.detectionAreaData);
            console.log('解析检测区域数据:', parsedData);

            if (this.cameraNumber === '2') {
              // 双摄像头设备：分别解析sensor0(球机)和sensor1(枪机)的数据
              // 数据结构：detect_area[0].sensor0 和 detect_area[1].sensor1
              const sensor0Data = parsedData?.detect_area?.[0]?.sensor0?.[0]?.area?.[0]?.pos;
              const sensor1Data = parsedData?.detect_area?.[1]?.sensor1?.[0]?.area?.[0]?.pos;

              if (sensor0Data && Array.isArray(sensor0Data) && sensor0Data.length === 4) {
                const coordsString = `[${sensor0Data[0]},${sensor0Data[1]}],[${sensor0Data[2]},${sensor0Data[3]}]`;
                this._parseAreaDataSpecValue(coordsString, 0); // 解析球机区域坐标
                console.log('解析球机区域坐标:', sensor0Data);
              } else {
                console.log('球机区域数据无效或不存在，使用默认值');
              }

              if (sensor1Data && Array.isArray(sensor1Data) && sensor1Data.length === 4) {
                const coordsString = `[${sensor1Data[0]},${sensor1Data[1]}],[${sensor1Data[2]},${sensor1Data[3]}]`;
                this._parseAreaDataSpecValue(coordsString, 1); // 解析枪机区域坐标
                console.log('解析枪机区域坐标:', sensor1Data);
              } else {
                console.log('枪机区域数据无效或不存在，使用默认值');
              }
            } else {
              // 单摄像头设备：使用原有逻辑
              const areaData = parsedData?.detect_area?.[0]?.sensor0?.[0]?.area?.[0]?.pos;
              if (areaData && Array.isArray(areaData) && areaData.length === 4) {
                const coordsString = `[${areaData[0]},${areaData[1]}],[${areaData[2]},${areaData[3]}]`;
                this._parseAreaDataSpecValue(coordsString); // 解析检测区域坐标
              } else {
                console.log('单摄像头区域数据无效或不存在，使用默认值');
              }
            }
          } catch (error) {
            console.error('解析检测区域数据失败:', error);
          }
        }
      });

      console.log('检测区域设置数据加载完成');

    } catch (error) {
      console.error('获取检测区域设置失败:', error);
      showToast(stringsTo('commLoadingFailText'));
      this.setState({
        detectionAreaSwitch: false,
        detectionAreaActualEnabled: false,
        detectionAreaDisplay: false
      });
    } finally {
      showLoading(false);
    }
  }



  // 解析Spec协议得出用户框定的线框的左上右下坐标值，并存入rectDatas刷新UI
  _parseAreaDataSpecValue(coordsArrayString, sensorType = null) { // "[0,0],[94,100]"
    let coordsStringArray = coordsArrayString.replace("],", "]_").split('_');// ["[0,0]","[94,100]"]
    let coordsArray = [...JSON.parse(coordsStringArray[0]), ...JSON.parse(coordsStringArray[1])];
    coordsArray = [coordsArray[0] / 100.0 * viewWidth, coordsArray[1] / 100.0 * viewHeight,
      coordsArray[2] / 100.0 * viewWidth, coordsArray[3] / 100.0 * viewHeight];

    // 尝试修正设置spec时转为百分比带来的误差
    coordsArray[0] < REACT_MARGIN ? coordsArray[0] = REACT_MARGIN : null;
    coordsArray[1] < REACT_MARGIN ? coordsArray[1] = REACT_MARGIN : coordsArray[1] - 1.5;
    coordsArray[2] > viewWidth - REACT_MARGIN ? coordsArray[2] = viewWidth - REACT_MARGIN : coordsArray[2] = coordsArray[2] - 2;
    coordsArray[3] > viewHeight - REACT_MARGIN ? coordsArray[3] = viewHeight - REACT_MARGIN : coordsArray[3] = coordsArray[3] - 1.5;

    // 根据传感器类型存储到对应的rectDatas
    if (this.cameraNumber === '2' && sensorType !== null) {
      if (sensorType === 0) {
        this.sensor0RectDatas = coordsArray; // 球机区域
      } else if (sensorType === 1) {
        this.sensor1RectDatas = coordsArray; // 枪机区域
      }
    } else {
      this.rectDatas = coordsArray; // 单摄像头或兼容模式
    }
  }



  // 检测区域开关变更
  _onDetectionAreaSwitchChange(value) {
    // 检查是否与其他AI功能冲突
    if (value && (this.state.fenceSwitch || this.state.peopleSwitch || this.state.motionSwitch)) {
      this.setState({ showAIDialog: true });
      return;
    }

    this._setDetectionAreaProperty(value);
  }

  // 设置区域检测开关的通用函数
  async _setDetectionAreaProperty(value) {
    // 立即更新UI状态，给用户即时反馈
    this.setState({ detectionAreaSwitch: value });

    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log('设置区域检测开关:', value);

      const paramJson = JSON.stringify({ msg_id: CAMERA_CONTROL.DETECTION_AREA_SWITCH.PIID, value: value });

      await LetDevice.setProperties(true, LetDevice.deviceID, CAMERA_CONTROL.DETECTION_AREA_SWITCH.PIID, paramJson);

      // 设置成功后更新真实状态，控制子组件显示
      this.setState({ detectionAreaActualEnabled: value });
      // 开关状态改变时调用回调函数
      if (this.props.route.params?.callback) {
        this.props.route.params.callback(value);
      }
      showToast(stringsTo('settings_set_success'));
      console.log('区域检测开关设置成功:', value);

    } catch (error) {
      console.error('设置区域检测开关失败:', error);
      showToast(stringsTo('operationFailed'));
      // 设置失败时恢复到原来的状态
      this.setState({ detectionAreaSwitch: !value });
    } finally {
      showLoading(false);
    }
  }

  // 检测区域直播展示开关变更
  _onDetectionAreaDisplayChange(value) {
    this._setDetectionAreaDisplayProperty(value);
  }

  // 设置检测区域直播展示开关的通用函数
  async _setDetectionAreaDisplayProperty(value) {
    // 立即更新UI状态，给用户即时反馈
    this.setState({ detectionAreaDisplay: value });

    try {
      showLoading(stringsTo('commWaitText'), true);
      console.log('设置检测区域直播展示开关:', value);

      const paramJson = JSON.stringify({ msg_id: CAMERA_CONTROL.DETECTION_AREA_DISPLAY_SWITCH.PIID, value: value });

      await LetDevice.setProperties(true, LetDevice.deviceID, CAMERA_CONTROL.DETECTION_AREA_DISPLAY_SWITCH.PIID, paramJson);

      showToast(stringsTo('settings_set_success'));
      console.log('检测区域直播展示开关设置成功:', value);

    } catch (error) {
      console.error('设置检测区域直播展示开关失败:', error);
      showToast(stringsTo('operationFailed'));
      // 设置失败时恢复到原来的状态
      this.setState({ detectionAreaDisplay: !value });
    } finally {
      showLoading(false);
    }
  }

  // 从区域编辑页面返回的回调 - 参考SmartPrivateSetting.js的实现
  _refreshEffectiveMonitorArea(rectangleCoords, areaType, hideArea, sensorType = null) {
    if (this.cameraNumber === '2' && sensorType !== null) {
      // 双摄像头设备：根据sensorType更新对应的区域数据
      if (sensorType === 0) {
        this.sensor0RectDatas = rectangleCoords; // 更新球机区域
        console.log('更新球机区域坐标:', rectangleCoords);
      } else if (sensorType === 1) {
        this.sensor1RectDatas = rectangleCoords; // 更新枪机区域
        console.log('更新枪机区域坐标:', rectangleCoords);
      }
    } else {
      // 单摄像头设备：使用原有逻辑
      this.rectDatas = rectangleCoords;
      console.log('更新检测区域坐标:', rectangleCoords);
    }

    // hideArea参数预留，用于后续扩展
    console.log('区域编辑回调:', { rectangleCoords, areaType, hideArea, sensorType });
  }

  render() {
    return (
      <View style={styles.container}>
        <NavigationBar
          title={stringsTo('detection_area')}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                // 返回时调用回调函数，传递检测区域开关状态
                if (this.props.route.params?.callback) {
                  this.props.route.params.callback(this.state.detectionAreaSwitch);
                }
                this.props.navigation.canGoBack() ? this.props.navigation.goBack() : IMIGotoPage.exit();
              },
              accessibilityLabel: 'detection_area_back',
            },
          ]}
          right={[]}
        />

        <ScrollView showsVerticalScrollIndicator={false}>
          {/* 第一行：区域检测开关（总开关） */}
          <ListItmeWithSwitch
            title={stringsTo('detection_area')}
            value={this.state.detectionAreaSwitch}
            onValueChange={(value) => this._onDetectionAreaSwitchChange(value)}
            accessibilityLabel={['DETECTION_AREA_SWITCH_on', 'DETECTION_AREA_SWITCH_off']}
          />

          {/* 第二行：检测区域编辑（只有总开关开启时才显示） */}
          {this.state.detectionAreaActualEnabled ? (
            this.cameraNumber === '2' ? (
              // 双摄像头设备：显示两个入口（枪机在前，球机在后）
              <>
                <ListItem
                  title={stringsTo('dual_camera_gun_area')}
                  subtitle={stringsTo('default_gun_area_alarm')}
                  value={stringsTo('go_draw')}
                  onPress={() => {
                    this.props.navigation.push('DetectionAreaModifyPage', {
                      areaData: [...this.sensor1RectDatas], // 枪机区域坐标
                      cameraNumber: this.cameraNumber,
                      areaType: 1, // 1代表sensor1(枪机)
                      callback: (areaDataNew, type, hideArea) => this._refreshEffectiveMonitorArea([...areaDataNew], type, hideArea, 1)
                    });
                  }}
                  accessibilityLabel={'detection_area_edit_sensor1'}
                />
                <ListItem
                  title={stringsTo('dual_camera_ptz_area')}
                  subtitle={stringsTo('default_ptz_area_alarm')}
                  value={stringsTo('go_draw')}
                  onPress={() => {
                    this.props.navigation.push('DetectionAreaModifyPage', {
                      areaData: [...this.sensor0RectDatas], // 球机区域坐标
                      cameraNumber: this.cameraNumber,
                      areaType: 0, // 0代表sensor0(球机)
                      callback: (areaDataNew, type, hideArea) => this._refreshEffectiveMonitorArea([...areaDataNew], type, hideArea, 0)
                    });
                  }}
                  accessibilityLabel={'detection_area_edit_sensor0'}
                />
              </>
            ) : (
              // 单摄像头设备：显示原有入口
              <ListItem
                title={stringsTo('detection_area_edit')}
                value={stringsTo('go_draw')}
                onPress={() => {
                  this.props.navigation.push('DetectionAreaModifyPage', {
                    areaData: [...this.rectDatas], // 检测区域坐标
                    cameraNumber: this.cameraNumber,
                    areaType: 0, // 单摄像头写死传0
                    callback: (areaDataNew, type, hideArea) => this._refreshEffectiveMonitorArea([...areaDataNew], type, hideArea)
                  });
                }}
                accessibilityLabel={'detection_area_edit'}
              />
            )
          ) : null}

          {/* 第三行：检测区域显示控制开关（只有总开关开启时才显示） */}
          {this.state.detectionAreaActualEnabled ? (
            <ListItmeWithSwitch
              title={stringsTo('detection_area_display_control')}
              subtitle={stringsTo('detection_area_display_subtitle')}
              value={this.state.detectionAreaDisplay}
              onValueChange={(value) => this._onDetectionAreaDisplayChange(value)}
              accessibilityLabel={['DETECTION_AREA_DISPLAY_SWITCH_on', 'DETECTION_AREA_DISPLAY_SWITCH_off']}
            />
          ) : null}

          <View style={{ height: 40 }} />
        </ScrollView>

        {this._renderAIDialog()}
      </View>
    );
  }

  // AI功能冲突提醒对话框
  _renderAIDialog() {
    return (
      <MessageDialog
        visible={this.state.showAIDialog}
        title={stringsTo('tips')}
        canDismiss={true}
        onDismiss={() => {
          this.setState({ showAIDialog: false });
        }}
        buttons={[
          {
            text: I18n.t('ok_button'),
            callback: () => {
              this.setState({ showAIDialog: false });
            },
          },
        ]}>
        <Text style={styles.dialogText}>
          {stringsTo('ai_function_conflict_message')}
        </Text>
      </MessageDialog>
    );
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  dialogText: {
    fontSize: 14,
    color: imiThemeManager.theme.textColor,
    lineHeight: 20,
    textAlign: 'center',
  },
});
