
import React from 'react';
import {
    StyleSheet,
    View,
    ScrollView,
    Dimensions,
    BackHandler,
    TouchableWithoutFeedback,
    Text,
    Image,
    Platform,
    Picker
} from 'react-native';
import { imiThemeManager} from '../../../../imilab-design-ui'
import ChoiceItem from "../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItem"
import ListItem from "../../../../imilab-design-ui/src/widgets/settingUI/ListItem";
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {LetDevice} from "../../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import DatePicker from "../../../../imilab-design-ui/src/widgets/settingUI/DatePicker";
import {showToast} from "../../../../imilab-design-ui/src/widgets/IMIToast";
import {showLoading} from "../../../../imilab-design-ui/src/widgets/Loading";
import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import {isAndroid} from "../../../../imilab-rn-sdk/utils/Utils";
import MessageDialog from "../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog";
import DeviceTemplatesUtils from "../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";

const screen_width = Dimensions.get('window').width;


/**
 * 生效时间页面
 */

export default class EffectiveTimeSetPage extends BaseDeviceComponent {

    static propTypes = {};

    constructor(props, context) {
        super(props, context);
        this.state={
            isPickerVisiable: false,
            isStartPicker:true,
            startTimeAll:'00:00',
            endTimeAll:'08:00',
            selectIndex:0,//生效时间 默认全天
            customStr:stringsTo('voice_for_custom')+'（00:00-08:00）',
            typeStr:this.props.route.params.type,//从不同页面进入生效时间类型
            effectTime:this.props.route.params.effectTimeValue,//不同类型的生效时间
            spotLightData:'',//声光报警值
        }
    }

    UNSAFE_componentWillMount() {
        if (isAndroid()) {
            BackHandler.addEventListener('hardwareBackPress', this.onBackHandler);
        }
    }

    componentWillUnmount() {
        if (this.state.typeStr == 'lightEffectTime') {
            this.timer && clearTimeout(this.timer);
        }
        if (isAndroid()) {
            BackHandler.removeEventListener('hardwareBackPress',this.onBackHandler);
        }
    }

    componentDidMount() {
        this.getEffectTimeValue();
        if (this.state.typeStr == 'lightEffectTime'){
            // showLoading(stringsTo('commWaitText'), true);
            LetDevice.getPropertyCloud('SoundLightAlarm').then((value) => {
                // showLoading(false);
                // console.log('生效时间里获取声光报警值getPropertyCloud$$$$$$$$$$$$' + value);
                this.setState({
                    spotLightData: value
                });
            }).catch(error => {
                // showToast(I18n.t('commLoadingFailText'));
                // showLoading(false);
                console.log(JSON.stringify(error))
            });
        }
    }

    onBackHandler = () => {
        console.log('onBackAndroid  navigatiosn.isFocused() ' + navigation.isFocused());
        if (navigation.isFocused()) {
            this._onPressBack();
            return true;
        } else {
        }
        return false;
    };

    // 返回上一页
    _onPressBack = () => {
        if (this.state.typeStr == 'lightEffectTime') {
        // 声光报警
            if (this.props.route.params.callback) {
                this.props.route.params.callback('1');
            }
            this.props.navigation.pop();

        }else {
            // 设置全天 白天 夜晚 直接返回，设置自定义时间，设置完成之后再返回
            if (this.state.selectIndex == 3) {
                // console.log('选中自定义')
                if (this.props.route.params.callback) {
                    // console.log('选中自定义走callBack', this.state.customStr, this.state.effectTime)
                    this.props.route.params.callback(this.state.customStr, this.state.effectTime);
                }
                // console.log('选中自定义未设置值正常返回')
                this.props.navigation.pop();

            } else {
                if (this.props.route.params.callback) {
                    let tempStr = '';
                    let paramsData = '';
                    let tempData = '';

                    if (this.state.selectIndex == 0) {
                        tempStr = stringsTo('allDay_time');
                        paramsData = {
                            "start_time": "00:00",
                            "end_time": "23:59"
                        }
                    } else if (this.state.selectIndex == 1) {
                        tempStr = stringsTo('day_time');
                        paramsData = {
                            "start_time": "08:00",
                            "end_time": "20:00"
                        };
                    } else if (this.state.selectIndex == 2) {
                        tempStr = stringsTo('night_time');
                        paramsData = {
                            "start_time": "20:00",
                            "end_time": "08:00"
                        };
                    }
                    tempData = JSON.stringify(paramsData);
                    this.props.route.params.callback(tempStr, tempData);
                }
                this.props.navigation.pop();
            }
        }
    };



    getEffectTimeValue() {
        let stateProps = {};
        let tempStr = JSON.parse(this.state.effectTime) ;
        let startTimeStr = tempStr.start_time;
        let endTimeStr = tempStr.end_time;
        if ((startTimeStr == "00:00")&&(endTimeStr == "23:59")){
            stateProps.timeStr = stringsTo('allDay_time');
            // stateProps.startTimeAll = '00:00';
            // stateProps.endTimeAll = '23：59';
            stateProps.selectIndex = 0;
        }else if ((startTimeStr == "08:00")&&(endTimeStr == "20:00")){
            stateProps.timeStr = stringsTo('day_time');
            // stateProps.startTimeAll = '08:00';
            // stateProps.endTimeAll = '20:00';
            stateProps.selectIndex = 1;
        }else if ((startTimeStr == "20:00")&&(endTimeStr == "08:00")){
            stateProps.timeStr = stringsTo('night_time');
            // stateProps.startTimeAll = '20:00';
            // stateProps.endTimeAll = '08:00';
            stateProps.selectIndex = 2;
        }else {
            // stateProps.effectTimeStr = stringsTo('voice_for_custom')+stringsTo('allDay_time');
            stateProps.timeStr = stringsTo('voice_for_custom')+'('+startTimeStr+'-'+endTimeStr+')';
            stateProps.customStr = stringsTo('voice_for_custom')+'('+startTimeStr+'-'+endTimeStr+')';
            stateProps.startTimeAll = startTimeStr;
            stateProps.endTimeAll = endTimeStr;
            stateProps.selectIndex = 3;
        }
        // console.log('statprops--'+ JSON.stringify(data));
        // 统一设置从设备端获取的值
        this.setState(stateProps);
    }

    render(){
        let {showSynchronous056} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        // const params = navigation.state.params.props;
        global.navigation = this.props.navigation;
        return(<View style = {styles.container}>
            <NavigationBar
                type={NavigationBar.TYPE.LIGHT} backgroundColor={"transparent"}
                title={showSynchronous056?stringsTo('nursingTimeSetting'):I18n.t('effective_time')}
                left={[{key: NavigationBar.ICON.BACK, onPress: ()=>this._onPressBack()}]}
                right={[]}
            />

            <ScrollView showsVerticalScrollIndicator={false}>
                {/*{this._renderTimeList()}*/}
                <View style={{height:14,backgroundColor: "#F1F1F1"}} />
                {this._renderItemOne()}
                {this._renderItemTwo()}
                {this._renderItemThe()}
                <View style={{height:14,backgroundColor: "#F1F1F1"}} />
                {this._renderItemFour()}
                {this._renderShowTime()}
                {this._showDatePicker()}
                {/*{this._diyTimeModal()}*/}
            </ScrollView>
        </View>)
    }

    _renderItemOne() {
        return (
            <View style={{width: '100%',height: 60,backgroundColor: 'white'}}>
                <TouchableWithoutFeedback
                    onPress={_ =>{
                        console.log('0')
                        this._onSelectedItem(0)
                        // this.setState({nightMode:0},callback=>{
                        //     console.log('设置成功0')
                        //     // Utils.hide();
                        //     // Toast.success('c_set_success');
                        // })
                    }}
                >
                    <View style={{flexDirection:'column',height: 60,backgroundColor: 'white'}}>
                        <View
                            style={{flex:1,}
                            }>
                            <Text style={{marginLeft:14,lineHeight:58,width:screen_width-14*3-25,flexGrow:1,fontSize: 15,textAlign:'left',justifyContent: 'center',color:this.state.selectIndex == 0 ? "#4A70A5" : "#000000",}}>{stringsTo("allDay_time")}</Text>

                        </View>
                        <View style = {{flexDirection:'row',position:'absolute',flex:1,right:14}}>
                            <Image style={{marginTop:17.5,height:25,width:25,}}
                                   source={ this.state.selectIndex == 0 ? require('../../resources/images/icon_select_s.png') : null} />
                        </View>
                    </View>
                </TouchableWithoutFeedback>

            </View>
        )
    }

    _renderItemTwo() {
        return (
            <View style={{width: '100%',height: 60,backgroundColor: 'white'}}>
                <TouchableWithoutFeedback
                    onPress={_ =>{
                        console.log('1')
                        this._onSelectedItem(1)
                        // this.setState({nightMode:1},callback=>{
                        //     console.log('设置成功1')
                        //     // Utils.hide();
                        //     // Toast.success('c_set_success');
                        // })
                    }}
                >
                    <View style={{flexDirection:'column',height: 60,backgroundColor: 'white'}}>
                        <View
                            style={{flex:1,}
                            }>
                            <Text style={{marginLeft:14,lineHeight:58,width:screen_width-14*3-25,flexGrow:1,fontSize: 15,textAlign:'left',justifyContent: 'center',color:this.state.selectIndex == 1 ? "#4A70A5" : "#000000",}}>{stringsTo("day_time")}</Text>
                        </View>
                        <View style = {{flexDirection:'row',position:'absolute',flex:1,right:14}}>
                            <Image style={{marginTop:17.5,height:25,width:25,}}
                                   source={ this.state.selectIndex == 1 ? require('../../resources/images/icon_select_s.png') : null} />
                        </View>
                    </View>
                </TouchableWithoutFeedback>

            </View>
        )
    }

    _renderItemThe() {
        return (
            <View style={{width: '100%',height: 60,backgroundColor: 'white'}}>
                <TouchableWithoutFeedback
                    onPress={_ =>{
                        console.log('2')
                        this._onSelectedItem(2)
                        // this.setState({nightMode:2},callback=>{
                        //     console.log('设置成功2')
                        //     // Utils.hide();
                        //     // Toast.success('c_set_success');
                        // })
                    }}
                >
                    <View style={{flexDirection:'column',height: 60,backgroundColor: 'white'}}>
                        <View
                            style={{flex:1,}
                            }>
                            <Text style={{marginLeft:14,lineHeight:58,width:screen_width-14*3-25,flexGrow:1,fontSize: 15,textAlign:'left',justifyContent: 'center',color:this.state.selectIndex == 2 ? "#4A70A5" : "#000000",}}>{stringsTo("night_time")}</Text>
                        </View>
                        <View style = {{flexDirection:'row',position:'absolute',flex:1,right:14}}>
                            <Image style={{marginTop:17.5,height:25,width:25,}}
                                   source={ this.state.selectIndex == 2 ? require('../../resources/images/icon_select_s.png') : null} />
                        </View>
                    </View>
                </TouchableWithoutFeedback>

            </View>
        )
    }

    _renderItemFour() {
        return (
            <View style={{width: '100%',height: 60,backgroundColor: 'white'}}>
                <TouchableWithoutFeedback
                    onPress={_ =>{
                        console.log('3')
                        this.setState({selectIndex:3})
                         this._onSelectedItem(3)
                        // this.setState({nightMode:2},callback=>{
                        //     console.log('设置成功2')
                        //     // Utils.hide();
                        //     // Toast.success('c_set_success');
                        // })
                    }}
                >
                    <View style={{flexDirection:'column',height: 60,backgroundColor: 'white'}}>
                        <View
                            style={{flex:1,}
                            }>
                            <Text style={{marginLeft:14,lineHeight:58,width:screen_width-14*3-25,flexGrow:1,fontSize: 15,textAlign:'left',justifyContent: 'center',color:this.state.selectIndex == 3 ? "#4A70A5" : "#000000",}}>{this.state.customStr}</Text>
                        </View>
                        <View style = {{flexDirection:'row',position:'absolute',flex:1,right:14}}>
                            <Image style={{marginTop:17.5,height:25,width:25,}}
                                   source={ this.state.selectIndex == 3 ? require('../../resources/images/icon_select_s.png') : null} />
                        </View>
                    </View>
                </TouchableWithoutFeedback>

            </View>
        )
    }
    _renderShowTime() {
        return (
            <View style = {{flex:1}}>
                <ListItem titleStyle={{fontSize: 15, fontWeight:'normal',color:'#000000'}}
                          title={I18n.t("picker_start_time")}
                          hideArrow={false}
                          value={this.state.startTimeAll}
                          onPress={() => {
                              if (this.state.selectIndex != 3) {
                                  showToast(I18n.t("noDIYTimeTip"));
                                  return;
                              }
                              this.setState({isPickerVisiable: true, isStartPicker: true});
                          }}/>
                <ListItem titleStyle={{fontSize: 15, fontWeight:'normal',color:'#000000'}}
                          title={I18n.t("picker_end_time")}
                          hideArrow={false}
                          value={this.state.endTimeAll}
                          onPress={() => {
                              if (this.state.selectIndex != 3) {
                                  showToast(I18n.t("noDIYTimeTip"));
                                  return;
                              }
                              this.setState({isPickerVisiable: true, isStartPicker: false});
                          }}/>
            </View>
        )
    }
    _onSelectedItem(value) {
        console.log('夜视选中value--', value);
        this.setState({isPickerVisiable:false})
        if (this.state.typeStr == 'lightEffectTime'){
            showLoading(stringsTo('commWaitText'), true);
            // 声光报警设置生效时间
            let tempStr = JSON.parse(this.state.spotLightData);
            let tempStartTime;
            let tempEndTime;
            if (value == 0) {
                //全天
                tempStartTime = '00:00';
                tempEndTime = '23:59';
            } else if (value == 1) {
                // 白天
                tempStartTime = '08:00';
                tempEndTime = '20:00';
            } else if (value == 2) {
                // 夜晚
                tempStartTime = '20:00';
                tempEndTime = '08:00';
            }
            let paramsData = {"sound_light_alarm_switch":tempStr.sound_light_alarm_switch,
                // "only_people_detect_switch":tempStr.only_people_detect_switch,
                "only_people_detect_switch":8,
                "start_time":tempStartTime,
                "end_time":tempEndTime,
                "audio_path":tempStr.audio_path,
                "mode":tempStr.mode,
                "duration":tempStr.duration};
            let params = {SoundLightAlarm:JSON.stringify(paramsData)};

            // console.log('生效时间页面设置声光报警-',paramsData,JSON.stringify(params));
            LetDevice.setPropertyCloud(JSON.stringify(params)).then( ()=> {
                this.timer = setTimeout(
                    () => {
                        showToast(I18n.t('settings_set_success'));
                        showLoading(false);
                        this.setState({selectIndex: value});
                    },
                    1500
                );

            }).catch((error) => {
                console.log('失败----',error);
                showToast(I18n.t('waitFailedTip'));
                showLoading(false);
            });
        }else if (this.state.typeStr == 'FenceEffectTime') {
            if (value==3){
              let  paramsData = {
                    "start_time": '00:00',
                    "end_time": "08:00"
                }
                this.setState({selectIndex: value,effectTime:JSON.stringify(paramsData)});
            }else {
                this.setState({selectIndex: value});
            }
        }else {
            // 人形侦测  移动侦测,异响侦测设置
            showLoading(stringsTo('commWaitText'), true);
            let paramsData;
            if (value == 0) {
                paramsData = {
                    "start_time": '00:00',
                    "end_time": "23:59"
                }
            } else if (value == 1) {
                paramsData = {
                    "start_time": '08:00',
                    "end_time": "20:00"
                }
            } else if (value == 2) {
                paramsData = {
                    "start_time": '20:00',
                    "end_time": "08:00"
                }
            }else if (value == 3) {
                // 自定义默认
                paramsData = {
                    "start_time": '00:00',
                    "end_time": "08:00"
                }
            }

            let params;
            if (this.state.typeStr == 'peopleEffectTime') {
                params = {PeopleDetectionTime: JSON.stringify(paramsData)};
            } else if (this.state.typeStr == 'moveEffectTime') {
                params = {MoveDetectionTime: JSON.stringify(paramsData)};
            }else if (this.state.typeStr == 'loudEffectTime') {
                params = {LoudDetectionTime: JSON.stringify(paramsData)};
            }
            console.log('设置生效时间-hhhh--', JSON.stringify(params));

            LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
                showToast(I18n.t('settings_set_success'));
                showLoading(false);
                if (value==3){
                    this.setState({selectIndex: value,effectTime:JSON.stringify(paramsData)});
                }else {
                    this.setState({selectIndex: value});
                }
                // if (value===true){
                //     this.setState({peopleSwitchPushValue: true});
                // }else {
                //
                // }
            }).catch((error) => {
                console.log('失败----', error);
                showToast(I18n.t('waitFailedTip'));
                this.setState({selectIndex: !value});
                showLoading(false);
            });
        }
    }

    _showDatePicker() {
        return(<View>
            <DatePicker
                visible={this.state.isPickerVisiable}
                title={this.state.isStartPicker ? I18n.t("picker_start_time") : I18n.t("picker_end_time")}
                type={'time24'}
                onDismiss={_ => {}}
                // currentSelectTime={this.state.isStartPicker ? this.state.startTimeArr:this.state.endTimeArr}
                onSelectConfirm={time => {
                    if (this.state.isStartPicker){
                        this.setState({isPickerVisiable:false,startTimeAll:time.rawArray[0] + ':' + time.rawArray[1],},()=>{
                            console.log("当前开始的时间为-------",this.state.startTimeAll,time.rawArray,time.date);
                            this.uploadTime()
                        });
                    }else {
                        this.setState({isPickerVisiable:false,endTimeAll:time.rawArray[0] + ':' + time.rawArray[1],},()=>{
                            console.log("当前结束的时间为-------",this.state.endTimeAll,time.rawArray,time.date);
                            this.uploadTime()
                        });
                    }
                }} //["02", "02"], Tue Nov 24 2020 02:02:43 GMT+0800 (China Standard Time)
            />
        </View>)
    }

    uploadTime() {

        if (this.state.typeStr == 'lightEffectTime') {
            // 声光报警设置生效时间
            showLoading(stringsTo('commWaitText'), true);
            let tempStr = JSON.parse(this.state.spotLightData);
            let paramsData = {
                "sound_light_alarm_switch": tempStr.sound_light_alarm_switch,
                // "only_people_detect_switch": tempStr.only_people_detect_switch,
                "only_people_detect_switch":8,
                "start_time": this.state.startTimeAll,
                "end_time": this.state.endTimeAll,
                "audio_path": tempStr.audio_path,
                "mode": tempStr.mode,
                "duration": tempStr.duration
            };
            let params = {SoundLightAlarm: JSON.stringify(paramsData)};
            // console.log('自定义生效时间页面设置声光报警-', paramsData, JSON.stringify(params));
            LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
                // this.setState({selectIndex: value});
                this.timer = setTimeout(
                    () => {
                        showToast(I18n.t('settings_set_success'));
                        showLoading(false);
                        let tempStr = stringsTo('voice_for_custom') + '(' + this.state.startTimeAll + '-' + this.state.endTimeAll + ')';
                        this.setState({customStr: tempStr, effectTime: JSON.stringify(paramsData)})
                    },
                    1500
                );

            }).catch((error) => {
                console.log('失败----', error);
                showToast(I18n.t('waitFailedTip'));
                showLoading(false);
            });
        }else if (this.state.typeStr == 'FenceEffectTime') {
            let paramsData = {
                "start_time": this.state.startTimeAll,
                "end_time": this.state.endTimeAll
            }
            let tempStr = stringsTo('voice_for_custom') + '(' + this.state.startTimeAll + '-' + this.state.endTimeAll + ')';
            this.setState({customStr: tempStr, effectTime: JSON.stringify(paramsData)})
        }else {
            // 人形侦测 移动侦测
            showLoading(stringsTo('commWaitText'), true);
            let paramsData;
            paramsData = {
                "start_time": this.state.startTimeAll,
                "end_time": this.state.endTimeAll
            }

            let params;
            if (this.state.typeStr == 'peopleEffectTime') {
                params = {PeopleDetectionTime: JSON.stringify(paramsData)};
            } else if (this.state.typeStr == 'moveEffectTime') {
                params = {MoveDetectionTime: JSON.stringify(paramsData)};
            } else if (this.state.typeStr == 'loudEffectTime') {
                params = {LoudDetectionTime: JSON.stringify(paramsData)};
            }
            // console.log('设置生效时间-hhhh--', JSON.stringify(params));

            LetDevice.setPropertyCloud(JSON.stringify(params)).then(() => {
                showToast(I18n.t('settings_set_success'));
                showLoading(false);
                let tempStr = stringsTo('voice_for_custom') + '(' + this.state.startTimeAll + '-' + this.state.endTimeAll + ')';

                this.setState({customStr: tempStr, effectTime: JSON.stringify(paramsData)})
                // this.setState({selectIndex: value});
                // if (value===true){
                //     this.setState({peopleSwitchPushValue: true});
                // }else {
                //
                // }
            }).catch((error) => {
                console.log('失败----', error);
                showToast(I18n.t('waitFailedTip'));
                // this.setState({selectIndex: !value});
                showLoading(false);
            });
        }
    }

}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: imiThemeManager.theme.pageBg,
    },
    functionSettingStyle: {
        color: '#0000007F',
        fontSize: 12,
        marginTop: 23,
        marginLeft: 14
    },
    modalTit: {
        fontSize: 16,
        color: '#333333',
        lineHeight:60,
        alignItems:'center',
        borderTopLeftRadius:20,
        borderTopRightRadius:20,
        textAlign:'center',
        // backgroundColor: 'red',
        fontWeight:'bold',
        justifyContent:'center',
    },
    text: {
        width: 200,
        height: 40,
        marginTop: 10,
        backgroundColor: 'white',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 5,
    },
    pickerContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        backgroundColor: 'black'
    },
    pickerViewContainer: {
        flex: 1,
        flexDirection: 'row',
        paddingTop: 30
    },
    pickerStyle: {
        flex: 1,
    }
});

