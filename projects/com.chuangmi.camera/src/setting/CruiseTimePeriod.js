import {View, StyleSheet, ScrollView} from 'react-native';
import React, {useEffect, useState, useRef} from 'react';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import {IMIGotoPage, LetDevice} from '../../../../imilab-rn-sdk';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import MessageDialog from '../../../../imilab-design-ui/src/widgets/settingUI/MessageDialog';
import ChoiceItemNew from '../../../../imilab-design-ui/src/widgets/settingUI/ChoiceItemNew';
import CustomSpinner from './component/CustomSpinner';
import {showToast} from '../../../../imilab-design-ui/src/widgets/Loading';
import {stringsTo} from '../../../../globalization/Localize';

const CruiseTimePeriod = props => {
  // 重复选项 - 使用数组表示，下标0代表周日，1-6代表周一到周六
  const repeatOptions = [
    {key: '1', title: stringsTo('do_everyday'), value: [1, 1, 1, 1, 1, 1, 1]},
    {key: '2', title: stringsTo('workday'), value: [0, 1, 1, 1, 1, 1, 0]},
    {key: '3', title: stringsTo('do_weekend'), value: [1, 0, 0, 0, 0, 0, 1]},
    {key: '4', title: stringsTo('do_once'), value: [0, 0, 0, 0, 0, 0, 0]},
    {key: '5', title: stringsTo('do_custom'), value: 'custom'},
  ];

  // 自定义重复选项 - 重新排序，下标0代表周日
  const [customRepeatOptions, setCustomRepeatOptions] = useState([
    {key: '7', title: stringsTo('Sunday'), value: 0, checked: false},
    {key: '1', title: stringsTo('Monday'), value: 1, checked: false},
    {key: '2', title: stringsTo('Tuesday'), value: 2, checked: false},
    {key: '3', title: stringsTo('Wednesday'), value: 3, checked: false},
    {key: '4', title: stringsTo('Thursday'), value: 4, checked: false},
    {key: '5', title: stringsTo('Friday'), value: 5, checked: false},
    {key: '6', title: stringsTo('Saturday'), value: 6, checked: false},
  ]);

  // 状态变量
  const [isAllDay, setIsAllDay] = useState(true);
  const [selectedRepeat, setSelectedRepeat] = useState([0, 0, 0, 0, 0, 0, 0]); // 默认执行一次
  const [customRepeatValue, setCustomRepeatValue] = useState([0, 0, 0, 0, 0, 0, 0]);
  const [showRepeatDialog, setShowRepeatDialog] = useState(false);
  const [showCustomRepeatDialog, setShowCustomRepeatDialog] = useState(false);
  const [showStartTimeDialog, setShowStartTimeDialog] = useState(false);
  const [showEndTimeDialog, setShowEndTimeDialog] = useState(false);

  // 自定义时间
  const [startHour, setStartHour] = useState('08');
  const [startMinute, setStartMinute] = useState('00');
  const [endHour, setEndHour] = useState('18');
  const [endMinute, setEndMinute] = useState('00');

  // 小时和分钟数据
  const hourArr = Array.from({length: 24}, (_, i) => i.toString().padStart(2, '0'));
  const minuteArr = Array.from({length: 60}, (_, i) => i.toString().padStart(2, '0'));

  // 引用以跟踪当前选择的时间
  const hourRef = useRef(startHour);
  const minuteRef = useRef(startMinute);

  // 获取重复显示文本
  const getRepeatText = () => {
    if (Array.isArray(selectedRepeat)) {
      // 检查是否匹配预设选项
      const option = repeatOptions.find(
        item => Array.isArray(item.value) && JSON.stringify(item.value) === JSON.stringify(selectedRepeat),
      );

      if (option) {
        return option.title;
      } else {
        console.log('option', option);

        // 自定义选择
        return stringsTo('do_custom');
      }
    }
    return stringsTo('do_custom');
  };

  // 获取开始时间显示文本
  const getStartTimeText = () => {
    return `${startHour}:${startMinute}`;
  };

  // 获取结束时间显示文本
  const getEndTimeText = () => {
    return `${endHour}:${endMinute}`;
  };

  // 处理全天开关
  const handleAllDayToggle = () => {
    setIsAllDay(!isAllDay);
  };

  // 处理重复选项点击
  const handleRepeatItemClick = option => {
    if (option.value === 'custom') {
      // 如果选择自定义，打开自定义重复对话框
      setShowCustomRepeatDialog(true);
    } else {
      setSelectedRepeat(option.value);
    }
    setShowRepeatDialog(false);
  };
  // 处理重复选项点击
  const handleRepeatItemOnClick = (option, checked) => {
    if (option.value === 'custom' && checked) {
      // 如果选择自定义，打开自定义重复对话框
      setShowCustomRepeatDialog(true);
      setShowRepeatDialog(false);
    }
  };
  // 处理自定义重复选项变更
  const handleCustomRepeatChange = (checked, index) => {
    const newOptions = [...customRepeatOptions];
    newOptions[index].checked = checked;
    setCustomRepeatOptions(newOptions);
  };

  // 确认自定义重复选择
  const confirmCustomRepeat = () => {
    // 创建新的重复数组
    const newRepeatArray = [0, 0, 0, 0, 0, 0, 0];

    customRepeatOptions.forEach(option => {
      if (option.checked) {
        newRepeatArray[option.value] = 1;
      }
    });

    if (newRepeatArray.some(val => val === 1)) {
      setCustomRepeatValue(newRepeatArray);
      setSelectedRepeat(newRepeatArray);
    } else {
      // 如果没有选择任何天，默认设置为执行一次
      setSelectedRepeat([0, 0, 0, 0, 0, 0, 0]);
    }

    setShowCustomRepeatDialog(false);
  };

  // 处理小时变更
  const handleHourChange = (value, index) => {
    hourRef.current = value;
  };

  // 处理分钟变更
  const handleMinuteChange = (value, index) => {
    minuteRef.current = value;
  };

  // 确认开始时间选择
  const confirmStartTime = () => {
    const newStartHour = hourRef.current;
    const newStartMinute = minuteRef.current;

    setStartHour(newStartHour);
    setStartMinute(newStartMinute);

    // 检查结束时间是否需要调整
    const startTime = parseInt(newStartHour) * 60 + parseInt(newStartMinute);
    const endTime = parseInt(endHour) * 60 + parseInt(endMinute);

    if (endTime <= startTime) {
      // 如果结束时间早于或等于开始时间，设置为开始时间后一小时
      const newEndTime = startTime + 60;
      const newEndHour = Math.floor(newEndTime / 60) % 24;
      const newEndMinute = newEndTime % 60;

      setEndHour(newEndHour.toString().padStart(2, '0'));
      setEndMinute(newEndMinute.toString().padStart(2, '0'));
    }

    setShowStartTimeDialog(false);
  };

  // 确认结束时间选择
  const confirmEndTime = () => {
    const newEndHour = hourRef.current;
    const newEndMinute = minuteRef.current;

    // 验证结束时间必须大于开始时间
    const startTime = parseInt(startHour) * 60 + parseInt(startMinute);
    const endTime = parseInt(newEndHour) * 60 + parseInt(newEndMinute);

    if (endTime <= startTime) {
      // 如果结束时间早于或等于开始时间，设置为开始时间后一小时
      const newTime = startTime + 60;
      const adjustedHour = Math.floor(newTime / 60) % 24;
      const adjustedMinute = newTime % 60;

      setEndHour(adjustedHour.toString().padStart(2, '0'));
      setEndMinute(adjustedMinute.toString().padStart(2, '0'));
    } else {
      setEndHour(newEndHour);
      setEndMinute(newEndMinute);
    }

    setShowEndTimeDialog(false);
  };

  // 打开开始时间选择对话框
  const openStartTimeDialog = () => {
    hourRef.current = startHour;
    minuteRef.current = startMinute;
    setShowStartTimeDialog(true);
  };

  // 打开结束时间选择对话框
  const openEndTimeDialog = () => {
    hourRef.current = endHour;
    minuteRef.current = endMinute;
    setShowEndTimeDialog(true);
  };
  const getCruiseSpec = async () => {
    //重复日期
    LetDevice.getSingleProperty('10036')
      .then(data => {
        console.log('重复日期--------setCruiseEnabled' + JSON.stringify(data));
        if (data?.value?.code == 0) {
          const value = data.value.value ? JSON.parse(data.value.value) : [0, 0, 0, 0, 0, 0, 0];
          if (value?.length > 0) {
            const newList = [...customRepeatOptions];
            value.forEach((item, index) => {
              newList[index].checked = item === 1;
            });
            setCustomRepeatOptions(newList);
          }
          setSelectedRepeat(value);
        }
      })
      .catch(e => {
        console.log('error111111', e);
      });
    const dataList = await Promise.all([LetDevice.getSingleProperty('10034'), LetDevice.getSingleProperty('10035')]);
    if (dataList) {
      console.log(' 巡航开始结束日期--------setCruiseFrequency' + JSON.stringify(dataList));
      let startTime = '';
      let endTime = '';
      if (dataList[0]?.value?.code == 0) {
        const list = dataList[0].value.value?.split(':');
        startTime = list[0] + ':' + list[1];
        setStartHour(list[0]);
        setStartMinute(list[1]);
      }
      if (dataList[1]?.value?.code == 0) {
        const list = dataList[1].value.value?.split(':');
        endTime = list[0] + ':' + list[1];
        setEndHour(list[0]);
        setEndMinute(list[1]);
      }

      setIsAllDay(startTime === '00:00' && endTime === '23:59');
    }
  };

  const saveCruiseSpec = async () => {
    //重复日期
    const paramJson = JSON.stringify({msg_id: '10036', value: JSON.stringify(selectedRepeat)});
    await LetDevice.setProperties(true, LetDevice.deviceID, '10036', paramJson, true).catch(e => {
      showToast(stringsTo('operationFailed'));
    });
    //开始时间
    const paramJson1 = JSON.stringify({msg_id: '10034', value: isAllDay ? '00:00' : `${startHour}:${startMinute}`});
    await LetDevice.setProperties(true, LetDevice.deviceID, '10034', paramJson1, true).catch(e => {
      showToast(stringsTo('operationFailed'));
    });
    //结束时间
    const paramJson2 = JSON.stringify({msg_id: '10035', value: isAllDay ? '23:59' : `${endHour}:${endMinute}`});
    await LetDevice.setProperties(true, LetDevice.deviceID, '10035', paramJson2, true).catch(e => {
      showToast(stringsTo('operationFailed'));
    });
    showToast(stringsTo('settings_set_success'));
    props.navigation.canGoBack() ? props.navigation.goBack() : IMIGotoPage.exit();
  };

  useEffect(() => {
    getCruiseSpec();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <View style={styles.container}>
      <NavigationBar
        title={stringsTo('cruise_time_period')}
        left={[
          {
            key: NavigationBar.ICON.CUSTOM,
            n_source: require('../../resources/images/icon_cancel.png'),
            onPress: () => {
              props.navigation.canGoBack() ? props.navigation.goBack() : IMIGotoPage.exit();
            },
            accessibilityLabel: 'cruise_time_period',
          },
        ]}
        right={[
          {
            key: NavigationBar.ICON.COMPLETE,
            onPress: () => {
              saveCruiseSpec();
            },
            accessibilityLabel: 'cruise_time_period',
          },
        ]}
      />

      <ScrollView style={styles.scrollView}>
        <View style={styles.section}>
          {/* 全天选项 */}
          <ChoiceItemNew
            backgroundColor="#FFFFFF"
            title={stringsTo('do_everyday')}
            checked={isAllDay}
            selectIcon={require('../../resources/images/icon_select_choose.png')}
            unselectIcon={require('../../resources/images/icon_unselect_s.png')}
            titleColor={'#000000'}
            containerStyle={{paddingTop: 26, paddingBottom: 26, borderRadius: 16}}
            onValueChange={handleAllDayToggle}
          />
          <View style={styles.lineHight} />
          {/* 自定义时间段选项 */}
          <View style={styles.choiceItemLabel}>
            <ChoiceItemNew
              backgroundColor="#FFFFFF"
              titleColor={'#000000'}
              selectIcon={require('../../resources/images/icon_select_choose.png')}
              unselectIcon={require('../../resources/images/icon_unselect_s.png')}
              title={stringsTo('custom_time_period')}
              checked={!isAllDay}
              containerStyle={{paddingTop: 26, paddingBottom: 26, borderRadius: 0}}
              onValueChange={() => setIsAllDay(false)}
            />

            {/* 开始和结束时间选项 - 只有在自定义时间模式下才可点击 */}
            {!isAllDay ? (
              <>
                <ListItem
                  title={stringsTo('picker_start_time')}
                  value={getStartTimeText()}
                  onPress={!isAllDay ? openStartTimeDialog : null}
                  disabled={isAllDay}
                  valueStyle={{paddingRight: 20}}
                />
                <ListItem
                  title={stringsTo('picker_end_time')}
                  value={getEndTimeText()}
                  onPress={!isAllDay ? openEndTimeDialog : null}
                  disabled={isAllDay}
                  valueStyle={{paddingRight: 20}}
                />
              </>
            ) : null}
          </View>
          <View style={styles.lineHightBorder} />
          <View style={styles.choiceItemLabel}>
            <ListItem
              title={stringsTo('plug_timer_repeat')}
              valueStyle={{paddingRight: 20}}
              value={getRepeatText()}
              onPress={() => setShowRepeatDialog(true)}
            />
          </View>
        </View>
      </ScrollView>

      {/* 重复选择对话框 */}
      <MessageDialog
        title={stringsTo('plug_timer_repeat')}
        visible={showRepeatDialog}
        onDismiss={() => setShowRepeatDialog(false)}
        buttons={[
          {
            text: stringsTo('cancel'),
            callback: () => setShowRepeatDialog(false),
            btnStyle: styles.btnCancelStyle,
          },
          {
            text: stringsTo('ok_button'),
            callback: () => setShowRepeatDialog(false),
            btnStyle: styles.btnOkStyle,
          },
        ]}>
        <View style={styles.dialogContent}>
          {repeatOptions.map(option => {
            const checked =
              (Array.isArray(option.value) && JSON.stringify(selectedRepeat) === JSON.stringify(option.value)) ||
              (option.value === 'custom' &&
                !repeatOptions.some(
                  opt => Array.isArray(opt.value) && JSON.stringify(opt.value) === JSON.stringify(selectedRepeat),
                ));
            return (
              <ChoiceItemNew
                key={option.key}
                title={option.title}
                backgroundColor={checked ? 'rgba(18,170,156,0.1)' : '#FFFFFF'}
                titleColor={'#000000'}
                checked={checked}
                selectIcon={require('../../resources/images/icon_select_s.png')}
                unselectIcon={'blank'}
                containerStyle={{paddingTop: 17, paddingBottom: 17, borderRadius: 0}}
                onValueChange={() => handleRepeatItemClick(option)}
                onPress={() => {
                  handleRepeatItemOnClick(option, checked);
                }}
              />
            );
          })}
        </View>
      </MessageDialog>

      {/* 自定义重复选择对话框 */}
      <MessageDialog
        title={stringsTo('custom_repeat')+1}
        visible={showCustomRepeatDialog}
        onDismiss={() => setShowCustomRepeatDialog(false)}
        buttons={[
          {
            text: stringsTo('cancel'),
            callback: () => setShowCustomRepeatDialog(false),
            btnStyle: styles.btnCancelStyle,
          },
          {
            text: stringsTo('ok_button'),
            callback: confirmCustomRepeat,
            btnStyle: styles.btnOkStyle,
          },
        ]}>
        <View style={styles.dialogContent}>
          {customRepeatOptions.map((option, index) => {
            return (
              <ChoiceItemNew
                key={option.key}
                title={option.title}
                checked={option.checked }
                backgroundColor={option.checked ? 'rgba(18,170,156,0.1)' : '#FFFFFF'}
                titleColor={'#000000'}
                selectIcon={require('../../resources/images/icon_select_s.png')}
                hideRightIcon={!option.checked}
                containerStyle={{paddingTop: 17, paddingBottom: 17, borderRadius: 0}}
                onValueChange={checked => handleCustomRepeatChange(checked, index)}
              />
            );
          })}
        </View>
      </MessageDialog>

      {/* 开始时间选择对话框 */}
      <MessageDialog
        title={stringsTo('picker_start_time_1')}
        subtile={`${startHour}:${startMinute}`}
        visible={showStartTimeDialog}
        onDismiss={() => setShowStartTimeDialog(false)}
        buttons={[
          {
            text: stringsTo('cancel'),
            callback: () => setShowStartTimeDialog(false),
            btnStyle: styles.btnCancelStyle,
          },
          {
            text: stringsTo('ok_button'),
            callback: confirmStartTime,
            btnStyle: styles.btnOkStyle,
          },
        ]}>
        <View>
          <View style={styles.timePickerContainer}>
            <CustomSpinner
              data={hourArr}
              selectedIndex={hourArr.indexOf(hourRef.current)}
              unit={stringsTo('hour')}
              onValueChange={handleHourChange}
              optionsWrapperWidth={100}
              optionHeight={40}
              highlightBorderColor={'transparent'}
              highlightBorderWidth={1}
              activeItemColor="#12AA9C"
              itemColor={'#999999'}
            />

            <CustomSpinner
              data={minuteArr}
              selectedIndex={minuteArr.indexOf(minuteRef.current)}
              unit={stringsTo('minute')}
              onValueChange={handleMinuteChange}
              optionsWrapperWidth={100}
              optionHeight={40}
              highlightBorderColor={'transparent'}
              highlightBorderWidth={1}
              activeItemColor="#12AA9C"
              itemColor={'#999999'}
            />
          </View>
        </View>
      </MessageDialog>

      {/* 结束时间选择对话框 */}
      <MessageDialog
        title={stringsTo('picker_end_time_1')}
        subtile={`${endHour}:${endMinute}`}
        visible={showEndTimeDialog}
        onDismiss={() => setShowEndTimeDialog(false)}
        buttons={[
          {
            text: stringsTo('cancel'),
            callback: () => setShowEndTimeDialog(false),
            btnStyle: styles.btnCancelStyle,
          },
          {
            text: stringsTo('ok_button'),
            callback: confirmEndTime,
            btnStyle: styles.btnOkStyle,
          },
        ]}>
        <View style={styles.timePickerContainer}>
          <CustomSpinner
            data={hourArr}
            selectedIndex={hourArr.indexOf(hourRef.current)}
            unit={stringsTo('hour')}
            onValueChange={handleHourChange}
            optionsWrapperWidth={100}
            optionHeight={40}
            highlightBorderColor={'transparent'}
            highlightBorderWidth={1}
            activeItemColor="#12AA9C"
            itemColor={'#999999'}
          />

          <CustomSpinner
            data={minuteArr}
            selectedIndex={minuteArr.indexOf(minuteRef.current)}
            unit={stringsTo('minute')}
            onValueChange={handleMinuteChange}
            optionsWrapperWidth={100}
            optionHeight={40}
            highlightBorderColor={'transparent'}
            highlightBorderWidth={1}
            activeItemColor="#12AA9C"
            itemColor={'#999999'}
          />
        </View>
      </MessageDialog>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F7F7F7',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginTop: 12,

    marginHorizontal: 12,
  },

  dialogContent: {
    width: '100%',
    paddingVertical: 18,
  },
  timePickerContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: 20,
    width: '100%',
  },
  lineHight: {
    height: 12,
    backgroundColor: '#F7F7F7',
  },
  lineHightBorder: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.15)',
    marginTop: 10,
    marginBottom: 10,
  },
  choiceItemLabel: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
  },
  btnCancelStyle: {
    background: 'rgba(0,0,0,0.04)',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
  },
  btnOkStyle: {
    backgroundColor: '#12AA9C',
    borderRadius: 23,
    paddingTop: 12,
    paddingBottom: 12,
  },
});

export default CruiseTimePeriod;
