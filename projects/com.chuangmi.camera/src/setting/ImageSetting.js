import React from 'react';

import {StyleSheet, View, ScrollView, TextInput, Text} from 'react-native';

import {imiThemeManager, showLoading} from '../../../../imilab-design-ui';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import I18n, {stringsTo} from '../../../../globalization/Localize';

import {LetDevice, BaseDeviceComponent, LetIMIIotRequest} from '../../../../imilab-rn-sdk';

import {showToast, AlertDialog} from '../../../../imilab-design-ui';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';

/**
 * 摄像机功能设置页面
 */

export default class ImageSetting extends BaseDeviceComponent {
  static propTypes = {};

  componentDidMount() {
    console.log('进来了');
    showLoading(stringsTo('commLoadingText'), true);
    this.getAllValue();
  }

  constructor(props, context) {
    super(props, context);
    this.state = {
      pictureCorrectionSwitch: false,
      oSDSwitch: false,
      imageFlipState: 0,
    };
  }

  async getAllValue() {
    try {
      // 获取设备配置
      const {isOutdoor, hidePictureCorrection} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);

      // 根据配置构建请求列表
      const propertyRequests = [];

      // 时间水印 - 始终请求
      propertyRequests.push(LetDevice.getSingleProperty('10004'));

      // 图像翻转 - 只有非户外设备才请求
      if (!isOutdoor) {
        propertyRequests.push(LetDevice.getSingleProperty('10002'));
      }

      // 镜头畸变校正 - 只有未隐藏的设备才请求
      if (!hidePictureCorrection) {
        propertyRequests.push(LetDevice.getSingleProperty('10006'));
      }

      const data = await Promise.all(propertyRequests);
      console.log('getAllValue data:', data);

      const stateProps = {};
      data?.forEach(item => {
        // 时间水印
        if (`${item?.thingId}` === `${10004}`) {
          console.log('10004 时间水印:', item);
          stateProps.oSDSwitch = Boolean(item?.value?.value);
        }
        // 图像翻转 - 只有非户外设备才处理
        if (`${item?.thingId}` === `${10002}` && !isOutdoor) {
          console.log('10002 图像翻转:', item);
          stateProps.imageFlipState = item?.value?.value;
        }
        // 镜头畸变校正 - 只有未隐藏的设备才处理
        if (`${item?.thingId}` === `${10006}` && !hidePictureCorrection) {
          console.log('10006 镜头畸变校正:', item);
          stateProps.pictureCorrectionSwitch = Boolean(item?.value?.value);
        }
      });

      // 为户外设备设置默认值
      if (isOutdoor) {
        stateProps.imageFlipState = 0; // 户外设备默认不翻转
      }

      console.log('物模型云端数据--------', stateProps);
      this.setState(stateProps);
      showLoading(false);
    } catch (error) {
      console.log('getAllValue 错误:', JSON.stringify(error));
      showLoading(false);
      showToast(I18n.t('commLoadingFailText'));
    }
  }
  componentWillUnmount() {
    // this.devicePropertyListener && this.devicePropertyListener.remove();
    console.log('走了');

    showLoading(false);
    this.setState = () => false; // 组件销毁的时候将异步方法撤销
  }

  render() {
    console.log('this.state.imageFlipInput', this.state.imageFlipInput);

    let {showSynchronous056, hidePictureCorrection} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    return (
      <View style={styles.container}>
        <NavigationBar
          title={
            LetDevice.model == 'a1Od0SjKPGt' || showSynchronous056
              ? stringsTo('imageSetting')
              : stringsTo('setting_picture_setting')
          }
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this.props.navigation.pop()}]}
          right={[]}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <Separator />

          {/*<View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}
          <ListItmeWithSwitch
            title={stringsTo('settings_flip_title')}
            value={this.state.imageFlipState === 180}
            subtitle={stringsTo('settings_flip_subtitle')}
            onValueChange={value => {
              // 0代表不翻转，180代表翻转
              showLoading(stringsTo('commWaitText'), true);
              let params = {msg_id: '10002', value: value ? 180 : 0};
              let paramJson = JSON.stringify(params);
              LetDevice.setProperties(true, LetDevice.deviceID, '10002', paramJson)
                .then(res => {
                  showLoading(false);
                })
                .catch(err => {
                  console.log('设置图像', JSON.stringify(err));
                  this.setState({
                    imageFlipState: value ? 0 : 180,
                  });
                  showToast(I18n.t('operationFailed'));
                  showLoading(false);
                });
              /* if (value) {
                showLoading(stringsTo('commWaitText'), true);
                let params = {msg_id: '10002', value};
                let paramJson = JSON.stringify(params);
                LetDevice.setProperties(true, LetDevice.deviceID, '10002', paramJson)
                  .then(res => {
                    showLoading(false);
                  })
                  .catch(err => {
                    console.log('设置图像', JSON.stringify(err));
                    this.setState({
                      imageFlipState: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                    showLoading(false);
                  });
              } else {
                showLoading(stringsTo('commWaitText'), true);
                let params = {msg_id: 10002, value: 111};
                let paramJson = JSON.stringify(params);
                LetDevice.setProperties(true, LetDevice.deviceID, 10002, paramJson)
                  .then(res => {
                    showLoading(false);
                  })
                  .catch(err => {
                    console.log('设置图像', JSON.stringify(err));
                    this.setState({
                      imageFlipState: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                    showLoading(false);
                  });
              } */
            }}
          />
          <ListItmeWithSwitch
            title={I18n.t('settings_watermark_title')}
            value={this.state.oSDSwitch}
            onValueChange={value => {
              showLoading(stringsTo('commWaitText'), true);

              let params = {msg_id: '10004', value};
              let paramJson = JSON.stringify(params);
              console.log('下发参数', paramJson);
              LetDevice.setProperties(true, LetDevice.deviceID, '10004', paramJson)
                .then(res => {
                  console.log('设置时间水印成功', res, value, 1);
                  showLoading(false);
                  /*  this.setState({
                    oSDSwitch: !value,
                  }); */
                })
                .catch(err => {
                  showLoading(false);
                  this.setState({
                    oSDSwitch: this.state.oSDSwitch
                  })
                  showToast(I18n.t('operationFailed'));
                  console.log('设置图像', JSON.stringify(err));
                });
            }}
            accessibilityLabel={['more_setting_time_watermark_off', 'more_setting_time_watermark_on']}
          />
          {hidePictureCorrection ? null : (
            <ListItmeWithSwitch
              title={I18n.t('pictureCorrection')}
              value={this.state.pictureCorrectionSwitch}
              subtitle={I18n.t('pictureCorrectionSubtitle')}
              onValueChange={value => {
                let params = {msg_id: '10006', value};
                let paramJson = JSON.stringify(params);
                showLoading(stringsTo('commWaitText'), true);
                console.log('下发参数,图像畸变矫正', paramJson);
                LetDevice.setProperties(true, LetDevice.deviceID, '10006', paramJson)
                  .then(res => {
                    showLoading(false);
                  })
                  .catch(() => {
                    showLoading(false);
                    this.setState({
                      pictureCorrectionSwitch: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                  });

                IMILogUtil.uploadClickEventValue({DistortionCorrectionSwitch: value ? '1' : '0'});
                /* if (LetDevice.model == 'a1znn6t1et8') {
                  showLoading(stringsTo('commWaitText'), true);
                  let params = {msg_id: 10006, value};
                  let paramJson = JSON.stringify(params);
                  console.log('下发参数', paramJson);
                  LetDevice.setProperties(true, LetDevice.deviceID, 10006, paramJson)
                    .then(res => {
                      showLoading(false);
                    })
                    .catch(() => {
                      showLoading(false);
                      this.setState({
                        pictureCorrectionSwitch: !value,
                      });
                      showToast(I18n.t('operationFailed'));
                    });

                  IMILogUtil.uploadClickEventValue({DistortionCorrectionSwitch: value ? '1' : '0'});
                } else {
                  showLoading(stringsTo('commWaitText'), true);
                  IMIStorage.save({
                    key: LetDevice.deviceID + 'DistortionCorrection',
                    data: {
                      lensCorrect: value,
                    },
                    expires: null,
                  });
                  setTimeout(() => {
                    showLoading(false);
                  }, 1000);

                  IMILogUtil.uploadClickEventValue({DistortionCorrection: value ? '1' : '0'});
                } */
              }}
              accessibilityLabel={['more_setting_lens_correction_off', 'more_setting_lens_correction_on']}
            />
          )}

          {/*<View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}

          {showSynchronous056 || LetDevice.model == 'a1znn6t1et8' ? null : (
            <ListItem
              title={I18n.t('wdrMode')}
              onPress={() => {
                this.props.navigation.push('WdrSettingPage');
              }}
              accessibilityLabel={'more_setting_dynamic_model'}
            />
          )}
        </ScrollView>
      </View>
    );
  }

  _assignRoot = component => {
    this.cameraGLView = component;
  };

  _onPrepared(data) {
    console.log(`_onPrepared code : ${data}  `);
    this.cameraGLView.start();
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  functionSettingStyle: {
    color: '#0000007F',
    fontSize: 12,
    marginTop: 23,
    marginLeft: 14,
  },
  input: {
    height: 40,
    margin: 12,
    borderWidth: 1,
    padding: 10,
  },
});
