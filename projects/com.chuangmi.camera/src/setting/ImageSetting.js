import React from 'react';

import {StyleSheet, View, ScrollView, TextInput, Text} from 'react-native';

import {imiThemeManager, showLoading} from '../../../../imilab-design-ui';
import NavigationBar from '../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import ListItem from '../../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import ListItmeWithSwitch from '../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch';
import Separator from '../../../../imilab-design-ui/src/widgets/settingUI/Separator';
import I18n, {stringsTo} from '../../../../globalization/Localize';

import {LetDevice, BaseDeviceComponent, LetIMIIotRequest} from '../../../../imilab-rn-sdk';

import {showToast, AlertDialog} from '../../../../imilab-design-ui';
import DeviceTemplatesUtils from '../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import IMILogUtil from '../../../../imilab-rn-sdk/native/local-kit/IMILogUtil';

/**
 * 摄像机功能设置页面
 */

export default class ImageSetting extends BaseDeviceComponent {
  static propTypes = {};

  componentDidMount() {
    console.log('进来了');
    // setTimeout(() => {
      showLoading(stringsTo('commLoadingText'), true);
    // }, 17);
    // 使用接口获取哦
    this.getLists()
    // Promise.all([
    //   LetDevice.getSingleProperty('10004'),
    //   LetDevice.getSingleProperty('10002'),
    //   LetDevice.getSingleProperty('10006'),
    // ])
    //   .then(data => {
    //     console.log('updateAllPropertyCloud:', data);
    //     showLoading(false);
    //     let stateProps = {};
    //     data?.forEach(item => {
    //       //时间水印
    //       if (`${item?.thingId}` === `${10004}`) {
    //         console.log('10004 ', item);
    //         stateProps.oSDSwitch = item?.value?.value;
    //       }
    //       //图像翻转
    //       if (`${item?.thingId}` === `${10002}`) {
    //         console.log('10002 ', item);
    //         stateProps.imageFlipState = item?.value?.value;
    //       }
    //       //镜头畸变校正
    //       if (`${item?.thingId}` === `${10006}`) {
    //         console.log('s10006', item);
    //         stateProps.pictureCorrectionSwitch = item?.value?.value;
    //       }
    //     });
    //     //TODO 镜头畸变校正固件端未实现
    //     /*  if (dataPackage.DistortionCorrection) {
    //       stateProps.pictureCorrectionSwitch = dataPackage.DistortionCorrection.value;
    //     }
    //     if (dataPackage.OSDSwitch) {
    //       if (typeof dataPackage.OSDSwitch.value === 'number') {
    //         if (dataPackage.OSDSwitch.value == 1) {
    //           stateProps.oSDSwitch = true;
    //         } else {
    //           stateProps.oSDSwitch = false;
    //         }
    //       } else {
    //         stateProps.oSDSwitch = dataPackage.OSDSwitch.value;
    //       }
    //     }
    //     //图像翻转
    //     if (dataPackage.ImageFlipState) {
    //       if (typeof dataPackage.ImageFlipState.value === 'number') {
    //         if (dataPackage.ImageFlipState.value == 1) {
    //           stateProps.imageFlipState = true;
    //         } else {
    //           stateProps.imageFlipState = false;
    //         }
    //       } else {
    //         stateProps.imageFlipState = dataPackage.ImageFlipState.value;
    //       }
    //     }
    //     // 和固件确认036是固件做了畸变纠正，插件只需要设置开关就好
    //     if (LetDevice.model == 'a1znn6t1et8') {
    //       // console.log('036当前畸变纠正值',dataPackage.DistortionCorrectionSwitch);
    //       if (dataPackage.DistortionCorrectionSwitch) {
    //         if (typeof dataPackage.DistortionCorrectionSwitch.value === 'number') {
    //           if (dataPackage.DistortionCorrectionSwitch.value == 1) {
    //             stateProps.pictureCorrectionSwitch = true;
    //           } else {
    //             stateProps.pictureCorrectionSwitch = false;
    //           }
    //         } else {
    //           stateProps.pictureCorrectionSwitch = dataPackage.DistortionCorrectionSwitch.value;
    //         }
    //       }
    //     } */
    //     console.log('物模型云端数据--------', stateProps);
    //     showLoading(false);
    //     this.setState(stateProps);
    //   })
    //   .catch(error => {
    //     showLoading(false);
    //     console.log('错误了', JSON.stringify(error));
    //   });
    /*
    if (LetDevice.model != 'a1znn6t1et8') {
      IMIStorage.load({
        key: LetDevice.deviceID + 'DistortionCorrection',
        autoSync: true,
        syncInBackground: true,
      })
        .then(res => {
          this.setState({pictureCorrectionSwitch: res.lensCorrect});
        })
        .catch(_ => {
          this.setState({pictureCorrectionSwitch: false});
        });
    } */
  }

  constructor(props, context) {
    super(props, context);
    this.state = {
      pictureCorrectionSwitch: false,
      oSDSwitch: false,
      imageFlipState: 0,
    };
  }

  getLists = async () => {
    const params1 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10004,
        method: 'sync'
      },
      Method: 'POST',
    };
    const params2 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10002,
        method: 'sync'
      },
      Method: 'POST',
    };
    const params3 = {
      Path: '/v1.0/imilab-01/device/control/property/getByCached',
      ParamMap: {
        iotId: LetDevice.deviceID,
        thingId: 10006,
        method: 'sync'
      },
      Method: 'POST',
    };
    let stateProps = {};

    Promise.all([LetIMIIotRequest.sendUserServerRequest(params1, true), 
      LetIMIIotRequest.sendUserServerRequest(params2, true), 
      LetIMIIotRequest.sendUserServerRequest(params3, true)]).then(res => {
        stateProps.oSDSwitch = res[0]?.value?.value;
        stateProps.imageFlipState = res[1]?.value?.value;
        stateProps.pictureCorrectionSwitch = res[2]?.value?.value;
        this.setState(stateProps);
        showLoading(false);
      }).catch(() => {
        showLoading(false);
      })    
  }
  componentWillUnmount() {
    // this.devicePropertyListener && this.devicePropertyListener.remove();
    console.log('走了');

    showLoading(false);
    this.setState = () => false; // 组件销毁的时候将异步方法撤销
  }

  render() {
    console.log('this.state.imageFlipInput', this.state.imageFlipInput);

    let {showSynchronous056, hidePictureCorrection} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
    return (
      <View style={styles.container}>
        <NavigationBar
          title={
            LetDevice.model == 'a1Od0SjKPGt' || showSynchronous056
              ? stringsTo('imageSetting')
              : stringsTo('setting_picture_setting')
          }
          left={[{key: NavigationBar.ICON.BACK, onPress: () => this.props.navigation.pop()}]}
          right={[]}
        />
        <ScrollView showsVerticalScrollIndicator={false}>
          <Separator />

          {/*<View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}
          <ListItmeWithSwitch
            title={stringsTo('settings_flip_title')}
            value={this.state.imageFlipState === 180}
            subtitle={stringsTo('settings_flip_subtitle')}
            onValueChange={value => {
              // 0代表不翻转，180代表翻转
              showLoading(stringsTo('commWaitText'), true);
              let params = {msg_id: '10002', value: value ? 180 : 0};
              let paramJson = JSON.stringify(params);
              LetDevice.setProperties(true, LetDevice.deviceID, '10002', paramJson)
                .then(res => {
                  showLoading(false);
                })
                .catch(err => {
                  console.log('设置图像', JSON.stringify(err));
                  this.setState({
                    imageFlipState: value ? 0 : 180,
                  });
                  showToast(I18n.t('operationFailed'));
                  showLoading(false);
                });
              /* if (value) {
                showLoading(stringsTo('commWaitText'), true);
                let params = {msg_id: '10002', value};
                let paramJson = JSON.stringify(params);
                LetDevice.setProperties(true, LetDevice.deviceID, '10002', paramJson)
                  .then(res => {
                    showLoading(false);
                  })
                  .catch(err => {
                    console.log('设置图像', JSON.stringify(err));
                    this.setState({
                      imageFlipState: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                    showLoading(false);
                  });
              } else {
                showLoading(stringsTo('commWaitText'), true);
                let params = {msg_id: 10002, value: 111};
                let paramJson = JSON.stringify(params);
                LetDevice.setProperties(true, LetDevice.deviceID, 10002, paramJson)
                  .then(res => {
                    showLoading(false);
                  })
                  .catch(err => {
                    console.log('设置图像', JSON.stringify(err));
                    this.setState({
                      imageFlipState: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                    showLoading(false);
                  });
              } */
            }}
          />
          <ListItmeWithSwitch
            title={I18n.t('settings_watermark_title')}
            value={this.state.oSDSwitch}
            onValueChange={value => {
              showLoading(stringsTo('commWaitText'), true);

              let params = {msg_id: '10004', value};
              let paramJson = JSON.stringify(params);
              console.log('下发参数', paramJson);
              LetDevice.setProperties(true, LetDevice.deviceID, '10004', paramJson)
                .then(res => {
                  console.log('设置时间水印成功', res, value, 1);
                  showLoading(false);
                  /*  this.setState({
                    oSDSwitch: !value,
                  }); */
                })
                .catch(err => {
                  showLoading(false);
                  this.setState({
                    oSDSwitch: this.state.oSDSwitch
                  })
                  showToast(I18n.t('operationFailed'));
                  console.log('设置图像', JSON.stringify(err));
                });
            }}
            accessibilityLabel={['more_setting_time_watermark_off', 'more_setting_time_watermark_on']}
          />
          {hidePictureCorrection ? null : (
            <ListItmeWithSwitch
              title={I18n.t('pictureCorrection')}
              value={this.state.pictureCorrectionSwitch}
              subtitle={I18n.t('pictureCorrectionSubtitle')}
              onValueChange={value => {
                let params = {msg_id: '10006', value};
                let paramJson = JSON.stringify(params);
                showLoading(stringsTo('commWaitText'), true);
                console.log('下发参数,图像畸变矫正', paramJson);
                LetDevice.setProperties(true, LetDevice.deviceID, '10006', paramJson)
                  .then(res => {
                    showLoading(false);
                  })
                  .catch(() => {
                    showLoading(false);
                    this.setState({
                      pictureCorrectionSwitch: !value,
                    });
                    showToast(I18n.t('operationFailed'));
                  });

                IMILogUtil.uploadClickEventValue({DistortionCorrectionSwitch: value ? '1' : '0'});
                /* if (LetDevice.model == 'a1znn6t1et8') {
                  showLoading(stringsTo('commWaitText'), true);
                  let params = {msg_id: 10006, value};
                  let paramJson = JSON.stringify(params);
                  console.log('下发参数', paramJson);
                  LetDevice.setProperties(true, LetDevice.deviceID, 10006, paramJson)
                    .then(res => {
                      showLoading(false);
                    })
                    .catch(() => {
                      showLoading(false);
                      this.setState({
                        pictureCorrectionSwitch: !value,
                      });
                      showToast(I18n.t('operationFailed'));
                    });

                  IMILogUtil.uploadClickEventValue({DistortionCorrectionSwitch: value ? '1' : '0'});
                } else {
                  showLoading(stringsTo('commWaitText'), true);
                  IMIStorage.save({
                    key: LetDevice.deviceID + 'DistortionCorrection',
                    data: {
                      lensCorrect: value,
                    },
                    expires: null,
                  });
                  setTimeout(() => {
                    showLoading(false);
                  }, 1000);

                  IMILogUtil.uploadClickEventValue({DistortionCorrection: value ? '1' : '0'});
                } */
              }}
              accessibilityLabel={['more_setting_lens_correction_off', 'more_setting_lens_correction_on']}
            />
          )}

          {/*<View style={{height:14,backgroundColor: "#F1F1F1"}} />*/}

          {showSynchronous056 || LetDevice.model == 'a1znn6t1et8' ? null : (
            <ListItem
              title={I18n.t('wdrMode')}
              onPress={() => {
                this.props.navigation.push('WdrSettingPage');
              }}
              accessibilityLabel={'more_setting_dynamic_model'}
            />
          )}
        </ScrollView>
      </View>
    );
  }

  _assignRoot = component => {
    this.cameraGLView = component;
  };

  _onPrepared(data) {
    console.log(`_onPrepared code : ${data}  `);
    this.cameraGLView.start();
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
  functionSettingStyle: {
    color: '#0000007F',
    fontSize: 12,
    marginTop: 23,
    marginLeft: 14,
  },
  input: {
    height: 40,
    margin: 12,
    borderWidth: 1,
    padding: 10,
  },
});
