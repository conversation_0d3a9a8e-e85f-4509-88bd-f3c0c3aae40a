import React, {useEffect, useState} from 'react';
import {StyleSheet, View, Dimensions, TouchableOpacity, Text, Image, BackHandler, StatusBar} from 'react-native';
import {XText, XImage, XView} from 'react-native-easy-app';
import IMIFile from '../../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import IMIPermission from '../../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import IMIToast from '../../../../../imilab-design-ui/src/widgets/IMIToast';
import {colors, IMIImageView, showLoading, showToast, TopSelectBar, imiThemeManager, MessageDialog} from '../../../../../imilab-design-ui';
import {aliAlarmEventCloudApi,LetDevice, DateUtils, IMILog} from '../../../../../imilab-rn-sdk';
import I18n, {stringsTo} from '../../../../../globalization/Localize';
import NavigationBar from '../../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import { ScrollView } from 'react-native-gesture-handler';
import IMP2pClient from '../../../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import moment from 'moment';
import { getAllBackListDetail, deleteContent } from '../dataUtils'
import {isIos, isIphoneXSeries, isIphone14ProMax} from '../../../../../imilab-rn-sdk/utils/Utils';
import DeviceTemplatesUtils from '../../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import {
  byteArrayToInt4,
  byteArrayToLong8,
} from '../../utils/GenericUtils';
const {width, height} = Dimensions.get('window');
let windowWidth = height > width ? width : height;
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
let isGoBack = false; //判断数组是否返回父视图
export default class BackTimeLists extends React.Component {
  constructor(props) {
    super(props);
    const {needCheck, timeList, dataList, localPicList} = this.getData()
    this.state = {
        dataList: dataList,
        eventTypeList: [],
        selectEventName: stringsTo('all_events_str'),
        currentIndex: 0,
        title: props.route.params.firstKey + ' ' + props.route.params.secondKey,
        isEdit: false,
        numAry: [],
        isSelectedAll: false,
        showDeleteTip: false,
        timeList: timeList,
        timeListAll: timeList,
        timeListData: [],
        timeListDataNew: [],
    };
    this.needCheck=needCheck;
    this.localPic = localPicList;
    this.areadyClick = false
    this.isForegroundPage = true;
    this.downloadFlag = false;
    this.firstLoad = true;
    this.currentVideoLength = 0;
    this.currentImgLength = 0;
    this.statusBarHeight = 100;
    this.lists = {};
  }

  getData() {
    const needCheck = [];
    const localPicList = {};
    const timeList = {};
    const data = getAllBackListDetail();
    const dataList = data[this.props.route.params.firstKey] && data[this.props.route.params.firstKey][this.props.route.params.secondKey] || [];
    const uniqueDataList = Array.isArray(dataList) && dataList.filter((item, index, self) => 
      index === self.findIndex((t) => t.timestamp === item.timestamp)
    ) || [];
    Array.isArray(uniqueDataList) && uniqueDataList.map(res => {
      const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${res.camera_id}_${res.timestamp}.jpg`
      needCheck.push(saveFilePath);
      localPicList[res.timestamp] = res.pic_loc
      timeList[res.timestamp] = saveFilePath
    })
    return {needCheck, timeList, dataList, localPicList}
  }
  componentDidMount() {
    this.getStatusBarHeight()
    this.firstLoad = true;
    this.loadImg(this.needCheck)
    this.loadMoreData();
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', this.backAction);
    this.getEventType()
    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this.isForegroundPage = true;
      this.areadyClick = false;
      this.downloadFlag = false;
      if (this.firstLoad) {
        return
      }
      const {needCheck, timeList, dataList, localPicList} = this.getData();
      this.setState({
        dataList: dataList,
        timeList: timeList,
        timeListAll: timeList,
      })
      this.needCheck=needCheck;
      this.localPic=localPicList;
      this.loadImg(this.needCheck);
    });
    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      console.log('我进入后台了')
      this.isForegroundPage = false;
      this.waitingData && clearInterval(this.waitingData);
      this.loadMoreTime && clearInterval(this.loadMoreTime)
    })

    this.currentIndex = 0;
    this.onP2pSendStateListener = IMP2pClient.onFileOperateAddListener(e => {
      if (!this.isForegroundPage) {
        return
      }
      if (e.iotId != LetDevice.deviceID) {
        return
      }
      this.waitingData && clearInterval(this.waitingData);
      if (e.code === 0 && e.data) {
          this.queryThreeMonthData(e.data)
        }
  });
  }

  loadMoreData() {
    if (this.state.dataList[0].timestamp > (new Date(moment(new Date().getTime()).format('yyyy-MM-DD HH') + ":00").getTime() / 1000)) {
      this.loadMoreTime = setInterval(() => {
        const {needCheck, timeList, dataList, localPicList} = this.getData();
        this.setState({
          dataList: dataList,
          timeList: timeList,
          timeListAll: timeList,
        })
        this.needCheck=needCheck;
        this.localPic=localPicList;
        this.loadImg(this.needCheck)
      }, 60000)
    }
  }

  loadImg(needCheck, back) {
    IMP2pClient.checkPlaybackFile(needCheck).then(res => {
      console.log('================图片', res, back)
      const lists = [];
      const timeList = back ? JSON.parse(JSON.stringify(this.state.timeListAll)) : this.state.timeList;
      Array.isArray(res) && res.map(item => {
          const timestamp = item.split('_').slice(-1)[0].slice(0, -4)
          lists.push(timestamp)
          delete timeList[timestamp]
      })
      this.setState({
          timeListData: lists,
          timeListDataNew: JSON.parse(JSON.stringify(lists)),
          timeList,
      }, () => {
        lists.length > 0 && this.sendData(0);
      })
  }).catch(error => {
  })
  }

  componentDidUpdate(prevProps) {
    // console.log('+++++++++++++++++++++',prevProps.route.params.children.length , this.props.route.params.children.length)
    // console.log(this.isForegroundPage)
  }

  uint8ArrayToBase64(uint8Array) {
    const rawData = new Uint8Array(uint8Array);
    const base64String = btoa(String.fromCharCode.apply(null, rawData));
    return base64String;
  }

  queryThreeMonthData(data) {
    const rawData = window.atob(data);
    const uint8Arraypart = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; i++) {
        uint8Arraypart[i] = rawData.charCodeAt(i);
    };
    if (byteArrayToInt4(uint8Arraypart, 4) == 0) {
      return;
    }
    if (byteArrayToInt4(uint8Arraypart, 4) == 2) {
        return;
    }
    if (byteArrayToInt4(uint8Arraypart, 4) == 1) {
      this.downloadTime && clearTimeout(this.downloadTime)
      this.downloadVideo(data)
      return;
    }
    // 删除的时候有轮询掉用删除状态
    if (this.downloadFlag) {
      try {
        // 删除
        const messageResult = JSON.parse(rawData?.slice(4))
        if (messageResult.thingid == 214) {
          // -1：删除失败，0删除中，1：删除成功
          if (messageResult.del_status == 1) {
            this.deleteSuccess();
          } else if (messageResult.del_status == -1) {
            this.downloadFlag = false;
            showLoading(false);
            showToast(stringsTo('delete_failed'));
            this.deleteTime && clearInterval(this.deleteTime);
          }
        }
      } catch (error) {
        console.log(error)
      }
    }
    if (byteArrayToInt4(uint8Arraypart, 4) !== 3) {
      console.log('收到消息11', typeof(rawData))
      try {
        const messageResult = JSON.parse(rawData.slice(4, -1));
        console.log('------1111111', messageResult)

        if (messageResult.cmd_type === 2) {
          if (messageResult.code === 1) {
            this.deleteSuccess();
          } else {
            this.downloadFlag = false;
            showLoading(false);
            showToast(stringsTo('delete_failed'));
            this.deleteTime && clearInterval(this.deleteTime);
          }
        }
        if (messageResult.cmd_type === 1 && messageResult.code === 1011) {
          this.downloadTime && clearTimeout(this.downloadTime);
          this.downloading && clearTimeout(this.downloading);
          // 下载异常结束，超时保持一致
          console.log('视频异常结束-------------')
          IMILog.logI('视频异常结束', JSON.stringify({preSeq: this.preSeq}));
          if (this.preSeq === 0) {
            this.downloadFlag = false;
            this.preSeq = 0;
            showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
            showLoading(false);
          }
          this.saveFile('', `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, '0')
        }
      } catch (error) {
        console.log(error,"收到错误的信息")
      }
      return;
    }
    const uint8Array = uint8Arraypart;
    const camera_id = byteArrayToInt4(uint8Array, 35); // 镜头id
    const timestamp = byteArrayToLong8(uint8Array, 39); // 开始时间utc , 单位s
    const cur_pack_len = byteArrayToLong8(uint8Array, 47); // 开始时间utc , 单位s
    const total_file_len = byteArrayToInt4(uint8Array, 55);
    this.currentIndex = this.currentIndex + 1;
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/${camera_id}_${timestamp}.jpg`;
    // console.log(uint8Array.length === cur_pack_len)
    let imgFinish = false;
    this.currentImgLength += uint8Array.slice(84).length;
    if (this.currentImgLength >= total_file_len) {
      imgFinish = true
    }
    IMP2pClient.downloadPlaybackImage(this.uint8ArrayToBase64(uint8Array.slice(84)), saveFilePath, imgFinish ? '0' : '-1').then(res => {
        const timeList = this.state.timeList || {};
        timeList[timestamp] = res;
        const timeListDataNew = this.state.timeListDataNew;
        timeListDataNew.splice(timeListDataNew.indexOf(timestamp), 1)
        this.setState({
            timeList,
            timeListDataNew
        })
    }).catch(e => {
        console.log(11, e);
    });
    if (this.currentIndex < this.state.timeListData.length && this.isForegroundPage) {
        this.sendData(this.currentIndex)
    }
  }

  sendData(index) {
    if (!this.isForegroundPage) {
      this.waitingData && clearInterval(this.waitingData);
      return
    }
    this.currentImgLength = 0;
    this.state.timeListData[index] && IMP2pClient.operationFile(['0'],['3'], [[String(this.state.timeListData[index])]], [[String(this.localPic[this.state.timeListData[index]])]]);
    // 十秒后未获取到数据就重发
    this.waitingData = setInterval(() => {
        this.currentIndex++
        this.currentImgLength = 0;
        this.state.timeListData[this.currentIndex] && IMP2pClient.operationFile(['0'],['3'], [[String(this.state.timeListData[this.currentIndex])]], [[String(this.localPic[this.state.timeListData[this.currentIndex]])]]);
    }, 10000)
  }


  downloadVideo(data) {
    const rawData = window.atob(data);
    const uint8Arraypart = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; i++) {
        uint8Arraypart[i] = rawData.charCodeAt(i);
    };
    const uint8Array = uint8Arraypart;
    const camera_id = byteArrayToInt4(uint8Array, 35); // 镜头id
    const timestamp = byteArrayToLong8(uint8Array, 39); // 开始时间utc , 单位s
    const cur_pack_len = byteArrayToInt4(uint8Array, 47);
    const seq = byteArrayToInt4(uint8Array, 51);
    const total_file_len = byteArrayToInt4(uint8Array, 55);
    const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/${camera_id}_${timestamp}.mp4`;
    console.log('===========', this.preSeq, cur_pack_len, seq, total_file_len, this.currentVideoLength);
    this.downloading && clearTimeout(this.downloading)
    if (this.isfinish) {
      return
    }

    this.currentVideoLength += uint8Array.slice(84).length;
    if (this.preSeq && Math.abs(this.preSeq - seq) > 1 || this.currentVideoLength >= total_file_len) {
      this.isfinish = true
    }
    if (!this.isFinish) {
      this.downloading = setTimeout(() => {
        console.log('超时直接保存-------------');
        IMILog.logI('视频超时20s直接保存', JSON.stringify({preSeq: this.preSeq, unit: '', saveFilePath: `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, isFinish: '0'}));
        this.saveFile('', `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, '0')
      }, 20000)
    }
    this.preSeq = seq;
    IMILog.logI('视频下载传递', JSON.stringify({isfinish: this.isfinish, cur_pack_len, total_file_len, saveFilePath, currentVideoLength: this.currentVideoLength}));
    this.saveFile(this.uint8ArrayToBase64(uint8Array.slice(84)), saveFilePath, this.isfinish ? '0' : '-1')
  }

  saveFile = (unit, saveFilePath, isFinish) => {
    IMP2pClient.downloadPlaybackVideo(unit, saveFilePath, isFinish).then(res => {
      console.log('===========', res)
           if (res !== '-1') {
            this.downloadFlag = false;
            this.preSeq = 0;
            this.downloading && clearTimeout(this.downloading)
            // 推送到相册
            IMIFile.saveVideoToPhotosAlbum(`${saveFilePath}`, LetDevice.deviceID)
              .then(_ => {
                IMIToast.showToast(stringsTo('download_system_album'), IMIToast.TYPE.BOTTOM);
                showLoading(false);
              })
              .catch(error => {
                showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
                showLoading(false);
              });
           }
    }).catch(e => {
        console.log(11, e);
        this.downloadFlag = false;
        this.preSeq = 0;
        this.downloading && clearTimeout(this.downloading)
        showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
        showLoading(false);
    });
  }

  /* 根据iotid获取事件类型 */
  getEventType() {
    const ALL = {eventTypeName: stringsTo('all_events_str'), eventType: 'housekeeping_event_all'};
    this.lists = {};
    aliAlarmEventCloudApi
      .getEventTypeListByIotId(LetDevice.deviceID)
      .then(res => {
        const eventAry = [ALL];
        const textList = {
          ObjectMotion: stringsTo('moveEvent'),
          PeopleMotion: stringsTo('peopleEvent'),
          AbnormalSound: stringsTo('soundEvent')
        }
        const numList = {
          ObjectMotion: 19,
          PeopleMotion: 18,
          AbnormalSound: 0
        }
        JSON.parse(res)?.forEach((element, index) => {
          eventAry.push({eventTypeName: textList[element?.eventType] || element.eventTypeName, eventType: element?.eventType});
          this.lists[index + 1] = numList[element?.eventType]
        });

        this.setState({eventTypeList: eventAry});
      })
      .catch(error => {
        console.log('error1111', JSON.stringify(error));
      });
  }

  componentWillUnmount() {
    try {
      this.onP2pSendStateListener && this.onP2pSendStateListener.remove();
      this._subscribe_blur && this._subscribe_blur()
      this.waitingData && clearInterval(this.waitingData);
      this.loadMoreTime && clearInterval(this.loadMoreTime);
      this.deleteTime && clearInterval(this.deleteTime);
      this.backHandler && this.backHandler.remove()
    } catch (error) {
      console.log('卸载失败1')
    }
  }
  // 下载
  _downloadPress = () => {
    if (this.downloadFlag) {
      console.log('拦截重复点击')
      return
    }
    this.downloadFlag = true;
    //需要检测权限
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            showLoading(stringsTo('alarm_download_downloading'), true);
            this.isfinish = false;
            this.currentVideoLength = 0;
            IMP2pClient.operationFile(['0'],['1'], [[String(this.state.numAry[0])]], [[String(this.localPic[this.state.numAry[0]])]]);
            this.setState({isEdit: false, numAry: []});
            this.downloadTime = setTimeout(() => {
              this.downloadFlag = false;
              IMIToast.showToast(stringsTo('video_download_fail'), IMIToast.TYPE.BOTTOM);
              showLoading(false);
            }, 10000)
          } else if (status2 === -1) {
            this.downloadFlag = false;
            showToast(stringsTo('storage_permission_denied'));
          }
        });
      } else if (status === -1) {
        this.downloadFlag = false
        showToast(stringsTo('storage_permission_denied'));
      }
    });
  };

  // 返回
  _onPressBack = () => {
    this.isView = false;
    if (this.state.isFullScreen) {
      this._exitFullScreen();
    } else {
      this.props.navigation.goBack();
    }
    return true;
  };

  backAction = () => {
    if (this.state.isEdit) {
      this.setState({isEdit: false, numAry: []});
      return true
    }
    if (this.downloadFlag) {
      return true
    }
    return false
  }

 fillArray = (arr) => {
    while (arr.length < 20) {
        arr.unshift('0');
    }
    return arr;
}

getStatusBarHeight() {
  if (isIos()) {
    this.statusBarHeight = isIphone14ProMax() ? 59 + 103 : isIphoneXSeries() ? 47 + 103 : 20 + 103;
  } else {
    this.statusBarHeight = 48 + 50;
  }
}

  _renderListView() {
    let dataList = this.state.dataList || [];
    dataList.sort((a, b) => b.timestamp - a.timestamp);
    
    const lists = this.lists;

    let typeDataList = this.state.currentIndex === 0 ? dataList : [];
    if (this.state.currentIndex !== 0) {
      dataList.map(item => {
        const eventLists1 = Number(item.event_type) && Number(item.event_type).toString(2) && Number(item.event_type).toString(2).split('');
        const eventLists = this.fillArray(eventLists1)
        if (eventLists[lists[this.state.currentIndex]] == '1') {
          typeDataList.push(item)
        }
      })
    }
    return (
      <ScrollView
        raw={true}
        data={typeDataList}
        style={{height: height - this.statusBarHeight}}
        contentContainerStyle={{paddingBottom: 30}}
      >
        {
          typeDataList.length > 0 ? <View style={{flex: 1, flexDirection: 'row', flexWrap: 'wrap', gap: 14, paddingLeft: 14}}>
            {Array.isArray(typeDataList) && typeDataList.map((item, index) => {
              return (this._renderItem(item, index))
            })}
          </View> : <View style={{ flex: 1, height: 200, justifyContent: 'center', alignItems: 'center' }}>
            <Text>{stringsTo('housekeeping_no_event')}</Text>
          </View>
        }
      </ScrollView>
    );
  }

  handleCurrent = (item) => {
    // console.log(11111, isEdit)
    if (this.state.isEdit) {
      const numAry = this.state.numAry;
      if (this.state.numAry.includes(String(item.timestamp))) {
        numAry.splice(this.state.numAry.indexOf(String(item.timestamp)), 1)
      } else {
        if (numAry.length >= 50) {
          showToast(stringsTo('delete_failed_limit'));
          return
        }
        numAry.push(String(item.timestamp))
      }
      this.setState({
        numAry
      })
      return
    }
    if (this.areadyClick) {
      return
    }
    this.areadyClick = true
    this.firstLoad = false;
    let { cameraNumber = '1' } = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model); //是否显示圆盘
    const isSingleCamera = cameraNumber === '1'
    this.props.navigation.push(isSingleCamera ? 'BackVideo' : 'BackDualVideo', {
      timeListDataNew: this.state.timeListDataNew,
      firstKey: this.props.route.params.firstKey,
      secondKey: this.props.route.params.secondKey,
      currentTimestamp: item.timestamp
    });
  }

  _renderItem = (item, index) => {
    let {timestamp, duration} = item;
    let time = moment(timestamp * 1000).format('HH:mm');
    const endTime = moment(timestamp * 1000 + duration).format('HH:mm');
    let isSelected = false;
    if (this.state.numAry.length > 0) {
      isSelected = this.state.numAry.includes(String(item.timestamp))
    } else {
      isSelected = this.state.isSelectedAll;
    }

    const lists = {
      19: require('../../../resources/images/pic_move.png'),
      18: require('../../../resources/images/pic_person.png'),
      0: require('../../../resources/images/pic_sound.png')
    }

    const eventTypeList = [];
    const eventLists1 = Number(item.event_type) && Number(item.event_type).toString(2) && Number(item.event_type).toString(2).split('');
    const eventLists = this.fillArray(eventLists1)

    Object.keys(lists).map(res => {
      if (eventLists[res] == '1') {
        eventTypeList.push(lists[res])
      }
    })
   
    return (
        <XView
            key={index}
            style={styles.itemParent}
            onPress={() => this.handleCurrent(item)}
            accessibilityLabel={'back_item_view_' + time}>
                {/* <PathImage item={item.imgUrl} deviceID={LetDevice.deviceID} /> */}
                {<IMIImageView style={styles.itemIcon} source={{uri: `file://${this.state.timeList[item.timestamp]}`}} />}
                <XView
                  style={{
                    flex: 1,
                    alignItems: 'center',
                    flexDirection: 'row',
                    marginTop: 4,
                  }}
                >
                  {eventTypeList &&
                    eventTypeList.length > 0 &&
                    eventTypeList.map(res => {
                      return <XImage raw={true} style={{marginLeft: 0, width: 12, height: 12}} icon={res} />;
                    })}
                  <XText style={styles.itemTime} text={`${time}-${endTime}`} />
                </XView>
                {this.state.isEdit ? (
                  <XImage
                    raw={true}
                    style={{position: 'absolute', bottom: 20, right: 5, width: 20, height: 20}}
                    icon={
                      isSelected
                        ? require('../../../resources/images/icon_select_s.png')
                        : require('../../../resources/images/icon_select.png')
                    }
                  />
              ) : null}
        </XView>
    );
  };

   /**
   *获取事件选择特殊布局
   * @returns {*}
   */
   getSelectEventListView() {
    let eventItemWidth = width < 360 ? 120 : 159; //事件选项的宽度，低于1080P的屏幕宽度为120
    let eventItemNum = parseInt(width / eventItemWidth); //每行事件选项的个数
    let containerWidth = width - 14; //事件列表容器宽度为屏幕宽度减去左右内边距7*2
    //每行除了事件选项留下的空白区域
    let eventItemMarginTotal =
      containerWidth - eventItemWidth * eventItemNum > 10
        ? containerWidth - eventItemWidth * eventItemNum
        : containerWidth - eventItemWidth * (eventItemNum - 1);
    eventItemNum = eventItemMarginTotal >= eventItemWidth ? eventItemNum - 1 : eventItemNum;
    //每个事件选项的水平边距
    let eventItemMarginHorizontal = eventItemMarginTotal / (eventItemNum * 2);

    return (
      <View
        style={{
          padding: 7,
          flexWrap: 'wrap',
          flexDirection: 'row',
          //justifyContent: 'space-between' //废除这个属性，计算每个时间选项的水平边距因为不能适配所有屏幕，见bug#6972
        }}>
        {this.state.eventTypeList.map((content, index) => (
          <TouchableOpacity key={index} activeOpacity={1} onPress={this._onEventItemPress.bind(this, content, index)}>
            <View
              style={[
                {
                  marginHorizontal: eventItemMarginHorizontal,
                  marginVertical: 7,
                  width: eventItemWidth,
                  backgroundColor: colors.page_bg,
                  flex: 1,
                  alignItems: 'center',
                }
              ]}>
              <Text style={{paddingTop: 10, paddingBottom: 10, color: this.state.currentIndex === index ? imiThemeManager.theme.primaryColor : ''}}>
                {content.eventTypeName}
              </Text>
            </View>
          </TouchableOpacity>
        ))}
      </View>
    );
  }

    /**
   * 事件选择监听按下
   * @param currentTitles
   * @param index
   */
    _onEventItemPress(conent, index) {
      this.setState({
        currentIndex: index
      })
      console.log(index)
      this.topSelectBarRoot.funOnPress(conent.eventTypeName);
    }

    _assignTopSelectBarRoot = component => {
      this.topSelectBarRoot = component;
    };

  handleSelect() {
    this.topSelectBarRoot?.currentActive() >=0 && this.topSelectBarRoot.openOrClosePanel(this.state.currentIndex);
    this.setState({isEdit: true, numAry: [], isSelectedAll: false});
  }

  handleAll(){
    const dataList = this.state.dataList
    let typeDataList = this.state.currentIndex === 0 ? dataList : [];
    const lists = this.lists
    if (this.state.currentIndex !== 0) {
      dataList.map(item => {
        const eventLists1 = Number(item.event_type) && Number(item.event_type).toString(2) && Number(item.event_type).toString(2).split('');
        const eventLists = this.fillArray(eventLists1)
        if (eventLists[lists[this.state.currentIndex]] == 1) {
          typeDataList.push(item)
        }
      })
    }

    const currentNumAry = this.state.numAry;
    if (currentNumAry.length === 50) {
      this.setState({
        isSelectedAll: false, 
        numAry: []
      });
      return
    }
    if (typeDataList.length > 50) {
      const newNumAry = [...currentNumAry]
      typeDataList.map((res) => {
        if (!newNumAry.includes(String(res.timestamp))) {
          if (newNumAry.length < 50) {
            newNumAry.push(String(res.timestamp))
          }
        }
      });
      
      this.setState({
        isSelectedAll: false, 
        numAry: newNumAry
      });
      showToast(stringsTo('delete_failed_limit'));
      return
    }
    this.setState({
      isSelectedAll: 
      !this.state.isSelectedAll, 
      numAry: this.state.isSelectedAll ? [] : typeDataList.map(res => {return String(res.timestamp)})
    });
  }


  _renderEditView() {
    if (this.state.isEdit && this.state.numAry && this.state.numAry.length > 0) {
      return (
        <View style={{bottom: 0, height: isIos() ? 100 : 64, width: '100%', backgroundColor: '#FFFFFF', flexDirection: 'column', position: 'absolute'}}>
          <View style={{height: 2, backgroundColor: '#F1F1F1'}} />
          <View
            style={{
              height: 62,
              width: '100%',
              backgroundColor: '#FFFFFF',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'row',
            }}>
            <TouchableOpacity
              onPress={() => {
                if (this.state.numAry.length === 1 ) {
                  this._downloadPress()
                } else {
                  showToast(stringsTo('max_download_limit'))
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center', marginRight: 30}}
              accessibilityLabel={'housekeeping_delete'}>
              <Image style={{width: 30, height: 30}} source={require('../../../resources/images/icon_alarm_down.png')} />
              <Text
                style={{fontSize: 16}} //设计稿改动
              >
                {stringsTo('downLoadTitle')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                if (this.state.numAry.length > 0) {
                  this.setState({showDeleteTip: true});
                } else {
                  showToast(stringsTo('select_tip'));
                }
              }}
              style={{alignItems: 'center', justifyContent: 'center'}}
              accessibilityLabel={'housekeeping_delete'}>
              <Image style={{width: 30, height: 30}} source={require('../../../resources/images/play_back_delete.png')} />
              <Text
                style={{fontSize: 16, color: '#E74D4D'}} //设计稿改动
              >
                {stringsTo('delete_title')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      );
    }
    return null;
  }

  deletImageArray() {
    console.log("shanchule11111");
    
    if (this.state.numAry.length > 50) {
      showToast(stringsTo('delete_failed_limit'));
      return;
    }
    if (this.downloadFlag) {
      console.log('拦截重复点击')
      return
    }
    this.downloadFlag = true;
    const pic_locals = this.state.numAry.map(res => {
      return String(this.localPic[res])
    })
    IMP2pClient.operationFile(['0'],['2'], [this.state.numAry], [pic_locals]);
    // this.deleteTime = setTimeout(() => {
    //   this.downloadFlag = false;
    //   showLoading(false);
    //   showToast(stringsTo('delete_failed'));
    // }, this.state.numAry.length <= 3 ? 10000 : this.state.numAry.length * 3000)
    // 每隔两秒去问询一次删除进度
    this.deleteTime && clearInterval(this.deleteTime);
    this.deleteTime = setInterval(() => {
      IMP2pClient.getChannelState(data => {
        if (parseInt(data, 10) === 1) {
          IMP2pClient.operationFile(['0'],['4'], [[]], [[]]);
        } else {
          // p2p通道断开
          this.downloadFlag = false;
          showLoading(false);
          showToast(stringsTo('delete_connect_failed'));
          this.deleteTime && clearInterval(this.deleteTime);
        }
      })
    }, 2000)
  }


  deleteSuccess() {
    this.deleteTime && clearInterval(this.deleteTime);
    if (!this.downloadFlag) {
      return
    }
    this.state.numAry.map(res => {
      deleteContent(this.props.route.params.firstKey, this.props.route.params.secondKey, res)
    })
    this.setState({isEdit: false, numAry: []});
    showLoading(false);
    showToast(stringsTo('delete_success'));
    this.downloadFlag = false;
    const {dataList} = this.getData()
    this.setState({
      dataList: dataList
    })
  }

  goBack() {
    if (this.props.navigation.canGoBack()) {
      this.props.navigation.goBack();
      return
    }
    IMIGotoPage.exit();
  }

  render() {
    let data = [
      [this.getSelectEventListView.bind(this)],
    ];
    let isEmpty = true;
    const lists = this.lists
    if (this.state.currentIndex !== 0) {
      this.state.dataList.map(item => {
        const eventLists1 = Number(item.event_type) && Number(item.event_type).toString(2) && Number(item.event_type).toString(2).split('');
        const eventLists = this.fillArray(eventLists1)
        if (eventLists[lists[this.state.currentIndex]] == 1) {
          isEmpty = false
        }
      })
    } else {
      isEmpty = this.state.dataList?.length <= 0
    }
    
    return (
      <View
        ref={ref => (this.componentContainer = ref)}
        pointerEvents="box-none"
        style={{flex: 1, backgroundColor: '#FFFFFF', flexDirection: 'column'}}>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={'transparent'}
          title={this.state.isEdit ? I18n.t('select_title_3', {code: this.state.numAry.length}) : this.state.title}
          left={!this.state.isEdit ? [
             {
              key: NavigationBar.ICON.BACK,
              onPress: () => {
                this.goBack()
              },
              accessibilityLabel: 'housekeeping_back',
            }
          ] : [{
            key: NavigationBar.ICON.CUSTOM,
            n_source: require('../../../resources/images/houseKeepingV2/icon_angel_del.png'),
            onPress: () => {
              this.setState({isEdit: false, numAry: []});
            },
            accessibilityLabel: 'housekeeping_back',
          }]}
          right={!this.state.isEdit ? [
             {
              key: NavigationBar.ICON.CUSTOM,
              n_source: !isEmpty ? require('../../../resources/images/icon_edit.png')
              :require('../../../resources/images/icon_edit_disabled.png'),
              onPress: _ => {!isEmpty && this.handleSelect()},
              accessibilityLabel: 'back_editor',
            },
          ] : [{
            key: NavigationBar.ICON.CUSTOM,
            n_source: require('../../../resources/images/houseKeepingV2/icon_angel_allSelect.png') ,
            onPress: _ => {
              this.handleAll()
            },
            accessibilityLabel: 'back_editor',
          }]}
        />
        <View>
          <View style={{height: 48, zIndex: 99}}>
            <TopSelectBar
              controlLoad={true}
              disabled={this.state.isEdit}
              ref={this._assignTopSelectBarRoot}
              style={{flex: 1}}
              bgColor={colors.page_bg}
              tintColor={colors.gray}
              activityTintColor={imiThemeManager.theme.primaryColor}
              titleStyle={{color: imiThemeManager.theme.primaryColor}}
              // hideTitleSelect={false}
              externalSelectText={[this.state.selectEventName]}
              data={data}>
            </TopSelectBar>
          </View>
          {this._renderListView()}
        </View>
        {this._renderEditView()}
        <MessageDialog
          title={stringsTo('delete_alert')}
          visible={this.state.showDeleteTip}
          canDismiss={true}
          onDismiss={() => {
            this.setState({showDeleteTip: false});
          }}
          buttons={[
            {
              text: I18n.t('cancel'),
              accessibilityLabel: 'cancelDeleteTimeList',
              callback: _ => {
                this.setState({showDeleteTip: false});
              },
            },
            {
              text: I18n.t('ok_button'),
              accessibilityLabel: 'okDeleteTimeList',
              callback: _ => {
                showLoading(stringsTo('delete_title_loading'), true);
                this.setState({showDeleteTip: false});
                this.deletImageArray();
              },
            },
          ]}
        />
      </View>
    );
  }
}

const styles = StyleSheet.create({
    itemTitle: {
      fontSize: 12,
      paddingLeft: 14,
      color: colors.black,
      fontWeight: 'bold'
    },
    itemParent: {
      // paddingLeft: 14,
      paddingTop: 14,
      backgroundColor: colors.white,
    },
    itemTime: {
      fontSize: 11,
      color: colors.gray,
      marginLeft: 1,
      borderRadius: 10,
    },
    itemIcon: {
      borderRadius: 4,
      width: (windowWidth - 4 * 14) / 3,
      height: (windowWidth - 4 * 14) / 3 / 16 * 9,
    },
});
  
