/**
 * PlayBackPagePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 *
 * 示例:
 * <PlayBackPagePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </PlayBackPagePlayerComponent>
 *
 * @author: yanmin
 * @date: 2020/12/28
 */

import React, {Component} from 'react';
import {
  View,
  Text,
  BackHandler,
  ActivityIndicator,
  StatusBar,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  Image,
  TouchableWithoutFeedback,
  Animated,
  PanResponder,
} from 'react-native';
import {
  DateUtils,
  LetDevice,
  IMILog,
  IMIStorage,
  aliAlarmEventCloudApi,
  IMIGotoPage,
  PlayerClass,
} from '../../../../../imilab-rn-sdk';
import IMVodPlayView from '../../../../../imilab-rn-sdk/native/camera-kit/IMVodPlayView';

import IMICameraVideoView from '../../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView';
import IMIFile from '../../../../../imilab-rn-sdk/native/local-kit/IMIFile';
import {stringsTo} from '../../../../../globalization/Localize';
import NavigationBar from '../../../../../imi-rn-commonView/NavigationBar/NavigationBar';
import Orientation from 'react-native-orientation';

import PropTypes from 'prop-types';
import {isAndroid, isIos, isIphoneXSeries, isPhoneX, isIphone14ProMax} from '../../../../../imilab-rn-sdk/utils/Utils';
import IMIToast from '../../../../../imilab-design-ui/src/widgets/IMIToast';

import TimeScaleDualView from '../TimeLineView/TimeScaleDualView';
import CenterTimeView from '../TimeLineView/CenterTImeView';
import {colors, imiThemeManager, showLoading, RoundedButtonView, showToast} from '../../../../../imilab-design-ui';
import ImageButton from '../../../../../imi-rn-commonView/ImageButton/ImageButton';
import I18n from '../../../../../globalization/Localize';
import {XText, XView} from 'react-native-easy-app';
import moment from 'moment';
import Toast from 'react-native-root-toast';
import IMIPermission from '../../../../../imilab-rn-sdk/native/local-kit/IMIPermission';
import {IMINativeLifeCycleEvent} from '../../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle';

import {timeFilter} from '../../../../../imilab-rn-sdk/utils/DateUtils';

import DeviceTemplatesUtils from '../../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils';
import NetInfo from '@react-native-community/netinfo';
import VersionUtils from '../../utils/VersionUtils';
import IMP2pClient from '../../../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import {
  byteArrayToInt4,
  byteArrayToLong8,
  // findOneMinuteIndex,
  findStartTime,
} from '../../utils/GenericUtils';
import {
  getAllBackList,
  changeAllBackList,
  changeAllBackListDetail,
  getTimeLineLists,
  changeTimeLineLists,
} from '../dataUtils';
import {EVENT_TYPE_COLOR} from '../TimeLineView/EventTypeConfig';
import {RN_ROUTE_PAGE_TAG} from '../../../../../config/configConstant';
import IMIRenderViewManager from '../../../../../imilab-rn-sdk/native/camera-kit/IMIRenderViewManager';

const screenWidth = Dimensions.get('window').width;
const screenHeight = Dimensions.get('window').height;

//import {open} from '@op-engineering/op-sqlite';

//import SQLite from 'react-native-sqlite-storage';

const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const speedTitleAry = ['1X', '4X', '8X', '16X'];
const speedAccessibilityLabelTitleAry = [
  'play_back_clarity_show_1X',
  'play_back_clarity_show_2X',
  'play_back_clarity_show_4X',
  'play_back_clarity_show_/8X',
  'play_back_clarity_show_16X',
];
const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});

const VOD_PLAYER_STATUS = {
  PREPARED: 'prepared', //准备完毕，调用start即可播放
  LOADING: 'loading',
  PLAYING: 'playing', //正在播放
  PAUSE: 'pause',
  ERROR: 'error',
};

let nowDay = new Date();
let backupIsPlay = false;
let currentPlayTime = -1; //记录断开前的时间
let playEndTime = 0; //记录播放完成的currentTime
let PauseLineTime = 0; //暂停时line进度
let retryNum = 0; //错误后，点击重试的提示
let time;
let automaticDay = null; //一天播放完后如果第二天有视频则自动跳转并切换
let lastClickSnapPhoto = 0; //一次进入切换天数的时间
let canRecord = true; //允许点击录屏
let isStopping = false; //正在结束录制中
let dateDataArr = new Map();
//今天0点的时间戳(s)
let todayStartTime =
  new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0, 0).getTime() / 1000;
let currentDayStartTime = todayStartTime; //当前选中天的0点，点击日历便会更新

let isCheckingPermission = false;
let {playBackPlayFinishCallback = false} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
let StatusBarHeight = 70;
let textColorUnselected = '#666666';
let textColorSelected = '#fff';
let unselectedColor = '#F7F7F7';

const PLAYER_TYPE = PlayerClass.VOD;

const TAG = 'PlayBackDualComponent';
export default class PlayBackDualComponent extends Component {
  static VOD_PLAYER_STATUS = VOD_PLAYER_STATUS;

  constructor(props, context) {
    super(props, context);
    this.state = {
      bps: -1,
      isFullScreen: false, //是否全屏
      switchCameraId: 0, //当前显示的camera
      isShowDoubleCamera: true, // 全屏时，是否显示双画面
      isShowSelectPicturePop: false, // 是否显示画面选择弹窗

      mute: true,
      recording: false, //录屏监听  true为录屏中
      recordingCameraId: -1, //处于录屏中的cameraId
      recordDuration: 0, //录屏时长,小于6秒提示失败
      showFullScreenTools: false,

      isLoading: false, //加载中
      hasData: false,
      noData: false,
      errorFind: false,
      showErrorView: false, //错误view
      showPauseView: false, //暂停view
      errorCode: null, //错误code
      isStart: false, // 记录是否开始录制
      dataArray: [],
      //正在显示中的日期的0点，注意：点击完日历，_queryDayData回调后才会更新，因此在_queryDayData慎用currentDate
      currentDate: new Date(new Date().getFullYear(), new Date().getMonth(), new Date().getDate(), 0, 0, 0),
      speed: 0, //倍速
      snapshotVisible: false, //截图是否显示中
      screenShotPath: null,
      screenShotPathType: 0,
      isPlay: false, //是否播放中 true为播放中
      isShowCalendar: false,
      isClickPause: false,
      dateData: {}, //日期显示
      isPlayFinish: false, //是否回看播放结束
      dateText: '',
      showNoData: false, //标记所选择的时间是否有回看视频
      isConnected: true,
      offsetTime: -1,
      isCenterValueChanged: false,
      timeBeingPlayed: 0, //正在播放的时间
      backList: [],
      playStartTime: {camera_id: 0, timestamp: null, duration: 0, event_type: 0, offset: 0}, //正在播放的视频的数据
      isDataUsage: false,
      isFinish: false,
      canPlay: true,
      isShowZoomScale: false, //展示缩放比例小窗
      zoomScale: 1.0,
      stateType: undefined,
      touchMovement: false,
      eventTypeList: [],
      eventTypeUnSelect: [],
    };

    this.timeBeingPlayedPre = 0;
    this.sdStatus = true;
    this.latestTime = 0;
    this.count = 1;
    this.leftData = null;
    this.isForegroundPage = true;
    this.playWithEndVersionSupport = false; //056是否支持默认从最近的视频进行播放
    this.returnPlaybakcReversListSupport = false; //固件是否支持逆序返回回看列表

    //currentDayStartTime是全局变量，插件销毁才会重置，又因为在点击日历时会更新，所以点击日历后退出再回来，会导致isToday不准
    currentDayStartTime = todayStartTime;
    this.errPrepare = false;
    this.errPlayTime = 0;
    this.getPlayWithEndVersionSupport(); //For ipc056
    this.getReverseVideoListSupport(); //For ipc062
    this.onP2pSendStateListener = null;
    this.operationFileInterval = null;
    this.currentSnapshotPath = null;
    this.allBackList = {}; //所有的视频列表
    this.lastBackDay = 0; // 最后一天的毫秒值（时间按照大到小排序）
    this.isView = false;
    this.currentData = null;
    this.allBackListDetail = {};
    this.currentTime = 0;
    this.getStatusBarHeight();
    this.currentCanPlay = true;
    this.currentMute = true;
    this.reconnectFlag = false;
    this.reconnectTime = '';
    this.goBackFlag = false;
    this.focusCanplay = true;
    this.timeLineLists = [];
    this.timeLineLists1 = [];
    this.goNext = false;
    this.areadyClick = false;
    this.navHeight = 50;
    this.CAMERA_ID = 0;

    this.pan = new Animated.ValueXY();
    this.panResponder = PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onStartShouldSetPanResponderCapture: () => true,
      onMoveShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponderCapture: () => true,

      // 手指按下
      onPanResponderGrant: (evt, gestureState) => {
        // 启动 500ms 计时器，标记可以开始拖动
        this.isLongPress = false;
        this.clickTimeout = setTimeout(() => {
          this.isLongPress = true; // 超过 500ms，允许拖动
          this.pan.setOffset({
            x: this.pan.x._value,
            y: this.pan.y._value,
          });
        }, 100);
      },

      // 手指移动
      onPanResponderMove: (evt, gestureState) => {
        // 长按500ms内不移动
        if (!this.isLongPress) {
          return;
        }

        // 超过500ms后，才更新Animated值
        Animated.event([null, {dx: this.pan.x, dy: this.pan.y}], {useNativeDriver: false})(evt, gestureState);
      },

      // 手指抬起
      onPanResponderRelease: (evt, gestureState) => {
        // 清除计时器
        if (this.clickTimeout) {
          clearTimeout(this.clickTimeout);
          this.clickTimeout = null;
        }
        this.pan.flattenOffset();

        if (!this.isLongPress) {
          // 点击逻辑：500ms内抬起，执行画面切换
          if (this.state.isFullScreen) {
            this.setState({
              switchCameraId: this.state.switchCameraId === 0 ? 1 : 0,
            });
          }
        } else {
          this.isLongPress = false;
        }
      },

      // 手指移出屏幕等意外结束
      onPanResponderTerminate: () => {
        if (this.clickTimeout) {
          clearTimeout(this.clickTimeout);
          this.clickTimeout = null;
        }
        this.pan.flattenOffset();
      },
    });
  }

  static propTypes = {
    navBar: PropTypes.func,
    videoRef: PropTypes.func,
    navBarRight: PropTypes.array,

    toolBarMoreItems: PropTypes.array,
    videoSubView: PropTypes.func,
    fullScreenToolBarMoreItems: PropTypes.array,
    lensCorrect: PropTypes.shape({
      use: PropTypes.bool.isRequired,
      x: PropTypes.number,
      y: PropTypes.number,
    }),
    onVideoClick: PropTypes.func,
    onVodPlayerStatusChange: PropTypes.func,

    loadingView: PropTypes.func,
    pauseView: PropTypes.func,
    errorView: PropTypes.func,

    isSleepStatus: PropTypes.bool, //休眠状态
    isOnLine: PropTypes.bool, //在线状态
    onCheckPermissionStatusChange: PropTypes.func, //回调是否处于权限检测状态中
  };

  static defaultProps = {
    navBar: undefined,
    navBarRight: [],
    toolBarMoreItems: [],
    fullScreenToolBarMoreItems: [],
    lensCorrect: {use: false, x: 0, y: 0},
    isSleepStatus: false,
    isOnLine: true,
  };

  UNSAFE_componentWillMount() {
    Orientation.lockToPortrait();
    this.count = 1;
    this.leftData = null;
    this.allBackList = {}; //所有的视频列表
    this.allBackListDetail = {};
    this.timeLineLists = [];
    this.timeLineLists1 = [];
    this.lastBackDay = 0; // 最后一天的毫秒值（时间按照大到小排序）
    this.latestTime = 0;
  }

  getStatusBarHeight() {
    if (isIos()) {
      StatusBarHeight = isIphone14ProMax() ? 59 + 50 : isIphoneXSeries() ? 47 + 50 : 20 + 50;
    } else {
      StatusBarHeight = parseInt(StatusBar.currentHeight) + 50;
    }
  }

  /**
   * 暂时缓存最近三个月每天是否有回看视频的情况
   * 请求最近三个月的每个月每天是否有回看的标记数组
   */
  queryThreeMonthData(data, count) {
    const rawData = window.atob(data);
    const uint8Arraypart = new Uint8Array(rawData.length);
    for (let i = 0; i < rawData.length; i++) {
      uint8Arraypart[i] = rawData.charCodeAt(i);
    }
    if (byteArrayToInt4(uint8Arraypart, 4) !== 0) {
      try {
        const messageResult = JSON.parse(rawData.slice(4, -1));
        if (messageResult.cmd_type === 0 && messageResult.code === 4) {
          showLoading(false);
          showToast(stringsTo('housekeeping_no_event'), Toast.positions.BOTTOM);
          this.setState({
            noData: true,
          });
          return;
        }

        // 获取视频文件索引失败
        if (
          messageResult.cmd_type === -1 &&
          (messageResult.code === 1001 || messageResult.code === 1004 || messageResult.code === 1011)
        ) {
          this.setState({
            errorFind: true,
          });
          return;
        }
      } catch (error) {
        console.log(error);
      }
      return;
    }
    const uint8Array = uint8Arraypart.slice(84);
    const list = uint8Arraypart.slice(0, 84);

    let currentFlag = 0;
    list.slice(47, 55).forEach((num, index) => {
      currentFlag += num * Math.pow(256, index);
    });
    if (String(currentFlag) !== this.operateTimeStamps && currentFlag !== 0) {
      IMILog.logI(
        '不属于这一次数据',
        JSON.stringify({
          currentFlag: currentFlag,
          operateTimeStamps: this.operateTimeStamps,
          origin: JSON.stringify(list.slice(47, 55)),
        }),
      );
      return;
    }
    this.waitingData && clearTimeout(this.waitingData);
    if (byteArrayToLong8(list, 39) !== 0) {
      IMILog.logI('最新数据来拉', JSON.stringify({time: new Date().getTime()}));
      this.loadMoreData(uint8Array);
      return;
    }
    const selectTime = this.state.dateData;
    let newDate = null;
    //1970年时间戳，为了去除不正确时间,只有时间不是近几年的 就可以认为失败
    const failureTime = new Date('Wed Jun 10 1970 15:00:00 GMT+0800').getTime();
    const notEmpty = this.allBackList && Object.keys(this.allBackList) && Object.keys(this.allBackList).length > 0;
    this.count++;
    for (let i = 0; i < uint8Array?.byteLength; i += 28) {
      const camera_id = byteArrayToInt4(uint8Array, i); // 镜头id
      const timestamp = byteArrayToLong8(uint8Array, i + 4); // 开始时间utc , 单位s
      const duration = byteArrayToInt4(uint8Array, i + 12); // 时长
      // const event_type = byteArrayToLong8(uint8Array, i + 16); //时间类型
      const event_type = byteArrayToInt4(uint8Array, i + 16); //时间类型
      const pic_loc = byteArrayToInt4(uint8Array, i + 20); //时间类型
      if (failureTime > timestamp * 1000) {
        continue;
      }
      // 获取最新的回看时间
      if (i === 0 && count === 1) {
        newDate = new Date(timestamp * 1000);
      }
      const event = {
        startTime: timestamp * 1000,
        duration: duration,
        eventType: event_type,
        endTime: timestamp * 1000 + duration,
        camera_id,
        timestamp,
        event_type,
        pic_loc,
      };
      if (camera_id === 0) {
        this.timeLineLists.push(event);
      } else {
        this.timeLineLists1.push(event);
      }
      //最后一天
      this.lastBackDay = timestamp;
      const timeKey = moment(new Date(timestamp * 1000)).format('yyyy-MM-DD');
      const timeKey1 = moment(new Date(timestamp * 1000)).format('HH') + ':00';
      let timeKey2 = '';
      let currentTime = 0;
      if (moment(new Date(timestamp * 1000 + duration)).format('yyyy-MM-DD') !== timeKey) {
        timeKey2 = moment(new Date(timestamp * 1000 + duration)).format('yyyy-MM-DD');
        currentTime = new Date(timeKey2 + ' 00:00:00').getTime() / 1000;
      }
      selectTime[timeKey] = {
        marked: true,
        selectedColor: imiThemeManager.theme.primaryColor,
      };
      const list = this.allBackList[timeKey] || [];
      const list1 = this.allBackListDetail[timeKey] || {};
      list1[timeKey1] = list1[timeKey1] || [];
      list.push({
        camera_id,
        timestamp,
        duration: timeKey2 ? (currentTime - timestamp + 1) * 1000 : duration,
        event_type,
        pic_loc,
      });
      list1[timeKey1].push({camera_id, timestamp, duration, event_type, pic_loc});
      this.allBackList[timeKey] = list;
      this.allBackListDetail[timeKey] = list1;
      if (timeKey2) {
        const list2 = this.allBackList[timeKey2] || [];
        list2.push({
          camera_id,
          timestamp: currentTime,
          oldTimestamp: timestamp,
          duration: duration - (currentTime - timestamp) * 1000,
          event_type,
          pic_loc,
        });
        this.allBackList[timeKey2] = list2;
      }
      changeAllBackList(this.allBackList);
      changeAllBackListDetail(this.allBackListDetail);
    }

    let backList = null;
    const timelineMerge = [...this.timeLineLists, ...this.timeLineLists1].sort((a, b) => a.timestamp - b.timestamp);
    let findIndex = timelineMerge.length - 1;
    // 时间轴最新的
    if (count === 2) {
      try {
        backList = JSON.parse(JSON.stringify(timelineMerge));
      } catch (error) {
        IMILog.logI('获取JSON解析报错了', JSON.stringify({test: 5}));
        backList = [];
      }
      this.timeLineLists.sort((a, b) => a.timestamp - b.timestamp);
      this.timeLineLists1.sort((a, b) => a.timestamp - b.timestamp);
      this.timelineViewNew.initData(this.timeLineLists, this.timeLineLists1);
      const currentKey = moment(newDate).format('yyyy-MM-DD');
      selectTime[currentKey] = {
        ...selectTime[currentKey],
        selected: true,
        marked: !!selectTime[currentKey]?.marked,
      };

      // 参考米家直接取最新的开始时间
      this.timeBeingPlayed = backList ? backList[findIndex]?.timestamp : 0;
      this.currentTime = this.timeBeingPlayed;

      this.latestTime = backList[0]?.timestamp;
      this.loadMoreDataTimeOut && clearInterval(this.loadMoreDataTimeOut);
      // 从获取第一包数据开始每隔1分钟获取一次新数据

      if (this.latestTime > 0) {
        // 延迟初始化，否则安卓计算宽高有问题，iOS无影响
        setTimeout(() => {
          this._initVideoPrepare();
        }, 100);
      }

      this.loadMoreDataTimeOut = setInterval(() => {
        IMILog.logI('要求最新数据的时间到了', JSON.stringify({count: count, currentCount: this.count}));
        this.needMoreData();
      }, 61 * 1000);
    } else if (count > 2) {
      console.log(TAG, 'queryThreeMonthData initData ', count);
      this.timeLineLists.sort((a, b) => a.timestamp - b.timestamp);
      this.timeLineLists1.sort((a, b) => a.timestamp - b.timestamp);
      this.timelineViewNew.initData(this.timeLineLists, this.timeLineLists1);
    }
    changeTimeLineLists(timelineMerge);
    showLoading(false);
    this.currentData = backList ? backList : this.state.backList;

    this.setState(
      {
        currentDate: newDate ? newDate : this.state.currentDate,
        dateData: count === 1 ? selectTime : this.state.dateData,
        playStartTime: backList ? backList[findIndex] : this.state.playStartTime,
        backList: timelineMerge,
        isLoading: !this.currentCanPlay ? this.state.isLoading : notEmpty ? this.state.isLoading : count === 1,
        hasData: true,
        noData: false,
      },
      () => {
        if (count === 2) {
          this.timelineViewNew && this.timelineViewNew.scrollToTimestamp(this.timeBeingPlayed * 1000);
        }
      },
    );
  }

  // 发送最新数据
  needMoreData() {
    IMILog.logI('要求最新数据', JSON.stringify({time: this.latestTime, operateTimeStamps: this.operateTimeStamps}));
    const latestTime = String(this.latestTime);
    console.log(TAG, `latestTime: ${latestTime}, operateTimeStamps: ${this.operateTimeStamps}`);
    IMP2pClient.operationFile(['0', '1'], ['0', '0'], [[latestTime], [latestTime]], [[], []], this.operateTimeStamps);
  }

  // 加载最新数据
  loadMoreData(uint8Array) {
    //1970年时间戳，为了去除不正确时间,只有时间不是近几年的 就可以认为失败
    const failureTime = new Date('Wed Jun 10 1970 15:00:00 GMT+0800').getTime();
    for (let i = 0; i < uint8Array?.byteLength; i += 28) {
      const camera_id = byteArrayToInt4(uint8Array, i); // 镜头id
      const timestamp = byteArrayToLong8(uint8Array, i + 4); // 开始时间utc , 单位s
      const duration = byteArrayToInt4(uint8Array, i + 12); // 时长
      // const event_type = byteArrayToLong8(uint8Array, i + 16); //时间类型
      const event_type = byteArrayToInt4(uint8Array, i + 16); //时间类型
      const pic_loc = byteArrayToInt4(uint8Array, i + 20); //时间类型
      if (failureTime > timestamp * 1000) {
        continue;
      }
      // 获取最新的回看时间
      if (i === 0) {
        this.latestTime = timestamp;
      }
      const event = {
        startTime: timestamp * 1000,
        duration: duration,
        eventType: event_type,
        endTime: timestamp * 1000 + duration,
        camera_id,
        timestamp,
        event_type,
        pic_loc,
      };
      if (camera_id === 0) {
        this.timeLineLists.push(event);
      } else {
        this.timeLineLists1.push(event);
      }
      const timeKey = moment(new Date(timestamp * 1000)).format('yyyy-MM-DD');
      const timeKey1 = moment(new Date(timestamp * 1000)).format('HH') + ':00';
      const list = this.allBackList[timeKey] || [];
      const list1 = this.allBackListDetail[timeKey] || {};
      list1[timeKey1] = list1[timeKey1] || [];
      list.unshift({camera_id, timestamp, duration, event_type, pic_loc});
      list1[timeKey1].unshift({camera_id, timestamp, duration, event_type, pic_loc});
      this.allBackList[timeKey] = list;
      this.allBackListDetail[timeKey] = list1;
      changeAllBackList(this.allBackList);
      changeAllBackListDetail(this.allBackListDetail);
    }
    const timelineMerge = [...this.timeLineLists, ...this.timeLineLists1].sort((a, b) => a.timestamp - b.timestamp);
    console.log(TAG, 'queryThreeMonthData load more initData');
    this.timelineViewNew.initData(this.timeLineLists, this.timeLineLists1);
    changeTimeLineLists(timelineMerge);
    this.setState({
      backList: timelineMerge,
    });
    if (!this.state.hasData) {
      this.setState({
        hasData: true,
        noData: false,
        hideVideo: false,
      });
    }
  }

  componentDidMount() {
    // 获取事件类型
    this.getEventType();
    this.navHeight = this.navigationBar?.getNavigationBarHeight();
    this.operateTimeStamps = String(new Date().getTime());
    // let monthStr = new Date(this.state.currentDate).getMonth() + 1;
    // let yearStr = new Date(this.state.currentDate).getFullYear();
    // currentPlayTime = 0;//记录断开前的时间 每次进来重新赋值
    // 每次进来需要把时间定位到当前
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    PauseLineTime = (new Date().getTime() - today.getTime()) / 1000;
    //this.queryThreeMonthData();
    // 物理返回键需打开，否则全屏返回事件不对
    this.backHandler = BackHandler.addEventListener('hardwareBackPress', _ => {
      this._onPressBack();
      return true;
    });
    const dateData = {};
    dateData[DateUtils.dateFormat(this.state.currentDate, 'yyyy-MM-dd')] = {
      selected: true,
    };
    this.setState({
      dateTime: DateUtils.dateFormat(this.state.currentDate, 'yyyy-MM-dd'),
      dateData: dateData,
      mute: true,
    });
    this.IMIVideoView && this.IMIVideoView.setIsMute(true);
    this._subscribe_focus = this.props.navigation.addListener('focus', () => {
      this.allBackList = getAllBackList();
      this.timeLineLists = getTimeLineLists();

      this.timelineViewNew && this.timelineViewNew.initData(this.timeLineLists, this.timeLineLists1);
      this.areadyClick = false;
      this.goNext = false;
      this.setState({
        goNext: false,
      });
      if (this.timeLineLists.length == 0 && this.state.hasData) {
        this.setState({
          isLoading: false,
          hasData: false,
          noData: true,
          backList: [],
        });
        return;
      }
      console.log('focus---进入当前回看页面全屏状态', this.state.playStartTime, this.timeBeingPlayed);
      //直连模式回到本页面在此处理，其他在PlayBackPage处理
      if (LetDevice.isLocalDevice && !this.isForegroundPage) {
        //this.isForegroundPage初始值为false,每次进入都会走此逻辑，导致seekTo(-1000),iOS出错
        // this.IMIVideoView && this.IMIVideoView.resume();
        //本地直连设备需要手动seekTo到暂停的位置，否则会从当天的初始时间点播放
        //this.IMIVideoView && this.IMIVideoView.seekTo(currentPlayTime * 1000.0);
      }
      const playStartTime = this.findAndSetPlayTime(this.timeLineLists);
      console.log('回看回来了---------', playStartTime);
      this.setState(
        {
          playStartTime,
          isLoading: this.currentCanPlay && this.state.hasData ? true : false,
          backList: this.timeLineLists,
          hideVideo: false,
        },
        () => {
          this.timelineViewNew && this.timelineViewNew.scrollToTimestamp(this.currentTime * 1000);
          if (!this.currentCanPlay) {
            return;
          }
          // setTimeout(() => {
          // this.uniqueKey == this.props.uniqueKey && this.IMIVideoView?.prepare();
          // if (this.uniqueKey == this.props.uniqueKey) {
          //   switch (this.state.speed) {
          //     case 0:
          //       this.IMIVideoView && this.IMIVideoView.speed(1);
          //       break;
          //     case 1:
          //       this.IMIVideoView && this.IMIVideoView.speed(4);
          //       break;
          //     case 2:
          //       this.IMIVideoView && this.IMIVideoView.speed(8);
          //       break;
          //     case 3:
          //       this.IMIVideoView && this.IMIVideoView.speed(16);
          //       break;
          //   }
          // }
          // console.log('===========', this.uniqueKey, this.props.uniqueKey);
          // if (!this.IMIVideoView) {
          //   this.setState({isLoading: false});
          // } else {
          //   this.setState({isLoading: false, showPauseView: false});
          // }
          // });
        },
      );
      this.isForegroundPage = true;
      //StatusBar.setBarStyle('light-content');
    });

    this._subscribe_blur = this.props.navigation.addListener('blur', () => {
      this.isForegroundPage = false;
      this.focusCanplay = this.currentCanPlay;
      this.uniqueKey = this.props.uniqueKey;

      // this.currentTime = this.timeBeingPlayed;
      showLoading(false);
      if (this.state.recording) {
        showToast(stringsTo('screen_recording'));
        return true;
      }
      // this.currentMute = true;
      this.IMIVideoView && this.IMIVideoView.setIsMute(true);
      this.setState(
        {
          mute: true,
          isLoading: false,
          isPlay: false,
          showPauseView: true,
        },
        () => {
          this.emptyVideo();
          this.setState({
            hideVideo: true,
          });
        },
      );
    });

    this.unsubscribe = NetInfo.addEventListener(state => {
      if (this.state.stateType !== state.type) {
        //切换网络类型
        this.setState({stateType: state.type, isConnected: state.isConnected});
        this.getNetWork(state.isConnected);
      }
    });
    // 每次进来先清空置定时器避免多次调用
    // this.operationFileInterval && clearInterval(this.operationFileInterval);
    // this.operationFileInterval = setInterval(() => {
    //   IMP2pClient.getChannelState(data => {
    //     console.log('zhixingle', data);
    //     if (parseInt(data, 10) === 1) {
    //       IMP2pClient.operationFile('0', []);
    //     }
    //   });
    // }, 10000);
    // 获取SD卡状态
    if (!this.sdStartTime) {
      this.sdStartTime = new Date().getTime();
    }

    this.count = 1;
    this.leftData = null;
    this.loadingData = false;
    this.allBackList = {}; //所有的视频列表
    this.allBackListDetail = {};
    changeAllBackList({});
    changeAllBackListDetail({});
    changeTimeLineLists([]);
    this.lastBackDay = 0; // 最后一天的毫秒值（时间按照大到小排序）
    this.getLoadData = 0;
    this.firstRenderTime = 0;
    this.prepareStart = 0;
    this.eventTime = 0;
    this.firstTime = 0;
    this.sendLoadData = 0;

    this.onP2pSendStateListener = IMP2pClient.onFileOperateAddListener(e => {
      if (e.iotId != LetDevice.deviceID) {
        return;
      }
      if (e.code === 0 && e.data) {
        IMILog.logI('有流到来', JSON.stringify({code: e.code}));
        if (!this.getLoadData) {
          this.getLoadData = new Date().getTime();
        }
        this.loadData(e);
      }
    });
    //shenyonggang@20210330 fixed bug IMI_HMI510_A01-327 start
    //进入后台
    this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
      if (this.state.recording) {
        console.log('录屏结束 切换后台保存视频-------------');
        this._stopRecord(true, true);
      }
      //关闭录像
      if (this.state.recording) {
        //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
        console.log('录制中进入台---------', this.state.recording);
        this._stopRecord(true);
        // if (CONST.isAndroid && this.state.isPlaying) {
        //   console.log('Android 进入后台--------_saveVideoToPhotosAlbum');
        //   console.log('调用_stopRecord——5');
        //   this._stopRecord(true);
        // } else {
        //   //录像停止IOS会自动停止视频录制，所以直接转存即可。Android因为直播流异常也只需直接转存视频即可
        //   console.log('iOS进入后台--------_saveVideoToPhotosAlbum');
        //   this._saveVideoToPhotosAlbum(true);
        // }
      }
      // if (this.state.isPlay && !isCheckingPermission) {
      //   backupIsPlay = true;
      //   // if(CONST.isIos){
      //   //     this.muteStatus = this.state.mute;
      //   //     console.log('保存当前声音状态===',this.muteStatus);
      //   // }
      //   // this.setState({isPlay: false, showPauseView: true}, () => {
      //   //   this.IMIVideoView && this.IMIVideoView.pause();
      //   // });
      // } else {
      //   backupIsPlay = false;
      // }
      if (!isCheckingPermission && this.isForegroundPage) {
        this.IMIVideoView && this.IMIVideoView?.stop();
        // this.currentMute = true;
        this.IMIVideoView && this.IMIVideoView.setIsMute(true);
        this.setState({mute: true});
      }
      console.log('addListener playbackPage enterBackgroundListener : backupIsPlay= ' + backupIsPlay);
    });
    //回到前台
    this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
      //启动
      console.log('addListener playbackPage enterForegroundListener : backupIsPlay= ' + backupIsPlay);
      // // console.log('回到前台当前声音状态---',this.state.mute);
      // if (backupIsPlay && !isCheckingPermission) {
      //   // console.log('获取当前声音状态===',this.muteStatus);
      //   retryNum = 0;
      //   this.setState({isPlay: true, showPauseView: false}, () => {
      //     this.isForegroundPage && this.IMIVideoView && this.IMIVideoView.resume();

      //     // if(CONST.isIos){
      //     //     if (this.muteStatus == false){
      //     //         // iOS 在进入后台前打开监听，在进入前台要打开监听
      //     //         this.setState({mute: false});
      //     //     }
      //     //     // this.muteStatus = this.state.mute;
      //     //     console.log('保存当前声音状态===',this.muteStatus);
      //     // }
      //   });
      // }
      // 只有不是初始化的的时候才能在进入播放时调用静音与否
      // this.setState({mute: true});
      const playStartTime = this.findAndSetPlayTime();
      this.setState(
        {
          showPauseView: false,
          isLoading: this.IMIVideoView ? true : false,
          playStartTime,
          canPlay: true,
          mute: this.currentMute,
          errorFind: false,
          showErrorView: false,
        },
        () => {
          setTimeout(() => {
            this.IMIVideoView && this.IMIVideoView.setIsMute(this.currentMute);
            this.IMIVideoView && this.IMIVideoView?.prepare();
            switch (this.state.speed) {
              case 0:
                this.IMIVideoView && this.IMIVideoView.speed(1);
                break;
              case 1:
                this.IMIVideoView && this.IMIVideoView.speed(4);
                break;
              case 2:
                this.IMIVideoView && this.IMIVideoView.speed(8);
                break;
              case 3:
                this.IMIVideoView && this.IMIVideoView.speed(16);
                break;
            }
          });
        },
      );
    });
    //shenyonggang@20210330 fixed bug IMI_HMI510_A01-327 end

    //510的播放结束回调
    /*  LetDevice.addDeviceEventChangeListener(data => {
      let {iotId, identifier, value} = JSON.parse(data);
      if (iotId == LetDevice.deviceID && identifier === 'onPlayBackEnd') {
        console.log('szm ============== 回看播放结束了:');
        this.IMIVideoView && this.IMIVideoView.pause();
        this.setState({isPlay: false, showPauseView: true});
      }
    }); */

    /*  this.unsubscribe = NetInfo.addEventListener(state => {
      console.log('当前网路状态', state);
      //监听防止录像未停止
      if (state.isConnected == false) {
        if (this.state.recording) {
          //直播流暂停时，停止录像
          if (CONST.isAndroid) {
            this._stopRecord(true, false);
            console.log('网络断开报错走这里');
            showToast('走网络监听停止录制');
          } else {
            //因为IOS会自动停止视频录制，所以直接转存即可
            this._saveVideoToPhotosAlbum();
          }
        }
      }
      console.log('Is connected?', state.isConnected);
    }); */
  }

  loadData = e => {
    //进来请求最近所有的数据
    this.queryThreeMonthData(e.data, this.count);
  };

  getPlayWithEndVersionSupport() {
    if (LetDevice.model != 'a1Godgpvr3D') {
      //目前仅ipc056通过固件升级支持了回看宫格事件和默认从最近开始播放视频
      return;
    }
    VersionUtils.getInstance()
      .get056FirmwareVersionPlayBackGridStatus()
      .then(res => {
        console.log('get056FirmwareVersionPlayBackGridStatus', res > 0);
        this.playWithEndVersionSupport = res > 0;
      })
      .catch(error => {
        console.log('get056FirmwareVersionPlayBackGridStatus error', error);
      });
  }

  getIsView() {
    return this.isView;
  }

  getReverseVideoListSupport() {
    if (LetDevice.model != 'a1MSKK9lmbs') {
      //目前仅ipc062支持固件端返回逆序的回看列表
      return;
    }
    VersionUtils.getInstance()
      .getIsSupportNewFunctionByVersion('062101_2.3.2_0163')
      .then(isSupport => {
        this.returnPlaybakcReversListSupport = isSupport;
      });
  }

  componentWillUnmount() {
    console.log('===========注销了');
    IMILog.logI('回看注销了', JSON.stringify({count: this.count}));
    if (this.props.goBack) {
      return;
    }
    try {
      this.onP2pSendStateListener && this.onP2pSendStateListener.remove();
      this.loadMoreDataTimeOut && clearInterval(this.loadMoreDataTimeOut);
      this.waitingData && clearTimeout(this.waitingData);
      Orientation.removeOrientationListener(this._orientationDidChange);
      // this.IMIVideoView && this.IMIVideoView.stop();
      // this._subscribe_focus && this._subscribe_focus();
      this._subscribe_blur && this._subscribe_blur();
      this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
      this.preparetimer && clearTimeout(this.preparetimer);
      this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
      this.backHandler && this.backHandler.remove();
      this._enterBackground && this._enterBackground.remove();
      this._enterForeground && this._enterForeground.remove();
      this.playTimeOut && clearTimeout(this.playTimeOut);
      this.unsubscribe && this.unsubscribe();
      currentPlayTime = -1;
      showLoading(false);
    } catch (error) {
      console.log('卸载失败-------');
    }
  }

  // 获取网络状态
  getNetWork(connected = true) {
    IMIStorage.load({
      key: LetDevice.deviceID + 'isDataUsageWarning',
      autoSync: true,
      syncInBackground: true,
    })
      .then(res => {
        this.setState({isDataUsage: res.isDataUsage, isFinish: true});
        if (res.isDataUsage) {
          NetInfo.fetch().then(state => {
            if (state.type == 'wifi') {
              this.setState({
                canPlay: true,
              });
              this.currentCanPlay = true;
              this.getFullData();
            } else {
              if (state.type == 'cellular') {
                showToast(stringsTo('isDataUsageTip'), '', 1000);
              }
              this.currentCanPlay = false;
              if (this.state.hasData && this.IMIVideoView) {
                this.IMIVideoView?.stop();
              } else {
                this.setState({
                  canPlay: false,
                  showPauseView: true,
                  isPlay: false,
                });
              }
            }
          });
        } else {
          this.setState({
            canPlay: true,
          });
          this.currentCanPlay = true;
          this.getFullData();
        }
      })
      .catch(_ => {
        this.setState({isDataUsage: false, isFinish: true, canPlay: true});
        this.currentCanPlay = true;
        connected && this.getFullData();
      });
  }

  // 获取数据
  getFullData(flag) {
    if (this.state.hasData) {
      const playStartTime = this.findAndSetPlayTime();
      this.focusCanplay = true;
      this.setState(
        {showPauseView: false, isLoading: this.IMIVideoView ? true : false, playStartTime, canPlay: true},
        () => {
          setTimeout(() => {
            this.IMIVideoView?.prepare();
            switch (this.state.speed) {
              case 0:
                this.IMIVideoView && this.IMIVideoView.speed(1);
                break;
              case 1:
                this.IMIVideoView && this.IMIVideoView.speed(4);
                break;
              case 2:
                this.IMIVideoView && this.IMIVideoView.speed(8);
                break;
              case 3:
                this.IMIVideoView && this.IMIVideoView.speed(16);
                break;
            }
          });
        },
      );
      return;
    }
    if (!this.props.isOnLine) {
      return;
    }
    flag && IMIToast.hideToast();
    this.setState({isLoading: true});
    LetDevice.getSingleProperty('10013')
      .then(data => {
        const value = data.value.value;
        if (value === 1) {
          showToast(stringsTo('noSdCardTitle'), Toast.positions.BOTTOM);
          this.sdStatus = false;
          this.setState({
            noData: true,
            isLoading: false,
          });
          return;
        }
        if (value === 8) {
          showToast(stringsTo('sdcard_status7'), Toast.positions.BOTTOM);
          this.sdStatus = false;
          this.setState({
            noData: true,
            isLoading: false,
          });
          return;
        }
        if (value !== 0 && value !== 2) {
          showToast(stringsTo('sdCardDamaged'), Toast.positions.BOTTOM);
          this.sdStatus = false;
          this.setState({
            noData: true,
            isLoading: false,
          });
          return;
        }
        if (!this.sdEndTime) {
          this.sdEndTime = new Date().getTime();
        }
      })
      .catch(error => {
        this.sdStatus = false;
        if (!this.isForegroundPage) {
          return;
        }
        this.setState({
          noData: true,
          isLoading: false,
        });
        showToast(stringsTo('sdCardName') + stringsTo('commLoadingFailText'), Toast.positions.BOTTOM);
      });

    // 双摄这样写
    IMP2pClient.operationFile(['0', '1'], ['0', '0'], [['0'], ['0']], [[], []], this.operateTimeStamps);
    this.sendLoadData = new Date().getTime();
    // 十秒后未获取到数据就报错
    this.waitingData = setTimeout(() => {
      IMILog.logI('超时了', JSON.stringify({sdStatus: this.sdStatus, isForegroundPage: this.isForegroundPage}));
      if (!this.sdStatus) {
        this.waitingData && clearTimeout(this.waitingData);
        return;
      }
      if (!this.isForegroundPage) {
        return;
      }
      this.setState({
        isLoading: false,
      });
      showToast(stringsTo('commLoadingFailText'), Toast.positions.BOTTOM);
    }, 10000);
  }

  _orientationDidChange = orientation => {
    console.log('回看页面方向---', orientation, this.state.isFullScreen);
    if (orientation === 'LANDSCAPE') {
    } else {
      // do something with portrait layout
      console.log('回看页退出全屏--全屏状态-gengxin', this.state.isFullScreen);
      if (this.state.isFullScreen && isIos()) {
        this._exitFullScreen();
        // StatusBar.setBarStyle("light-content");
        // StatusBar.setBackgroundColor('transparent');//状态栏字体刷黑色
        // NavigationBar.setBarStyle('dark-content');
      }
    }
  };

  _onPressFullScreen = (cameraId = 0) => {
    if (!this.state.hasData) {
      showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
      return;
    }
    isAndroid() ? Orientation.lockToLandscape() : Orientation.lockToLandscapeRight();
    this.setState({isFullScreen: true, switchCameraId: cameraId});
    NavigationBar.setStatusBarHidden(true);
    this.props.navigation.setOptions({tabBarVisible: false});
    this._onPressFullScreenTools();
  };

  _exitFullScreen = () => {
    this.pan.setValue({x: 0, y: 0});
    this._onCloseFullScreenTools();
    this.setState({isFullScreen: false}, () => {
      Orientation.lockToPortrait();
      NavigationBar.setStatusBarHidden(false);
      this.props.navigation.setOptions({tabBarVisible: true});
    });
  };

  _onPressBack = () => {
    if (this.state.isFullScreen) {
      this._exitFullScreen();
      return true;
    } else {
      if (this.state.recording) {
        showToast(stringsTo('screen_recording'));
        return true;
      }
      this.isView = false;
      this.goBackFlag = true;
      // 这样表明是从首页回来的
      if (this.props.route?.params?.starPageTag === RN_ROUTE_PAGE_TAG.playback) {
        IMIGotoPage.exit();
      } else {
        this.props.navigation.goBack();
      }
    }
    return true;
  };

  _onPressFullScreenTools = showFullScreenTools => {
    if (!showFullScreenTools) {
      this.setState({showFullScreenTools: true});
      this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
      this.fullScreenTooltsTimer = setTimeout(() => {
        if (this.timelineViewNew && !this.timelineViewNew.getIsScrolling()) {
          this._onCloseFullScreenTools();
        }
      }, 8000);
    } else {
      this._onCloseFullScreenTools();
    }
  };

  _onCloseFullScreenTools() {
    this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
    this.setState({showFullScreenTools: false});
  }

  _onPressMute = () => {
    console.log('点击静音');

    if (!this._canStepIn()) {
      return;
    }
    console.log('点击静音11');
    // if (this.state.speed != 0){
    //     showToast(stringsTo('play_speed_tip'));
    //     return;
    // }

    // if (this.state.speed)return;

    // currentPlayTime = 0;
    console.log(!this.state.mute);
    this.IMIVideoView && this.IMIVideoView.setIsMute(!this.state.mute);
    this.setState({mute: !this.state.mute});
    this.currentMute = !this.state.mute;
    // this.IMIVideoView && this.IMIVideoView.prepare();
  };

  getMute() {
    return this.state.mute;
  }

  setMute(mute) {
    this.currentMute = mute;
    this.IMIVideoView && this.IMIVideoView.setIsMute(mute);
    this.setState({mute: mute});
  }

  findAndSetPlayTime(backList) {
    console.log(TAG, 'findAndSetPlayTime');
    // 因为每次暂停播放，无论是主动还是被动都要重新记录播放时间和偏移
    // const {blackIndex} = findStartTime(this.state.backList, this.timeBeingPlayed, this.state?.currentDate);
    const lasterBakList = backList || this.state.backList;
    const {blackIndex} = findStartTime(lasterBakList, this.currentTime);
    const playStartTime = {...lasterBakList[blackIndex]};
    if (!playStartTime) {
      return 0;
    }
    let offset = Math.abs(playStartTime?.timestamp - this.currentTime);
    const isLast = blackIndex == lasterBakList.length - 1;
    // 最后一个片段的结束时间则向前偏移4s
    if (Math.floor(offset) >= Math.floor(playStartTime?.duration / 1000) && isLast) {
      offset = playStartTime?.duration / 1000 - 4;
    }
    playStartTime.offset = Math.floor(offset);
    if (playStartTime.oldTimestamp) {
      playStartTime.offset = playStartTime.timestamp - playStartTime.oldTimestamp + playStartTime.offset;
      playStartTime.timestamp = playStartTime.oldTimestamp;
    }
    return playStartTime;
  }

  /*告知外部调用者监听状态*/
  getFullscreen() {
    return this.state.isFullScreen;
  }

  quitFullScreen() {
    Orientation.lockToPortrait();
    this.setState({isFullScreen: false});
    NavigationBar.setStatusBarHidden(false);
    this.props.navigation.setOptions({tabBarVisible: true});
    this._onCloseFullScreenTools();
    console.log('回看页面调用退出全屏方法');
  }

  //获取录屏状态
  getRecordStatus() {
    console.log('回看获取当前是否录像', this.state.recording);
    return this.state.recording;
  }

  //点击截屏按钮
  _onPressScreenShot = (cameraId = 0) => {
    if (!this._canStepIn()) {
      return;
    }
    this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
    isCheckingPermission = true;
    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
      if (status === 0) {
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
          if (status2 === 0) {
            this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
            this.IMIVideoView?.snap(cameraId, this.currentSnapshotPath);
            // .then(_ => {
            //   IMIFile.saveImageToPhotosAlbum( this.currentSnapshotPath , LetDevice.deviceID).then(_ => {
            //     this.setState({screenShotPath:  this.currentSnapshotPath , snapshotVisible: true});
            //     IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);

            //     this.snapshotTimeout = setTimeout(() => {
            //       //3秒后截屏缩略图自动隐藏
            //       this.setState({snapshotVisible: false});
            //     }, 3000);
            //   });
            // })
            // .catch(() => showToast(stringsTo('action_fail')));
          } else if (status2 === -1) {
            showToast(stringsTo('storage_permission_denied'));
          }
          isCheckingPermission = false;
        });
      } else if (status === -1) {
        showToast(stringsTo('storage_permission_denied'));
        isCheckingPermission = false;
      }
      this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
      // isCheckingPermission = false;
    });
  };

  //点击录屏按钮
  _onPressRecord = (cameraId = 0) => {
    console.log('点击了录屏');

    if (!this._canStepIn()) {
      return;
    }
    // 停止操作按钮必须录制大于1s
    // 20250721 NKGT-63，支持0s的时候点击停止录制
    // if (!canRecord) {
    //   showToast(stringsTo('click_too_fast'));
    //   return
    // }
    if (this.state.recording) {
      console.log('录屏结束-------------');
      this._stopRecord();
    } else {
      this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(true);
      isCheckingPermission = true;
      IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
        if (status === 0) {
          IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
            if (status2 === 0) {
              time = moment(new Date().getTime()).format('yyyyMMDD') + '_' + new Date().getTime();
              let pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称

              canRecord = false;
              this.IMIVideoView?.startRecord(cameraId, pathUrl);

              this.setState({recording: true, recordDuration: 0, recordingCameraId: cameraId});
            } else if (status2 === -1) {
              showToast(stringsTo('storage_permission_denied'));
            }
            isCheckingPermission = false;
          });
        } else if (status === -1) {
          showToast(stringsTo('storage_permission_denied'));
          isCheckingPermission = false;
        }
        this.props.onCheckPermissionStatusChange && this.props.onCheckPermissionStatusChange(false);
        // isCheckingPermission = false;
      });
    }
  };

  //停止录像并保存在相册
  _stopRecord(forSave = true, isBackground = false) {
    console.log('停止录制', this.state.recordDuration);
    // NKGT-66, 去除此行，支持多次点击stoprecord
    // if (isStopping) {
    //   return
    // }
    isStopping = true;
    this.IMIVideoView?.stopRecord(this.state.recordingCameraId);
    this.setState({
      recording: false,
      recordingCameraId: -1,
    });
    //.then(_ => {
    //停止录制
    // if (this.state.recordDuration < 6) {
    //   console.log('_stopRecord-------------录制时长过短', this.state.recordDuration);
    //   IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
    //   this.setState({recording: false, recordDuration: 0});
    //   return;
    // }

    // this.setState({recording: false, recordDuration: 0});
    // if (!forSave) {
    //   //只停止，不保存
    //   return;
    // }

    // let pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
    // if (isBackground) {
    //   //如果切换后台不显示图片
    //   console.log('后台不显示');
    //   return;
    // }
    // console.log('保存图片成功');
    // this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
    // this.setState({screenShotPath: pathUrl, snapshotVisible: true});
    // console.log('显示截图');
    // this.snapshotTimeout = setTimeout(() => {
    //   //3秒后截屏缩略图自动隐藏
    //   this.setState({snapshotVisible: false});
    // }, 3000);
    // IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID)
    //   .then(_ => {
    //     //转存视频
    //     IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);

    //     //this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
    //     //this.IMIVideoView.screenShot(this.currentSnapshotPath);
    //     // .then(_ => {
    //     //   //截屏一张作为悬浮缩略图，不转存
    //     //   this.setState({screenShotPath: currentSnapshotPath, snapshotVisible: true});
    //     //   console.log('显示截图');
    //     //   this.snapshotTimeout = setTimeout(() => {
    //     //     //3秒后截屏缩略图自动隐藏
    //     //     this.setState({snapshotVisible: false});
    //     //   }, 3000);
    //     // });
    //   })
    //   .catch(error => {
    //     IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
    //   });
    // // })
    // // .catch(error => {
    // //   if (this.state.recordDuration < 6) {
    // //     //目前为止进入error的情况：iOS点击录制然后立马点击停止录制
    // //     IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
    // //   } else {
    // //     IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
    // //   }
    // //   this.setState({recording: false, recordDuration: 0});
    // // });
  }

  //IOS在视频流暂停时,将录制的视频保存到相册
  _saveVideoToPhotosAlbum() {
    if (this.state.recordDuration < 6) {
      IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
      this.IMIVideoView?.stopRecord()
        .then(_ => {})
        .catch(error => {}); //不调用，会导致iOS下次录制失败
      this.setState({recording: false, recordDuration: 0, isStart: false});
      return;
    }
    let pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
    // if (isIos()) {
    //   pathUrl = `${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`; //同步IOS名称
    // }
    this.IMIVideoView?.stopRecord();
    this.setState({recording: false, recordDuration: 0, isStart: false});
    IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID)
      .then(_ => {
        //转存视频
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.setState({screenShotPathType: 2, screenShotPath: _.data, snapshotVisible: true});
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
        this.IMIVideoView?.stopRecord()
          .then(_ => {})
          .catch(error => {});
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
      })
      .catch(error => {
        // console.log('保存报错了',error);
        showToast(stringsTo('action_fail'));
      });
  }

  _onPressSpeed = () => {
    if (!this._canStepIn()) {
      return;
    }

    if (this.state.recording) {
      showToast(stringsTo('screen_recording'));
      return;
    }
    if (LetDevice.model == 'a1FKrifIRwH') {
      return;
    }

    // currentPlayTime = 0;
    switch (this.state.speed) {
      // case 0:
      //   this.setState({speed: 1, mute: true});
      //   this.IMIVideoView && this.IMIVideoView.speed(2);
      //   break;
      case 0:
        this.setState({speed: 1, mute: true});
        this.currentMute = true;
        this.IMIVideoView && this.IMIVideoView.setIsMute(true);
        this.IMIVideoView && this.IMIVideoView.speed(4);
        break;
      case 1:
        this.setState({speed: 2, mute: true});
        this.currentMute = true;
        this.IMIVideoView && this.IMIVideoView.setIsMute(true);
        this.IMIVideoView && this.IMIVideoView.speed(8);
        break;
      case 2:
        this.setState({speed: 3, mute: true});
        this.currentMute = true;
        this.IMIVideoView && this.IMIVideoView.setIsMute(true);
        this.IMIVideoView && this.IMIVideoView.speed(16);
        break;
      case 3:
        this.setState({speed: 0, mute: this.currentMute});
        this.IMIVideoView && this.IMIVideoView.setIsMute(this.currentMute);
        this.IMIVideoView && this.IMIVideoView.speed(1);
        break;
    }
    // this.IMIVideoView && this.IMIVideoView.prepare();
  };

  _onPressPlay = () => {
    if (this.state.recording) {
      showToast(stringsTo('screen_recording'));
      return;
    }
    //如果在错误状态下，点击会出现暂停、播放按钮与播放状态对应不上的问题
    if (this.state.showErrorView) {
      showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
      return;
    }
    if (this.state.showNoData) {
      //已经有相同的文案常驻在播放器中央了，不用再提示  bug#7004
      //showToast(I18n.t('playback_no_video_data_tip'),Toast.positions.BOTTOM);
      return;
    }
    this.setState({isClickPause: true, isStart: false});
    if (this.state.recording) {
      console.log('暂停前自动关闭录屏 录屏结束-------------');
      this._stopRecord();
    }

    // 播放状态
    if (this.state.isPlay) {
      this.IMIVideoView?.stop();
      this.setState({isPlay: false, showPauseView: true, isLoading: false});
    } else {
      if (!this.state.hasData) {
        this.setState({canPlay: true, showPauseView: false, isLoading: false});
        this.getFullData(1);
        return;
      }
      if (!(this.state.backList.length > 0)) {
        return;
      }
      const playStartTime = this.findAndSetPlayTime();

      this.setState(
        {
          showPauseView: false,
          isLoading: this.IMIVideoView ? true : false,
          playStartTime,
          canPlay: true,
          mute: this.currentMute,
        },
        () => {
          setTimeout(() => {
            this.IMIVideoView && this.IMIVideoView.setIsMute(this.currentMute);
            this.IMIVideoView?.prepare();
            switch (this.state.speed) {
              case 0:
                this.IMIVideoView && this.IMIVideoView.speed(1);
                break;
              case 1:
                this.IMIVideoView && this.IMIVideoView.speed(4);
                break;
              case 2:
                this.IMIVideoView && this.IMIVideoView.speed(8);
                break;
              case 3:
                this.IMIVideoView && this.IMIVideoView.speed(16);
                break;
            }
          });
        },
      );
    }
    // if (this.tempScrollPause) {
    //   // 暂停状态下滑动时间轴
    //   this.setState({isPlay: true, showPauseView: false}, () => {
    //     this.tempScrollPause = false;
    //     this.IMIVideoView && this.IMIVideoView.resume();
    //     this.IMIVideoView && this.IMIVideoView.seekTo(this.tempScrollTime * 1000.0);
    //   });
    // } else {
    //   if (!this.state.isPlay) {
    //     // let isResume = false;
    //     // if (this.state.dataArray.length){
    //     //     let time = this.state.dataArray[this.state.dataArray.length-1];
    //     //     let selectTime = (new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 0, 0, 0)).getTime();
    //     //     if (time.EndTime > selectTime+currentPlayTime){
    //     //         isResume = true;
    //     //     }
    //     // }

    //     this.setState({isPlay: true, showPauseView: false}, () => {
    //       // if (isResume) {
    //       // this.IMIVideoView&&this.IMIVideoView.resume();

    //       if (this.state.isPlayFinish || this.isAutoRefreshing) {
    //         //回看结束调用了stop方法 所以需要调用prepare方法
    //         this.setState({isPlayFinish: false});
    //         PauseLineTime = currentPlayTime;

    //         //适配Android，iOS以及正常模式、直连模式，统一播放完成stop,重新播放prepare
    //         // this.IMIVideoView && this.IMIVideoView.prepare();
    //       } else {
    //         this.IMIVideoView && this.IMIVideoView.resume();
    //         //本地直连设备需要手动seekTo到暂停的位置，否则会从当天的初始时间点播放
    //         LetDevice.isLocalDevice ? this.IMIVideoView && this.IMIVideoView.seekTo(currentPlayTime * 1000.0) : null;
    //       }
    //       this.isAutoRefreshing = false;
    //     });
    //   } else {
    //     if (this.state.recording) {
    //       console.log('暂停前自动关闭录屏 录屏结束-------------');
    //       this._stopRecord();
    //     }
    //     this.setState({isPlay: false, showPauseView: true, isLoading: false}, () => {
    //       // this.IMIVideoView&&this.IMIVideoView.resume();
    //       // this.IMIVideoView && this.IMIVideoView.pause();
    //     });
    //   }
    // }
  };

  _onEventChange = event => {
    if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
      //this.setState({bps: event.extra.bps})
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
      this.reconnectTime = event.extra.currentTime / 1000;
      if (!this.firstTime && event.extra.currentTime.length > 12) {
        this.firstTime = new Date().getTime();
        const params1 = {
          // '0SD开始时间': [this.sdStartTime, new Date(this.sdStartTime)],
          // '1SD结束时间': [this.sdEndTime, new Date(this.sdEndTime)],
          // 'SD卡消耗, 异步不影响首次加载速度': this.sdEndTime - this.sdStartTime,
          '1发送请求时间': [this.sendLoadData, new Date(this.sendLoadData)],
          '2获取第一包数据时间': [this.getLoadData, new Date(this.getLoadData)],
          '3处理数据结束': [this.firstRenderTime, new Date(this.firstRenderTime)],
          '4发送prepare': [this.prepareStart, new Date(this.prepareStart)],
          '5事件10011': [this.eventTime, new Date(this.eventTime)],
          '6事件10014': [this.firstTime, new Date(this.firstTime)],
          // '加载数据': this.firstLoadEnd,
          // '加载ref': this.prepareStart,
          // '第一帧数据出现':  [this.eventTime, new Date(this.eventTime)],
          // '4获取第一个有用数据': new Date().getTime(),
        };
        const params2 = {
          '1获取第一包数据时长消耗': this.getLoadData - this.sendLoadData,
          '2处理数据至渲染时长': this.firstRenderTime - this.getLoadData,
          '3渲染至发送prepare': this.prepareStart - this.firstRenderTime,
          '4发送prepare至第一帧': this.eventTime - this.prepareStart,
          '5事件10011至10014时长': this.firstTime - this.eventTime,
        };
        console.log('时间记录', params1, params2);
        IMILog.logD('回看时间点记录', 'params' + params1);
        IMILog.logD('回看时间时长记录', 'params' + params2);
      }
      // currentTime = event.extra.currentTime/1000;
      retryNum = 0;
      const currentTime = parseInt(event.extra.currentTime, 10);

      //后台给的时间和时间轴拨动的时间一定要相差10秒才算是正常的时间，不然时间轴跳动有问题
      // 改为25s，因为支持16倍数, 3s误差 + 可能存在的结束点向前推4s + 16倍数
      const intervalTime = Math.abs(this.timeBeingPlayed - event.extra.currentTime / 1000) <= 25;

      // 时间轴切换
      if (this.timeBeingPlayedPre && this.state?.isCenterValueChanged && intervalTime) {
        this.setState({
          isCenterValueChanged: false,
        });
        this.timeBeingPlayedPre = 0;
        this.timeBeingPlayed = event.extra.currentTime / 1000;
        this.currentTime = this.timeBeingPlayed;
        this.timelineViewNew && this.timelineViewNew.scrollToTimestamp(currentTime);
      }

      // 自动跳转
      if (!this.state?.isCenterValueChanged && !this.timeBeingPlayedPre && event.extra.currentTime) {
        this.timeBeingPlayed = event.extra.currentTime / 1000;
        this.currentTime = this.timeBeingPlayed;
        this.timelineViewNew && this.timelineViewNew.scrollToTimestamp(currentTime);
      }

      //console.log(currentPlayTime,'-----',currentPlayTime-1,currentPlayTime,event.extra.currentTime/1000.0);
      // let scrollTime = currentPlayTime - 5 >= event.extra.currentTime ? currentPlayTime : event.extra.currentTime;
      // playEndTime = scrollTime;
      // PauseLineTime = scrollTime;
      // //播放时间水印与时间轴数据无法对应，
      // // 复现的问题是播放出现错误后，拖动时间轴，
      // // 然后点击重试，这时视频如果恢复播放，时间轴与播放水印就无法对应
      // if (currentPlayTime - 5 >= event.extra.currentTime && this.state.showErrorView) {
      //   this.playTimeOut && clearTimeout(this.playTimeOut);
      //   this.playTimeOut = setTimeout(() => {
      //     this.IMIVideoView && this.IMIVideoView.seekTo(currentPlayTime);
      //   }, 10);
      // } else {
      //   //  currentPlayTime = parseInt(event.extra.currentTime/1000.0); //0105 放在这里，某些时间端的视频会循环往复播放，一直走上面的seekTo
      // }
      // currentPlayTime = parseInt(event.extra.currentTime, 10);

      // //console.log('时间轴进度回调----',scrollTime,playEndTime)
      // this.state.isPlay && this.timeLine && this.timeLine.scrollToTimestamp(scrollTime);

      // //播放到最后一个视频片段结束
      // if (this.state.dataArray.length > 0) {
      //   let isToday = currentDayStartTime == todayStartTime;
      //   //越接近结束，越容易报错，所以需要提前处理。  此外，今天的哪怕提前1秒，都会走多遍_onEventChange，所以用isAutoRefreshing来控制防止多次拉取数据
      //   if (
      //     parseInt(event.extra.currentTime / 1000.0 + (isToday ? 3 : 2), 10) >=
      //       this.lastVideo.EndTime - currentDayStartTime &&
      //     this.lastVideo.EndTime - currentDayStartTime > 50
      //   ) {
      //     //有大于50这个因为出现了有的最后的视频结束时间是次日的0分5秒，又因为一个视频片段最小60秒
      //     if (isToday && !this.isAutoRefreshing) {
      //       //如果是今天，尝试拉取更多
      //       this.isAutoRefreshing = true;
      //       //this._queryRecordListOfDay(true, false);
      //       console.log('播放完成，尝试拉取更多');
      //     } else if (!isToday && (!playBackPlayFinishCallback || LetDevice.isLocalDevice)) {
      //       //不是今天的回看，播放完成直接暂停，再次点击播放时，从头播放
      //       //支持onPlayCompletion回调的，回调里会有相应的处理逻辑，不需要走此逻辑
      //       //this.IMIVideoView && this.IMIVideoView.stop();
      //       this.setState({isPlay: false, showErrorView: false, showPauseView: true, isPlayFinish: true});
      //       showLoading(false);
      //       currentPlayTime = this.state.dataArray[0].BeginTime - currentDayStartTime;
      //       console.log('播放完成，STOP');
      //       // this.timeLine && this.timeLine.scrollToTimestamp(currentPlayTime);
      //     }
      //   }
      // }

      // if (scrollTime >= 86400) {
      //   console.log('-AlarmListPlayerPage----', scrollTime);
      //   if (new Date().getTime() - lastClickSnapPhoto < 2000) {
      //     //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
      //     return;
      //   }
      //   lastClickSnapPhoto = new Date().getTime();
      //   if (currentPlayTime - 1 > event.extra.currentTime / 1000.0) {
      //   }
      //   //this._getAutomaticDay(scrollTime);
      // }
    } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) {
      this.reconnectFlag = true;
      //调用video.start()后的第一个回调
      // console.log("回看 直播流----_onEventChange,开始启用");
      /*if (this.state.dataArray.length > 0 && currentPlayTime == -1) { //刚进入回看，播放最后视频片段的前3分钟 TODO 临时方案，待优化
                let lastVideo = this.state.dataArray[this.state.dataArray.length - 1];
                let nowTime = new Date(this.state.currentDate.getFullYear(), this.state.currentDate.getMonth(), this.state.currentDate.getDate(), 0, 0, 0).getTime();
                let playTimeDefault = lastVideo.EndTime - nowTime / 1000 - 180;
                if (playTimeDefault < lastVideo.BeginTime - nowTime / 1000) {
                    playTimeDefault = lastVideo.BeginTime - nowTime / 1000;
                }

                this.IMIVideoView && this.IMIVideoView.seekTo(playTimeDefault*1000);
                this.timeLine && this.timeLine.scrollToTimestamp(playTimeDefault);
                currentPlayTime = playTimeDefault;
                this.playlastVideo = true;
                return;
            }*/
      let {playBackWithLast} = DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model);
      if (playBackWithLast && this.errPrepare) {
        // console.log('播放报错走这里');
        this.errPrepare = false;
        this.setState({isLoading: true, isPlay: false, showPauseView: false, showErrorView: false, errorFind: false});
        // this.IMIVideoView&&this.IMIVideoView.seekTo(this.errPlayTime*1000); //此句注销，因为点击重试时已经调用了prepare
      } else {
        this.setState({isLoading: true, isPlay: false, showPauseView: false, showErrorView: false, errorFind: false});
        this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);
      }
      if (this.state.recording) {
        this._stopRecord();
      }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
      if (this.reconnectFlag) {
        this.reconnectFlag = false;
        if (
          this.reconnectTime &&
          this.currentTime !== this.state.playStartTime?.timestamp + this.state.playStartTime?.offset
        ) {
          console.log('P2P重连++++++++================================', {
            currentTime: this.currentTime,
            oldPlay: this.state.playStartTime,
            newPlay: playStartTime,
          });
          const playStartTime = this.findAndSetPlayTime();
          IMILog.logI(
            'P2P重连',
            JSON.stringify({
              currentTime: this.currentTime,
              oldPlay: this.state.playStartTime,
              newPlay: playStartTime,
            }),
          );
          this.timeBeingPlayedPre = 0;
          this.setState(
            {
              showPauseView: false,
              isLoading: this.IMIVideoView ? true : false,
              playStartTime,
              canPlay: true,
              mute: this.currentMute,
              isCenterValueChanged: false,
              errorFind: false,
              showErrorView: false,
            },
            () => {
              setTimeout(() => {
                this.reconnectTime = '';
                this.IMIVideoView?.prepare();
                this.IMIVideoView && this.IMIVideoView.setIsMute(this.currentMute);
                switch (this.state.speed) {
                  case 0:
                    this.IMIVideoView && this.IMIVideoView.speed(1);
                    break;
                  case 1:
                    this.IMIVideoView && this.IMIVideoView.speed(4);
                    break;
                  case 2:
                    this.IMIVideoView && this.IMIVideoView.speed(8);
                    break;
                  case 3:
                    this.IMIVideoView && this.IMIVideoView.speed(16);
                    break;
                }
              });
            },
          );
          return;
        }
      }
      console.log(
        '回看 直播流----_onEventChange,出现关键帧',
        currentPlayTime,
        this.state.dataArray[this.state.dataArray.length - 1],
      );
      // if(isAndroid()&&this.playlastVideo){  //临时方案，为了Android解决进入此页面，播放最后一个视频片段，loading中断又出现loading然后才播放的问题(因为在BUFFERING调用了seekTo)
      //           this.playlastVideo = false;
      //           return;
      //       }
      if (!this.eventTime) {
        this.eventTime = new Date().getTime();
      }
      this.setState({
        isLoading: false,
        isPlay: true,
        showPauseView: false,
        showErrorView: false,
        mute: this.currentMute,
        errorFind: false,
        // isCenterValueChanged: false,
      });
      this.IMIVideoView && this.IMIVideoView.setIsMute(this.currentMute);
      switch (this.state.speed) {
        case 0:
          this.IMIVideoView && this.IMIVideoView.speed(1);
          break;
        case 1:
          this.IMIVideoView && this.IMIVideoView.speed(4);
          break;
        case 2:
          this.IMIVideoView && this.IMIVideoView.speed(8);
          break;
        case 3:
          this.IMIVideoView && this.IMIVideoView.speed(16);
          break;
      }
      // retryNum = 0;
      // let {playBackWithLast} = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');
      // console.log(playBackWithLast, 'playBackWithLast');

      // if (!playBackWithLast && this.errPrepare) {
      //   // 不支持offset 的情况下，进行seekTo跳转到错误播放时间
      //   console.log('播放报错走这里');
      //   this.setState({isLoading: true, isPlay: false, showPauseView: false, showErrorView: false});
      //   this.IMIVideoView && this.IMIVideoView.seekTo(this.errPlayTime * 1000); //此句注销，因为点击重试时已经调用了prepare
      //   this.errPrepare = false;
      //   this.errPlayTime = 0;
      // } else {
      //   this.setState({isLoading: false, showPauseView: false, isPlay: true, showNoData: false});
      //   this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.PLAYING); ////
      //   //this.IMIVideoView && this.IMIVideoView.seekTo(18130*1000.0);
      // }
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
      // const playStartTime = this.findAndSetPlayTime();
      // console.log('PLAYER_EVENT_ON_STOP', playStartTime);

      // IMILog.logD("王 回看错误 PayBack _onEventChange PLAYER_EVENT_ON_STOP>",event.toString())
      this.setState({isLoading: false, isPlay: false, showPauseView: true, showNoData: false});
      if (this.state.recording) {
        this._stopRecord();
      }
      // console.log("回看 直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
      this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.PAUSE);
      this.timelineViewNew && this.timelineViewNew.scrollToTimestamp(this.timeBeingPlayed * 1000);
    } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
      // console.log("回看 直播流----_onEventChange,开始播放");
      //this.setState({isLoading:false});
    }
  };

  // 缩放比例系数变化
  videoScaleChanged(data) {
    // if (!this._canStepIn()) {
    //   return;
    // }
    let scale = data.scaleRatio;
    let newScale = scale.toFixed(1);
    let zoomScale = this.state.zoomScale;

    if (Math.abs(zoomScale - newScale) < 0.1) {
      return;
    }

    // 进行节流操作
    let endTime = Date.now();
    if (endTime - this.startScaleTime < 50) {
      console.log('_onVideoScaleChanged', scale);
      return;
    }
    this.startScaleTime = endTime;
    this._updateScale(newScale);
  }

  _updateScale(scale) {
    if (scale) {
      if (this.angleViewTimeout) {
        // 隔一段时间就需要隐藏
        clearTimeout(this.angleViewTimeout);
        this.angleViewTimeout = null;
      }
      this.angleViewTimeout = setTimeout(() => {
        this.setState({isShowZoomScale: false});
      }, 3000);
      this.setState({zoomScale: scale, isShowZoomScale: true, showPlayToolBar: scale > 1.03 ? false : true});
    }
  }

  // 时间轴滚动
  _onCenterValueChanged = currentTime => {
    if (!currentTime) {
      return;
    }
    console.log('回看播放回调-------', currentTime + ' --> isForegroundPage:' + this.isForegroundPage);
    if (!this.state.hasData) {
      return;
    }
    if (!this.isForegroundPage) {
      return;
    }
    if (!this._getDeviceStatus()) {
      return;
    }
    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({centerTimestamp: 0});

    if (this.state.recording) {
      showToast(stringsTo('screen_recording'));
      return false;
    }
    const {blackIndex, currentTimeStamp} = findStartTime(
      this.state.backList,
      currentTime / 1000,
      this.state?.currentDate,
    );

    const playStartTime = {...this.state?.backList[blackIndex]};
    if (!playStartTime) {
      return;
    }

    this.timeBeingPlayedPre = this.timeBeingPlayed || 0;
    // const offset = 0
    let offset = playStartTime.timestamp > currentTimeStamp ? 0 : Math.abs(playStartTime?.timestamp - currentTimeStamp);
    const isLast = blackIndex == this.state.backList.length - 1;
    // 最后一个片段的结束时间则向前偏移4s
    if (Math.floor(offset) >= Math.floor(playStartTime?.duration / 1000) && isLast) {
      // playStartTime.timestamp = 0;
      offset = playStartTime?.duration / 1000 - 4;
    }
    playStartTime.offset = Math.floor(offset);
    if (playStartTime.oldTimestamp) {
      playStartTime.offset = playStartTime.timestamp - playStartTime.oldTimestamp + playStartTime.offset;
      playStartTime.timestamp = playStartTime.oldTimestamp;
    }
    //this.IMIVideoView?.stop();
    this.timeBeingPlayed = playStartTime.timestamp + playStartTime.offset;
    this.currentTime = this.timeBeingPlayed;
    this.timelineViewNew && this.timelineViewNew.scrollToTimestamp(this.timeBeingPlayed * 1000);
    this.setState(
      {
        playStartTime: {...playStartTime},
        isLoading: true,
        isCenterValueChanged: true,
        showPauseView: false,
        showErrorView: false,
      },
      () => {
        console.log('stateplayStartTime', this.state?.playStartTime);
        setTimeout(() => {
          this.IMIVideoView?.prepare();
          if (!this.IMIVideoView) {
            this.setState({isLoading: false});
          }
        });
      },
    );

    //超出时间轴，显示最后时间
    // currentPlayTime = currentTime;
    // if (this.state.dataArray.length > 0) {
    //   let time = this.state.dataArray[this.state.dataArray.length - 1];
    //   console.log('最后一个视频片段----', time);
    //   let nowTime = new Date(
    //     this.state.currentDate.getFullYear(),
    //     this.state.currentDate.getMonth(),
    //     this.state.currentDate.getDate(),
    //     0,
    //     0,
    //     0,
    //   ).getTime();
    //   console.log('今天的nowTime', nowTime);
    //   if (time.EndTime < nowTime / 1000 + currentTime) {
    //     //滑动指针超过了今天最后一个视频片段，要把指针置于最后一个片段前2分钟
    //     currentPlayTime = time.EndTime - nowTime / 1000 - 120; //固件沟通要预留120秒
    //     // console.log('_onCenterValueChanged--1--',currentPlayTime)
    //   }
    // }
    // currentPlayTime = currentPlayTime > 0 ? currentPlayTime : 0;
    // // console.log('_onCenterValueChanged--2--',currentPlayTime)
    // PauseLineTime = currentPlayTime;
    // if (!this.state.isPlay) {
    //   // 暂停状态
    //   this.tempScrollPause = true;
    //   this.tempScrollTime = currentPlayTime;
    // } else {
    //   this.setState({isLoading: true});
    //   this.IMIVideoView && this.IMIVideoView.seekTo(currentPlayTime);
    // }

    if (this.state.isFullScreen) {
      this._onPressFullScreenTools();
    }
  };

  timeOnScrolling = currentTime => {
    if (!currentTime) {
      return;
    }
    if (!this.state.hasData) {
      return;
    }
    if (this.timeIndicatorView == null) {
      return;
    }
    this.timeIndicatorView.setState({centerTimestamp: currentTime});
  };

  _onRecordTimeChange = event => {
    // this.setState({recordDuration: event.extra});
    if (event?.extra > 0) {
      canRecord = true;
    }
    this.setState({recordDuration: event.extra}, () => {
      // if (this.state.recordDuration == 1 && isIos()) {
      //   // 临时截图
      //   let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
      //   this.tempSnapShotPath = currentSnapshotPath;
      //   this.IMIVideoView.snap(currentSnapshotPath, LetDevice.model)
      //     .then(_ => {
      //       console.log('截屏路径 临时路径---', this.tempSnapShotPath);
      //     })
      //     .catch(e => {
      //       console.log('截屏路径 临时路径---', e);
      //     });
      // }
    });

    // this.setState({recordDuration: event.extra}, callback => {
    //   if (this.state.recordDuration == 1) {
    //     // 临时截图
    //     this.currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
    //     this.IMIVideoView.screenShot(this.currentSnapshotPath).then(_ => {
    //       this.tempSnapShotPath = this.currentSnapshotPath;
    //       // console.log('临时路径---',this.tempSnapShotPath);
    //     });
    //   }
    // });
  };

  // 截图成功回调
  _onCommCallback = event => {
    console.log('------截图成功-----保存截图成功回调', event);

    IMIFile.saveImageToPhotosAlbum(this.currentSnapshotPath, LetDevice.deviceID)
      .then(_ => {
        this.setState({
          screenShotPathType: 1,
          screenShotPath: _.data,
          snapshotVisible: true,
        });
        IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        console.log('------截图成功-----保存截图成功', this.currentSnapshotPath, LetDevice.deviceID);
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.snapshotTimeout = setTimeout(() => {
          //3秒后截屏缩略图自动隐藏
          this.setState({snapshotVisible: false});
        }, 3000);
      })
      .catch(e => {
        console.log('截图失败', JSON.stringify(e));
      });
  };
  // 录像结束回调
  _onIRecordState = event => {
    if (!this.state.isStart) {
      if (event?.code !== 0) {
        this.setState({recording: false, recordDuration: 0, isStart: false});
        IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
        return;
      }
      this.setState({isStart: true});
      return;
    }
    if (event?.type == 1) {
      canRecord = true;
    }
    isStopping = false;
    console.log('录像开始', event);
    if (this.state.isStart && this.state.recordDuration < 6) {
      console.log('_stopRecord-------------录制时长过短', this.state.recordDuration);
      IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
      this.setState({recording: false, recordDuration: 0, isStart: false});
      return;
    }
    if (event?.code === 0) {
      console.log('event?.extra', event?.extra);

      IMIFile.saveVideoToPhotosAlbum(event?.extra, this.props.albumName)
        .then(_ => {
          //转存视频
          this.setState({recording: false, recordDuration: 0, isStart: false});
          console.log('_stopRecord-----停止录制并且成功保存录像');
          IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
          this.setState({screenShotPathType: 2, screenShotPath: _.data, snapshotVisible: true});
          console.log('显示截图');
          this.snapshotTimeout = setTimeout(() => {
            //3秒后截屏缩略图自动隐藏
            this.setState({snapshotVisible: false});
          }, 3000);
        })
        .catch(error => {
          this.setState({recording: false, recordDuration: 0, isStart: false});
          IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
        });
    } else {
      this.setState({recording: false, recordDuration: 0, isStart: false});
      IMIToast.showToast(stringsTo('save_album_failed'), IMIToast.TYPE.BOTTOM);
    }
  };

  //判断当前是否可以操作
  _canStepIn() {
    if (!this._getDeviceStatus()) {
      return;
    }
    //获取设备是否在线的状态可能不准确 增加一个条件
    if (!this.state.isPlay || this.state.showErrorView) {
      showToast(I18n.t('onlyDoInLive'), Toast.positions.BOTTOM);
      return false;
    }
    return true;
  }

  //离线以及休眠提示
  _getDeviceStatus() {
    if (!this.props.isOnLine) {
      showToast(stringsTo('device_offline'));
      return false;
    }
    if (this.props.isSleepStatus) {
      showToast(stringsTo('power_off'));
      return false;
    }
    return true;
  }

  /* 根据iotid获取事件类型 */
  getEventType() {
    aliAlarmEventCloudApi
      .getEventTypeListByIotId(LetDevice.deviceID)
      .then(res => {
        let eventAry = [];
        const textList = {
          ObjectMotion: stringsTo('moveEvent'),
          PeopleMotion: stringsTo('peopleEvent'),
          AbnormalSound: stringsTo('soundEvent'),
        };
        JSON.parse(res)?.forEach((element, index) => {
          if (textList[element?.eventType]) {
            eventAry.push({
              eventTypeName: textList[element?.eventType] || element.eventTypeName,
              eventType: element?.eventType,
            });
          }
        });
        this.setState({eventTypeList: eventAry});
      })
      .catch(error => {
        console.log('error1111', JSON.stringify(error));
      });
  }

  // 切换事件
  changeEventType(eventType) {
    const eventTypeUnSelect = [...this.state.eventTypeUnSelect];
    const index = this.state.eventTypeUnSelect.indexOf(eventType);
    if (index > -1) {
      eventTypeUnSelect.splice(index, 1);
    } else {
      eventTypeUnSelect.push(eventType);
    }
    this.setState({eventTypeUnSelect});
  }

  /**
   * 竖屏状态视屏区域填充UI
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenVideoViewArea() {
    return this.props.navBar ? (
      this.props.navBar(this.state.bps, this.state.isFullScreen)
    ) : (
      <NavigationBar
        type={NavigationBar.TYPE.LIGHT}
        ref={component => (this.navigationBar = component)}
        // backgroundColor={'#000'}
        title={stringsTo('play_back_text')}
        // subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
        left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
        right={this.props.navBarRight ? this.props.navBarRight : []}
      />
    );
  }

  // 全部视频
  handleGoAll = () => {
    if (this.areadyClick) {
      return;
    }
    if (this.getRecordStatus()) {
      showToast(stringsTo('screen_recording'));
      return;
    }
    this.goNext = true;
    this.setState({
      goNext: true,
    });
    this.areadyClick = true;
    this.props.navigation.push('PlayBackDualAllList');
  };

  _renderTimeLineView() {
    // 如果这里设置为null，等切屏回来后，就生成了一个新的view。原先绑定的数据就没了。
    // if (this.state.fullScreen) {
    //   return null;
    // }
    let containerStyle;
    if (this.state.isFullScreen) {
      containerStyle = {
        position: 'absolute',
        bottom: 0,
        width: '100%',
      };
    } else {
      containerStyle = {width: '100%', flex: 1};
    }
    const {touchMovement} = this.state;

    const colorList = {
      ObjectMotion: {
        color: EVENT_TYPE_COLOR.motionSelectedColor,
        unSelectedImg: require('../../../resources/images/playback/time_line_event_icon_erea_move_sel.png'),
        selectedImg: require('../../../resources/images/playback/time_line_event_icon_erea_move_nor.png'),
      },
      PeopleMotion: {
        color: EVENT_TYPE_COLOR.peopleSelectedColor,
        unSelectedImg: require('../../../resources/images/playback/time_line_event_icon_people_selected.png'),
        selectedImg: require('../../../resources/images/playback/time_line_event_icon_people_move_nor.png'),
      },
      AbnormalSound: {
        color: EVENT_TYPE_COLOR.abnormalSoundSelectedColor,
        unSelectedImg: require('../../../resources/images/playback/time_line_event_icon_abnormalSound_selected.png'),
        selectedImg: require('../../../resources/images/playback/time_line_event_icon_abnormalSound_nor.png'),
      },
    };
    let containerPadding = 15;
    let eventPadding = 30;
    let eventMargin = 13.5;

    if (screenWidth < 700) {
      containerPadding = 10;
      eventPadding = 20;
      eventMargin = 13.5;
    }
    return (
      <View style={[this.state.isFullScreen && !this.state.showFullScreenTools ? {display: 'none'} : containerStyle]}>
        <TimeScaleDualView
          ref={ref => {
            this.timelineViewNew = ref;
          }}
          // onCenterValueChanged={this._onCenterValueChanged}
          onScrolling={this.timeOnScrolling}
          onScrollEnd={this._onCenterValueChanged}
          isDisabled={this.state.recording || !this.state.hasData ? true : false}
          landscape={this.state.isFullScreen}
          eventTypeUnSelect={this.state.eventTypeUnSelect}
          eventTypeList={this.state.eventTypeList}
          touchMovement={touchMovement}
        />
        {this.state.hasData && !this.state.isFullScreen && (
          <View
            contentContainerStyle={{
              flexDirection: 'row',
              flexWrap: 'wrap',
              justifyContent: 'flex-start',
            }}
            nestedScrollEnabled={true}
            style={{
              display: 'flex',
              flexDirection: 'row',
              flex: 1,
              paddingHorizontal: containerPadding,
              paddingTop: 5,
              marginTop: 20,
            }}>
            {Array.isArray(this.state.eventTypeList) &&
              this.state.eventTypeList.map(res => {
                const action = colorList[res.eventType];
                if (!action) {
                  return null;
                }
                return (
                  <TouchableOpacity
                    style={{
                      height: 36,
                      width: screenWidth / 3 - (eventMargin / 2) * 3,
                      marginHorizontal: eventMargin / 2,
                      paddingLeft: 10,
                      paddingRight: 10,
                      marginBottom: 10,
                      backgroundColor: this.state.eventTypeUnSelect.includes(res.eventType)
                        ? unselectedColor
                        : action.color,
                      borderRadius: 360,
                      display: 'flex',
                      flexDirection: 'row',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                    onPress={() => {
                      this.changeEventType(res.eventType);
                    }}>
                    <Image
                      style={{width: 15, height: 15, marginRight: 5}}
                      source={
                        this.state.eventTypeUnSelect.includes(res.eventType) ? action.unSelectedImg : action.selectedImg
                      }
                    />
                    <Text
                      style={{
                        color: this.state.eventTypeUnSelect.includes(res.eventType)
                          ? textColorUnselected
                          : textColorSelected,
                        fontSize: 12,
                      }}>
                      {res.eventTypeName}
                    </Text>
                  </TouchableOpacity>
                );
              })}
          </View>
        )}
        {this.state.hasData && !this.state.isFullScreen && (
          <View
            style={{
              marginBottom: 20,
              marginTop: 20,
              width: screenWidth - 50,
              textAlign: 'center',
              marginLeft: 25,
              backgroundColor: '#ddd',
              borderRadius: 20,
            }}>
            <TouchableOpacity onPress={() => this.handleGoAll()}>
              <Text
                style={{
                  width: screenWidth - 50,
                  textAlign: 'center',
                  lineHeight: 40,
                  color: colors.black,
                }}>
                {stringsTo('play_back_text_all')}
              </Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
    );
  }

  /**
   * 全屏状态videoView区域填充UI
   * @returns {Element}
   * @private
   */
  _renderLandscapeScreenVideoViewArea() {
    if (!this.state.showFullScreenTools) {
      return null;
    }
    const cameraId = this.state.switchCameraId;
    const isCurrentCameraRecording = this.state.recording && this.state.recordingCameraId === cameraId;
    const isCurrentCameraRecordEnable =
      !this.state.recording || (this.state.recording && this.state.recordingCameraId === cameraId);

    let scene;
    if (this.state.isShowDoubleCamera) {
      scene = require('../../../resources/images/playback/icon_double_camera.png');
    } else {
      if (this.state.switchCameraId === 0) {
        scene = require('../../../resources/images/playback/icon_fixed_camera.png');
      } else {
        scene = require('../../../resources/images/playback/icon_ptz_camera.png');
      }
    }

    return (
      <View style={{width: '100%', height: 60}}>
        <Image
          style={{width: '100%', height: '100%'}}
          source={require('../../../resources/images/playback/bg_player_control_bar_top.webp')}
        />
        <View
          style={{
            width: '100%',
            height: '100%',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            position: 'absolute',
            top: 0,
            left: 0,
          }}>
          {/*退出全屏*/}
          <TouchableOpacity style={{marginLeft: 14}} onPress={this._exitFullScreen}>
            <Image style={{width: 40, height: 40}} source={require('../../../resources/images/icon_back_white.png')} />
          </TouchableOpacity>
          <View style={{flexDirection: 'row'}}>
            {/*画面切换*/}
            <TouchableOpacity
              style={{marginRight: 12}}
              onPress={() => {
                this.setState({isShowSelectPicturePop: true});
              }}>
              <Image style={{width: 40, height: 40}} source={scene} />
            </TouchableOpacity>
            {/*静音*/}
            <TouchableOpacity style={{marginRight: 12}} onPress={this._onPressMute}>
              <Image
                style={{width: 40, height: 40}}
                source={
                  this.state.mute
                    ? require('../../../resources/images/icon_mute_white.png')
                    : require('../../../resources/images/icon_not_mute_white.png')
                }
              />
            </TouchableOpacity>
            {/*截图*/}
            <TouchableOpacity
              style={{marginRight: 12}}
              onPress={() => {
                this._onPressScreenShot(cameraId);
              }}>
              <Image
                style={{width: 40, height: 40}}
                source={require('../../../../../imi-rn-commonView/PlayerToolBarView/res/player_toolbar_screenshot_white.png')}
              />
            </TouchableOpacity>
            {/*录屏*/}
            <TouchableOpacity
              style={{marginRight: 34}}
              disabled={!isCurrentCameraRecordEnable}
              onPress={() => {
                this._onPressRecord(cameraId);
              }}>
              <Image
                style={{width: 40, height: 40}}
                source={
                  isCurrentCameraRecording
                    ? require('../../../../../imi-rn-commonView/PlayerToolBarView/res/landscape_recording.png')
                    : require('../../../../../imi-rn-commonView/PlayerToolBarView/res/player_toolbar_record_white.png')
                }
              />
            </TouchableOpacity>
          </View>
        </View>
      </View>
    );
  }

  /**
   * 竖屏状态下半屏区域填充UI  按钮
   * @returns {Element}
   * @private
   */
  _renderPortraitScreenPlayerToolBarArea(isFullScreen) {
    const speedArr = ['×1', '×4', '×8', '×16'];
    const speed = speedArr[this.state.speed];
    return (
      <View
        style={{
          display: isFullScreen ? 'none' : '',
          height: 60,
          width: '100%',
          position: 'absolute',
          bottom: 0,
          left: 0,
        }}>
        <Image source={require('../../../resources/images/playback/bg_player_control_bar.webp')} />
        <View
          style={{
            width: '100%',
            height: '100%',
            flexDirection: 'row',
            justifyContent: 'space-between',
            alignItems: 'center',
            position: 'absolute',
            bottom: 0,
            left: 0,
            paddingHorizontal: 10,
          }}>
          {/*播放/暂停*/}
          <TouchableOpacity onPress={this._onPressPlay}>
            <Image
              style={{width: 40, height: 40}}
              source={
                this.state.isPlay
                  ? require('../../../resources/images/icon_pause_white.png')
                  : require('../../../resources/images/icon_play_white.png')
              }
            />
          </TouchableOpacity>
          {/*静音*/}
          <TouchableOpacity onPress={this._onPressMute}>
            <Image
              style={{width: 40, height: 40}}
              source={
                this.state.mute
                  ? require('../../../resources/images/icon_mute_white.png')
                  : require('../../../resources/images/icon_not_mute_white.png')
              }
            />
          </TouchableOpacity>
          {/*倍速*/}
          <TouchableOpacity onPress={this._onPressSpeed}>
            <View style={{width: 40, height: 40, justifyContent: 'center', alignItems: 'center'}}>
              <Image
                style={{width: '100%', height: '100%', position: 'absolute', bottom: 0, left: 0}}
                source={require('../../../resources/images/icon_speed_border.webp')}
              />
              <Text style={{color: 'white', fontSize: 11}}>{speed}</Text>
            </View>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  _renderRecordingView() {
    if (!this.state.recording) {
      return null;
    }
    let duration = this.state.recordDuration > 0 ? `${timeFilter(this.state.recordDuration)}` : '00:00';
    return (
      <View
        style={
          this.state.isFullScreen //修改全屏状态下的录屏时间显示不准确问题
            ? {
                position: 'absolute',
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: 60,
                zIndex: 999,
                // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
              }
            : {
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: 'center',
                alignItems: 'center',
                marginTop: this.navHeight + 10,
                // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
              }
        }>
        <View style={{backgroundColor: '#E74D4D', opacity: 0.9, width: 6, height: 6, borderRadius: 3}} />
        <Text style={{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}}>{duration}</Text>
      </View>
    );
  }

  _loadingView() {
    if (!this.props.isOnLine) {
      return;
    }
    if (this.props.isSleepStatus) {
      return;
    }
    if (this.state.showPauseView) {
      return;
    }
    if (this.state.showNoData) {
      return;
    }
    if (!this.state.isLoading) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
        }}>
        <ActivityIndicator style={{width: 54, height: 54}} color={'#ffffff'} size={'large'} />
        <Text
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}>
          {stringsTo('commLoadingText')}
        </Text>
      </View>
    );
  }

  // 设置对应错误文案
  _renderErrorTex() {
    if (this.state.errorCode?.includes('202')) {
      return I18n.t('onPlayErrorMaxText', {code: this.state.errorCode});
    }
    return I18n.t('onPlayErrorText', {code: this.state.errorCode});
  }

  _errorView() {
    if (!this.props.isOnLine) {
      return;
    }
    if (this.props.isSleepStatus) {
      return;
    }
    if (this.state.showNoData) {
      return;
    }
    if (!this.state.showErrorView) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          paddingTop: this.state.isFullScreen ? 0 : this.navHeight,
        }}>
        <XText
          raw={true}
          style={{
            textAlign: 'center',
            color: colors.white,
            fontSize: 15,
          }}
          // text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
          text={this._renderErrorTex()}
        />

        <RoundedButtonView
          buttonText={stringsTo('error_code_common_retry')}
          buttonStyle={{
            margin: 14,
            paddingHorizontal: 15,
            height: 40,
          }}
          buttonTextStyle={{textAlign: 'center'}}
          onPress={() => {
            const playStartTime = this.findAndSetPlayTime();
            this.timeBeingPlayedPre = 0;
            this.setState(
              {
                showPauseView: false,
                showErrorView: false,
                isLoading: this.IMIVideoView ? true : false,
                playStartTime,
                canPlay: true,
                mute: this.currentMute,
                isCenterValueChanged: false,
              },
              () => {
                setTimeout(() => {
                  this.IMIVideoView && this.IMIVideoView.setIsMute(this.currentMute);
                  this.IMIVideoView?.prepare();
                  switch (this.state.speed) {
                    case 0:
                      this.IMIVideoView && this.IMIVideoView.speed(1);
                      break;
                    case 1:
                      this.IMIVideoView && this.IMIVideoView.speed(4);
                      break;
                    case 2:
                      this.IMIVideoView && this.IMIVideoView.speed(8);
                      break;
                    case 3:
                      this.IMIVideoView && this.IMIVideoView.speed(16);
                      break;
                  }
                });
              },
            );
          }}
        />
      </View>
    );
  }

  _errorFindView() {
    if (!this.props.isOnLine) {
      return;
    }
    if (this.props.isSleepStatus) {
      return;
    }
    if (this.state.showNoData) {
      return;
    }
    if (this.state.showErrorView) {
      return;
    }
    if (!this.state.errorFind) {
      return;
    }
    return (
      <XView
        pointerEvents="box-none"
        activeOpacity={1}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          backgroundColor: '#000',
          top: 0,
        }}
        onPress={() => {
          this.state.isFullScreen && this._onPressFullScreenTools(this.state.showFullScreenTools);
        }}>
        <View
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '100%',
          }}>
          <XText
            raw={true}
            style={{
              textAlign: 'center',
              color: colors.white,
              fontSize: 15,
            }}
            text={I18n.t('no_video_data_failed')}
          />
          <RoundedButtonView
            buttonText={stringsTo('retry_connect')}
            buttonStyle={{
              margin: 14,
              width: 110,
              height: 40,
            }}
            onPress={() => {
              const playStartTime = this.findAndSetPlayTime();
              this.setState(
                {
                  errorFind: false,
                  showPauseView: false,
                  showErrorView: false,
                  isLoading: this.IMIVideoView ? true : false,
                  playStartTime,
                  canPlay: true,
                  mute: this.currentMute,
                },
                () => {
                  setTimeout(() => {
                    this.IMIVideoView?.prepare();
                    this.IMIVideoView && this.IMIVideoView.setIsMute(this.currentMute);
                    switch (this.state.speed) {
                      case 0:
                        this.IMIVideoView && this.IMIVideoView.speed(1);
                        break;
                      case 1:
                        this.IMIVideoView && this.IMIVideoView.speed(4);
                        break;
                      case 2:
                        this.IMIVideoView && this.IMIVideoView.speed(8);
                        break;
                      case 3:
                        this.IMIVideoView && this.IMIVideoView.speed(16);
                        break;
                    }
                  });
                },
              );
            }}
            accessibilityLabel={'playbackPage_retry_connect'}
          />
        </View>
      </XView>
    );
  }

  _pauseView() {
    if (!this.props.isOnLine) {
      return;
    }
    if (this.props.isSleepStatus) {
      return;
    }
    if (this.state.showNoData) {
      return null;
    }
    if (!this.state.showPauseView) {
      return null;
    }
    if (this.state.showErrorView) {
      return;
    }
    return (
      <View
        pointerEvents="box-none"
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          paddingTop: this.state.isFullScreen ? 0 : this.navHeight,
        }}>
        <ImageButton
          style={{width: 52, height: 52}}
          source={require('../../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          highlightedSource={require('../../../../../imi-rn-commonView/HomePageLivePlayerComponent/res/icon_play.png')}
          onPress={() => {
            this._onPressPlay();
            // this.isAutoRefreshing = false;
            // // 点击过暂停 需要
            // console.log('isPlayzhuangtai---', this.state.isPlayFinish, this.state.isClickPause);
            // if (this.tempScrollPause) {
            //   // 暂停状态下滑动时间轴
            //   this.tempScrollPause = false;
            //   this.IMIVideoView && this.IMIVideoView.resume();
            //   this.IMIVideoView && this.IMIVideoView.seekTo(this.tempScrollTime * 1000.0);
            // } else {
            //   if (this.state.isPlayFinish) {
            //     this.setState({isPlayFinish: false});
            //     PauseLineTime = currentPlayTime;

            //     //适配Android，iOS以及正常模式、直连模式，统一播放完成stop,重新播放prepare
            //     //this.IMIVideoView && this.IMIVideoView.prepare();
            //   } else {
            //     //  this.state.isClickPause ? this.IMIVideoView.resume() : this.IMIVideoView.prepare();
            //     //本地直连设备需要手动seekTo到暂停的位置，否则会从当天的初始时间点播放  bug #9796
            //     // LetDevice.isLocalDevice
            //     //   ? this.IMIVideoView && this.IMIVideoView.seekTo(currentPlayTime * 1000.0)
            //     //   : null;
            //   }
            // }

            // if (this.state.isClickPause) {
            //   this.setState({isPlay: true, showPauseView: false});
            // } else {
            //   this.setState({speed: 0});
            // }
          }}
        />
      </View>
    );
  }

  getStatusBarHeight() {
    if (isIos()) {
      return isIphone14ProMax() ? 59 + 50 : isIphoneXSeries() ? 47 + 50 : 20 + 50;
    } else {
      return parseInt(StatusBar.currentHeight) + 50;
    }
  }

  _noDataView() {
    if (!this.state.hasData) {
      if (!this.state.noData) {
        return;
      }
    }
    if (this.state.backList.length > 0) {
      return;
    }
    return (
      <XView
        pointerEvents="box-none"
        activeOpacity={1}
        style={{
          position: 'absolute',
          width: '100%',
          height: '100%',
          backgroundColor: '#000',
          top: 0,
        }}
        onPress={() => {
          this.state.isFullScreen && this._onPressFullScreenTools(this.state.showFullScreenTools);
        }}>
        {!this.state.isFullScreen && (
          <NavigationBar
            type={NavigationBar.TYPE.DARK}
            backgroundColor={'transparent'}
            title={stringsTo('play_back_text')}
            left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
            right={this.props.navBarRight ? this.props.navBarRight : []}
          />
        )}
        <View
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            width: '100%',
            height: '100%',
          }}>
          <XText
            raw={true}
            style={{
              textAlign: 'center',
              color: colors.white,
              fontSize: 15,
              marginTop: this.state.isFullScreen ? 0 : -(this.getStatusBarHeight() + 20),
            }}
            text={I18n.t('no_video_data_new')}
          />
        </View>
      </XView>
    );
  }

  _renderSnapshotView() {
    if (!this.state.snapshotVisible) {
      return null;
    }
    let tempSnapShotPath = this.state.screenShotPath;
    if (isIos() && this.tempSnapShotPath) {
      tempSnapShotPath = this.tempSnapShotPath;
    }
    return (
      <View
        style={{
          backgroundColor: 'transparent',
          position: 'absolute',
          bottom: this.state.isFullScreen ? 150 : 19,
          left: isPhoneX() ? (this.state.isFullScreen ? 44 + 14 : 14) : 14,
          width: 140,
          height: 80,
          zIndex: 999,
        }}>
        <ImageButton
          style={{
            width: '100%',
            height: '100%',
            borderWidth: 2,
            borderColor: 'white',
            borderRadius: 10,
          }}
          accessibilityLabel={'back_page_picture_show'}
          source={{uri: 'file://' + tempSnapShotPath}}
          onPress={_ => {
            //TODO 跳转到相册预览？
            if (this.state.recording) {
              showToast(stringsTo('screen_recording'));
              return;
            }
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            this.setState({snapshotVisible: false});
            this.isView = true;
            if (this.state.screenShotPathType === 1) {
              this.props.navigation.push('PhotoView', {
                imageUrl: 'file://' + this.state.screenShotPath,
                isFullScreen: this.state.isFullScreen,
              });
            } else {
              this.props.navigation.push('VideoPreView', {
                mediaData: {url: 'file://' + this.state.screenShotPath},
                isFullscreen: this.state.isFullScreen,
              });
            }
            // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
          }}
        />
      </View>
    );
  }

  //进入相册
  goToAlbum() {
    this.props.navigation.push('CameraListPage');
    // let {showRNAlbum} = DeviceTemplatesUtils.getConfigProject('camera', 'a1FKrifIRwH');
    // if (showRNAlbum || LetDevice.model === 'a1l4Z7lJ1ns' || LetDevice.model === 'a1yMb5JVWDa') {
    //   510项目使用rn相册
    //   this.checkIMIPermission();
    //   if (isAndroid() && IMIPackage.minApiLevel>=10006){
    //       this.props.navigation.push('CameraListPage');
    //   }else if (isIos() &&IMIPackage.minApiLevel>=10005){
    //       this.props.navigation.push('CameraListPage');
    //   }else {
    //       IMIGotoPage.startAlbumPage(LetDevice.deviceID);
    //   }
    //   if (IMIPackage.minApiLevel < 10007) {
    //     IMIGotoPage.startAlbumPage(LetDevice.deviceID);
    //   } else {
    //     this.props.navigation.push('CameraListPage');
    //   }
    //   this.props.navigation.push('CameraListPage');
    // } else {
    //   IMIGotoPage.startAlbumPage(LetDevice.deviceID);
    // }
  }

  _onDayPress() {
    console.log('_onDayPress');
    let m = this.state.dateTime.substring(5, 7);
    let d = this.state.dateTime.substring(8);
    this.topSelectBarRoot.funOnPress(m + '/' + d);

    this.props.onDayPress && this.props.onDayPress(this.state.dateTime);
  }

  emptyVideo() {
    this.IMIVideoView = null;
  }

  // 缩放比例view
  renderZoomScaleView() {
    if (!this.state.isShowZoomScale) {
      return null;
    }
    if (!(this.state.backList.length > 0)) {
      return null;
    }
    return (
      <View
        style={{
          backgroundColor: '#00000099',
          borderRadius: 4,
          position: 'absolute',
          top: this.state.isFullScreen ? 60 : this.getStatusBarHeight() + 15,
          // bottom: this.state.isFullScreen ? 206 : 0,
          // left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
          left: this.state.isFullScreen ? 65 : 20,
          width: 50,
          height: 30,
          zIndex: 999,
        }}>
        <Text
          accessibilityLabel={'zoom'}
          numberOfLines={1}
          ellipsizeMode={'tail'}
          style={{lineHeight: 29, fontSize: 15, color: '#FFFFFF', textAlignVertical: 'center', textAlign: 'center'}}>
          {`x${this.state.zoomScale}`}
        </Text>
      </View>
    );
  }

  // 滚动时间轴
  renderTimeScroll() {
    const width = this.state.isFullScreen ? screenHeight : screenWidth;
    return (
      <View
        style={{
          backgroundColor: '#00000066',
          borderRadius: 4,
          position: 'absolute',
          top: this.state.isFullScreen ? 60 : StatusBarHeight + 60,
          // bottom: this.state.isFullScreen ? 206 : 0,
          // left: this.state.isFullScreen && isIphoneXSeries() ? 50 : 14,
          left: width / 2 - 50,
          width: 100,
          zIndex: 999,
        }}>
        <CenterTimeView
          ref={ref => {
            this.timeIndicatorView = ref;
          }}></CenterTimeView>
      </View>
    );
  }

  shouldComponentUpdate(nextProps, nexStatus) {
    var start_time = this.isView
      ? parseInt(this.timeBeingPlayed)
      : this.state?.playStartTime?.timestamp || parseInt(this.timeBeingPlayed);
    if (this.goBackFlag) {
      return false;
    }
    if (start_time == 0) {
      return false;
    }
    return true;
  }

  //初始化VideoView
  _initVideoPrepare() {
    console.log(`_initVideoPrepare ${this.IMIVideoView}`);
    this.setDataSource();
    // 俩端统一先addExtraRenderView再prepare
    // 安卓需要先setCameraId然后在addExtraRenderView，否则无法获取CameraId
    if (this._rootManagerView && this._rootManagerView.setCameraId) {
      console.log('this._rootManagerView setDataSource---setScale--');
      this._rootManagerView.setCameraId(1);
    }
    this.IMIVideoView?.addExtraRenderView(this._rootManagerView);
    // 延迟200ms，然后执行prepare（防止addExtraRenderView和prepare错序）
    setTimeout(() => {
      this.IMIVideoView?.prepare();
    }, 200);
  }

  // 设置资源
  setDataSource() {
    console.log('PLAYER_TYPE setDataSource-----', PLAYER_TYPE, this.latestTime);
    this.latestTime = Math.floor(Date.now() / 1000) - 15 * 60;
    console.log('PLAYER_TYPE ---latestTime--', PLAYER_TYPE, this.latestTime);
    this.setState({
      dataSource: {
        iotId: LetDevice.deviceID,
        playerClass: PlayerClass.VOD,
        cameraNumber: '2',
        start_time: `${this.latestTime}`,
        end_time: `${0}`,
        offset: '0',
      },
    });
  }

  _assignRootManager = component => {
    if (!this._rootManagerView) {
      this._rootManagerView = component;
    }
  };

  _renderTopToolBar(cameraId) {
    const isCurrentCameraRecording = this.state.recording && this.state.recordingCameraId === cameraId;
    const isCurrentCameraRecordEnable =
      !this.state.recording || (this.state.recording && this.state.recordingCameraId === cameraId);
    return (
      <View style={{width: '100%', height: 40, zIndex: 1}}>
        <Image
          style={{position: 'absolute', left: 0, top: 0, width: '100%', height: 80}}
          source={require('../../../resources/images/playback/bg_player_control_bar_top.webp')}
        />
        <View
          style={{
            position: 'absolute',
            right: 10,
            top: 10,
            height: 40,
            justifyContent: 'center',
            flexDirection: 'row',
          }}>
          {/*截图*/}
          <TouchableOpacity
            onPress={() => {
              this._onPressScreenShot(cameraId);
            }}>
            <Image
              style={{width: 40, height: 40, marginRight: 5}}
              source={require('../../../../../imi-rn-commonView/PlayerToolBarView/res/player_toolbar_screenshot_white.png')}
            />
          </TouchableOpacity>
          {/*录屏*/}
          <TouchableOpacity
            disabled={!isCurrentCameraRecordEnable}
            onPress={() => {
              this._onPressRecord(cameraId);
            }}>
            <Image
              style={{width: 40, height: 40, marginRight: 5}}
              source={
                isCurrentCameraRecording
                  ? require('../../../../../imi-rn-commonView/PlayerToolBarView/res/landscape_recording.png')
                  : require('../../../../../imi-rn-commonView/PlayerToolBarView/res/player_toolbar_record_white.png')
              }
            />
          </TouchableOpacity>
          {/*全屏*/}
          <TouchableOpacity
            onPress={() => {
              this._onPressFullScreen(cameraId);
            }}>
            <Image
              style={{width: 40, height: 40}}
              source={require('../../../../../imi-rn-commonView/PlayerToolBarView/res/icon_live_full.png')}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // 画面切换弹窗
  _renderPictureSelectPop() {
    const {isShowDoubleCamera, switchCameraId} = this.state;
    const fontStyle = {fontSize: 16, fontWeight: 'bold', color: 'black'};
    const itemBoxStyle = {
      flexDirection: 'row',
      width: '100%',
      height: 54,
      justifyContent: 'space-between',
      alignItems: 'center',
    };
    const items = [
      {
        title: stringsTo('dual_camera_view'),
        icon: isShowDoubleCamera
          ? require('../../../resources/images/playback/icon_double_camera_primary.png')
          : require('../../../resources/images/playback/icon_double_camera_black.png'),
        isSelected: isShowDoubleCamera,
        onPress: () => {
          this.setState({isShowDoubleCamera: true, isShowSelectPicturePop: false});
        },
      },
      {
        title: stringsTo('fixed_camera_view'),
        isSelected: !isShowDoubleCamera && switchCameraId === 0,
        icon:
          !isShowDoubleCamera && switchCameraId === 0
            ? require('../../../resources/images/playback/icon_fixed_camera_primary.png')
            : require('../../../resources/images/playback/icon_fixed_camera_black.png'),
        onPress: () => {
          this.setState({isShowDoubleCamera: false, switchCameraId: 0, isShowSelectPicturePop: false});
        },
      },
      {
        title: stringsTo('ptz_camera_view'),
        isSelected: !isShowDoubleCamera && switchCameraId === 1,
        icon:
          !isShowDoubleCamera && switchCameraId === 1
            ? require('../../../resources/images/playback/icon_ptz_camera_primary.png')
            : require('../../../resources/images/playback/icon_ptz_camera_black.png'),
        onPress: () => {
          this.setState({isShowDoubleCamera: false, switchCameraId: 1, isShowSelectPicturePop: false});
        },
      },
    ];

    return (
      <TouchableWithoutFeedback
        onPress={() => {
          this.setState({isShowSelectPicturePop: false});
        }}>
        <View
          style={{
            position: 'absolute',
            width: '100%',
            height: '100%',
            backgroundColor: '#00000066',
            justifyContent: 'center',
            alignItems: 'center',
          }}>
          <View
            style={{
              flexDirection: 'column',
              backgroundColor: '#fff',
              borderRadius: 20,
              width: 360,
              paddingTop: 26,
              paddingBottom: 28,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <Text style={[fontStyle, {marginBottom: 16}]}>{stringsTo('picture_select')}</Text>
            {items.map(item => {
              return (
                <TouchableOpacity
                  style={[itemBoxStyle, item.isSelected ? {backgroundColor: '#32BAC01A'} : {}]}
                  onPress={item.onPress}>
                  <Image style={{width: 40, height: 40, marginLeft: 16}} source={item.icon} />
                  <Text
                    style={[
                      fontStyle,
                      {
                        flex: 1,
                        marginLeft: 4,
                        color: item.isSelected ? imiThemeManager.theme.primaryColor : 'black',
                      },
                    ]}>
                    {item.title}
                  </Text>
                  {item.isSelected && (
                    <Image
                      style={{width: 24, height: 24, marginRight: 26}}
                      source={require('../../../resources/images/icon_select_choose1.png')}
                    />
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </TouchableWithoutFeedback>
    );
  }

  render() {
    const {isFullScreen, switchCameraId, isShowDoubleCamera} = this.state;
    const isSwitchCamera0 = switchCameraId === 0;
    const isSwitchCamera1 = switchCameraId === 1;
    const isCamera0Show = !isFullScreen || (isFullScreen && isShowDoubleCamera) || (isFullScreen && isSwitchCamera0);
    const isCamera1Show = !isFullScreen || (isFullScreen && isShowDoubleCamera) || (isFullScreen && isSwitchCamera1);

    var start_time = (this.isView = this.state?.playStartTime?.timestamp || parseInt(this.timeBeingPlayed));
    start_time = start_time || 0;
    console.log(
      TAG,
      start_time,
      'isView',
      this.isView,
      'timeBeingPlayed',
      this.timeBeingPlayed,
      'offset',
      this.state?.playStartTime?.offset,
    );

    if (this.state.hasData && !this.firstRenderTime) {
      this.firstRenderTime = new Date().getTime();
    }
    let camera0Height = isFullScreen ? '100%' : '50%';
    let camera1Height = isFullScreen ? '100%' : '50%';
    const videoStyle = isFullScreen ? {flex: 1} : {height: (screenWidth / 16) * 9 * 2};
    // 用于全屏时，ScrollView内容区域占满整个屏幕
    const scrollContainerStyle = isFullScreen ? {flex: 1} : {};
    const smallWindowStyle = isFullScreen
      ? {
          position: 'absolute',
          right: 0,
          top: 60,
          width: 195,
          height: 111,
          zIndex: 1,
          borderRadius: 5,
          borderWidth: 2,
          borderColor: '#fff',
          transform: [{translateX: this.pan.x}, {translateY: this.pan.y}],
        }
      : {};
    return (
      <View
        ref={ref => (this.componentContainer = ref)}
        style={{height: '100%', backgroundColor: '#fff', flexDirection: 'column', flex: 1}}>
        {!this.state.isFullScreen && this._renderPortraitScreenVideoViewArea()}
        <ScrollView style={{flex: 1}} contentContainerStyle={scrollContainerStyle}>
          <View style={videoStyle}>
            {!this.state.hasData || !this.state.canPlay || this.state.hideVideo ? (
              <View style={{width: screenWidth, height: '100%', backgroundColor: 'black'}} />
            ) : (
              <View style={{flex: 1}}>
                <Animated.View
                  style={[
                    {height: camera0Height, display: isCamera0Show ? 'flex' : 'none'},
                    isSwitchCamera0 ? {} : smallWindowStyle,
                  ]}
                  {...(isFullScreen && isSwitchCamera1 ? this.panResponder.panHandlers : {})}>
                  <IMVodPlayView
                    style={{height: '100%', width: '100%'}}
                    ref={ref => {
                      if (!this.IMIVideoView && ref) {
                        if (!this.prepareStart) {
                          this.prepareStart = new Date().getTime();
                        }
                        this.IMIVideoView = ref;
                        this.focusCanplay && ref.prepare();
                        this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                      }
                    }}
                    mute={this.state.mute}
                    dataSource={{
                      iotId: LetDevice.deviceID,
                      start_time: `${start_time}`,
                      end_time: '0',
                      playerClass: PlayerClass.VOD,
                      cameraNumber: '2',
                      offset: `${this.state?.playStartTime?.offset || 0}`,
                    }}
                    onPrepared={() => {
                      this.props.onVodPlayerStatusChange &&
                        this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED, this.state.speed);
                    }}
                    onVideoViewClick={() => {
                      this.state.isFullScreen && this._onPressFullScreenTools(this.state.showFullScreenTools);
                    }}
                    onCommCallback={this._onCommCallback}
                    onIRecordState={this._onIRecordState}
                    onEventChange={this._onEventChange}
                    onPlayCompletion={() => {
                      console.log('播放结束---');
                      //超出时间轴
                      if (this.state.dataArray.length > 0) {
                        let lastVideo = this.state.dataArray[this.state.dataArray.length - 1];
                        let nowTime = new Date(
                          this.state.currentDate.getFullYear(),
                          this.state.currentDate.getMonth(),
                          this.state.currentDate.getDate(),
                          0,
                          0,
                          0,
                        ).getTime();
                        let tempEndTime = playEndTime + 80;
                        let tempStopTime = nowTime / 1000 + tempEndTime;
                        console.log('播放结束时间------', lastVideo.EndTime, tempEndTime, tempStopTime, nowTime / 1000);
                        if (this.state.recording) {
                          this._stopRecord();
                          console.log('播放完成停止录制');
                        }

                        this.setState({isPlay: false, showPauseView: true, isPlayFinish: true}, () => {
                          if (currentDayStartTime == todayStartTime) {
                            //如果是今天
                            this.IMIVideoView && this.IMIVideoView.stop();
                            currentPlayTime =
                              lastVideo.EndTime - 180 < lastVideo.BeginTime
                                ? lastVideo.BeginTime - currentDayStartTime
                                : lastVideo.EndTime - currentDayStartTime - 180;
                          } else {
                            this.IMIVideoView && this.IMIVideoView.stop();
                            currentPlayTime = this.state.dataArray[0].BeginTime - currentDayStartTime;
                          }
                        });
                        console.log('停止播放');
                        console.log('结束播放时间的currentPlay', currentPlayTime);
                      }
                    }}
                    onErrorChange={event => {
                      console.log('回看-----onErrorChange------   event.code ' + event.code, event);
                      do {
                        this.setState({
                          isLoading: false,
                          isPlay: false,
                          showErrorView: true,
                          errorCode: event?.extra?.arg1 + '(' + event?.extra?.arg3 + ')',
                        });
                        return;
                      } while (false);
                    }}
                    onVideoZoomScale={data => {
                      console.log('onVideoZoomScale---', data);
                      if (data) {
                        console.log('缩放有值走这里');
                        this.videoScaleChanged(data);
                      } else {
                        console.log('缩放无值走这里');
                      }
                    }}
                    onRecordTimeChange={this._onRecordTimeChange}
                  />
                  {!this.state.isFullScreen && (
                    <View style={{position: 'absolute', left: 0, top: 0, width: '100%', height: 40}}>
                      {this._renderTopToolBar(0)}
                    </View>
                  )}
                </Animated.View>
                <Animated.View
                  style={[
                    {
                      height: camera1Height,
                      display: isCamera1Show ? 'flex' : 'none',
                    },
                    isSwitchCamera1 ? {} : smallWindowStyle,
                  ]}
                  {...(isFullScreen && isSwitchCamera0 ? this.panResponder.panHandlers : {})}>
                  <IMIRenderViewManager style={{height: '100%', width: '100%'}} ref={this._assignRootManager} />
                  {!this.state.isFullScreen && (
                    <View style={{position: 'absolute', left: 0, top: 0, width: '100%', height: 40}}>
                      {this._renderTopToolBar(1)}
                    </View>
                  )}
                </Animated.View>
                {!this.state.isFullScreen && this._renderPortraitScreenPlayerToolBarArea(this.state.isFullScreen)}
              </View>
            )}
            {/*全屏?横屏UI:竖屏UI(navBar)*/}
            <View
              pointerEvents="box-none"
              style={{
                position: 'absolute',
                width: '100%',
                height: '100%',
                flexDirection: 'column',
                alignItems: 'center',
              }}>
              {this._renderSnapshotView()}
              {this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()}
              {this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()}
              {this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()}
              {this._noDataView()}
              {this._errorFindView()}
              {this._renderRecordingView()}
              {this.props.videoSubView ? this.props.videoSubView(this.state.isFullScreen) : null}
              {this.state.isFullScreen && this._renderLandscapeScreenVideoViewArea()}
            </View>
          </View>
          {this._renderTimeLineView()}
          {this.renderZoomScaleView()}
          {this.renderTimeScroll()}
        </ScrollView>
        {this.state.isFullScreen && this.state.isShowSelectPicturePop && this._renderPictureSelectPop()}
      </View>
    );
  }
}
