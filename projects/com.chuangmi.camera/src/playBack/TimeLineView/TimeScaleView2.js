import React from 'react';
import { View, Image, Text, Dimensions, PanResponder, TouchableOpacity, FlatList, StyleSheet, Animated } from 'react-native';
import { Surface, Shape, Path } from '@react-native-community/art'
import PropTypes from 'prop-types';
import {EVENT_TYPE_KEY, EVENT_TYPE_COLOR } from './EventTypeConfig';
import {stringsTo} from '../../../../../globalization/Localize';
import dayjs from 'dayjs';

const kIsCN = true;

const Scale_Type_Max = 1;
const Scale_Type_Big = 2;
const Scale_Type_Middle = 3;
const Scale_Type_Small = 4;

const SCALING_FACTOR_MAX = 10; // 最大放大参数
const SCALING_FACTOR_MIN = 0.5; // 最小缩小参数

const WEEKS = [stringsTo('SundayShort'), stringsTo('MondayShort'), stringsTo('TuesdayShort'), stringsTo('WednesdayShort'), stringsTo('ThursdayShort'), stringsTo('FridayShort'), stringsTo('SaturdayShort')];
const WEEKENDS = [stringsTo('Sunday'), stringsTo('Monday'), stringsTo('Tuesday'), stringsTo('Wednesday'), stringsTo('Thursday'), stringsTo('Friday'), stringsTo('Saturday')];
const TODAY = stringsTo('todayTitle')
const SCROLLSTATE = {
  IDLE: 0,
  SCROLLING: 1,
  SCALING: 2
};

const peopleMoveColor = EVENT_TYPE_COLOR.peopleSelectedColor;
const areaMoveColor = EVENT_TYPE_COLOR.motionSelectedColor;
const abnormalSoundColor = EVENT_TYPE_COLOR.abnormalSoundSelectedColor;
const defaultColor = "#A2A2A2";
const kWindowHeight = Math.max(Dimensions.get("screen").height, Dimensions.get("screen").width);
const kWindowWidth = Math.min(Dimensions.get("screen").height, Dimensions.get("screen").width);

export default class TimeScaleView2 extends React.Component {

  constructor(props) {
    super(props);
    this.drawWidth = 0;
    this.initItemInterval = 20;
    this.scaleType = Scale_Type_Middle;
    this.intervalCount = 6; // 多少个小刻度间隔一个大刻度
    this.itemTime = 30 * 60 * 1000; // 大刻度之间间隔时间 毫秒
    this.itemInterval = this.initItemInterval; // 刻度间隔宽度
    this.intervalTime = this.itemTime / this.intervalCount; // 每一个格子间隔时间 毫秒
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval; // 一个宽度占多少毫秒
    this.panResponder = null;
    this.dateTime = new Date();
    this.dataList = [];
    this.isDrawArea = false;
    this.isLeftPressed = false;
    this.isRightPressed = false;
    this.drawColorX = 0;
    this.isScrolling = false;
    this.timeTextList = [];
    this.motionAreaList = [];
    this.isNeedInit = true;
    this.motionType = 1;
    this.selectedDayTime = new Date().getTime();

    this.timelineViewWidth = 0;
    this.viewWidth = 0;
    this.viewHeight = 0;
    this.curScaleFactor = 1;

    this.dayListOffset = 0;
    this.selectedDayTag = "";
    this.endTimeout = null;
    this.scrollState = SCROLLSTATE.IDLE;

    this.scrollDayIndexTimeout = null;

    this.textSize = 12; // 文字的大小
    this.textTop = Number.parseInt(120 / 14); // 1/14的高度 控制文字的顶部距离控件的顶部的距离；控制时间轴的高度
    this.hourTextHalfWidth = 15;// 11:00 这个文字的宽度的一半，用于控制绘制时间文本时 起始处的x坐标。
    this.timelineTop = this.textTop + this.textSize * 3;// 时间轴顶部的y偏移
    this.timelineBottom = this.timelineTop + this.textTop;// 时间轴底部的y偏移
    this.dotPosition = this.timelineBottom + (120 - this.timelineBottom) / 2; // 时间轴组件里底部的小圆点圆心y坐标
    this.leftRightButtonWidth = 20; // 左右button的宽高
    this.leftRightButtonTop = this.dotPosition - this.leftRightButtonWidth / 2; // 左右button的上边缘y坐标
    this.leftRightButtonBottom = this.dotPosition + this.leftRightButtonWidth / 2; // 左右button的下边缘y坐标
    this.leftRigthButtonPadding = 17; // 左右button距离左右边缘的padding。

    this.pan = new Animated.Value(0);


    // here we put some areas here;
    this.defaultArea = [];
    this.motionArea = [];
    this.peopleArea = [];
    this.faceArea = [];
    this.abnormalSoundArea = [];
    this.aiArea = [];
    this.petArea = [];
    this.packageArea = [];
    this.cameraCallingArea = [];
    this.vehicleArea = [];
    this.nonVehicleArea = [];
    this.keyAreaArea = [];
    this.fenceInArea = [];
    this.fenceOutArea = [];
    this.displayCutoutTop = 0;
    this.lastTime = 0;
  }
  static propTypes = {
    style: PropTypes.any,
    // data: PropTypes.array,
    // time: PropTypes.number,
    landscape: PropTypes.bool,
    // onTimeScaleChange: PropTypes.func,
    // backgroundColor: PropTypes.string,
    onScrollEnd: PropTypes.func,
    onScrolling: PropTypes.func,
    isDisabled: PropTypes.bool,
    eventTypeUnSelect: PropTypes.array,
    isCloud: PropTypes.bool
  }
  state = {
    showTimelineView: false,
    dateData: [],
    isMoving: false,
    currentTime: 0,
    showDayList: false,
    showDayIndicator: true,
    selectedDayIndex: 0,
    eventTypeUnSelect: this.props.eventTypeUnSelect,
  }
  static defaultProps = {
    data: null,
    time: null,
    landscape: false,
    onTimeScaleChange: null,
    onScrollEnd: null,
    onScrolling: null,
    isCloud: false
  }

  // 重新赋值大刻度之间间隔时长
  initInterval() {
    this.intervalCount = 6;
    switch (this.scaleType) {
      case Scale_Type_Max:
        this.itemTime = 6 * 60 * 60 * 1000; // 间隔6小时
        break;
      case Scale_Type_Big:
        this.itemTime = 60 * 60 * 1000; // 间隔1小时
        break;
      case Scale_Type_Middle:
        this.itemTime = 30 * 60 * 1000; // 间隔30分钟
        break;
      case Scale_Type_Small:
        this.intervalCount = 10;
        this.itemTime = 10 * 60 * 1000; // 间隔10分钟
        break;
    }
    this.intervalTime = this.itemTime / this.intervalCount;
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval;
  }

  componentWillMount() {
    //console.log(TAG, `componentWillMount: `);
    this.dataList = this.props.data;
    this.initPanResponder();
    // this.initTempList();
  }

  componentDidUpdate() {

    if (this.props.eventTypeUnSelect == this.state.eventTypeUnSelect) {
      if (this.props.landscape == !this.state.showDayIndicator) {
        return;
      }
      if (this.props.landscape) {
        this.setState({ showDayIndicator: false });
      } else {
        this.setState({ showDayIndicator: true });
      }
    } else {
      this.setState({ eventTypeUnSelect: this.props.eventTypeUnSelect });
    }
  }

  componentDidMount() {
    this.scrollToTimestamp(new Date().getTime());
  }


  getIsScrolling = () => {
    return this.isLeftLongPressed || this.isRightLongPressed || this.scrollState != SCROLLSTATE.IDLE
  }
  initData(data) {
    this.dataList = data;
    this.setState({});
    let dayMillis = 24 * 60 * 60 * 1000;
    let maxDays = 29;
    let date = new Date();
    date.setHours(0);
    date.setMinutes(0);
    date.setSeconds(0, 0);
    let currentDayStartTimestamp = date.getTime();
    let dateListDays = [];
    for (let i = maxDays; i >= 0; i--) {
      let dayItem = {};
      let timestamp = currentDayStartTimestamp - i * dayMillis;
      this.dateTime.setTime(timestamp);
      let day = this.dateTime.getDate();
      let month = this.dateTime.getMonth() + 1;
      let year = this.dateTime.getFullYear();
      let week = this.dateTime.getDay();// 星期几  方便得到周几的字串
      dayItem.day = day > 9 ? (`${day}`) : (`0${day}`);
      dayItem.month = month > 9 ? (`${month}`) : (`0${month}`);
      dayItem.year = `${year}`;
      dayItem.week = week;
      dayItem.startTimestamp = timestamp;
      dayItem.hasVideo = false;
      dayItem.dateStr = (month > 9 ? `${month}` : `0${month}`) + (day > 9 ? `${day}` : `0${day}`);
      dateListDays.push(dayItem);
    }

    let dayItems = [];
    let lastTime = 0;
    for (let i = 0; i < data.length; i++) {
      let item = data[i];
      if (item.startTime >= lastTime) {
        let dayItem = {};
        dayItem.startTimestamp = item.startTime;
        this.dateTime.setTime(item.startTime);
        this.dateTime.setSeconds(0, 0);
        this.dateTime.setMinutes(0);
        this.dateTime.setHours(0);
        let month = this.dateTime.getMonth() + 1;
        let date = this.dateTime.getDate();
        dayItem.dateStr = (month > 9 ? `${month}` : `0${month}`) + (date > 9 ? `${date}` : `0${date}`);
        dayItems.push(dayItem);
        lastTime = this.dateTime.getTime() + dayMillis;
      }

    }


    let indexInDayItems = 0;
    let indexInDayList = 0;
    while (indexInDayItems < dayItems.length && indexInDayList < dateListDays.length) {
      let dayItem = dayItems[indexInDayItems];
      let dateItem = dateListDays[indexInDayList];
      this.dateTime.setTime(dayItem.startTimestamp);
      this.dateTime.setHours(0);
      this.dateTime.setMinutes(0);
      this.dateTime.setSeconds(0, 0);
      if (this.dateTime.getTime() == dateItem.startTimestamp) { // 从数据里得到的时间与从自己生成的日期是一样的
        dateItem.hasVideo = true;
        dateItem.startTimestamp = dayItem.startTimestamp;
        dateItem.dateStr = dayItem.dateStr;
        indexInDayItems++;
        indexInDayList++;
        continue;
      }
      if (this.dateTime.getTime() < dateItem.startTimestamp) {
        indexInDayItems++;
        continue;
      }
      if (this.dateTime.getTime() > dateItem.startTimestamp) {
        indexInDayList++;
      }
    }
    // 这里得到的dateListDays 就包含了日期、是否有视频、当天最早的一个视频
    this.setState(() => { return { dateData: dateListDays }; }, () => {
      this.refreshTopDateView(this.state.currentTime);
    });
    this.cloudSdMaxTime = this.dataList[this.dataList.length - 1]?.endTime;
    this.cloudSdMinTime = this.dataList[0]?.startTime;
  }

  // 外部的view同步时间轴进度的。
  scrollToTimestamp(timestamp) {
    // //console.log(TAG, `scroll to timestmap:${ this.formatTimeInMMSS(timestamp) }  last scrollTime: ${ this.formatTimeInMMSS(this.lastTime) } leftPressed:${ this.isLeftLongPressed } rightPressed:${ this.isRightLongPressed } state:${ this.scrollState != SCROLLSTATE.IDLE }`);
    // this.scrollToTimestampWithNotify(time, false);
    if (this.isLeftLongPressed || this.isRightLongPressed || this.scrollState != SCROLLSTATE.IDLE) {
      return;
    }
    if (timestamp <= 0) {
      return;
    }
    if (Math.abs(this.lastTime - timestamp) > 2000) { // 如果外面通知太快，refreshtopdateview就不能正常滚动了
      this.refreshTopDateView(timestamp);
    }
    if (this.lastTime == timestamp) {
      return;// 避免快速刷新
    }
    this.lastTime = timestamp;
    this.setState({ currentTime: timestamp }); // 刷新页面
  }

  isTimelineIdle() {
    if (this.isLeftLongPressed || this.isRightLongPressed || this.scrollState != SCROLLSTATE.IDLE) {
      return false;
    }
    return true;
  }

  _calculateOffset(selectedDayIndex) { // copy horizonalScrollView.java computeScrollDeltaToGetChildRectOnScreen
    // selectedDayIndex = selectedDayIndex;
    let width = timeWidth + timeMargin * 2;
    let screenLeft = this.dayListOffset || 0;
    let screenRight = screenLeft + width;
    let itemLeft = selectedDayIndex * timeWidth_2;
    let itemRight = (selectedDayIndex + 1) * timeWidth_2;
    let scrollXDelta = 0;

    if (itemRight > screenRight && itemLeft > screenLeft) {
      // need to move right to get it in view: move right just enough so
      // that the entire rectangle is in view (or at least the first
      // screen size chunk).

      // get entire rect at right of screen
      scrollXDelta += (itemRight - screenRight + timeWidth_2 + 12);


      // make sure we aren't scrolling beyond the end of our content

    } else if (itemLeft < screenLeft && itemRight < screenRight) {
      // need to move right to get it in view: move right just enough so that
      // entire rectangle is in view (or at least the first screen
      // size chunk of it).
      scrollXDelta -= (screenLeft - itemLeft - timeWidth_2);
      // make sure we aren't scrolling any further than the left our content
    }
    return scrollXDelta;
  }


  setSelectedDay(centerTimestamp) {
    this.selectedDayTime = centerTimestamp;
    this.dateTime.setTime(centerTimestamp);
    let year = this.dateTime.getFullYear();
    let month = this.dateTime.getMonth() + 1;
    let day = this.dateTime.getDate();
    let str = (month > 9 ? `${month}` : `0${month}`) + (day > 9 ? `${day}` : `0${day}`);

    // //console.log("refreshTopdate", "选中日期:", str);
    let selectedDayIndex = -1;
    let dayArray = this.state.dateData;
    for (let i = 0; i < dayArray.length; i++) {

      let day = dayArray[i];
      if (day.dateStr === str) {
        dayArray[i].isSelected = true;
        selectedDayIndex = i;
      } else {
        dayArray[i].isSelected = false;
      }
    }
    this.setState({ dateData: dayArray });
    if (selectedDayIndex == this.selectedDayIndex) {
      this.selectedDayIndex = selectedDayIndex;
      return selectedDayIndex;// 如果一样。
    }
    this.selectedDayIndex = selectedDayIndex;

    this.setState({ dateData: dayArray });
    return this.selectedDayIndex;
  }

  // here to see what happened  should make a debugger
  refreshTopDateView(centerTimestamp) {
    // //console.log("refreshTopdate");
    this.selectedDayTime = centerTimestamp;
    let selectedDayIndex = this.setSelectedDay(centerTimestamp);
    if (selectedDayIndex == -1) {
      return;
    }
    clearTimeout(this.scrollDayIndexTimeout);

    if (!this.state.showDayList) {
      return;
    }
    this.selectedDayIndex = selectedDayIndex;
    clearTimeout(this.scrollDayIndexTimeout);

    let offset = this._calculateOffset(selectedDayIndex) + this.dayListOffset;

    this.scrollDayIndexTimeout = setTimeout(() => {
      if (selectedDayIndex < 0) {
        return;
      }
      if (this.dayListRef != null) {
        //console.log("refreshTopdate", "移动距离", offset);
        this.dayListRef.scrollToOffset({
          animated: false,
          offset: offset
        });
      }

    }, 300);

  }

  _getSelectedDayIndex() {
    let index = -1;
    for (let i = 0; i < this.state.dateData.length; i++) {
      if (this.state.dateData[i].isSelected) {
        index = i;
        break;
      }
    }
    return index;
  }

  _getPrevDayIndex(currentSelectedIndex) {
    for (let i = currentSelectedIndex - 1; i >= 0; i--) {
      if (this.state.dateData[i].hasVideo) {
        return i;
      }
    }
    return -1;
  }

  _getNextDayIndex(currentSelectedIndex) {
    if (currentSelectedIndex == -1) {
      return -1;
    }
    for (let i = currentSelectedIndex + 1; i < this.state.dateData.length; i++) {
      if (this.state.dateData[i].hasVideo) {
        return i;
      }
    }
    return -1;
  }

  _onPressDayItem(dayItem) {
    let time1 = new Date().getTime();
    //console.log("onpressDayItem:", JSON.stringify(dayItem));
    if (this.props.isDisabled) {
      return;
    }
    if (!dayItem.hasVideo) {
      return;
    }
    let timestamp = dayItem.startTimestamp;
    //console.log(`selected item:${CloudVideoUtil.convertTimestampToTimeStr(timestamp)}`);
    // 防止在两边的情况 做一下处理
    this.scrollToTimestampWithNotify(timestamp, true);
    let time2 = new Date().getTime();
    //console.log("time diff:", time2 - time1);
  }

  _dayKeyExtractor = (item) => item.startTimestamp.toString();

  _getDayItemLayout = (item, index) => {
    return {
      length: (timeWidth + 2 * timeMargin),
      offset: (timeWidth + 2 * timeMargin) * index,
      index
    };
  }

  componentWillUnmount() {
    //console.log(TAG, `componentWillUnmount: `);
    clearTimeout(this.scrollDayIndexTimeout);
  }

  render() {
    if (this.isNeedInit) {
      this.isNeedInit = false;
      this.initInterval();
    }
    this.timeTextList = [];
    this.motionAreaList = [];
    return (

        <View   style={[{ width: "100%", display: "flex", flexDirection: "row" }, this.state.showDayIndicator ? null : { paddingHorizontal: this.displayCutoutTop }]}>
        <View style={[{ flexGrow: 1, display: "flex", flexDirection: "column", backgroundColor: "#ffffff", overflow: "hidden" }]}
          removeClippedSubviews={true}
        >
          {this._renderDayIndicator()}

          {this._renderDayList()}

          {this._renderTimelineView()}
        </View>

      </View>

    );
  }
  // 时间轴
  _renderTimelineView() {
    let timelineHeight = 120;
    if (kWindowHeight < 700) {
      timelineHeight = 100;
    }
    return (

      <View
        style={{ width: "100%", borderTopColor: "#f0f0f0", borderTopWidth: 0.8, height: timelineHeight, display: "flex", flexDirection: "row", backgroundColor: this.state.showDayIndicator ? "#F7F7F7" : "#262626" }}
        onLayout={(event) => {
          //console.log(TAG, "onlayout", event.nativeEvent.layout);
          let width = event.nativeEvent.layout.width;
          let height = event.nativeEvent.layout.height;
          if (this.viewHeight == height && (this.viewWidth == width)) {
            return;// onlayout 布局发生了改变，但是view宽高没有改变，不需要重新渲染
          }
          let paddingWidth = width - this.leftRightButtonWidth * 2 - this.leftRigthButtonPadding * 2;
          this.viewHeight = event.nativeEvent.layout.height;

          //console.log(TAG, `finalWidth:${width}`);
          if (paddingWidth == null || paddingWidth <= 0) {
            this.setState({ showTimelineView: false });
            return;
          }
          this.timelineViewWidth = paddingWidth;
          this.viewWidth = width;

          if (this.state.showTimelineView) {
            this.setState(() => { return { showTimelineView: false }; }, () => {
              this.setState({ showTimelineView: true });
            });
          } else {
            this.setState({ showTimelineView: true });
          }
        }}
      >

        {this._renderHours()}
        {this._renderAlarmAreas()}
        {this._renderCenterLine()}
        {this._renderLeftRightButton()}

      </View>
    );
  }

  _renderAlarmAreas() {

    this.defaultArea = [];
    this.motionArea = [];
    this.peopleArea = [];
    this.abnormalSoundArea = [];

    if (!this.state.showTimelineView) {
      return null;
    }

    // const viewWidth = Math.ceil(this.timelineViewWidth);
    const centerX = Number.parseInt(this.viewWidth / 2); // 得到一半的长度
    let leftTimestamp = Math.ceil(this.state.currentTime - centerX * this.oneUnitWidthTime); // 计算出最小时间戳
    let rightTimestamp = Math.ceil(this.state.currentTime + centerX * this.oneUnitWidthTime); // 计算出最大时间戳

    this._calculteSdEvents(leftTimestamp, rightTimestamp);

    let shouldDrawDefault = true;
    let shouldDrawAbnormalSound =  !this.state.eventTypeUnSelect.includes(EVENT_TYPE_KEY['AbnormalSound']);
    let shouldDrawAreaMove = !this.state.eventTypeUnSelect.includes(EVENT_TYPE_KEY['ObjectMotion']);
    let shouldDrawPeopleMove = !this.state.eventTypeUnSelect.includes(EVENT_TYPE_KEY['PeopleMotion']);

    //TODO 重点区域事件？
    let path = new Path();
    let motionPath = new Path();
    let peoplePath = new Path();
    let abnormalSoundPath = new Path();
    if (shouldDrawDefault) {
      for (let i = 0; i < this.defaultArea.length; i++) {
        let area = this.defaultArea[i];
        path.moveTo(area.startX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop + this.textTop);
        path.lineTo(area.startX, this.timelineTop + this.textTop);
      }
    }
    if (!shouldDrawAreaMove) { // 不绘制区域移动的颜色 那就画默认的颜色
      for (let i = 0; i < this.motionArea.length; i++) {
        let area = this.motionArea[i];
        path.moveTo(area.startX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop + this.textTop);
        path.lineTo(area.startX, this.timelineTop + this.textTop);
      }
    } else {
      for (let i = 0; i < this.motionArea.length; i++) {
        let area = this.motionArea[i];
        motionPath.moveTo(area.startX, this.timelineTop);
        motionPath.lineTo(area.endX, this.timelineTop);
        motionPath.lineTo(area.endX, this.timelineTop + this.textTop);
        motionPath.lineTo(area.startX, this.timelineTop + this.textTop);
      }
      motionPath.close();
    }
    if (!shouldDrawPeopleMove) {
      for (let i = 0; i < this.peopleArea.length; i++) {
        let area = this.peopleArea[i];
        path.moveTo(area.startX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop + this.textTop);
        path.lineTo(area.startX, this.timelineTop + this.textTop);
      }
    } else {
      for (let i = 0; i < this.peopleArea.length; i++) {
        let area = this.peopleArea[i];
        peoplePath.moveTo(area.startX, this.timelineTop);
        peoplePath.lineTo(area.endX, this.timelineTop);
        peoplePath.lineTo(area.endX, this.timelineTop + this.textTop);
        peoplePath.lineTo(area.startX, this.timelineTop + this.textTop);
      }
      peoplePath.close();

    }
    if (!shouldDrawAbnormalSound) {
      for (let i = 0; i < this.abnormalSoundArea.length; i++) {
        let area = this.abnormalSoundArea[i];
        path.moveTo(area.startX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop);
        path.lineTo(area.endX, this.timelineTop + this.textTop);
        path.lineTo(area.startX, this.timelineTop + this.textTop);
      }
    } else {
      for (let i = 0; i < this.abnormalSoundArea.length; i++) {
        let area = this.abnormalSoundArea[i];
        abnormalSoundPath.moveTo(area.startX, this.timelineTop);
        abnormalSoundPath.lineTo(area.endX, this.timelineTop);
        abnormalSoundPath.lineTo(area.endX, this.timelineTop + this.textTop);
        abnormalSoundPath.lineTo(area.startX, this.timelineTop + this.textTop);
      }
      abnormalSoundPath.close();
    }


    path.close();

    let color = defaultColor;


    return (
      <Surface style={{ position: "absolute" }}
        width={this.viewWidth}
        height={120}
      >
        <Shape d={path} fill={color} strokeWidth={1} />
        <Shape d={motionPath} fill={areaMoveColor} strokeWidth={1} />
        <Shape d={peoplePath} fill={peopleMoveColor} strokeWidth={1} />
        <Shape d={abnormalSoundPath} fill={abnormalSoundColor} strokeWidth={1} />

      </Surface>
    );
  }

  // 这里是竖线
  _renderCenterLine() {
    let color = (this.state.showDayIndicator) ? "#333333" : "#ffffff";
    return (
      <View
        style={{ width: "100%", height: "100%", display: "flex", flexDirection: "row", justifyContent: "center", alignItems: "flex-end", position: "absolute" }}>
        <View style={{ position: "absolute", backgroundColor: color, width: 2, height: 95 }}/>
      </View>
    );
  }

  _renderLeftRightButton() {

    let leftButton = (
      <TouchableOpacity
        style={{ width: this.leftRightButtonWidth + this.leftRigthButtonPadding, height: 20, paddingLeft: this.leftRigthButtonPadding, left: 0, marginTop: this.leftRightButtonTop }}
        onPressIn={() => this.onLeftPressIn()}
        onPressOut={() => this.onLeftPressOut()}
        underlayColor={"#55555522"}
        activeOpacity={0.88}
      >
        <Image
          style={{ width: "100%", height: "100%",transform: [{ scaleX: 1}] }}
          source={require("../../../resources/images/playback/progress_button_last_nor2.png")}

        />
      </TouchableOpacity>
    );

    let rightButton = (
      <TouchableOpacity
        style={{ width: this.leftRightButtonWidth + this.leftRigthButtonPadding, height: 20, paddingRight: this.leftRigthButtonPadding, right: 0, marginTop: this.leftRightButtonTop }}
        onPressIn={() => this.onRightPressIn()}
        onPressOut={() => this.onRightPressOut()}
        underlayColor={"#55555522"}
        activeOpacity={0.88}
      >
        <Image
          style={{ width: "100%", height: "100%" ,transform: [{ scaleX: 1}]}}
          source={require("../../../resources/images/playback/progress_button_next_nor2.png")}

        />
      </TouchableOpacity>
    );
    return (
      <View style={{ width: "100%", height: "100%", display: "flex", flexDirection: "row" }}>
        {leftButton}
        <View style={{ flexGrow: 1, height: "100%", backgroundColor: "#00000000" }} // 这里添加backgroundolor的原因：android端会默认没有背景色的占位View 导致panResponder不生效
          {...this.panResponder.panHandlers}

        >
        </View>
        {rightButton}
      </View>
    );


  }

  _renderHours() {
    // 这里绘制一条条整点时刻竖着的时间轴；底部的小圆圈；时间值
    let viewWidth = Number.parseInt(this.viewWidth);
    let centerX = Number.parseInt(this.viewWidth / 2);
    let leftTimestamp = Number.parseInt(this.state.currentTime - centerX * this.oneUnitWidthTime); // 计算出左边的最小时间戳;
    let drawX = 0; // 记录绘制刻度的位置
    let drawTimestamp = 0; // 记录所绘制刻度线代表的时间戳
    // 先计算出绘制的第一个刻度的位置和时间戳
    let temp = Number.parseInt(leftTimestamp / this.intervalTime);
    drawTimestamp = Number.parseInt((temp + 1) * this.intervalTime); // 得到最左边的那个刻度线的时间值。
    drawX = Number.parseInt((drawTimestamp - this.state.currentTime) / this.oneUnitWidthTime) + centerX; // 离屏绘制 距离屏幕开始处左边一个屏幕宽度的位置
    let rulerViews = [];
    let index = 0;
    while (drawX < (viewWidth)) { // 绘制左右两边超出分别一个屏幕的区域
      this.dateTime.setTime(drawTimestamp);
      let year = this.dateTime.getFullYear();
      let month = this.dateTime.getMonth() + 1;
      let day = this.dateTime.getDate();
      let hour = this.dateTime.getHours();
      let min = this.dateTime.getMinutes();
      let sec = this.dateTime.getSeconds();
      let dateStr = null;
      let hourStr = null;
      let shouldDraw = false;
      if (hour == 0 && min == 0 && sec == 0) { // 需要添加日期
        dateStr = `${month > 9 ? month : `0${month}`}/${day > 9 ? day : `0${day}`}`;
        hourStr = `${hour > 9 ? `${hour}` : `0${hour}`}:${min > 9 ? min : `0${min}`}`;
        shouldDraw = true;
      } else if ((min == 0 || min == 30) && sec == 0) {
        hourStr = `${hour > 9 ? `${hour}` : `0${hour}`}:${min > 9 ? min : `0${min}`}`;
        shouldDraw = true;
      }

      if (shouldDraw) { // 绘制小圆点 小竖线 文字
        let dotView = (<View key={index} style={{ width: 4, height: 4, backgroundColor: "#DBDBDB", position: "absolute", left: drawX - 1, top: this.dotPosition - 1, borderRadius: 4 }} />);
        rulerViews.push(dotView);
        index++;

        let textView = (<Text key={index}
          style={{
            fontSize: 12,
            position: "absolute",
            left: drawX - this.hourTextHalfWidth,
            top: this.textTop,
            color:this.props.landscape?'#fff':'#000'
          }}>{hourStr}
        </Text>);
        index++;
        let lineView = (<View key={index} style={{ width: 1, height: this.textTop, position: "absolute", left: drawX -1, backgroundColor:"#D5D5D5" }} />);
        index++;
        rulerViews.push(textView);
        rulerViews.push(lineView);
      }
      drawX = drawX + this.itemInterval;// 增加一个刻度所需的宽度。
      drawTimestamp = drawTimestamp + this.intervalTime; // 增加一个刻度之间所需的时间差

    }
    return (
      <View style={{ width: "100%", height: "100%", position: "absolute" }}
      >
        {rulerViews}
      </View>
    );
  }
  // 这里是星期几
  _renderDayList() {

    return (
      <View
        style={(this.state.showDayList && this.state.showDayIndicator) ? { width: "100%", height: 60, display: "flex", flexDirection: "row" } : [{ width: "100%", height: 60, display: "flex", flexDirection: "row" }, { display: "none" }]}
      >
        <FlatList
          ref={(ref) => { this.dayListRef = ref; }}
          style={{ width: "100%", height: 60 }}
          data={this.state.dateData}
          horizontal={true}
          renderItem={({ item, index }) => {
            return this._renderDayItem(item, index);
          }}
          onScroll={(event) => {
            this.dayListOffset = event.nativeEvent.contentOffset.x;
            //console.log(`当前偏移量：${this.dayListOffset}`);
          }}
          keyExtractor={this._dayKeyExtractor}
          getItemLayout={this._getDayItemLayout}
          showsHorizontalScrollIndicator={false}
        />
      </View>

    );
  }

  _renderDayIndicator() {
    if (!this.state.showDayIndicator) {
      return null;
    }
    // 渲染顶部的时间
    let dayArrays = this.state.dateData;
    let timestamp = 0;
    let currentTimestamp = new Date().getTime();
    let index = this._getSelectedDayIndex();
    if (index != -1) {
      timestamp = dayArrays[index].startTimestamp;
    }
    if (timestamp == 0) {
      timestamp = currentTimestamp;
    }
    this.selectedDayTime = timestamp;
    this.dateTime.setTime(timestamp);
    let day = this.dateTime.getDate();
    let month = this.dateTime.getMonth() + 1;
    let week = this.dateTime.getDay();
    let year = this.dateTime.getFullYear();
    this.dateTime.setTime(currentTimestamp);
    let currentDay = this.dateTime.getDate();
    let currentMonth = this.dateTime.getMonth() + 1;
    let currentYear = this.dateTime.getFullYear();
    let isToday = false;
    if (day == currentDay && month == currentMonth && year == currentYear) {
      isToday = true;
    }
    let str = "";
    if (isToday) {
      str = TODAY;
    } else {
      str = dayjs.unix(timestamp/1000).format(stringsTo('MonthAndDay'));
      str = `${str} ${WEEKENDS[week]}`;
    }
    let dateHeight = 50;
    return (
      <View style={{ width: "100%", height: dateHeight, display: "flex", flexDirection: "row", alignItems: "center", marginTop: 20, marginBottom: 20 }}>
        <TouchableOpacity
          style={{ paddingLeft: 23, width: 42, height: 22 }}
          onPress={() => {
            // 跳到左边的日期 或者跳到右边的日期
            //console.log("跳到左边一天");
            let index = this._getSelectedDayIndex();
            let prevIndex = this._getPrevDayIndex(index);
            if (prevIndex < 0) {
              return;
            }

            let prevItem = this.state.dateData[prevIndex];
            this._onPressDayItem(prevItem);
          }}
        >

          <Image
            style={{ width: 22, height: 22 ,transform: [{ scaleX: 1}]}}
            source={require("../../../resources/images/playback/time_line_icon_pre_day.png")}

          >

          </Image>
        </TouchableOpacity>
        <TouchableOpacity
          style={{ flex: 1, display: "flex", alignItems: "center" }}
          onPress={() => {
            if (this.props.isDisabled) {
              return;
            }
            this.setState((state) => {
              return {
                showDayList: !state.showDayList
              };
            }, () => {
              if (this.state.showDayList) {
                this.refreshTopDateView(this.state.currentTime);
              } else {
                this.selectedDayIndex = -1;
              }
            });
          }}
        >
          <Text style={{ fontSize: kIsCN ? 19 : 17, color: "black", fontWeight: "bold" }}>
            {str}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={{ paddingRight: 20, width: 42, height: 22 }}
          onPress={() => {
            // 跳到左边的日期 或者跳到右边的日期
            //console.log("跳到右边一天");
            let index = this._getSelectedDayIndex();
            let nextIndex = this._getNextDayIndex(index);
            if (nextIndex < 0) {
              return;
            }
            let nextItem = this.state.dateData[nextIndex];
            this._onPressDayItem(nextItem);
          }}
        >
          <Image
            style={{ width: 22, height: 22 ,transform: [{ scaleX: 1}]}}
            source={require("../../../resources/images/playback/time_line_icon_next_day.png")}
          >

          </Image>
        </TouchableOpacity>

      </View>
    );
  }
  // 具体的星期几
  _renderDayItem(item, index) {
    let startTime = item.startTimestamp;
    this.dateTime.setTime(startTime);
    let month = this.dateTime.getMonth() + 1;
    let day = this.dateTime.getDate();
    let str = day > 9 ? `${day}` : `0${day}`;
    let week = WEEKS[item.week];
    return (
      <View
        style={item.isSelected ? styles.dayItemSelected : styles.dayItem}
        key={index}
      >
        <TouchableOpacity
          style={{ display: "flex", flexDirection: "column", alignItems: "center", paddingTop: 2 }}
          onPress={() => this._onPressDayItem(item)}
        >
          <Text
            style={item.hasVideo ? styles.dayItemTextWeek : { color: "#CCCCCC", fontSize: kIsCN ? 11 : 10 }}
          >
            {week}
          </Text>

          <Text
            style={[styles.dayItemTextDay, { fontWeight: "bold", color: item.hasVideo ? "black" : "#CCCCCC" }]}
          >
            {str}
          </Text>

        </TouchableOpacity>

      </View>
    );
  }

  _calculteSdEvents(leftTimestamp, rightTimestamp) {
    // //here nothing todo
    // return null;
    if (this.dataList == null || this.dataList.length <= 0) {
      return;
    }
    // 对于事件类型 与上一个item的type不一样，就记录startPosition, 并把上一个item的endPosition与之前的startPosition一起结合。
    // 如果
    let lastType = -8888;
    let startPosition = -8888;
    let lastEndPosition = 0;
    let drawAreaX = 0; // 记录绘制的位置，过滤绘制重复的区域
    let positionArray = [];
    for (let i = 0; i < this.dataList.length; i++) {
      let dataItem = this.dataList[i];
      let startTime = dataItem.startTime;
      let endTime = dataItem.endTime;
      let type = dataItem.eventType;// 这里需要根据021修改规则。
      // 获取开始时间和结束时间

      // if (this.selectedDayTime >= this.dataList[0].startTime && this.selectedDayTime <= this.dataList[this.dataList.length - 1].endTime) {
      //   this.cloudSdMaxTime = this.dataList[this.dataList.length - 1].endTime;
      //   this.cloudSdMinTime = this.dataList[0].startTime;
      // }
      if (endTime > leftTimestamp && startTime < rightTimestamp) { // 只对绘制区域内的作处理。
        let drawStartX = (startTime - leftTimestamp) / this.oneUnitWidthTime;
        let drawEndX = (endTime - leftTimestamp) / this.oneUnitWidthTime;
        if (drawAreaX != 0 && drawAreaX >= drawStartX) { // 纠正数据重复的
          drawStartX = drawAreaX;
        }
        if (drawEndX <= drawStartX) {
          continue;
        }
        // TODO 比较进的地方 就画成一样的吧。
        if (type != lastType || drawStartX != lastEndPosition) { // 事件类型不一样了，起始位置跟上一次的终止不一样
          if (startPosition != -8888) { // 不是第一个
            let position = {};
            position.type = lastType;
            position.startX = startPosition;
            position.endX = lastEndPosition;

            this.putIntoAlarmAreaList(lastType, startPosition, lastEndPosition);

            // positionArray.push(position);
          }
          startPosition = drawStartX;
          lastType = type;
        }
        drawAreaX = drawEndX;
        lastEndPosition = drawEndX;
      }

    }
    // 需要补上最后一个item
    if (lastType != -8888) {
      let position = {};
      position.startX = startPosition;
      position.endX = lastEndPosition;
      position.type = lastType;
      this.putIntoAlarmAreaList(lastType, startPosition, lastEndPosition);
      // positionArray.push(position);
    }
  }



  clearList() {
    this.dataList = [];
    this.setState({});
  }
  getDataListItem(index) {
    if (index < 0 || this.state.dataList == null || index >= this.state.dataList.length) {
      return;
    }
    let item = this.dataList[index];
    return item;
  }

  onLeftPressed() {

    if (this.props.isDisabled) {
      return;
    }
    this.isLeftPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = null;
    if (!this.isLeftLongPressed) {
      this.onMovePrev();
      return;
    } else {
      this.isLeftLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  onLeftPressIn() { // 同一触摸序列  只会被调用一次。
    this.endTimeout && clearTimeout(this.endTimeout);
    this.isLeftPressed = true;// 标记用户手指是否停在控件上
    //console.log("on left press in");
    if (this.props.isDisabled) {
      return;
    }
    this.isLeftLongPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = setTimeout(() => {
      //console.log("on long press left");
      this.handleLongPressLeft();
    }, 1500);
  }

  handleLongPressLeft() {
    if (this.props.isDisabled||!this.isLeftPressed) {
      if(this.props.touchMovement){
        this.onLeftPressed()
      }
      return;
    }
    this.isLeftLongPressed = true;
    this.onFastMovePrev();
    this.longLeftPressTimeout = setTimeout(() => {
      //console.log("on long press left");
      this.handleLongPressLeft();
    }, 50);
  }

  onLeftPressOut() {
    if (this.props.isDisabled) {
      return;
    }
    this.isLeftPressed = false;
    clearTimeout(this.longLeftPressTimeout);
    this.longLeftPressTimeout = null;
    if (!this.isLeftLongPressed) {
      this.onMovePrev();
      return;
    } else {
      this.isLeftLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  // touchableopcity组件调用顺序： onpressin onpressout onpress  如果是划出去的  onpress就不调用了
  onRightPressed() {

    if (this.props.isDisabled) {
      return;
    }
    this.isRightPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = null;
    if (!this.isRightLongPressed) {
      this.onMoveNext();
      return;
    } else {
      this.isRightLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      // this.scrollToTimestampWithNotify(this.state.currentTime, true);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  onRightPressIn() {
    this.endTimeout && clearTimeout(this.endTimeout);
    this.isRightPressed = true;
    if (this.props.isDisabled) {
      return;
    }
    this.isRightLongPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = setTimeout(() => {
      //console.log("on long press right");
      this.handleLongPressRight();
    }, 1500);
  }

  onRightPressOut() {
    if (this.props.isDisabled) {
      return;
    }
    this.isRightPressed = false;
    clearTimeout(this.longRightPressTimeout);
    this.longRightPressTimeout = null;
    if (!this.isRightLongPressed) {
      this.onMoveNext();
      return;
    } else {
      this.isRightLongPressed = false;
      // this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      // this.scrollToTimestampWithNotify(this.state.currentTime, true);
      this._onTimeScaleScrolled(this.state.currentTime);
    }
  }
  handleLongPressRight() {
    if (this.props.isDisabled || !this.isRightPressed) {
      if (this.props.touchMovement) {
        this.onRightPressed();
      }
      return;
    }
    this.isRightLongPressed = true;
    this.onFastMoveNext();
    this.longRightPressTimeout = setTimeout(() => {
      this.handleLongPressRight();
    }, 50);
  }
  onFastMoveNext() {
    if (this.dataList.length == 0) {
      return;
    }
    let moveTime = 30000;
    let selectTime = this.state.currentTime + moveTime;
    if (selectTime > this.dataList[this.dataList.length - 1].endTime) {
      return;
    }
    if (selectTime >= this.dataList[this.dataList.length - 1].startTime) {
      this.scrollToTimestampWithNotify(selectTime, false);
      return;
    }
    for (let i = this.dataList.length - 2; i >= 0; i--) {
      let timeItem = this.dataList[i];
      this.dateTime.setTime(timeItem.startTime);
      if (selectTime > this.dateTime.getTime()) {
        if (selectTime < timeItem.endTime) {
          this.scrollToTimestampWithNotify(selectTime, false);// 滑动 但是不通知
        } else {
          this.scrollToTimestampWithNotify(this.dataList[i + 1].startTime, false);// 滑动但是不通知
        }
        break;
      }
    }
  }
  onFastMovePrev() {
    if (this.dataList.length == 0)
      return;
    let moveTime = 30000;
    let selectTime = this.state.currentTime - moveTime;
    this.dateTime.setTime(this.dataList[0].startTime);
    if (selectTime < this.dateTime.getTime()) {
      return;
    }
    if (selectTime < this.dataList[0].endTime) {
      this.scrollToTimestampWithNotify(selectTime, false);
      return;
    }
    for (let i = 1, len = this.dataList.length; i < len; i++) {
      let timeItem = this.dataList[i];
      if (selectTime < timeItem.startTime) {
        if (selectTime < this.dataList[i - 1].endTime - moveTime) {
          this.scrollToTimestampWithNotify(selectTime, false);
        } else {
          this.scrollToTimestampWithNotify(this.dataList[i - 1].endTime - moveTime, false);
        }
        break;
      }
    }
  }

  onMoveNext() {
    if (this.dataList.length <= 0) {
      return;
    }
    let centerValue = this.state.currentTime;
    if (centerValue >= this.dataList[this.dataList.length - 1].endTime) { // 超过最后一个了 不让继续跑了
      this.onEndReach();
      return;
    }
    if (centerValue > this.dataList[this.dataList.length - 1].startTime) {
      this.onEndReach();
      return;
    }
    let timeItem = this.getNeedItemNew(centerValue, true);;
    if (timeItem != null) {
      this._onTimeScaleScrolled(timeItem.startTime);
      return;
    }
    this.onEndReach();
  }

  onMovePrev() {
    if (this.dataList == null) {
      return;
    }
    if (this.dataList.length <= 0) {
      return;
    }
    let currentValue = this.state.currentTime;
    let selectTime = currentValue;
    let time = this.dataList[0].startTime;
    if (selectTime < time) {
      return;// 不让继续往前跑了
    }
    if (selectTime < this.dataList[0].endTime) {
      this._onTimeScaleScrolled(this.dataList[0].startTime);
      return;
    }
    let timeItem = this.getNeedItemNew(selectTime, false);
    if (timeItem != null) {
      this._onTimeScaleScrolled(timeItem.startTime);
      return;
    }
    this._onTimeScaleScrolled(this.dataList[0].startTime);
    return;
  }

  _onTimeScaleScrolled(timestamp) {

    this.scrollState = SCROLLSTATE.SCROLLING;
    this.scrollToTimestampWithNotify(timestamp, false);// 滑动 但是不通知
    this.endTimeout && clearTimeout(this.endTimeout);
    this.endTimeout = setTimeout(() => {
      this.scrollState = SCROLLSTATE.IDLE;
      this.scrollToTimestampWithNotify(this.state.currentTime, true);
    }, 1200);
  }

  getNeedItemNew(time, next) {
    if (next) { // 找到下一个连续区域的起点。
      for (let i = 0, j = this.dataList.length - 1; i <= j; i++) {
        let curItem = this.dataList[i];
        let startTime = curItem.startTime;
        let eventType = curItem.eventType;
        if (startTime > time) {
          let shouldDraw = this._shouldDrawEvent(eventType);
          if (shouldDraw) {
            return curItem;
          }
        }
      }
      return null;
    } else { // 找到上一个连续区域的的起点。
      for (let i = this.dataList.length - 1, j = 0; i >= j; i--) {
        let curItem = this.dataList[i];
        let startTime = curItem.startTime;
        let eventType = curItem.eventType;
        if (startTime < time) {
          let shouldDraw = this._shouldDrawEvent(eventType);
          if (shouldDraw) {
            return curItem;
          }
        }
      }
      return null;
    }
  }

  _shouldDrawEvent(eventType) {
    console.log(eventType)
    let shouldDrawAbnormalSound =  !this.state.eventTypeUnSelect.includes(EVENT_TYPE_KEY['AbnormalSound']);
    let shouldDrawAreaMove = !this.state.eventTypeUnSelect.includes(EVENT_TYPE_KEY['ObjectMotion']);
    let shouldDrawPeopleMove = !this.state.eventTypeUnSelect.includes(EVENT_TYPE_KEY['PeopleMotion']);
    
    if (shouldDrawAreaMove) {
      return true;
    }
    if (shouldDrawPeopleMove) {
      return true;
    }
    if (shouldDrawAbnormalSound) {
      return true;
    }
    return false;
  }

  onEndReach() {
    if (this.dataList == null || this.dataList.length <= 0) {
      return;
    }
    this._onTimeScaleScrolled(this.dataList[this.dataList.length - 1].endTime);
  }

  scrollToTimestampWithNotify(timestamp, shouldNotify) {

    if (shouldNotify) { // todo 暂时只有通知的时候会刷新，其他的时候 例如左右长按 点按不启用刷新，看看后续效果
      this.setState(() => {
        return { currentTime: timestamp };
      }, () => {
        this.refreshTopDateView(this.state.currentTime);
        this.props.onScrollEnd && this.props.onScrollEnd(this.state.currentTime);
      });
    } else {
      // let time = this.state.currentTime - Math.ceil((this.pan.__getValue()) * this.oneUnitWidthTime)
      this.setState(() => { return { currentTime: timestamp }; }, () => {
        this.props.onScrolling && this.props.onScrolling(this.state.currentTime);
      });
    }
  }

  setScale(scale) {
    let diffScale = Number(((scale) / 100).toFixed(2));
    let tempScaleFactor = this.curScaleFactor + diffScale;
    if (tempScaleFactor > SCALING_FACTOR_MAX) {
      tempScaleFactor = SCALING_FACTOR_MAX;
    } else if (tempScaleFactor < SCALING_FACTOR_MIN) {
      tempScaleFactor = SCALING_FACTOR_MIN;
    }
    this.curScaleFactor = tempScaleFactor;
    this.itemInterval = this.initItemInterval * tempScaleFactor;
    this.oneUnitWidthTime = this.intervalTime / this.itemInterval; // 一个宽度占多少毫秒
    this.setState({});
  }

  fillArray = (arr) => {
    while (arr.length < 20) {
        arr.unshift('0');
    }
    return arr;
  }

  putIntoAlarmAreaList(type, startX, endX) {
    const eventLists1 = Number(type) && Number(type).toString(2) && Number(type).toString(2).split('');
    const eventLists = this.fillArray(eventLists1);
    if(type == 0) {
      this.defaultArea.push({ startX: startX, endX: endX });
    }
    if (eventLists[19] == '1') {
      this.motionArea.push({ startX: startX, endX: endX });
    }
    if (eventLists[18] == '1') {
      this.peopleArea.push({ startX: startX, endX: endX });
    }
    if (eventLists[0] == '1') {
      this.abnormalSoundArea.push({ startX: startX, endX: endX });
    }
  }

  initPanResponder() {

    let startX = 0;
    let lastScale = 0;
    let isMulti = false;
    let panResponderStartX = -1;
    let panResponderStartY = -1;


    this.panResponder = PanResponder.create({
      // 单机手势是否可以成为响应者
      onStartShouldSetPanResponder: (evt, gestureState) => {
        panResponderStartX = evt.nativeEvent.changedTouches[0].locationX;
        panResponderStartY = evt.nativeEvent.changedTouches[0].locationY;
        return false;
      },
      // 移动手势是否可以成为响应者
      onMoveShouldSetPanResponder: (evt, gestureState) => true,
      // 拦截子组件的单击手势传递,是否拦截
      onStartShouldSetPanResponderCapture: (evt, gestureState) => false,
      // 拦截子组件的移动手势传递,是否拦截
      onMoveShouldSetPanResponderCapture: (evt, gestureState) => {
        return true;
      },
      /** *************** 响应者事件回调处理 **************** */
      // 单击手势监听回调
      onPanResponderGrant: (e, gestureState) => {
        if (this.props.isDisabled) {
          return;
        }

        // //console.log(TAG, 'onPanResponderGrant==>' + '单击手势申请成功,开始处理手势');
        // //console.log(TAG, "onPanResponderGrant: " + e.nativeEvent.locationX + " " + e.nativeEvent.locationY
        // + " " + gestureState.dx + " " + gestureState.dy
        // + " " + gestureState.vx + " " + gestureState.vy);
        isMulti = false;
        startX = 0;
        lastScale = 0;
        // 一直拉一直拉中间停顿会调用此
        // this.scrollState = SCROLLSTATE.IDLE;
        // this.endTimeout && clearTimeout(this.endTimeout);
        this.pan.setValue(0);
      },
      // 移动手势监听回调
      onPanResponderMove: (e, gestureState) => {
        if (this.props.isDisabled) {
          return;
        }
        // console.log('onPanResponderMove==>' + '移动手势申请成功,开始处理手势');
        // //console.log(TAG, `onPanResponderMove: down length = ${e.nativeEvent.changedTouches.length}`);
        if (e.nativeEvent.changedTouches.length > 1) {
          this.scrollState = SCROLLSTATE.SCALING;

          isMulti = true;
          let value = Math.abs(e.nativeEvent.changedTouches[1].locationX - e.nativeEvent.changedTouches[0].locationX);
          if (lastScale != 0) {
            let scale = Number.parseInt(value - lastScale);
            this.setScale(scale);
          }
          lastScale = value;
        } else {
          if (!isMulti) {
            this.scrollState = SCROLLSTATE.SCROLLING;

            // console.log("横向偏移量", gestureState.dx);
            // this.state.currentTime = this.state.currentTime - Math.ceil((gestureState.dx - startX) * this.oneUnitWidthTime); //这里暂时不修改
            // this.setState({});
            // this.notifyChangeTime(true, gestureState.dx < 0);

            // 等效于 this.pan = gestureState.dx && notify
            // this.pan.setValue(startX);
            let time = this.state.currentTime - Math.ceil(gestureState.dx - startX) * this.oneUnitWidthTime;
            // let limitTime = new Date
            // 在云存 SD卡上 左划
            // 右滑
            time = time < this.cloudSdMaxTime ? (time > this.cloudSdMinTime ? time : this.cloudSdMinTime) : this.cloudSdMaxTime;
            this.setState(() => { return { currentTime: time }; }, () => {
              this.scrollToTimestampWithNotify(this.state.currentTime, false);
            });
            // let time = this.state.currentTime - Math.ceil(gestureState.dx - startX) * this.oneUnitWidthTime;
            // //console.log("time:",time);
            // this.setState(() => { return { currentTime: time }; }, () => {
            //   this.scrollToTimestampWithNotify(this.state.currentTime, false);
            // });
            startX = gestureState.dx;
          }
        }
      },
      // 手势动作结束回调
      onPanResponderEnd: (e, gestureState) => {
        if (this.props.isDisabled) {
          return;
        }
        // //console.log(TAG, 'onPanResponderEnd==>' + '手势操作完成了,用户离开');
        this.isScrolling = false;
        // if (!isMulti) {

        // this.pan.setValue(0);

        this.endTimeout && clearTimeout(this.endTimeout);
        this.endTimeout = setTimeout(() => {
          this.scrollState = SCROLLSTATE.IDLE;
          this.scrollToTimestampWithNotify(this.state.currentTime, true);
        }, 1200);
        // }
      },
      // 手势释放, 响应者释放回调
      onPanResponderRelease: (e) => {//

      },
      // 手势申请失败,未成为响应者的回调
      onResponderReject: (e) => {
        // 申请失败,其他组件未释放响应者
        // //console.log(TAG, 'onResponderReject==>' + '响应者申请失败');
      },
      // 当前手势被强制取消的回调
      onPanResponderTerminate: (e) => {//取消的时候也要处理一下。
        if (this.props.isDisabled) {
          return;
        }
        // //console.log(TAG, 'onPanResponderEnd==>' + '手势操作完成了,用户离开');
        this.isScrolling = false;
        // if (!isMulti) {

        // this.pan.setValue(0);

        this.endTimeout && clearTimeout(this.endTimeout);
        this.endTimeout = setTimeout(() => {
          this.scrollState = SCROLLSTATE.IDLE;
          this.scrollToTimestampWithNotify(this.state.currentTime, true);
        }, 1200);
        // 另一个组件已经成为了新的响应者，所以当前手势将被取消
        // //console.log(TAG, 'onPanResponderTerminate==>' + '由于某些原因(系统等)，所以当前手势将被取消');
      },
      onShouldBlockNativeResponder: (evt, gestureState) => {
        // 返回一个布尔值，决定当前组件是否应该阻止原生组件成为JS响应者
        // 默认返回true。目前暂时只支持android。
        return true;
      }
    });
  }

  formatTimeInMMSS(timestamp) {
    this.dateTime.setTime(timestamp);
    return `${this.dateTime.getDay()} ${this.dateTime.getHours()} ${this.dateTime.getMinutes()} ${this.dateTime.getSeconds()}`;
  }
}

let timeMargin = 8;

let timeWidth = (kWindowWidth - 30 - timeMargin * 8) / 7;
let timeWidth_2 = kWindowWidth / 7;
// let eventMarginLeft = (kWindowWidth-timeWidth*8-eventMargin*4)/2
export const styles = StyleSheet.create({
  container: {
    backgroundColor: "#ffffff",
    width: '100%',
    height: '100%',
    display: "flex",
    flexWrap: "nowrap"
  },
  // 每天的样式是这里
  dayItemSelected: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: timeWidth,
    width: timeWidth,
    marginHorizontal: timeMargin,
    borderRadius: timeWidth / 2,
    backgroundColor: "#f0f0f0"
  },

  dayItem: {
    display: "flex",
    alignItems: "center",
    justifyContent: "center",
    height: timeWidth,
    width: timeWidth,
    marginHorizontal: timeMargin
    // borderRadius: timeWidth/2


  },

  dayItemTextWeek: {
    color: "#7f7f7f", fontSize: kIsCN ? 11 : 9
  },

  dayItemTextDay: {
    color: "#000000", fontSize: kIsCN ? 16 : 14
  }
});
