import React, {Component} from 'react';
import {
  Dimensions,
  Image, ScrollView,
  Text, TouchableOpacity,
  View
} from 'react-native';
import NavigationBar from "../../../imi-rn-commonView/NavigationBar/NavigationBar";
import I18n, {stringsTo} from "../../../globalization/Localize";
import {imiAlarmEventCloudApi, LetDevice, LetIMIIotRequest,IMIGotoPage} from "../../../imilab-rn-sdk";
import AnimatedCircleProgress from "../../com.chuangmi.watch.wd11/src/views/AnimatedCircleProgress";
import {showToast} from "../../../imilab-design-ui";
import {LetIDeviceManager} from "../../../imilab-rn-sdk/native/iot-kit/IMIDeviceManager";
import BluetoothGateway from "./BluetoothGateway";
import {METHOD} from "../../../imilab-rn-sdk/native/iot-kit/IMIIotRequest";
import IMILogUtil from "../../../imilab-rn-sdk/native/local-kit/IMILogUtil";

const radius = (Dimensions.get('window').width) / 3;//

/**
 * 添加子设备状态显示界面
 */
export default class AddSubDeviceState extends Component {

  constructor(props, context) {
    super(props, context);
    this.subDevice = this.props.route.params.subDevice;

      this.state = {
          addState: 0,//上报添加状态给插件端 添加状态，0：没开始添加，1：设备端收到开始添加消息 2：搜索设备中 3：注册 4：登陆以及物模型通道 5：添加完成结束
          seconds: 0,//码表计时
          msg: "",//显示信息
          topBindingText:'',
          productImg:'',
          name:'',
      }

      this.homeId = "";
  }

  componentDidMount() {
    LetDevice.addDeviceEventChangeListener((data) => {
      let {iotId, identifier, value} = JSON.parse(data);
      if (iotId == LetDevice.deviceID && identifier === "onAddGatewaySubDeviceState") {
        this._parseStateValue(value)
      }
    });
    //this.subDevice.productKey  a1kWMNz9FH1  SUBSET_SELECT_GATEWAY_BIND  AP
    imiAlarmEventCloudApi.getProductBindInfo(this.subDevice.productKey,['SUBSET_SELECT_GATEWAY_BIND']).then(res=>{
      console.log('getProductBindInfo---',res);
      let data = res.SUBSET_SELECT_GATEWAY_BIND[0];
        console.log('getProductBindInfo---',data.pageLayout);
        let titleData = data.pageLayout[0];
        let bindData = data.pageLayout[1];
        let imageData = data.pageLayout[2];
        this.setState({topBindingText:bindData.content,productImg:imageData.content,name:titleData.content});

    }).catch(error=>{
        console.log('getProductBindInfo---',JSON.stringify(error));
    });
    //开始计时
    this.startTimeRun();

    this._requestHomeId();
  }

  //解析添加状态
  _parseStateValue(value) {
    console.log("szm _parseStateValue ============== 设备添加状态:", value)
    let msg = "";

    switch (value.state) {
      case 1:
        msg = stringsTo("door_getWay_scan");
        break;
      case 2:
        if (value.data == "-1") {
          value.state = -1;
          msg = stringsTo("door_getWay_scan_timeOut");
          clearInterval(this.intervalID);
        } else {
          msg = stringsTo("door_getWay_scan_device") + value.data;
        }
        break;
      case 3:
        msg = stringsTo("door_getWay_add_device");
        break;
      case 4:
        msg = stringsTo("door_getWay_login_device");
        break;
      case 5:
        this._bindSubDeviceWithUser(JSON.parse(value.data));
        clearInterval(this.intervalID);
        break;
    }

    this.setState({addState: value.state, msg: msg})
  }

  startTimeRun() {
    this.state.seconds = 0;
    this.state.addState = 0;
    clearInterval(this.intervalID);
    this.intervalID = setInterval(() => {
      let s = this.state.seconds + 1;
      if (s <= 60) {
        this.setState({seconds: this.state.seconds + 1})
      } else {
        clearInterval(this.intervalID);
        this.setState({addState: -1})
      }
    }, 1000)
  }

  componentWillUnmount() {
    clearInterval(this.intervalID);
    LetDevice.removeDeviceEventChangeListener();
  }

  _onPressBack = () => {
      if (this.props.route.params.isInRN){
          this.props.navigation.pop();
      } else {
          IMIGotoPage.exit();
      }
  };

  render() {
    return (
      <View style={{flex: 1, backgroundColor: "#FFFFFF", alignItems: 'center'}}>
        <NavigationBar type={NavigationBar.TYPE.LIGHT} backgroundColor={"#FFFFFF"}
                       title={this.state.name}
                       left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                       right={[]}/>

        <View style={{width: "100%", height: 1, backgroundColor: "#CCCCCC"}}/>

        <Text style={{
          width: "80%",
          minHeight: 60,
          fontSize: 14,
          color: "#7F7F7F",
          marginTop: 24
        }}>{this.state.topBindingText}</Text>

        {this.state.productImg === '' ? null : <Image style={{width: "100%", height: 220, marginTop: 20}}
               source={{uri: this.state.productImg, cache: 'force-cache'}}
               resizeMode="cover"/>}

        {this._renderTimeProgress()}
        {this._renderMsgView()}
        {this._renderButton()}
      </View>
    );
  }

  /**
   * 渲染时间进度
   * @private
   */
  _renderTimeProgress() {
    if (this.state.addState === -1) {
      return null;
    }
    return (
      <View style={{marginTop: 36}}>
        <AnimatedCircleProgress totalNum={60} progress={this.state.seconds} progressBaseColor={"#EBEFF5"}
                                progressColor={"#1E5BA9"}
                                progressWidth={5}
                                baseProgressWidth={5}
                                durTime={0}>
          <View style={{flex: 1, alignItems: 'center', justifyContent: 'center'}}>
            <Text style={{fontSize: 34, color: "#1E5BA9"}}>{this.state.seconds + "s"}</Text>
          </View>
        </AnimatedCircleProgress>
      </View>
    )
  }

  _renderMsgView() {
    let msg = this.state.msg;
    return (
        <View style={{width: "100%", height: 40, marginTop: 36, alignItems: 'center', justifyContent: "center"}}>
          <Text style={{
            fontSize: 18,
            color: msg.includes(stringsTo("door_getWay_scan_timeOut")) ? "#e24c4c" : "#1E5BA9"
          }}>
            {msg}</Text>
        </View>
    )
  }

  _renderButton() {
    let text = "";
    let click = null;
    if (this.state.addState === -1) {
      text = stringsTo("door_connect_retry");
      click = () => this._reStartAdd();
    } else if (this.state.addState === 6) {
      text = stringsTo("door_getWay_add");
      //this.props.navigation.navigate("GatewayChildrenList")  BluetoothGateway
      click = () => this.props.navigation.navigate("BluetoothGateway");
    } else {
      return null;
    }
    return (
      <TouchableOpacity
        style={{position: 'absolute', bottom: 20, height: 48, width: "80%", marginTop: 30, alignItems: 'center'}}
        onPress={click}
      >
        <View style={{
          flex: 1,
          width: '100%',
          borderRadius: 7,
          backgroundColor: '#1E5BA9',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <Text style={{fontSize: 14, color: '#FFFFFF', fontWeight: 'bold'}}>{text}</Text>
        </View>
      </TouchableOpacity>
    )
  }

  _reStartAdd(){
    LetDevice.sendDeviceServerRequest("requestAddChildrenDevices", {
      "pk": this.subDevice.productKey,
      "dsp": this.subDevice.dsp,
      "pid": this.subDevice.pid == null ? "" : this.subDevice.pid,
      "did": this.subDevice.did == null ? "" : this.subDevice.did,
      "devicetype": this.subDevice.targetType,
      "epnumber": parseInt(this.subDevice.targetEpNumber),
      "zoneid": this.subDevice.targetZoneId == null ? "" : this.subDevice.targetZoneId,
    }).then(result => {
      console.log("requestAddChildrenDevices result:", result);
      this.startTimeRun();
    }).catch(err => {
      console.log("requestAddChildrenDevices err=" + err);
      showToast(I18n.t("connect_err") + err);
    });
  }

  _requestHomeId(){
    const params = {
      Path: "api/app/home/<USER>/no-init",
      Method: METHOD.GET,
      ParamMap: {}
    };
    console.log("_requestHomeId params -> " + JSON.stringify(params));

    //sendUserServerRequest:请求创米服务器  sendIotServerRequest:请求阿里云服务器
    LetIMIIotRequest.sendUserServerRequest(params).then(result => {

      console.log("=======_requestHomeId result:", result);

      let data = JSON.parse(result);

      this.homeId = data.homeId;

      console.log("=======_requestHomeId homeId:", this.homeId);

    }).catch((error) => {
      console.log("=======_requestHomeId error:", error);
    });

  }

  //将子设备和用户关系绑定
  _bindSubDeviceWithUser(data) {
    console.log("_bindSubDeviceWithUser params -> ", data);
    for (let i = 0; i < data.length; i++) {
      let devItem = data[i];
      const params = {
        Path: "/awss/time/window/user/bind",
        APIVersion: '1.0.8',
        ParamMap: {
          productKey: devItem.productKey,
          deviceName: devItem.deviceName,
          homeId: this.homeId,
        }
      };
      console.log("_bindSubDeviceWithUser params -> " + JSON.stringify(params));
      LetIMIIotRequest.sendIotServerRequest(params).then(result => {
        console.log("=======_bindSubDeviceWithUser result:", result);
        showToast(stringsTo('pairedSuccessTitle'));
        IMILogUtil.uploadClickEventValue({ZigBeeAddSuccessDevice:this.subDevice.deviceInfo.name})
        this.setState({addState: 6, msg: stringsTo("door_getWay_add_success")});
      }).catch((error) => {
        showToast(stringsTo('pairedFailTitle'));
        console.log('_bindSubDeviceWithUser error ' + error)
        // this.removeTopo()
        this.setState({msg: stringsTo("door_getWay_add_fail")});
      });
    }
  }

  //解除子设备和网关的拓扑关系
  removeTopo(subIotid) {
    const params = {
      Path: "/thing/topo/remove",
      APIVersion: '1.0.6',
      ParamMap: {
        iotId: LetDevice.deviceID,
        subIotid: subIotid,
      }
    };
    console.log("_deleteSubDev params -> " + JSON.stringify(params));
    LetIMIIotRequest.sendIotServerRequest(params).then(result => {
      console.log("=======_deleteSubDev result1:", result);
    }).catch((error) => {
      console.log('_deleteSubDev error1 ' + error)
    });

  }
}
