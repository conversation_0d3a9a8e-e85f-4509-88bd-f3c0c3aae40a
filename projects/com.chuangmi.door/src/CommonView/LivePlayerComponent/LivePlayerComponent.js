/**
 * HomePageLivePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 * @property {string}  pageBackgroundColor 直播播放器下方空白页面的颜色
 *
 * 示例:
 * <HomePageLivePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </HomePageLivePlayerComponent>
 *
 * @author: yanmin
 * @date: 2020/11/25
 */

import React, {Component} from 'react';
import {
    View, Text, BackHandler, ActivityIndicator, StyleSheet, AlertIOS, ScrollView,ImageBackground
} from 'react-native';
import {IMIGotoPage, IMIPackage, INTERCOM_MODE, IntercomModeKey, LetDevice} from "../../../../../imilab-rn-sdk";
import IMICameraVideoView, {
    CAMERA_PLAYER_MODE,
    VOICE_TYPE
} from "../../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIFile from "../../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import I18n, {stringsTo} from "../../../../../globalization/Localize";
import LivePlayerToolBarView from "../../../../../imi-rn-commonView/PlayerToolBarView/LivePlayerToolBarView";
import NavigationBar from "../../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';
import TouchableOpacityText from "../../../../../imi-rn-commonView/TouchableOpacityText/TouchableOpacityText";
import ModalView from "../../../../../imi-rn-commonView/ModalView/ModalView";

import PropTypes from 'prop-types';
import Utils, {isAndroid, isIos,getScreenWidth} from "../../../../../imilab-rn-sdk/utils/Utils";
import IMIToast from "../../../../../imilab-design-ui/src/widgets/IMIToast";
import TouchableWithoutFeedbackView from "../../../../../imi-rn-commonView/TouchableWithoutFeedbackView/TouchableWithoutFeedbackView";
import LivePlayerFullScreenToolBarView from "../../../../../imi-rn-commonView/PlayerToolBarView/LivePlayerFullScreenToolBarView";
import {XText} from "react-native-easy-app";
import {ChoiceItem, colors, RoundedButtonView, showLoading} from "../../../../../imilab-design-ui";
import ImageButton from "../../../../../imi-rn-commonView/ImageButton/ImageButton";
import {PLAYER_EVENT_CODE} from "../../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import IMIPermission from "../../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {showToast} from "../../../../../imilab-design-ui/src/widgets/Loading";
import Toast from "react-native-root-toast";
import * as Typography from "../../../../../imilab-design-ui/src/style/Typography";
import PlayerToolBarView from "../../../../../imi-rn-commonView/PlayerToolBarView/PlayerToolBarView";
import TouchableOpacityImage from "../../../../../imi-rn-commonView/TouchableOpacityImage/TouchableOpacityImage";
import {IMINativeLifeCycleEvent} from "../../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import {RRCAlert} from "imilab-design-ui/src/widgets/overlayer";
import DoorCardButton from "../DoorCardButton/DoorCardButton";
import SettingItemView from "../SettingItemView/SettingItemView";
import DoorBleManager from "../../utils/DoorBleManager";
import DoorStorageUtil from "../../utils/DoorStorageUtil";
const LIVE_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading' ,
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};
Object.freeze(LIVE_PLAYER_STATUS);

const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});
const fillZero = n => parseInt(n)>9?n:"0"+n;

const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
let lastClickSnapPhoto = 0; //上一次点击截图的时间
let saveVedioPath = VEDIO_RECORD_PATH;
//处理权限问题
let isCheckingPermission = false;
let audioNeedRecovery = false; //通话结束是否需要恢复监听状态


export default class LivePlayerComponent extends Component {
    static LIVE_PLAYER_STATUS = LIVE_PLAYER_STATUS;

    constructor(props, context) {
        super(props, context);
        this.state = {
            // qualityData: [ stringsTo("quality_sd"), stringsTo("quality_fhd"),stringsTo("quality_2k"),],
            qualityIndex: 1,//0,//隐藏“流畅”
            qualityVisible: false,
            bps: -1,
            isFullScreen: false,
            mute: true,
            recording: false,
            recordDuration: 0,
            showFullScreenTools: false,
            showScreenTools:false,

            isLoading: false,
            isPlaying: false,
            showErrorView: false,
            showPauseView: true,
            errorCode: null,
            isCalling:false,

            snapshotVisible: false,
            screenShotPath: null,
            isWaring:false,
            hideSoundView:false,
            soundSelectValue:[true,false,false,false],
            isAL:LetDevice.isAL,
        }
        //进入后台
        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            //如果是权限则不暂停
            if (isCheckingPermission)return;
            //如果处于播放状态退到后台暂停
            //关闭通话
            this._stopCall();
            //关闭录像 安卓在stop时会调用，无需调用俩次
            if(isAndroid()){
                this._stopRecord();
            }
            //关闭实时流
            this.IMIVideoView.stop();
        });
        //回到前台
        this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
            //如果是权限则不恢复
            if (isCheckingPermission)return;
            //启动
            // this.IMIVideoView.prepare();
            if (!this.state.isPlaying){
                this.setState({showPauseView:true});
            }
            console.log(`addListener livePlayerComponent enterForegroundListener : `);
        });

        this._onPauseListener = IMINativeLifeCycleEvent.onPauseListener.addListener(()=>{
            //如果处于播放状态退到后台暂停
            //关闭通话
            this._stopCall();
            //关闭录像 安卓在stop时会调用，无需调用俩次
            if(isAndroid()){
                this._stopRecord();
            }
            //关闭实时流
            this.IMIVideoView.stop();
        });

    }

    static propTypes = {
        navBar: PropTypes.func,
        videoRef: PropTypes.func,
        navBarRight: PropTypes.array,

        toolBarMoreItems: PropTypes.array,
        videoSubView: PropTypes.func,

        coverView: PropTypes.func,
        loadingView: PropTypes.func,
        pauseView: PropTypes.func,
        errorView: PropTypes.func,
        moreView: PropTypes.func,

        onLivePlayerStatusChange: PropTypes.func,

        fullScreenToolBarMoreItems: PropTypes.array,
        lensCorrect: PropTypes.shape({
            use: PropTypes.bool.isRequired,
            x: PropTypes.number,
            y: PropTypes.number,
            r: PropTypes.number
        }),
        lensCorrect2: PropTypes.shape({
            use: PropTypes.bool.isRequired,
            x: PropTypes.number,
            y: PropTypes.number,
            r: PropTypes.number
        }),
        onVideoClick: PropTypes.func,
        pageBackgroundColor: PropTypes.string,
        qualityData:PropTypes.array,//清晰度数据
        bindCatEye: PropTypes.func,//猫眼未绑定处理
        goBack: PropTypes.func,//清空数据
    };

    static defaultProps = {
        navBar: undefined,
        navBarRight: [],
        toolBarMoreItems: [],
        fullScreenToolBarMoreItems: [],
        lensCorrect: {use: false, x: 0, y: 0},
        qualityData:[{title:stringsTo("quality_low"),index:0},{title:stringsTo("quality_sd"),index:1},{title:stringsTo("quality_fhd"),index:2}],
    };


    UNSAFE_componentWillMount() {

        Orientation.lockToPortrait();
        Orientation.addOrientationListener(this._orientationDidChange);
        LetDevice.getPropertyCloud('StreamVideoQuality').then(data => {
            let  index = data;
            for (let m = 0;m < this.props.qualityData.length;m++){
                let qualityData = this.props.qualityData[m]
                if (qualityData.index == data){
                    index = m;
                }
            }
            this.setState({qualityIndex: index});
        }).catch(error => {
            console.log('StreamVideoQuality---',JSON.stringify(Utils.parseError(error)));
        });
        if(isAndroid()) {
            this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onPressBack());
        }

    }

    componentDidMount() {
        // setTimeout(() => this.IMIVideoView.start(), 2000)
    }

    componentWillUnmount() {
        Orientation.removeOrientationListener(this._orientationDidChange);
        this.IMIVideoView && this.IMIVideoView.stop();
        this.IMIVideoView && this.IMIVideoView.destroy();
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.screenTooltsTimer && clearTimeout(this.screenTooltsTimer);
        this.backHandler && this.backHandler.remove();
        this._enterBackground && this._enterBackground.remove();
        this._enterForeground && this._enterForeground.remove();
        this._onPauseListener && this._onPauseListener.remove();
    }


    _orientationDidChange = (orientation) => {
        if (orientation === 'LANDSCAPE') {
            if (!this.state.isFullScreen&&this.state.isPlaying) {
                //不先全屏和不加延时，直接走点击全屏按钮逻辑无法正确切换全屏
                Orientation.lockToPortrait();
                this.delayFullScreen = setTimeout(()=>{
                    this._onPressFullScreen();
                    this.delayFullScreen && clearTimeout(this.delayFullScreen);
                },50);
            }
        } else {
            this.state.isFullScreen && this._exitFullScreen();
        }
    }


    _onPressFullScreen = () => {
        isAndroid()?Orientation.lockToLandscape():Orientation.lockToLandscapeRight();
        this.setState({isFullScreen: true});
        NavigationBar.setStatusBarHidden(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
        console.log('onVideoViewClick---------_onPressFullScreen----------',this.state.isPlaying);

    };

    _exitFullScreen = () => {
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
    }

    _onPressBack = () => {
        if(isAndroid()) {
            if (this.state.isFullScreen) {
                this._exitFullScreen();
                return true;
            }
            //退出插件，页面销毁时断开
            // DoorBleManager.getInstance().disconnect();
            return false;
        }

    };
    _goBack = () => {
        if (this.state.isFullScreen) {
            this._exitFullScreen();
            return true;
        }else{
            // DoorBleManager.getInstance().disconnect();
            this.props.goBack?this.props.goBack(): IMIGotoPage.exit();
        }
    }

    _onPressFullScreenTools = () => {

        this.setState({showFullScreenTools: true});
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.fullScreenTooltsTimer = setTimeout(() => {
            this._onCloseFullScreenTools();
        }, 5000);
    }

    _onCloseFullScreenTools = () => {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.setState({showFullScreenTools: false});
    }
    _onPressScreenTools = () => {
        this.setState({showScreenTools: true});
        this.screenTooltsTimer && clearTimeout(this.screenTooltsTimer);
        this.screenTooltsTimer = setTimeout(() => {
            this._onCloseScreenTools();
        }, 5000);
    }
    _onCloseScreenTools = () => {
        this.screenTooltsTimer && clearTimeout(this.screenTooltsTimer);
        this.setState({showScreenTools: false});
    }

    _onPressMute = () => {
        if(!this._canStepIn())  return;
        this.setState({mute: !this.state.mute})
    };

    getMute() {
        return this.state.mute;
    }

    setMute(mute) {
        this.setState({mute: mute});
    }

    //设置是否显示暂停按钮
    setPauseView(showPauseView){
        this.setState({showPauseView: showPauseView});
    }
//同步清晰度
    setQuality(quality) {
        let  index = parseInt(quality);
        for (let m = 0;m < this.props.qualityData.length;m++){
            let qualityData = this.props.qualityData[m]
            if (qualityData.index == parseInt(quality)){
                index = m;
            }
        }
        this.setState({qualityIndex: index});
    }
    /***
     * 通话
     * @private
     */
    _onPressCall = () => {
        isCheckingPermission = true;
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_RECORD_AUDIO, (status) => {
            if (status === 0) {
                isCheckingPermission = false;
                if(!this.state.isCalling){
                    this._startCall();
                }else{
                    this._stopCall();
                }
            } else if (status === -1) {
                isCheckingPermission = false;
                showToast(stringsTo('audio_permission_denied'))
            }
        })

    };
    _startCall= () => {
        if(!this._canStepIn())  return;
        if(this.state.isCalling){
            return;
        }else {
            audioNeedRecovery = this.state.mute;
            if (this.state.mute){
                this.setMute(false)
            }

            this.IMIVideoView.startSpeak();
            this.setState({isCalling: true,soundSelectValue:[true,false,false,false]})
        }
    }
    _stopCall = () => {
        if(this.state.isCalling) {
            this.IMIVideoView.stopSpeak();
            audioNeedRecovery&&this.setMute(true);
            this.setState({isCalling: false})
        }
    }

    //判断当前是否可以操作
    _canStepIn(){
        if (!LetDevice.isOnline){
            console.log("设备离线，不可操作");
            showToast(stringsTo('device_offline'));
            return false;
        }
        if(!this.state.isPlaying){
            console.log("直播流未开始，不可操作");
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }

    //点击截屏按钮
    _onPressScreenShot = () => {

        if(!this._canStepIn())  return;
        if(new Date().getTime()-lastClickSnapPhoto<1000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
            return;
        }
        if(this.state.snapshotVisible){
            this.setState({ snapshotVisible: false});
        }

        lastClickSnapPhoto = new Date().getTime();
        isCheckingPermission = true;
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
            if (status === 0) {
                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                    isCheckingPermission = false;
                    if (status2 === 0) {
                        // if(!this._canStepIn())  return;
                        let currentSnapshotPath = `${IMIFile.storageBasePath}/tmp/${new Date().getTime()}snapshot.jpg`;
                        if(isIos()) {
                            currentSnapshotPath = `${IMIFile.storageBasePath}/tmp/IMG_${new Date().getTime()}.jpg`;
                        }
                        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);

                        this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                            console.log("排查页面卡死问题------截图成功");
                            IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, LetDevice.deviceID).then(_ => {
                                this.setState({screenShotPath:currentSnapshotPath,snapshotVisible: true});
                                // IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
                                console.log("排查页面卡死问题------截图成功-----保存截图成功",new Date());
                                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                                    console.log("3秒后截屏缩略图自动隐藏------截图成功-----3秒后截屏缩略图自动隐藏",new Date());
                                    this.setState({ screenShotPath: null,snapshotVisible: false});
                                }, 3000);
                            });
                        });
                    } else if (status2 === -1) {
                        showToast(stringsTo('storage_permission_denied'));
                    }
                });
            } else if (status === -1) {
                isCheckingPermission = false;
                showToast(stringsTo('storage_permission_denied'))
            }
        })

    };
    //点击录屏按钮
    _onPressRecord = () => {
        if(!this._canStepIn())  return;
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        } else {
            isCheckingPermission = true;
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
                if (status === 0) {
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                        isCheckingPermission = false;
                        if (status2 === 0) {
                            this._startRecording();
                        } else if (status2 === -1) {
                            showToast(stringsTo('storage_permission_denied'));
                        }
                    })
                } else if (status === -1) {
                    isCheckingPermission = false;
                    showToast(stringsTo('storage_permission_denied'))
                }
            })
        }
    };

    _startRecording(){
        if(this.state.recording){
            return;
        }else {
            saveVedioPath =VEDIO_RECORD_PATH;
            if(isIos()) {
                saveVedioPath = `${IMIFile.storageBasePath}/tmp/VID_${new Date().getTime()}.mp4`;
            }
            this.IMIVideoView.startRecord(saveVedioPath).then(_ => {
                this.setState({recording: true, recordDuration: 0});
            });
        }
    }
    //停止录像并保存在相册
    _stopRecord(forSave=true){
        console.log("停止录制anle",this.state.recordDuration);
        if(this.state.recording) {
            if (this.state.recordDuration < 6) {
                IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                forSave = false;
            }
            this.IMIVideoView.stopRecord().then(_ => { //停止录制
                console.log("停止录制-------");
                if (!forSave) { //只停止，不保存
                    console.log("停止录制-------保存失败");//save_system_album_failed
                    // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                    return;
                }
                IMIFile.saveVideoToPhotosAlbum(saveVedioPath, LetDevice.deviceID).then(_ => { //转存视频
                    this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                    let tempShotPath = `${IMIFile.storageBasePath}/tmp/snapshot.jpg`;
                    this.IMIVideoView.screenShot(tempShotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                        this.setState({screenShotPath: tempShotPath, snapshotVisible: true});
                        this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                            this.setState({screenShotPath: null, snapshotVisible: false});
                        }, 3000);
                    });
                    // IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
                });
            }).catch(err=>{
            });
            this.setState({recording: false, recordDuration: 0});
        }
    }

    _showStartWarningDialog = ()=>{
        if (LetDevice.isAL){
            RRCAlert.alert(stringsTo('bell_alarm_warn'),stringsTo('bell_alarm_warn_hint'), [
                {
                    text: stringsTo('cancel'),
                    style:{color:'#B2B2B2'}
                },
                {
                    text: stringsTo('ok_button'),
                    style:{color:'#1E5BA9'}
                }
            ], (index) => {
                if (index != 0) {
                    this._onPressWarning()
                }
            });
        }else {
            RRCAlert.alert(stringsTo('prompt'), stringsTo('door_need_bind_camera'), [
                {
                    text: stringsTo('know_button'),
                    style:{color:'#1E5BA9'}
                }
            ], (index) => {
                if (index == 0) {
                    console.log("FaceManagerPage");
                }
            });
        }

    }
    _onPressWarning = () =>{

        this.warningTimer && clearTimeout(this.warningTimer);
        let param;
        let value = this.state.isWaring;
        param = {Warning: value? 0 : 1}
        let paramStr = JSON.stringify(param);
        console.log(`上传数据-paramStrObject----${paramStr}`);

        LetDevice.setPropertyCloud(paramStr).then(res => {
            this.setState({
                isWaring: !value
            });
            if(!value) {
                this.warningTimer = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                    this.setState({isWaring: false});
                }, 10000);
            }
        }).catch(err => {
        });
    }

    _onPressQuality(index,selectIndex) {
        if (this.state.qualityIndex === selectIndex) return;
        // IMIToast.showToast((index+'--'+selectIndex), IMIToast.TYPE.BOTTOM);
        LetDevice.setPropertyCloud(JSON.stringify({"StreamVideoQuality": index})).then(data => {
            this.setState({qualityIndex: selectIndex});
        }).catch(error => {
            console.log(JSON.stringify(error))
        });
    }

    _onEventChange = (event) => {
        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            console.log("直播流----_onEventChange,回调网速值");
            this.setState({bps: event.extra.bps})

        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            console.log("直播流----_onEventChange,开始启用");
            this.setState({isLoading: true, isPlaying: false, showPauseView: false, showErrorView: false});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.LOADING);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            console.log("直播流----_onEventChange,出现关键帧");
            this.setState({isLoading: false, isPlaying: true});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PLAYING);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
            console.log("直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            this.setState({isLoading: false, isPlaying: false, showPauseView: this.state.isPlaying});
            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PAUSE);

            this._stopCall();
            if(this.state.recording){ //直播流暂停时，停止录像 //Android因为跳转其他页面、进入后台导致直播流暂停时，因为setState的时延isPlaying此时为true，需要手动停止录像
                if(isAndroid()&&this.state.isPlaying){
                    this._stopRecord();
                }else{ //录像停止IOS会自动停止视频录制，所以直接转存即可。Android因为直播流异常也只需直接转存视频即可
                    this._saveVideoToPhotosAlbum();
                }
            }

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            console.log("直播流----_onEventChange,开始播放");
            //this.setState({isLoading:false});
        }
    };

    _onRecordTimeChange = (event) => {
        this.setState({recordDuration: event.extra})
    }
//IOS在视频流暂停时,将录制的视频保存到相册
    _saveVideoToPhotosAlbum() {
        console.log("排查页面卡死问题------录屏因为实时流停止------",this.state.recordDuration,this.state.isPlaying);
        if (this.state.recordDuration < 6&&this.state.recordDuration>0) { //Android因为直播流停止recordDuration会直接边为0
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            this.setState({recording: false, recordDuration: 0});
            return;
        }

        IMIFile.saveVideoToPhotosAlbum(saveVedioPath, LetDevice.deviceID).then(_ => { //转存视频
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            let tempShotPath = `${IMIFile.storageBasePath}/tmp/snapshot.jpg`;
            this.IMIVideoView.screenShot(tempShotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                this.setState({screenShotPath: tempShotPath, snapshotVisible: true});
                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                    this.setState({snapshotVisible: false});
                }, 3000);
            });
            IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        });
        this.setState({recording: false, recordDuration: 0});
    }
    /**
     * 竖屏状态视屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenVideoViewArea() {
        return (
            this.props.navBar ? this.props.navBar(this.state.bps, this.state.isFullScreen) : (
                <NavigationBar
                    type={NavigationBar.TYPE.LIGHT} backgroundColor={"transparent"}
                    title={LetDevice.devNickName}
                    subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
                    left={[{key: NavigationBar.ICON.BACK, onPress: this._goBack}]}
                    right={this.props.navBarRight ? this.props.navBarRight : []}/>)
        );
    }

    /**
     * 清晰度选择器
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenQualityPopView() {
        return (
            <ModalView visible={this.state.qualityVisible}
                       onClose={_ => this.setState({qualityVisible: false})}>
                <View style={{flex: 1, flexDirection: "column", justifyContent: "center", alignItems: "center"}}>
                    {
                        this.props.qualityData.map((item, index) => {
                            if(index==0){//隐藏“流畅”
                                return null;
                            }
                            let qualityData = this.props.qualityData[index];
                            return (
                                <TouchableOpacityText
                                    key={`qualityItem_${index}`}
                                    title={qualityData.title}
                                    style={{
                                        width: 120,
                                        height: 40,
                                        borderRadius: 20,
                                        backgroundColor: index === this.state.qualityIndex ? "#496EE0" : "rgba(255,255,255,0.3)",
                                        justifyContent: "center",
                                        alignItems: "center",
                                        marginVertical: 10
                                    }}
                                    textStyle={{color: 'rgba(255,255,255,0.9)', fontSize: 15, fontWeight: "bold"}}
                                    onPress={_ => {
                                        this.setState({qualityVisible: false});
                                        this._onPressQuality(qualityData.index,index);
                                    }}
                                />
                            );
                        })
                    }
                </View>
            </ModalView>
        );
    }

    /**
     * 全屏状态videoView区域填充UI
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenVideoViewArea() {
        let qualityData = this.props.qualityData[this.state.qualityIndex];
        let warningBtn = {//报警
            isText: false,
            data: [require("../../../res/new_ui/icon_land_warn.png"), require("../../../res/new_ui/icon_land_warning.png")],
            onPress:this.state.isWaring?this._onPressWarning:this._showStartWarningDialog,
            disabled: false,
            dataIndex: this.state.isWaring?1:0,
        }
        let moreItems = [{item: warningBtn, insertIndex: 4}]

        return (
            this.state.showFullScreenTools ? (
                <LivePlayerFullScreenToolBarView
                    // style={{position: "absolute"}}
                    exitPress={this._exitFullScreen}
                    qualityTitle={qualityData.title}
                    qualityPress={_ => this.setState({qualityVisible: true})}
                    qualityDisabled = {this.state.recording}
                    mutePress={this._onPressMute}
                    mute={this.state.mute}
                    screenshotPress={this._onPressScreenShot}
                    recordPress={this._onPressRecord}
                    recording={this.state.recording}
                    moreItems={moreItems}/>
            ) : null
        );
    }

    /**
     * 全屏状态videoView区域填充UI
     * @returns {Element}
     * @private
     */
    _renderScreenVideoViewArea() {
        let qualityData = this.props.qualityData[this.state.qualityIndex];
        let items = [{
            isText: false,
            data: '',
            onPress: null,
            disabled: true,
            dataIndex: 0,
        } ,{
            isText: true,
            data: [qualityData.title],
            onPress: _ => this.setState({qualityVisible: true}),
            disabled: this.state.recording?true:false,
            dataIndex: 0,
        } , {//报警
            isText: false,
            data: [require("../../../res/new_ui/icon_warn.png"), require("../../../res/new_ui/icon_warning.png")],
            onPress:this.state.isWaring?this._onPressWarning:this._showStartWarningDialog,
            disabled: false,
            dataIndex: this.state.isWaring?1:0,
        },{
            isText: false,
            data: '',
            onPress: null,
            disabled: true,
            dataIndex: 0,
        } ];

        return (
            this.state.showScreenTools ?
                <View
                    style={{
                        position: "absolute",
                        right: 0,
                        width: 84,
                        height: '100%', backgroundColor:'rgba(0, 0, 0, 0.3)'
                    }}
                >
                    {/*<ImageBackground*/}
                        {/*style={{*/}
                            {/*position: "absolute",*/}
                            {/*bottom: 0,*/}
                            {/*width: '100%',*/}
                            {/*height: 50, flexDirection: "row"*/}
                    {/*}}*/}
                        {/*source={require("./res/masking_bg.png")} >*/}
                    {


                        items.map((item, index) => {
                            return (
                                item.isText ? (
                                    <TouchableOpacityText key={`playerFullScreenToolBarItem${index}`}
                                                          title={item.data[item.dataIndex]}
                                                          style={{
                                                              flex: 0.5,
                                                              justifyContent: "center",
                                                              alignItems: "center",
                                                          }}
                                                          textStyle={{
                                                              color: item.disabled ? "#999999" : "#FFFFFF",
                                                              fontSize: 12,
                                                              paddingVertical: 4,
                                                              paddingHorizontal: 6,
                                                              alignItems: "center",
                                                              borderWidth: 0.5,
                                                              borderRadius: 5,
                                                              borderColor:"#FFF",
                                                          }}
                                                          disabled={item.disabled}
                                                          onPress={item.onPress}/>
                                ) : (
                                    <TouchableOpacityImage key={`playerFullScreenToolBarItem${index}`}
                                                           style={{
                                                               flex: 0.5,
                                                               justifyContent: "center",
                                                               alignItems: "center"
                                                           }}
                                                           imageStyle={{width: 30, height: 30}}
                                                           source={item.data[item.dataIndex]}
                                                           disabled={item.disabled}
                                                           onPress={item.onPress}/>
                                )
                            );
                        })

                    }
                    {/*</ImageBackground>*/}
                    </View>
                : null
        );
    }

    /**
     * 竖屏状态下半屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenPlayerToolBarArea() {
        let items = [{//通话
            isText: false,
            data: [require("../../../res/new_ui/icon_call.png"), require("../../../res/new_ui/icon_calling.png")],
            onPress: this._onPressCall,
            disabled: false,
            dataIndex: this.state.isCalling?1:0,
        }, {
            isText: false,
            data: [require("../../../res/new_ui/icon_mute.png"), require("../../../res/new_ui/icon_volume.png")],
            onPress: this._onPressMute,
            disabled: false,
            dataIndex: this.state.mute ? 0 : 1,
        },{//截图
            isText: false,
            data: [require("../../../res/new_ui/icon_screenshot.png")],
            onPress: this._onPressScreenShot,
            disabled: false,
            dataIndex: 0,
        }, {//录像
            isText: false,
            data: [require("../../../res/new_ui/icon_recording.png"), require("../../../res/new_ui/icon_record.png")],
            onPress: this._onPressRecord,
            disabled: false,
            dataIndex: this.state.recording ? 0 : 1,
        },{
            isText: false,
            data: [require("../../../res/new_ui/icon_fullscreen.png")],
            onPress: this._onPressFullScreen,
            disabled: false,
            dataIndex: 0,
        }];
    // , {//设置
    //         isText: false,
    //             data: [require("../../../res/new_ui/icon_setting.png")],
    //             onPress: this.props.moreView,
    //             disabled: false,
    //             dataIndex: 0,
    //     }
        return (
            <View style={{flex: 1, flexDirection: "column",marginHorizontal: 14}}>
                <PlayerToolBarView items={items} isShowLine={false}/>
                {this.props.children}
                {this.state.isCalling?this._renderChangeSoundView():null}
            </View>
            );
    }
    _renderRecordingView() {
        let duration = this.state.recordDuration > 0 ? `${fillZero(parseInt(this.state.recordDuration / 60))}:${fillZero(parseInt(this.state.recordDuration % 60))}` : "00:00";
        return (
            <View style={[{
                width: 64,
                height: 26,
                backgroundColor: 'rgba(0,0,0,0.6)',
                borderRadius: 4,
                flexDirection: 'row',
                justifyContent: "center",
                alignItems: "center",
                marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
            },Typography.textFix]}>
                <View style={{backgroundColor: "#E74D4D", opacity: 0.9, width: 6, height: 6, borderRadius: 3}}/>
                <Text style={{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}}>{duration}</Text>
            </View>
        );
    }

    _loadingView() {
        if (!this.state.isAL) return null;
        if (!this.state.isLoading) return;
        return (<View  pointerEvents="box-none"
            style={{
                position: "absolute",
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center"
            }}
        >
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.state.isAL) return null;
        if (this.state.showPauseView) return null;
        if (!this.state.showErrorView) return;
        return (
            <View  pointerEvents="box-none"
                style={{
                    position: "absolute",
                    width: "100%",
                    height: "100%",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center"
                }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
                />

                <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                   buttonStyle={{
                                       margin: 14,
                                       paddingHorizontal:15,
                                       height: 40
                                   }}
                                   onPress={() => {
                                       this.IMIVideoView.start();
                                   }}/>
            </View>
        );
    }

    _pauseView() {
        if (!this.state.isAL) return null;
        if (!this.state.showPauseView) return null;
        return (<View
            style={{
                position: "absolute",
                width: "100%",
                height: "100%",
                display: "flex",
                justifyContent: "center",
                alignItems: "center"
            }}
        >
            <ImageButton
                style={{width: 52, height: 52}}
                source={require("./res/icon_play.png")}
                highlightedSource={require("./res/icon_play_p.png")}
                onPress={() => {
                    this.setState({showPauseView:false});
                    this.IMIVideoView.prepare();
                }}
            />
        </View>);
    }

    _activationView() {
        if (this.state.isAL) return null;
        return (<View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center"
                       }}
        >
            <TouchableWithoutFeedbackView style={{width:121,height:40,justifyContent: "center",alignItems: "center",borderRadius:6.67,borderWidth:1,borderColor:'#FFFFFF'}} onPress={()=>{
                this.props.bindCatEye&&this.props.bindCatEye();
            }}>
                <XText style={{fontSize:14,textAlign:'center',color:'#FFFFFF'}} text={stringsTo('activation_camera')}/>
            </TouchableWithoutFeedbackView>
        </View>);
    }

    _renderSnapshotView() {
        if (!this.state.snapshotVisible) {
            return null;
        }
        return (
            <View pointerEvents="box-none" style={{backgroundColor: 'transparent',display: "flex", position: "absolute", bottom: 19, left: 14, width: 140, height: 80}}>
                <ImageButton
                    style={{width: "100%", height: "100%", borderWidth: 2, borderColor: 'white', borderRadius: 10}}
                    source={{uri: 'file://' + this.state.screenShotPath}}
                    onPress={(_) => { //TODO 跳转到相册预览？
                        if (this.state.recording) {
                            showToast(I18n.t('screen_recording'));
                            return;
                        }
                        if (this.state.calling) {
                            showToast(I18n.t('camera_calling'));
                            return;
                        }
                        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                        this.setState({snapshotVisible:false});
                        if(this.state.isFullScreen){ //横屏则退出全屏
                            Orientation.lockToPortrait();
                            this.props.navigation.setOptions({tabBarVisible: true});
                            this._onCloseFullScreenTools();
                            NavigationBar.setStatusBarHidden(false);

                            this.setState({isFullScreen: false},()=>{
                                // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                                if (IMIPackage.minApiLevel < 10007) {
                                    IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                                } else {
                                    this.props.navigation.push('CameraListPage');
                                }//rn相册
                            });
                            return;
                        }
                        // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                        if (IMIPackage.minApiLevel < 10007) {
                            IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                        } else {
                            this.props.navigation.push('CameraListPage');
                        }//rn相册
                    }}
                />
            </View>
        )
    }

    _renderChangeSoundView() {
        return (
            <View style={{position: "absolute", top: 50, flex: 1, width: "100%", flexDirection: "column", alignItems: "center", paddingBottom: 50}}>
                {
                    this.state.hideSoundView ?
                        <View style={{flex: 1, width: "100%", alignItems: "center"}}>
                            <ImageButton
                                style={{width: 80, height: 40, top: -13}}
                                source={require("./res/icon_show.png")}
                                highlightedSource={require("./res/icon_show.png")}
                                onPress={() => {
                                    this.setState({hideSoundView: false})

                                }}
                            />
                        </View>
                        :
                        <View style={{flex: 1, width: "100%", flexDirection: "column", alignItems: "center"}}>
                            <View style={{flex: 1, width: "100%", flexDirection: "column", alignItems: "center", backgroundColor: "#ffffff"}}>
                                <SettingItemView title={stringsTo("original_sound")}
                                                 titleStyle={{color:this.state.soundSelectValue[0]?'#1E5BA9': '#333333'}}
                                                 bottomLine={SettingItemView.LINE.NONE}
                                                 selectIcon={require("./res/icon_sound_select.png")}
                                                 unselectIcon={null}
                                                 type={SettingItemView.TYPE.SELECT}
                                                 switchValue={this.state.soundSelectValue[0]}
                                                 onPress={() => {
                                                     this.IMIVideoView.setVoiceChangeType(VOICE_TYPE.ORIGINAL)
                                                     this.setState({soundSelectValue: [true, false, false, false]})
                                                 }}
                                />
                                <SettingItemView title={stringsTo("joker_sound")}
                                                 titleStyle={{color:this.state.soundSelectValue[1]?'#1E5BA9': '#333333'}}
                                                 bottomLine={SettingItemView.LINE.NONE}
                                                 selectIcon={require("./res/icon_sound_select.png")}
                                                 unselectIcon={null}
                                                 type={SettingItemView.TYPE.SELECT}
                                                 switchValue={this.state.soundSelectValue[1]}
                                                 onPress={() => {
                                                     this.IMIVideoView.setVoiceChangeType(VOICE_TYPE.JOKER)
                                                     this.setState({soundSelectValue: [false, true, false, false]})
                                                 }}
                                />
                                <SettingItemView title={stringsTo("young_sound")}
                                                 titleStyle={{color:this.state.soundSelectValue[2]?'#1E5BA9': '#333333'}}
                                                 bottomLine={SettingItemView.LINE.NONE}
                                                 selectIcon={require("./res/icon_sound_select.png")}
                                                 unselectIcon={null}
                                                 type={SettingItemView.TYPE.SELECT}
                                                 switchValue={this.state.soundSelectValue[2]}
                                                 onPress={() => {
                                                     this.IMIVideoView.setVoiceChangeType(VOICE_TYPE.YOUNGER)
                                                     this.setState({soundSelectValue: [false, false, true, false]})
                                                 }}
                                />
                                <SettingItemView title={stringsTo("old_sound")}
                                                 titleStyle={{color:this.state.soundSelectValue[3]?'#1E5BA9': '#333333'}}
                                                 bottomLine={SettingItemView.LINE.NONE}
                                                 selectIcon={require("./res/icon_sound_select.png")}
                                                 unselectIcon={null}
                                                 type={SettingItemView.TYPE.SELECT}
                                                 switchValue={this.state.soundSelectValue[3]}
                                                 onPress={() => {
                                                     this.IMIVideoView.setVoiceChangeType(VOICE_TYPE.UNCLE)
                                                     this.setState({soundSelectValue: [false, false, false, true]})
                                                 }}
                                />
                            </View>
                            <View style={{flex: 1, width: "100%", alignItems: "center", backgroundColor: "#ffffff"}}>
                                <ImageButton
                                    style={{width: 20, height: 20}}
                                    source={require("./res/icon_hide.png")}
                                    highlightedSource={require("./res/icon_hide.png")}
                                    onPress={() => {
                                        this.setState({hideSoundView: true})
                                    }}
                                />
                            </View>
                            <View style={{flex: 1, width: "100%", height: 200, backgroundColor: "#00000000"}}>
                            </View>
                        </View>
                }
            </View>
        );
    }

    render() {
        return (
            <View pointerEvents="box-none" style={{flex: 1, backgroundColor: this.props.pageBackgroundColor?this.props.pageBackgroundColor:"white", flexDirection: "column"}}>
                {
                    this.state.isFullScreen ? null : this._renderPortraitScreenVideoViewArea()
                }
                <View pointerEvents="box-none" style={this.state.isFullScreen?styles.cameraViewFullStyles:styles.cameraViewStyles} >
                    <IMICameraVideoView
                        style={{flex: 1}}
                        ref={ref => {
                            this.IMIVideoView = ref;
                            this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                        }}
                        mute={this.state.mute}
                        playerClass={IMICameraVideoView.PlayerClass.LIVE}
                        dataSource={{
                            playerClass: IMICameraVideoView.PlayerClass.LIVE,
                            did: LetDevice.deviceID,
                            isSpecialCallChannel:false,
                        }}
                        optionMap={{[IntercomModeKey]: INTERCOM_MODE.SingleTalk}}
                        onPrepared={() => {
                            console.log("直播流-----onPrepared------");
                            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.PREPARED);
                            // this._onPressScreenTools();
                            //this.IMIVideoView.start();
                        }}
                        onEventChange={this._onEventChange}

                        onErrorChange={(event) => {
                            console.log("直播流-----onErrorChange------   event.code " + event.code, event);
                            // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示
                            this._stopCall();
                            do {
                                //判断如果是通话报错则此处进行判断是否为占线
                                /*if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                                    alert("量产5-12——直播流Error-------- 不是流意外关闭");
                                    continue
                                }*/
                                if(isIos()){
                                    if(event.code==12||event.code==15||event.code==16||event.code==19){ //IOS的通话异常
                                        // alert("量产5-12——直播流Error-------- IOS其他设备正在通话中，请稍后重试");
                                        event.code==12?showToast(stringsTo('call_busy_tips')):showToast(stringsTo("action_fail"));
                                        //通知外部调用者通话异常中断，需要更新通话相关的UI
                                        return;
                                    }else{ //不是通话异常，则是正常的实时流异常，走do-while之后的逻辑
                                        continue;
                                    }

                                }
                                //判断如果是通话报错则此处进行判断是否为占线
                                if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                                    continue
                                }
                                //判断是否为对讲模式
                                if (event.extra.arg1 !== CAMERA_PLAYER_MODE.MODE_SPEAK) {
                                    continue
                                }
                                //暂时阿里SDK无法返回指定错误码，此处暂时使用info 对比
                                if (event.extra.arg2 === "voice intercom existed") {
                                    showToast(stringsTo('call_busy_tips'));
                                } else {
                                    showToast(stringsTo('call_connect_error'));
                                }
                                return
                            } while (false);

                            this.setState({isPlaying:false, showErrorView: true, showPauseView: false, isCalling:false, errorCode: event.code,});
                            this.props.onLivePlayerStatusChange && this.props.onLivePlayerStatusChange(LIVE_PLAYER_STATUS.ERROR);
                        }}
                        onRecordTimeChange={this._onRecordTimeChange}
                        lensCorrect={this.state.qualityIndex==2?(this.props.lensCorrect2?this.props.lensCorrect2:this.props.lensCorrect):this.props.lensCorrect}
                        onVideoViewClick={()=>{
                            console.log('onVideoViewClick-------------------',this.state.isPlaying);
                            if(this.state.isFullScreen){
                                this._onCloseScreenTools()
                                if(this.state.showFullScreenTools){
                                    this._onCloseFullScreenTools()
                                }else{
                                    this._onPressFullScreenTools();
                                }
                            }else{
                                this._onCloseFullScreenTools();
                                if (this.state.isPlaying){//非全屏时，改为播放状态才显示功能按钮
                                    if(this.state.showScreenTools){
                                        this._onCloseScreenTools()
                                    }else{
                                        this._onPressScreenTools()
                                    }
                                }
                            }
                            this.props.onVideoClick && this.props.onVideoClick();
                        }}
                    />
                    {/*全屏?横屏UI:竖屏UI(navBar)*/}
                    <View pointerEvents="box-none" style={{position: "absolute", width: "100%", height: "100%", flexDirection: "column", alignItems: "center"}}>
                        {
                            this.props.videoSubView ? this.props.videoSubView(this.state.isFullScreen,this.state.showFullScreenTools) : null
                        }
                        {this._activationView()}

                        {
                            this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()
                        }
                        {
                            this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()
                        }
                        {
                            this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()
                        }
                        {
                            this._renderSnapshotView()
                        }
                        {
                            this.state.isFullScreen ? this._renderLandscapeScreenVideoViewArea() :this._renderScreenVideoViewArea()
                        }
                        {
                            this.state.recording ? this._renderRecordingView() : null

                        }

                    </View>
                </View>

                {/*全屏?null:渲染外层传入的UI*/}
                {this.state.isFullScreen ? null : this._renderPortraitScreenPlayerToolBarArea()}

                {/*渲染清晰度选择器，不占UI空间的*/}
                {this._renderLandscapeScreenQualityPopView()}
            </View>
        );
    }
}
let styles = StyleSheet.create({
    cameraViewStyles: {
        // flex:0.5,
        // marginHorizontal:14,
        marginTop:14,
        height:(getScreenWidth())*9/16,
        marginBottom:4
    },
    cameraViewFullStyles: {
        flex:1,
    }
});
