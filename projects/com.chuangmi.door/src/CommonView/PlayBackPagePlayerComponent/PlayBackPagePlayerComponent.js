/**
 * PlayBackPagePlayerComponent.js 首页直播模块组件
 *
 * @property {func} videoRef - 播放器videoView的ref
 * @property {func} navBar - 自定义导航条，
 * @property {NavigationBar.right} navBarRight-使用默认导航条，只需要传入右侧按钮
 * @property {LivePlayerToolBarView.moreItems} toolBarMoreItems 增加功能条的按钮
 * @property {LivePlayerToolBarView.moreItems} fullScreenToolBarMoreItems 横屏增加功能条的按钮
 * @property {func} videoSubView - 增加视频区域更多功能，
 * @property {JSON} lensCorrect 畸变校正{use: false,x: 0,y: 0}
 * @property {func} onVideoClick 视频区域点击事件
 *
 * 示例:
 * <PlayBackPagePlayerComponent
 *  {...this.props}
 *  videoRef={ref => this.IMIVideoView = ref}
 *  navBar={(bps, isFullScreen) => this._renderPortraitScreenVideoViewArea(bps, isFullScreen)}>
 *  navBarRight={[]}
 *  videoSubView={(isFullScreen)=><View style={{position: 'absolute', bottom: 0,width:"100%",height:50,backgroundColor:'red'}}/>}
 *  {this._renderDoorArea()}
 * </PlayBackPagePlayerComponent>
 *
 * @author: yanmin
 * @date: 2020/12/28
 */

import React, {Component} from 'react';
import {
    View, Text, BackHandler, Platform, ActivityIndicator, StatusBar
} from 'react-native';
import {DateUtils, IMIGotoPage, IMIVideoView, LetDevice,IMIPackage} from "../../../../../imilab-rn-sdk";
import IMICameraVideoView, {CAMERA_PLAYER_MODE} from "../../../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIFile from "../../../../../imilab-rn-sdk/native/local-kit/IMIFile";
import {stringsTo,locales} from "../../../../../globalization/Localize";
import NavigationBar from "../NavigationBar/NavigationBar";
import Orientation from 'react-native-orientation';

import PropTypes from 'prop-types';
import {isAndroid, isIos, isPhoneX} from "../../../../../imilab-rn-sdk/utils/Utils";
import IMIToast from "../../../../../imilab-design-ui/src/widgets/IMIToast";
import TouchableWithoutFeedbackView from "../TouchableWithoutFeedbackView/TouchableWithoutFeedbackView";
import PlayBackToolBarView from "../PlayerToolBarView/PlayBackToolBarView";
import TimeLineView from "../TimeLineView/TimeLineView";
import {Calendar} from "react-native-calendars";
import {colors, imiThemeManager, showLoading,RoundedButtonView,showToast} from "../../../../../imilab-design-ui";
import ImageButton from "../ImageButton/ImageButton";
import ModalView from "../ModalView/ModalView";
import I18n from "../../../../../globalization/Localize";
import {PLAYER_EVENT_CODE} from "../../../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import { XText,XView} from 'react-native-easy-app';
import moment from "moment";
import Toast from "react-native-root-toast";
import IMIPermission from "../../../../../imilab-rn-sdk/native/local-kit/IMIPermission";
import {CONST} from "../../../../../imilab-design-ui/src/style/ThemeManager";
import {IMINativeLifeCycleEvent} from "../../../../../imilab-rn-sdk/native/local-kit/IMINativeLifeCycle";
import TimeLineScrollView from "../TimeLineView/TimeLineScrollView";
import LivePlayerFullScreenToolBarView from "../PlayerToolBarView/LivePlayerFullScreenToolBarView";
import {timeFilter} from "../../../../../imilab-rn-sdk/utils/DateUtils";
import PlayBackFullScreenToolBarView from "../PlayerToolBarView/PlayBackFullScreenToolBarView";
import CalendarHeader from "react-native-calendars/src/calendar/header";
import IMILog from "../../../../../imilab-rn-sdk/native/local-kit/IMILog"
import DeviceTemplatesUtils from "../../../../../imilab-rn-sdk/core/device/DeviceTemplatesUtils";
import tr from "../../../../../globalization/string/tr";
const TMP_SNAPSHOT_PATH_PREFIX = `${IMIFile.storageBasePath}/tmp/`;
const VEDIO_RECORD_PATH = `${IMIFile.storageBasePath}/tmp/videorecord.mp4`;
const speedTitleAry = ["1X", "2X", "4X", "8X", "16X"];
const speedAccessibilityLabelTitleAry = ["play_back_clarity_show_1X","play_back_clarity_show_2X",
    "play_back_clarity_show_4X","play_back_clarity_show_/8X","play_back_clarity_show_16X"];
const appendZero = n => parseInt(n).toLocaleString({}, {minimumIntegerDigits: 2});

const VOD_PLAYER_STATUS = {
    PREPARED: 'prepared',//准备完毕，调用start即可播放
    LOADING: 'loading' ,
    PLAYING: 'playing',//正在播放
    PAUSE: "pause",
    ERROR: "error"
};

let nowDay = new Date();
//shenyonggang@20210330 fixed bug IMI_HMI510_A01-327
let backupIsPlay = false;
let currentPlayTime = 0;//记录断开前的时间
let playEndTime = 0;//记录播放完成的currentTime
let PauseLineTime=0;//暂停时line进度
let retryNum=0;//错误后，点击重试的提示
let time;
let automaticDay=null;//一天播放完后如果第二天有视频则自动跳转并切换
let lastClickSnapPhoto = 0; //一次进入切换天数的时间
let dateDataArr = new Map();
export default class PlayBackPagePlayerComponent extends Component {
    static VOD_PLAYER_STATUS = VOD_PLAYER_STATUS;

    constructor(props, context) {
        super(props, context);
        this.state = {
            bps: -1,
            isFullScreen: false,//是否全屏
            mute: true,
            recording: false,//录屏监听  true为录屏中
            recordDuration: 0, //录屏时长,小于6秒提示失败
            showFullScreenTools: false,

            isLoading: false,  //加载中
            showErrorView: false, //错误view
            showPauseView: false, //暂停view
            errorCode: null, //错误code

            dataArray: [],
            currentDate: new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 0, 0, 0),
            speed: 0,  //倍速
            snapshotVisible: false, //截图是否显示中
            screenShotPath: null,
            isPlay:false,  //是否播放中 true为播放中
            isShowCalendar:false,
            isClickPause:false,
            dateData: {},//日期显示
            isPlayFinish:false,//是否回看播放结束
            dateText:'',
            showNoData:false,//标记今天是否有回看视频
        }
    }

    static propTypes = {
        navBar: PropTypes.func,
        videoRef: PropTypes.func,
        navBarRight: PropTypes.array,

        toolBarMoreItems: PropTypes.array,
        videoSubView: PropTypes.func,
        fullScreenToolBarMoreItems: PropTypes.array,
        lensCorrect: PropTypes.shape({
            use: PropTypes.bool.isRequired,
            x: PropTypes.number,
            y: PropTypes.number
        }),
        onVideoClick: PropTypes.func,
        onVodPlayerStatusChange: PropTypes.func,

        loadingView: PropTypes.func,
        pauseView: PropTypes.func,
        errorView: PropTypes.func,

        isSleepStatus: PropTypes.bool,//休眠状态
        isOnLine: PropTypes.bool,//在线状态
        onCheckPermissionStatusChange:PropTypes.func, //回调是否处于权限检测状态中
    };

    static defaultProps = {
        navBar: undefined,
        navBarRight: [],
        toolBarMoreItems: [],
        fullScreenToolBarMoreItems: [],
        lensCorrect: {use: false, x: 0, y: 0},
        isSleepStatus:false,
        isOnLine:true,
    };


    UNSAFE_componentWillMount() {
        Orientation.lockToPortrait();
        Orientation.addOrientationListener(this._orientationDidChange);

    }
    //失败后重新加载一次
    _queryDayTryAgain(isTry){
        this._queryDayData(this.state.currentDate).then(dataArr => {
            console.log("szm 回看 查询数据：", dataArr);
            this.setState({dataArray: dataArr});
            this.preparetimer && clearTimeout(this.preparetimer);
            if (dataArr.length > 0) {//数据为空不加载
                //20220323@byh 以前进入页面就会初始化播放器，以前这个地方可能会出现2次请求数据
                //现在进入页面，不直接初始化，根据当天是否有数据，去初始化当天数据
                this.setState({isLoading:true},callback=>{
                    showLoading(false);
                });
                // console.log('loading---显示---1');
                this.preparetimer = setTimeout(() => {
                    if (!this.state.isPlay){  //沟通固件,会造成两次请求,所以修改延时三秒,不知是否影响其他项目
                        this.IMIVideoView && this.IMIVideoView.prepare();
                    }
                    // this.IMIVideoView.start()
                }, 50)
            } else {
                if (!isTry){
                  //  this._queryDayTryAgain(false);
                   // IMIToast.showToast(stringsTo('commLoadingFailText'), IMIToast.TYPE.BOTTOM);
                }
                this.setState({isPlay: false, showPauseView: true},callback=>{
                    // console.log('重试显示暂停');
                    showLoading(false);
                    // showToast(stringsTo('commLoadingFailText'));
                });
               // IMIToast.showToast(stringsTo('commLoadingFailText'), IMIToast.TYPE.BOTTOM);
            }
        }).catch(err => {
            // 网络请求数据失败
            console.log('重试失败',err);
            showLoading(false);
        });
    }

    async _queryDayData(currDate) {
        let startDate = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
        let endDate = new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 23, 59, 59);
        let startTime = parseInt(startDate.getTime() / 1000);
        let endTime = parseInt(endDate.getTime() / 1000);
        let dataArr = [];
        let isMore = true;
        const MAX_LEN = 128;
        while (isMore) {
            let params = { //超过128条获取
                "BeginTime": startTime,
                "EndTime": endTime,
                "QuerySize": MAX_LEN,//传10会返回10+条
                "Type": 0
            }
            console.log('playback QueryRecordTimeList params='+JSON.stringify(params));
            await LetDevice.sendDeviceServerRequest("QueryRecordTimeList", params, true).then(result => {
                console.log('playback QueryRecordTimeList result='+JSON.stringify(result));
                let timelist = result.TimeList;
                let resLen = timelist.length;
                console.log('playback QueryRecordTimeList result='+resLen);
                if (resLen > 0) {
                    dataArr.push(...timelist);
                    if (endTime<=timelist[resLen - 1].EndTime) {
                        isMore = false;
                    }
                    startTime = timelist[resLen - 1].EndTime;
                }else {
                    isMore = false;
                }
               /* if (resLen < MAX_LEN) {
                    isMore = false;
                }*/
            }).catch(err => {
                isMore = false;
                // alert(err)
            });
        }
        //this.setState({dataArray: dataArr});
        return dataArr;
    }

    /**
     * 暂时缓存最近三个月的数据
     * 请求最近三个月的月份对应数据
     */
    queryThreeMonthData(){
        showLoading(stringsTo('commLoadingText'),true);
        //当前月数据
        let date = new Date();
       // date.setMonth(date.getMonth()-1);
        this._queryMonthData(DateUtils.dateFormat(date, "yyyyMM"),true,true);
        //上一月数据
        date.setMonth(date.getMonth()-1);
        this._queryMonthData(DateUtils.dateFormat(date, "yyyyMM"),true);
        //上上月数据
        date.setMonth(date.getMonth()-1);
        this._queryMonthData(DateUtils.dateFormat(date, "yyyyMM"),true);
    }

    /**
     * 检测某个月份是否有缓存数据
     * @param month
     */
    checkMonth(month){
        if (dateDataArr.has(month)){
            let dateArr = dateDataArr.get(month);
            this.setState({dateData:dateArr});
        }else {
            showLoading(true);
            this._queryMonthData(month);
        }
    }
    _queryMonthData(month,isFirst = false,isCheckDay = false) {
        LetDevice.sendDeviceServerRequest("QueryMonthRecord", {"Month": month?month:DateUtils.dateFormat(new Date(), "yyyyMM")}, true).then(result => {
            let ary = result.RecordFlags.split('');
            console.log('QueryMonthRecord--',month?month:DateUtils.dateFormat(new Date(), "yyyyMM"));
            let dayNoVideo=false;
            if (isCheckDay){
                //需要判断今天是否有视频，今天如果没有视频，找最近的一天
                let curDay = new Date().getDate();
                console.log("curDay"+curDay);
                console.log('当天是否有数据',curDay,ary,ary[curDay-1]);
                if (ary[curDay-1] != 1){
                    dayNoVideo=true;
                    //表示当天没有回看数据，暂时给个提示
                    showLoading(false);
                    this.setState({showNoData:true});
                    //showToast(stringsTo('playback_no_video_data_tip'));
                }else {
                    this._queryDayTryAgain(true);
                }

            }
            this._setDateString(ary,month?month:DateUtils.dateFormat(new Date(), "yyyyMM"),isFirst,dayNoVideo);
            // this.setState({})
            // showLoading(false);
            // console.log('loading隐藏---1');
        }).catch(err => {
            // showLoading(false);
            // console.log('loading隐藏---2');
            //网络请求失败了，请求数据看看
            console.log('获取月数据失败--',month,err);
            this._queryDayTryAgain(true);
            // alert("QueryMonthRecord:" + JSON.stringify(Utils.parseError(err)));
        });
    }

    _getAutomaticDay(scrollTime=86400){
        if (automaticDay==null){
            return
        }
        this.IMIVideoView&&this.IMIVideoView.stop();
        let  oldTime= new Date(automaticDay).getTime()+scrollTime*1000 ;
        console.log('AlarmListPlayerPage onDayPress oldTime:' + oldTime);
        let newAutomaticDay=moment(oldTime).format('YYYY-MM-DD');//新的日期
       let ddd= new Date(new Date(oldTime).toLocaleDateString());//新的日期 //new Date(new Date(new Date().toLocaleDateString()).getTime());
        console.log('AlarmListPlayerPage onDayPress ddd:' + ddd);
        console.log('AlarmListPlayerPage onDayPress newAutomaticDay:' + newAutomaticDay);
        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
        // 刷新完成后调用确保取值正确
        this.setState({isShowCalendar:false}, () => {
            showLoading(stringsTo('commLoadingText'),true);
            this._queryDayData(ddd).then(dataArr => {
                automaticDay=ddd;
                showLoading(false);
                if (dataArr.length>=1){
                    currentPlayTime = 0;
                    PauseLineTime=0;
                    let preDay = this.state.currentDate;
                    this.setState({dataArray: dataArr,currentDate: ddd,isClickPause:false,dateTime: newAutomaticDay},()=>{
                        this.IMIVideoView && this.IMIVideoView.prepare();
                        this.preparetimer && clearTimeout(this.preparetimer);
                        this.preparetimer=setTimeout(() =>{
                            this.IMIVideoView && this.IMIVideoView.start()
                        }, 2000)
                        this.resetSelectDay(preDay);
                    });
                } else {
                   // showToast(stringsTo('commLoadingFailText'));
                    this.setState({isClickPause:false});
                }

            });
        });
    }
    /**
     *
     * @param dataAry
     * @param month
     * @param isFirst 是否第一次加载
     * @param isCheckDay 是否需要判断当前时间是否有回看
     * @private
     */
    _setDateString(dataAry,month,isFirst = false,dayNoVideo=false){
        let dateAry = {};
        console.log(dataAry);
        for (let m = 0; m < dataAry.length; m++){
            let day = new Date(month.substr(0,4), parseInt(month.substr(4,2))-1, m+1);
            let disabled = parseInt(dataAry[m])?false:true;
            let seleteDay = new Date(this.state.currentDate).getDate();
            let seleteMonth = new Date(this.state.currentDate).getMonth()+1;
            if (seleteMonth != parseInt(month.substr(4,2)) || seleteDay != m+1){
                dateAry[moment(day).format('yyyy-MM-DD')]={disabled: disabled, disableTouchEvent: disabled, };
            }
        }

        if (dayNoVideo){
            let seleteDay = new Date(this.state.currentDate).getDate();
            let day = new Date(month.substr(0,4), parseInt(month.substr(4,2))-1, seleteDay);
            dateAry[moment(day).format('yyyy-MM-DD')]={disabled: false, disableTouchEvent: false, };
            dateAry.splice(seleteDay-1, 0, dateAry[dateAry.length-1]);
        }
        if (isFirst){
            dateDataArr.set(month,dateAry);
        }else {
            this.setState({dateData:dateAry});
        }
    }

    resetSelectDay(preDay){
        // this.state.dateData[moment(this.state.currentDate).format('yyyy-MM-DD')] = {disabled: false, disableTouchEvent: false };
        // this.state.dateData[day] = {selected: true, marked: false, disableTouchEvent: true, selectedColor: imiThemeManager.theme.primaryColor};
        //由于缓存最近三个月的数据，this.state.dateData为当前选中的月份，preDay可能是不在当前月份
        //需要把缓存的三个月 找到preDayYMD所对应的月份，更新对应天数据，并且更新dateDataArr
        let preDayYMD = moment(preDay).format('yyyy-MM-DD');
        let preYM = moment(preDay).format('yyyyMM');
        let chooseDayYMD = moment(this.state.currentDate).format('yyyy-MM-DD');
        let chooseYM = moment(this.state.currentDate).format('yyyyMM');
        let items = this.state.dateData;
        if (preYM == chooseYM){
            //属于同一个月
            items[preDayYMD] = {disabled: false, disableTouchEvent: false };
            items[chooseDayYMD] = {selected: true, marked: false, disableTouchEvent: true, selectedColor: imiThemeManager.theme.primaryColor};
            this.setState({
                dateData: items
            });
        }else {
            //属于不同月份
            if (dateDataArr.has(preYM)){
                console.log("preYM"+preYM)
                let dateArr = dateDataArr.get(preYM)
                dateArr[preDayYMD] = {disabled: false, disableTouchEvent: false };
                dateDataArr.set(preYM,dateArr)
            }

            items[chooseDayYMD] = {selected: true, marked: false, disableTouchEvent: true, selectedColor: imiThemeManager.theme.primaryColor};
            this.setState({
                dateData: items
            });
        }
    }

    componentDidMount() {

        let monthStr = new Date(this.state.currentDate).getMonth()+1;
        let yearStr = new Date(this.state.currentDate).getFullYear();
        currentPlayTime = 0;//记录断开前的时间 每次进来重新赋值
        PauseLineTime=0;
        // 物理返回键需打开，否则全屏返回事件不对
        this.backHandler = BackHandler.addEventListener("hardwareBackPress", _ => this._onPressBack());
        this.setState({dateTime:DateUtils.dateFormat(this.state.currentDate, "yyyy-MM-dd")});
        //this._queryDayTryAgain(true);
        //进来请求最近三个月的数据
        this.queryThreeMonthData();
        // let languageStr = locales[0]?.languageCode;
        // let tempStr = "zh";
        // if (languageStr.indexOf(tempStr) != -1){
        //     // 中文
        //     let tempStr = yearStr +'年'+monthStr+'月';
        //     this.setState({dateText:tempStr})
        // }else {
        //     // 英文
        //     this.tempStr = yearStr +'-'+monthStr;
        //     this.setState({dateText:tempStr})
        // }
        // console.log('初始进入获取月和年---',monthStr,yearStr,this.state.dateText);
        this._subscribe_blur = this.props.navigation.addListener('blur', () => {
            if (this.state.recording) {
                console.log("回看RN页面切换不保存视频-------------");
                this._stopRecord();
            }
        });

        //shenyonggang@20210330 fixed bug IMI_HMI510_A01-327 start
        //进入后台
        this._enterBackground = IMINativeLifeCycleEvent.enterBackgroundListener.addListener(() => {
            //如果处于播放状态退到后台暂停
            // console.log('进入后台当前声音状态---',this.state.mute);
            if (this.state.recording) {
                console.log("录屏结束 切换后台保存视频-------------");
                this._stopRecord(true,true);
            }
            //关闭录像
            if(this.state.isPlay) {
                backupIsPlay = true;
                // if(CONST.isIos){
                //     this.muteStatus = this.state.mute;
                //     console.log('保存当前声音状态===',this.muteStatus);
                // }
                this.setState({isPlay: false, showPauseView: true}, () => {
                    this.IMIVideoView&&this.IMIVideoView.pause();
                });
            }else{
                backupIsPlay = false;
            }
            console.log(`addListener playbackPage enterBackgroundListener : backupIsPlay= `+backupIsPlay);
        });
        //回到前台
        this._enterForeground = IMINativeLifeCycleEvent.enterForegroundListener.addListener(() => {
            //启动
            console.log(`addListener playbackPage enterForegroundListener : backupIsPlay= `+backupIsPlay);
            // console.log('回到前台当前声音状态---',this.state.mute);
            if(backupIsPlay) {
                // console.log('获取当前声音状态===',this.muteStatus);
                retryNum = 0;
                this.setState({isPlay: true, showPauseView: false}, () => {
                    this.IMIVideoView&&this.IMIVideoView.resume();

                    // if(CONST.isIos){
                    //     if (this.muteStatus == false){
                    //         // iOS 在进入后台前打开监听，在进入前台要打开监听
                    //         this.setState({mute: false});
                    //     }
                    //     // this.muteStatus = this.state.mute;
                    //     console.log('保存当前声音状态===',this.muteStatus);
                    // }
                });
            }
        });
        //shenyonggang@20210330 fixed bug IMI_HMI510_A01-327 end

        //510的播放结束回调
        LetDevice.addDeviceEventChangeListener((data) => {
            let {iotId, identifier, value} = JSON.parse(data);
            if (iotId == LetDevice.deviceID && identifier === "onPlayBackEnd") {
                console.log("szm ============== 回看播放结束了:")
                this.IMIVideoView&&this.IMIVideoView.pause();
                this.setState({isPlay: false, showPauseView: true});
            }
        });
    }

    componentWillUnmount() {
        Orientation.removeOrientationListener(this._orientationDidChange);
        this.IMIVideoView && this.IMIVideoView.stop();
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.preparetimer && clearTimeout(this.preparetimer);
        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
        this.backHandler && this.backHandler.remove();
        this._enterBackground&&this._enterBackground.remove();
        this._enterForeground&&this._enterForeground.remove();
        this.playTimeOut&&clearTimeout(this.playTimeOut);
    }


    _orientationDidChange = (orientation) => {
        if (orientation === 'LANDSCAPE') {
        } else {
            // do something with portrait layout
        }
    }


    _onPressFullScreen = () => {
        // if(!this._canStepIn())  return;
        isAndroid()?Orientation.lockToLandscape():Orientation.lockToLandscapeRight();
        this.setState({isFullScreen: true});
        NavigationBar.setStatusBarHidden(true);
        this.props.navigation.setOptions({tabBarVisible: false});
        this._onPressFullScreenTools();
    };

    _exitFullScreen = () => {
        // if(!this._canStepIn())  return;
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
    }

    _onPressBack = () => {
        if (this.state.isFullScreen) {
            this._exitFullScreen();
        } else {
            if (this.state.recording) {
                showToast(stringsTo('screen_recording'));
                return ;
            }
            this.props.navigation.goBack();
        }
        return true;
    };


    _onPressFullScreenTools = () => {
        this.setState({showFullScreenTools: true});
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.fullScreenTooltsTimer = setTimeout(() => {
            this._onCloseFullScreenTools();
        }, 6500);

    }

    _onCloseFullScreenTools() {
        this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
        this.setState({showFullScreenTools: false});
    }

    _onPressMute = () => {
        if(!this._canStepIn())  return;
        // if (this.state.speed != 0){
        //     showToast(stringsTo('play_speed_tip'));
        //     return;
        // }

        // if (this.state.speed)return;

        // currentPlayTime = 0;
        this.setState({mute: !this.state.mute})
        // this.IMIVideoView && this.IMIVideoView.prepare();
    };

    getMute() {
        return this.state.mute;
    }

    setMute(mute) {
        this.setState({mute: mute});
    }

    /*告知外部调用者监听状态*/
    getFullscreen() {
        return this.state.isFullScreen;
    }
    quitFullScreen() {
        Orientation.lockToPortrait();
        this.setState({isFullScreen: false})
        NavigationBar.setStatusBarHidden(false);
        this.props.navigation.setOptions({tabBarVisible: true});
        this._onCloseFullScreenTools();
    }


    //点击截屏按钮
    _onPressScreenShot = () => {
        if(!this._canStepIn())  return;
        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {

            if (status === 0) {
                IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                    if (status2 === 0) {
                        let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                        this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => {
                            IMIFile.saveImageToPhotosAlbum(currentSnapshotPath, LetDevice.deviceID).then(_ => {
                                this.setState({screenShotPath:currentSnapshotPath,snapshotVisible: true});
                                // IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);

                                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                                    this.setState({ snapshotVisible: false});
                                }, 3000);
                            });
                        });
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    } else if (status2 === -1) {
                        showToast(stringsTo('storage_permission_denied'));
                        this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                    }
                });
            } else if (status === -1) {
                showToast(stringsTo('storage_permission_denied'))
            }
        })


    };

    //点击录屏按钮
    _onPressRecord = () => {
        if(!this._canStepIn())  return;
        if (this.state.recording) {
            console.log("录屏结束-------------");
            this._stopRecord();
        } else {
            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(true);
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, (status) => {
                if (status === 0) {
                    IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, (status2) => {
                        if (status2 === 0) {
                            time=moment(new Date().getTime()).format('yyyyMMDD')+"_"+new Date().getTime();
                            let pathUrl=VEDIO_RECORD_PATH;
                            if (isIos()){
                                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
                            }
                            this.IMIVideoView.startRecord(pathUrl).then(_ => {
                                this.setState({recording: true, recordDuration: 0});
                            });
                            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                        } else if (status2 === -1) {
                            showToast(stringsTo('storage_permission_denied'));
                            this.props.onCheckPermissionStatusChange&&this.props.onCheckPermissionStatusChange(false);
                        }
                    })
                } else if (status === -1) {
                    showToast(stringsTo('storage_permission_denied'))
                }
            })

        }
    };

    //停止录像并保存在相册
    _stopRecord(forSave=true,isBackground=false){
        console.log("停止录制anle",this.state.recordDuration);
        if(this.state.recordDuration<6){
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            forSave = false;
        }
        this.IMIVideoView.stopRecord().then(_ => { //停止录制
            console.log("停止录制-------");
            if(!forSave){ //只停止，不保存
                console.log("停止录制-------保存失败");//save_system_album_failed
                // IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
                return;
            }
            let pathUrl=VEDIO_RECORD_PATH;
            if (isIos()){
                pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
            }
            IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID).then(_ => { //转存视频
                if (isBackground){  //如果切换后台不显示图片
                    return
                }
                this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                let currentSnapshotPath = `${TMP_SNAPSHOT_PATH_PREFIX}IMG_${new Date().getTime()}.jpg`;
                this.IMIVideoView.screenShot(currentSnapshotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                    this.setState({screenShotPath: currentSnapshotPath, snapshotVisible: true});
                    this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                        this.setState({snapshotVisible: false});
                    }, 3000);
                });
                // IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
            });
        });
        this.setState({recording: false, recordDuration: 0});
    }
    //IOS在视频流暂停时,将录制的视频保存到相册
    _saveVideoToPhotosAlbum() {
        if (this.state.recordDuration < 6) {
            IMIToast.showToast(stringsTo('save_system_album_failed'), IMIToast.TYPE.BOTTOM);
            this.setState({recording: false, recordDuration: 0});
            return;
        }
        let pathUrl=VEDIO_RECORD_PATH;
        if (isIos()){
            pathUrl=`${TMP_SNAPSHOT_PATH_PREFIX}VIDEO_IMI_${time}.mp4`;//同步IOS名称
        }
        IMIFile.saveVideoToPhotosAlbum(pathUrl, LetDevice.deviceID).then(_ => { //转存视频
            this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
            let tempShotPath = `${IMIFile.storageBasePath}/tmp/snapshot.jpg`;
            this.IMIVideoView.screenShot(tempShotPath).then(_ => { //截屏一张作为悬浮缩略图，不转存
                this.setState({screenShotPath: tempShotPath, snapshotVisible: true});
                this.snapshotTimeout = setTimeout(() => { //3秒后截屏缩略图自动隐藏
                    this.setState({snapshotVisible: false});
                }, 3000);
            });
            // IMIToast.showToast(stringsTo('saved_system_album'), IMIToast.TYPE.BOTTOM);
        });
        this.setState({recording: false, recordDuration: 0});
    }

    _onPressSpeed = () => {
        if(!this._canStepIn())  return;

        if (this.state.recording)
        {
            showToast(stringsTo('screen_recording'));
            return ;
        }
        if (LetDevice.model == "a1FKrifIRwH"){
            return;
        }

        // currentPlayTime = 0;
        switch (this.state.speed) {
            case 0:
                this.setState({speed: 1,mute: true});
                this.IMIVideoView && this.IMIVideoView.speed(2);
                break;
            case 1:
                this.setState({speed: 2,mute: true});
                this.IMIVideoView && this.IMIVideoView.speed(4);
                break;
            case 2:
                this.setState({speed: 3,mute: true});
                this.IMIVideoView && this.IMIVideoView.speed(8);
                break;
            case 3:
                this.setState({speed: 4,mute: true});
                this.IMIVideoView && this.IMIVideoView.speed(16);
                break;
            case 4:
                this.setState({speed: 0});
                this.IMIVideoView && this.IMIVideoView.speed(1);
                break;
        }
        // this.IMIVideoView && this.IMIVideoView.prepare();
    }

    _onPressPlay = () => {
        if (this.state.recording)
        {
            showToast(stringsTo('screen_recording'));
            return ;
        }
        //如果在错误状态下，点击会出现暂停、播放按钮与播放状态对应不上的问题
        if (this.state.showErrorView){
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return;
        }
        if (this.state.showNoData){
            showToast(I18n.t('playback_no_video_data_tip'),Toast.positions.BOTTOM);
            return;
        }
        this.setState({isClickPause:true});
        if (this.tempScrollPause){
            // 暂停状态下滑动时间轴
            this.setState({isPlay:true,showPauseView:false},()=> {
                this.tempScrollPause = false;
                this.IMIVideoView && this.IMIVideoView.seekTo(this.tempScrollTime*1000.0);
            });
        }else {
            if (!this.state.isPlay){
                // let isResume = false;
                // if (this.state.dataArray.length){
                //     let time = this.state.dataArray[this.state.dataArray.length-1];
                //     let selectTime = (new Date((new Date()).getFullYear(),(new Date()).getMonth(),(new Date()).getDate(), 0, 0, 0)).getTime();
                //     if (time.EndTime > selectTime+currentPlayTime){
                //         isResume = true;
                //     }
                // }


                this.setState({isPlay:true,showPauseView:false},()=>{
                    // if (isResume) {
                    // this.IMIVideoView&&this.IMIVideoView.resume();

                    if (this.state.isPlayFinish){
                        //回看结束调用了stop方法 所以需要调用prepare方法
                        this.setState({isPlayFinish:false});
                        currentPlayTime = 0;
                        PauseLineTime=0;
                        this.IMIVideoView&&this.IMIVideoView.prepare();
                    }else {
                        this.IMIVideoView&&this.IMIVideoView.resume();
                    }


                    // }else {
                    //      this.IMIVideoView&&this.IMIVideoView.prepare();
                    // }
                });

            } else {
                if (this.state.recording) {
                    console.log("暂停前自动关闭录屏 录屏结束-------------");
                    this._stopRecord();
                }
                this.setState({isPlay:false,showPauseView:true,isLoading: false,},()=>{
                    // this.IMIVideoView&&this.IMIVideoView.resume();
                    this.IMIVideoView&&this.IMIVideoView.pause();
                });
            }
        }


    };

    _onEventChange = (event) => {

        // console.log('当前event----',event);

        if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            //this.setState({bps: event.extra.bps})
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PROGRESS_CHANGE) {
            this.setState({isLoading:false,showNoData:false});
            // currentTime = event.extra.currentTime/1000;
            retryNum = 0;
            if (currentPlayTime-1 >= event.extra.currentTime/1000.0){
                // this.playTimeOut&&clearTimeout(this.playTimeOut);
                // this.playTimeOut = setTimeout(()=>{
                // currentPlayTime = currentPlayTime > 0 ?currentPlayTime:0;
                // this.IMIVideoView&&this.IMIVideoView.seekTo(currentPlayTime*1000);
                // },50);
            }else {
                // currentPlayTime = event.extra.currentTime/1000;
            }
            //console.log(currentPlayTime,'-----',currentPlayTime-1,currentPlayTime,event.extra.currentTime/1000.0);
            let scrollTime = currentPlayTime-1 >= event.extra.currentTime/1000.0 ? currentPlayTime : event.extra.currentTime/1000;
            playEndTime = scrollTime;
            PauseLineTime=scrollTime;
            //播放时间水印与时间轴数据无法对应，
            // 复现的问题是播放出现错误后，拖动时间轴，
            // 然后点击重试，这时视频如果恢复播放，时间轴与播放水印就无法对应
            if (currentPlayTime-1 >= event.extra.currentTime/1000.0){
                this.playTimeOut&&clearTimeout(this.playTimeOut);
                this.playTimeOut = setTimeout(()=>{
                this.IMIVideoView&&this.IMIVideoView.seekTo(currentPlayTime*1000);
                },50);
            }
            //console.log('scrollTime----',scrollTime,playEndTime)
            this.timeLine && this.timeLine.scrollToTimestamp(scrollTime);
           if (scrollTime>=86400){
               console.log('-AlarmListPlayerPage----',scrollTime);
               if(new Date().getTime()-lastClickSnapPhoto<2000) { //为了避免快速点击导致的截屏白屏问题，每秒只给截图一次
                   return;
               }
               lastClickSnapPhoto = new Date().getTime();
               if (currentPlayTime-1> event.extra.currentTime/1000.0){

               }
               this._getAutomaticDay(scrollTime);
           }

        } else if (event.code == IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_BUFFERING) { //调用video.start()后的第一个回调
            // console.log("回看 直播流----_onEventChange,开始启用");
            if (this.errPrepare){
                // console.log('播放报错走这里');
                this.errPrepare = false;
                this.setState({isLoading: true, isPlay: false, showPauseView: false, showErrorView: false});
                this.IMIVideoView&&this.IMIVideoView.seekTo(this.errPlayTime*1000);
            }else {
                this.setState({isLoading: true, isPlay: false, showPauseView: false, showErrorView: false});
                this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.LOADING);
            }

        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_PREVIEW) {
            // console.log("回看 直播流----_onEventChange,出现关键帧");
            retryNum = 0;
            this.setState({isLoading: false,showPauseView: false, isPlay: true,showNoData:false});
            this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.PLAYING);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_STOP) {
            // console.log("回看 直播流----_onEventChange,出现关键帧");
           // IMILog.logD("王 回看错误 PayBack _onEventChange PLAYER_EVENT_ON_STOP>",event.toString())
            this.setState({isLoading: false, isPlay: false, showPauseView: true,speed: 0,showNoData:false});
            if(this.state.recording){ //直播流暂停时，停止录像
                if(CONST.isAndroid){
                    this._stopRecord();
                }else{ //因为IOS会自动停止视频录制，所以直接转存即可
                    this._saveVideoToPhotosAlbum();
                }
            }
            // console.log("回看 直播流----_onEventChange,停止播放");//每次进页面都会来这一出，是什么鬼,目前只有根据当前的播放状态来设置showPauseView
            this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.PAUSE);
        } else if (event.code === IMICameraVideoView.PLAYER_EVENT_CODE.PLAYER_EVENT_ON_START) {
            // console.log("回看 直播流----_onEventChange,开始播放");
            //this.setState({isLoading:false});

        }
    };


    _onCenterValueChanged = (currentTime) => {
        if (!this._getDeviceStatus())return;
        if (this.state.recording)
        {
            showToast(stringsTo('screen_recording'));
            return false;
        }
        //超出时间轴，显示最后时间
        currentPlayTime = currentTime;
        console.log('_onCenterValueChanged--',currentPlayTime)
        if (this.state.dataArray.length>0){
            let time = this.state.dataArray[this.state.dataArray.length-1];
            let nowTime = new Date(this.state.currentDate.getFullYear(), this.state.currentDate.getMonth(), this.state.currentDate.getDate(), 0, 0, 0).getTime();
            if (time.EndTime < nowTime/1000+currentTime){
                currentPlayTime = time.EndTime - nowTime/1000-120;//固件沟通要预留120秒
                // console.log('_onCenterValueChanged--1--',currentPlayTime)
            }
        }
        currentPlayTime = currentPlayTime > 0? currentPlayTime : 0;
        // console.log('_onCenterValueChanged--2--',currentPlayTime)
        PauseLineTime=currentPlayTime;
        if (!this.state.isPlay){
            // 暂停状态
            this.tempScrollPause = true;
            this.tempScrollTime = currentPlayTime;
        }else {
            if (this.state.isFullScreen){
                this.setState({showFullScreenTools: true});
                this.fullScreenTooltsTimer && clearTimeout(this.fullScreenTooltsTimer);
                this.fullScreenTooltsTimer = setTimeout(() => {
                    this._onCloseFullScreenTools();
                }, 6500);
            }
            this.setState({isLoading:true});
            this.IMIVideoView && this.IMIVideoView.seekTo(currentPlayTime*1000.0);
        }

        // if (this.state.isPlay){

        // } else {
        //     this.IMIVideoView && this.IMIVideoView.prepare();
        // }
    }

    _onRecordTimeChange = (event) => {
        this.setState({recordDuration: event.extra})
    }

    //判断当前是否可以操作
    _canStepIn(){
        if (!this._getDeviceStatus())return;
        //获取设备是否在线的状态可能不准确 增加一个条件
        if(!this.state.isPlay || this.state.showErrorView){
            showToast(I18n.t('onlyDoInLive'),Toast.positions.BOTTOM);
            return false;
        }
        return true;
    }

    //离线以及休眠提示
    _getDeviceStatus(){
        if (!this.props.isOnLine){
            showToast(stringsTo('device_offline'));
            return false;
        }
        if (this.props.isSleepStatus){
            showToast(stringsTo('power_off'));
            return false;
        }
        return true;
    }

    /**
     * 竖屏状态视屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenVideoViewArea() {
        return (

            this.props.navBar ? this.props.navBar(this.state.bps, this.state.isFullScreen) : (
                <NavigationBar
                    type={NavigationBar.TYPE.DARK}
                    backgroundColor={"transparent"}
                    title={stringsTo('play_back_text')}
                    // subtitle={this.state.bps >= 0 ? `${this.state.bps}KB/S` : " "}
                    left={[{key: NavigationBar.ICON.BACK, onPress: this._onPressBack}]}
                    right={this.props.navBarRight ? this.props.navBarRight : []}/>)
        );
    }


    /**
     * 全屏状态videoView区域填充UI
     * @returns {Element}
     * @private
     */
    _renderLandscapeScreenVideoViewArea() {
        let title = speedTitleAry[this.state.speed];
        let speedAccessibilityLabel= speedAccessibilityLabelTitleAry[this.state.speed];
        let showModel=  LetDevice.model == 'a1Od0SjKPGt'||LetDevice.model == 'a1Godgpvr3D'||LetDevice.model == 'a1zcQKoHQ83'||LetDevice.model == 'a1QRbHvcYBd'||LetDevice.model == "a1FKrifIRwH"|| LetDevice.model == "a1Ikkj5vsiK" || LetDevice.model == "a1znn6t1et8";
        let isShowSpeed = true;
        if (LetDevice.model == "a1FKrifIRwH" || LetDevice.model == "a1Ikkj5vsiK" || LetDevice.model == "a1znn6t1et8"){
            isShowSpeed = false;
        }
        return (
            this.state.showFullScreenTools ? (
                <View pointerEvents="box-none" style={{width: "100%",height:'100%'}}>
                    {/*<ImageButton*/}
                        {/*style={{*/}
                            {/*position: "absolute",left: 20,top:10,width: 30, height: 30*/}
                        {/*}}*/}
                        {/*source={require('../../CommonView/PlayerToolBarView/res/landscape_back.png')}*/}
                        {/*onPress={(_) => { //TODO 跳转到相册预览？*/}
                            {/*this._onPressBack();*/}
                        {/*}}*/}
                    {/*/>*/}
                    {showModel?
                        <PlayBackFullScreenToolBarView
                            exitPress={this._exitFullScreen}
                            playPress={this._onPressPlay}
                            isPlay={this.state.isPlay}
                            mutePress={this._onPressMute}
                            mute={this.state.mute}
                            muteDisabled={this.state.speed == 0 ? false:true}
                            speedTitle={title}
                            speedPress={this._onPressSpeed}
                            speedAccessibilityLabel={speedAccessibilityLabel}
                            // speedDisabled={LetDevice.model == "a1FKrifIRwH" ? true:false}
                            screenshotPress={this._onPressScreenShot}
                            recordPress={this._onPressRecord}
                            recording={this.state.recording}
                            recordDisabled={this.state.speed == 0 ? false:true}
                            fullScreenPress={this._exitFullScreen}
                            isShowSpeedItem={isShowSpeed}
                        />
                        :
                        <LivePlayerFullScreenToolBarView
                        // style={{position: "absolute"}}
                        exitPress={this._exitFullScreen}
                        // qualityTitle={title}
                        // qualityPress={this._onPressSpeed}
                        // qualityDisabled={this.state.recording}
                        mutePress={this._onPressMute}
                        mute={this.state.mute}
                        muteDisabled={this.state.speed == 0 ? false:true}
                        screenshotPress={this._onPressScreenShot}
                        recordPress={this._onPressRecord}
                        recording={this.state.recording}
                        recordDisabled={this.state.speed == 0 ? false:true}
                        moreItems={this.props.fullScreenToolBarMoreItems}
                    /> }
                    {/*<LivePlayerFullScreenToolBarView*/}
                    {/*    // style={{position: "absolute"}}*/}
                    {/*    exitPress={this._exitFullScreen}*/}
                    {/*    qualityTitle={title}*/}
                    {/*    qualityPress={this._onPressSpeed}*/}
                    {/*    qualityDisabled={this.state.recording}*/}
                    {/*    mutePress={this._onPressMute}*/}
                    {/*    mute={this.state.mute}*/}
                    {/*    screenshotPress={this._onPressScreenShot}*/}
                    {/*    recordPress={this._onPressRecord}*/}
                    {/*    recording={this.state.recording}*/}
                    {/*    moreItems={this.props.fullScreenToolBarMoreItems}*/}
                    {/*/>*/}
                    <View style={{
                        position: "absolute",left: 0,bottom:10,right:0
                    }}>
                        <TimeLineView
                            ref={ref => this.timeLine = ref}
                            darkType={true}
                            style={{backgroundColor: "rgba(0, 0, 0, 0.6)"}}
                            // isShowCalendarTitle={false}
                            currentDate={this.state.currentDate}
                            dataArray={this.state.dataArray}
                            calendarClick={()=>{
                                if (this.state.recording)
                                {
                                    showToast(stringsTo('screen_recording'));
                                    return false;
                                }
                                let month = moment(this.state.currentDate).format('yyyyMM');
                                this.checkMonth(month);
                                this.setState({isShowCalendar:!this.state.isShowCalendar})
                            }}
                            onCenterValueChanged={this._onCenterValueChanged}
                            setIsPlay={!this.state.isPlay&&PauseLineTime>0&&this.state.showPauseView}
                            setPauseLineTime={PauseLineTime}
                        />
                    </View>
                </View>
            ) : null
        );
    }


    /**
     * 竖屏状态下半屏区域填充UI
     * @returns {Element}
     * @private
     */
    _renderPortraitScreenPlayerToolBarArea() {
        let isShowSpeed = true;
        if (LetDevice.model == "a1FKrifIRwH" || LetDevice.model == "a1Ikkj5vsiK" || LetDevice.model == "a1znn6t1et8"){
           // 0216 021 036 回看不支持倍速播放
            isShowSpeed = false;
        }
        return (
            <View style={{flex: 1, flexDirection: "column"}}>
                <PlayBackToolBarView
                    speedTitle={["1X", "2X", "4X", "8X", "16X"]}
                    speedPress={this._onPressSpeed}
                    speedIndex={this.state.speed}
                    fullscreenPress={this._onPressFullScreen}
                    mutePress={this._onPressMute}
                    mute={this.state.mute}
                    muteDisabled={this.state.speed == 0 ? false:true}
                    screenshotPress={this._onPressScreenShot}
                    recordPress={this._onPressRecord}
                    recordDisabled={this.state.speed == 0 ? false:true}
                    recording={this.state.recording}
                    moreItems={this.props.toolBarMoreItems}
                    playPress = {this._onPressPlay}
                    play={this.state.isPlay}
                    isShowSpeedItem={isShowSpeed}
                />
                <TimeLineView
                    ref={ref => this.timeLine = ref}
                    style={{backgroundColor: "#FAFAFA"}}
                    currentDate={this.state.currentDate}
                    dataArray={this.state.dataArray}
                    calendarClick={()=>{
                        if (this.state.recording)
                        {
                            showToast(stringsTo('screen_recording'));
                            return false;
                        }
                        let month = moment(this.state.currentDate).format('yyyyMM');
                        this.checkMonth(month);
                        this.setState({isShowCalendar:!this.state.isShowCalendar})
                    }}
                    onCenterValueChanged={this._onCenterValueChanged}
                    setIsPlay={!this.state.isPlay&&PauseLineTime>0&&this.state.showPauseView}
                    setPauseLineTime={PauseLineTime}
                />
                {this.props.children}
            </View>
        );
    }


    _renderRecordingView() {
        if (!this.state.recording) {
            return null;
        }
        let duration = this.state.recordDuration > 0 ? `${timeFilter(this.state.recordDuration)}` : "00:00";
        return (
            <View style={this.state.isFullScreen? //修改全屏状态下的录屏时间显示不准确问题
                {
                    position: "absolute",
                    width: 64,
                    height: 26,
                    backgroundColor: 'rgba(0,0,0,0.6)',
                    borderRadius: 4,
                    flexDirection: 'row',
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 10 + this.state.showFullScreenTools ? 50 : 0,
                    zIndex: 999,
                    // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
                }
                :
                {
                    width: 64,
                    height: 26,
                    backgroundColor: 'rgba(0,0,0,0.6)',
                    borderRadius: 4,
                    flexDirection: 'row',
                    justifyContent: "center",
                    alignItems: "center",
                    marginTop: 10 + this.state.showFullScreenTools ? 50 : 0,
                    // marginTop: 10 + ((this.state.isFullScreen && !this.state.showFullScreenTools) ? 50 : 0),
                }
            }>
                <View style={{backgroundColor: "#E74D4D", opacity: 0.9, width: 6, height: 6, borderRadius: 3}}/>
                <Text style={{marginLeft: 6, fontSize: 12, color: 'rgba(255,255,255,0.9)'}}>{duration}</Text>
            </View>
        );
    }

    _loadingView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (this.state.showPauseView) return;
        if (this.state.showNoData) return;
        if (!this.state.isLoading) return;
        return (<View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center"
                       }}
        >
            <ActivityIndicator
                style={{width: 54, height: 54}}
                color={"#ffffff"}
                size={"large"}
            />
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("commLoadingText")}
            </Text>
        </View>);
    }

    _errorView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (this.state.showNoData) return;
        if (!this.state.showErrorView) return;
        return (
            <View  pointerEvents="box-none"
                   style={{
                       position: "absolute",
                       width: "100%",
                       height: "100%",
                       display: "flex",
                       justifyContent: "center",
                       alignItems: "center"
                   }}
            >
                <XText style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}
                       text={I18n.t('onPlayErrorText', {code: this.state.errorCode})}
                />

                <RoundedButtonView buttonText={stringsTo('error_code_common_retry')}
                                   buttonStyle={{
                                       margin: 14,
                                       paddingHorizontal:15,
                                       height: 40
                                   }}
                                   buttonTextStyle={{textAlign:'center'}}
                                   onPress={() => {
                                       // ;

                                       // this.timeLine && this.timeLine.scrollToTimestamp(scrollTime);
                                       // console.log('播放失败---',playEndTime);
                                       console.log("retryNum"+retryNum);
                                       let showModel= retryNum>2 && (LetDevice.model == 'a1Godgpvr3D'||LetDevice.model == 'a1zcQKoHQ83'||LetDevice.model == 'a1QRbHvcYBd');
                                       if ( showModel){
                                           showToast(stringsTo("common_error"));
                                           return;
                                       }
                                       retryNum++;
                                       this.errPrepare = true;
                                       this.errPlayTime = playEndTime;

                                       this.IMIVideoView&&this.IMIVideoView.prepare();
                                       this.setState({speed: 0});

                                       // this.errTimer && clearTimeout(this.errTimer);
                                       // this.errTimer=setTimeout(() =>{
                                       //     this.IMIVideoView&&this.IMIVideoView.seekTo(playEndTime*1000);
                                       // }, 2000);

                                   }}/>
            </View>
        );
    }

    _pauseView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (this.state.showNoData) return null;
        if (!this.state.showPauseView) return null;
        if (this.state.showErrorView) return;
        return (<View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center"
                       }}
        >
            <ImageButton
                style={{width: 52, height: 52}}
                source={require("../HomePageLivePlayerComponent/res/icon_play.png")}
                highlightedSource={require("../HomePageLivePlayerComponent/res/icon_play.png")}
                onPress={() => {
                    // 点击过暂停 需要
                    console.log('isPlayzhuangtai---',this.state.isPlayFinish,this.state.isClickPause);
                    if(this.tempScrollPause){
                        // 暂停状态下滑动时间轴
                        this.tempScrollPause = false;
                        this.IMIVideoView && this.IMIVideoView.seekTo(this.tempScrollTime*1000.0);
                    }else {
                        if (this.state.isPlayFinish){
                            currentPlayTime = 0;
                            PauseLineTime=0;
                            this.IMIVideoView&&this.IMIVideoView.prepare();
                        }else {
                            this.state.isClickPause?this.IMIVideoView.resume():this.IMIVideoView.prepare();
                        }
                    }

                        // this.state.isClickPause?this.IMIVideoView.resume():this.IMIVideoView.prepare();
                    if (this.state.isClickPause){
                        this.setState({isPlay:true,showPauseView:false});
                    }else {
                        this.setState({speed: 0});
                    }
                }}
            />
        </View>);
    }

    _noDataView() {
        if (!this.props.isOnLine)return;
        if (this.props.isSleepStatus) return;
        if (!this.state.showNoData) return null;
        return (<View  pointerEvents="box-none"
                       style={{
                           position: "absolute",
                           width: "100%",
                           height: "100%",
                           display: "flex",
                           justifyContent: "center",
                           alignItems: "center"
                       }}
        >
            <Text
                style={{
                    textAlign: 'center',
                    color: colors.white,
                    fontSize: 15,
                }}>
                {stringsTo("playback_no_video_data_tip")}
            </Text>
        </View>);
    }

    _renderSnapshotView() {
        if (!this.state.snapshotVisible) {
            return null;
        }
        return (
            <View style={{
                backgroundColor: 'transparent',
                position: "absolute",
                bottom: this.state.isFullScreen ? 106 : 19,
                left: isPhoneX()?(this.state.isFullScreen?44+14:14):14,
                width: 140,
                height: 80,
                zIndex: 999,
            }}>
                <ImageButton
                    style={{
                        width: "100%",
                        height: "100%",
                        borderWidth: 2,
                        borderColor: 'white',
                        borderRadius: 10
                    }}
                    source={{uri: 'file://' + this.state.screenShotPath}}
                    onPress={(_) => { //TODO 跳转到相册预览？
                        if (this.state.recording)
                        {
                            showToast(stringsTo('screen_recording'));
                            return ;
                        }
                        this.snapshotTimeout && clearTimeout(this.snapshotTimeout);
                        this.setState({snapshotVisible:false});
                        if(this.state.isFullScreen){ //横屏则退出全屏
                            if(CONST.isAndroid){
                                this.goToAlbum();
                                setTimeout(() =>{
                                    Orientation.lockToPortrait();
                                    this.props.navigation.setOptions({tabBarVisible: true});
                                    this._onCloseFullScreenTools();
                                    NavigationBar.setStatusBarHidden(false);
                                    this.setState({isFullScreen: false},()=>{
                                        console.log('Android回看退出全屏');
                                    });
                                }, 1000);
                                return;
                            }else {
                                Orientation.lockToPortrait();
                                this.props.navigation.setOptions({tabBarVisible: true});
                                this._onCloseFullScreenTools();
                                NavigationBar.setStatusBarHidden(false);
                                this.setState({isFullScreen: false},()=>{
                                    // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                                    this.goToAlbum();
                                    console.log('iOS回看退出全屏');
                                });
                                return;
                            }
                        }else {
                            this.goToAlbum();
                        }
                        // IMIGotoPage.startAlbumPage(LetDevice.deviceID);
                    }}
                />
            </View>
        )
    }

    //进入相册
    goToAlbum() {
        let {showRNAlbum} = DeviceTemplatesUtils.getConfigProject(LetDevice.category, LetDevice.model);
        if (showRNAlbum || LetDevice.model === "a1l4Z7lJ1ns" || LetDevice.model === "a1yMb5JVWDa"){//510项目使用rn相册
            // this.checkIMIPermission();
            // if (isAndroid() && IMIPackage.minApiLevel>=10006){
            //     this.props.navigation.push('CameraListPage');
            // }else if (isIos() &&IMIPackage.minApiLevel>=10005){
            //     this.props.navigation.push('CameraListPage');
            // }else {
            //     IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            // }
            if (IMIPackage.minApiLevel < 10007) {
                IMIGotoPage.startAlbumPage(LetDevice.deviceID);
            } else {
                this.props.navigation.push('CameraListPage');
            }
        }else {
            IMIGotoPage.startAlbumPage(LetDevice.deviceID);
        }
    }

    /**
     * 日历控件逻辑
     * @returns {*}
     * @constructor
     */
    getCalendarView() {
        return (
            <ModalView style={[{justifyContent:'center'},this.state.isFullScreen?{alignItems:'center'}:{}]} visible={this.state.isShowCalendar} onClose={()=>{this.setState({isShowCalendar:false})}}>
                <Calendar
                    style={{width:'100%'}}
                    onDayPress={(day) => {
                        console.log('AlarmListPlayerPage onDayPress pressed formatDate' + day.dateString);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        let ddd = new Date(day.year,day.month-1,day.day, 0, 0, 0);
                        automaticDay=ddd;

                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        // 刷新完成后调用确保取值正确
                        this.setState({isShowCalendar:false,dateTime: day.dateString,showNoData:false}, () => {
                            showLoading(stringsTo('commLoadingText'),true);
                            this.IMIVideoView.stop();
                            this._queryDayData(ddd).then(dataArr => {
                                showLoading(false);
                                if (dataArr.length>=1){
                                    currentPlayTime = 0;
                                    PauseLineTime=0;
                                    let preDay = this.state.currentDate;
                                    this.setState({dataArray: dataArr,currentDate: ddd,isClickPause:false,dateTime: day.dateString},()=>{
                                        this.IMIVideoView && this.IMIVideoView.prepare();
                                        this.preparetimer && clearTimeout(this.preparetimer);
                                        this.preparetimer=setTimeout(() =>{
                                            this.IMIVideoView.start()
                                        }, 2000)
                                        this.resetSelectDay(preDay);
                                    });
                                } else {
                                    showToast(stringsTo('commLoadingFailText'));
                                    let tempDay = DateUtils.dateFormat(this.state.currentDate, "yyyy-MM-dd");
                                    console.log('当前选中天',tempDay);
                                    this.setState({isClickPause:false,dateTime:tempDay});
                                }

                            });
                        });

                        // this.setState({isShowCalendar:false});

                    }}

                    onMonthChange={(day) => {
                        // this.setState({isShowCalendar:false});
                        console.log('AlarmListPlayerPage onMonthChange pressed' + day.month+day.year);
                        console.log('切换日期显示----',day.year,day.month);
                        // let languageStr = locales[0]?.languageCode;
                        // let tempStr = "zh";
                        // if (languageStr.indexOf(tempStr) != -1){
                        //     // 中文
                        //     let tempStr = day.year +'年'+day.month+'月';
                        //     console.log('临时日期---',tempStr);
                        //      this.setState({dateText:tempStr},callback=>{
                        //          console.log('设置后日期显示---',this.state.dateText,);
                        //      });
                        // }else {
                        //     // 英文
                        //     this.tempStr = day.year +'-'+day.month;
                        //     this.setState({dateText:tempStr});
                        // }
                        let ary = day.dateString.split('-');
                        let month = ary[0]+ary[1];
                        this.checkMonth(month)
                    }}
                    hideArrows={false}
                    disabledByDefault={true}
                    hideExtraDays={true}
                    maxDate={new Date().toDateString()}//控制台报错接收是string，原来传入的是date
                    current={this.state.dateTime}  //设置选中时间
                    markedDates={{
                        [this.state.dateTime]: {
                            selected: true,
                            marked: false,
                            disableTouchEvent: true,
                            selectedColor: imiThemeManager.theme.primaryColor
                        }, ...this.state.dateData
                    }}
                    theme={{
                        arrowColor: '#000000', //左右箭头的颜色
                        todayTextColor: imiThemeManager.theme.primaryColor,
                        textMonthFontWeight: 'bold',//标题yyyy-MM的字重
                        //textDayHeaderFontWeight: 'bold',//周几的字重 20220211@byh去掉字重及周文字大小减小1号，防止有些机型文字显示不全
                        textSectionTitleColor: '#000000',//周几的颜色
                         textDayHeaderFontSize:12,//周大小 字体设置 英文会突出
                    }}

                    // renderHeader={(date) => {return(<Text style={{color:"black"}}>{this.state.dateText}</Text>)}}

                    // renderHeader={(date) => {return(<Text style={{color:"red"}}>{moment(date).format('YYYY-MM')}</Text>)}}
                    // monthFormat={'yyyy-MM'}
                />

            </ModalView>
        );
    };


    //暂时的052横屏日历控件，与其他项目分开
    getCalendarLandscapeView() {
        return (
            <ModalView style={[{justifyContent:'center',backgroundColor:"transparent"},this.state.isFullScreen?{alignItems:'flex-end'}:{}]} visible={this.state.isShowCalendar} onClose={()=>{this.setState({isShowCalendar:false})}}>
                <Calendar
                    style={{width:360,height:"100%",backgroundColor:"#000000",borderTopLeftRadius:20,borderBottomLeftRadius:20}}
                    onDayPress={(day) => {
                        console.log('AlarmListPlayerPage onDayPress pressed formatDate' + day.dateString);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        let ddd = new Date(day.year,day.month-1,day.day, 0, 0, 0);
                        // new Date(currDate.getFullYear(), currDate.getMonth(), currDate.getDate(), 0, 0, 0);
                        // 刷新完成后调用确保取值正确
                        this.setState({isShowCalendar:false,dateTime: day.dateString}, () => {
                            showLoading(stringsTo('commLoadingText'),true);
                            this.IMIVideoView.stop();
                            this._queryDayData(ddd).then(dataArr => {
                                showLoading(false);
                                if (dataArr.length>=1){
                                    currentPlayTime = 0;
                                    PauseLineTime=0;
                                    let preDay = this.state.currentDate;
                                    this.setState({dataArray: dataArr,currentDate: ddd,isClickPause:false,dateTime: day.dateString},()=>{
                                        this.IMIVideoView.prepare();
                                        this.preparetimer && clearTimeout(this.preparetimer);
                                        this.preparetimer=setTimeout(() =>{
                                            this.IMIVideoView.start()
                                        }, 2000)
                                        this.resetSelectDay(preDay);
                                    });
                                } else {
                                    showToast(stringsTo('commLoadingFailText'));
                                    let tempDay = DateUtils.dateFormat(this.state.currentDate, "yyyy-MM-dd");
                                    console.log('当前选中天--横屏',tempDay);
                                    this.setState({isClickPause:false,dateTime:tempDay});
                                }

                            });
                        });

                        // this.setState({isShowCalendar:false});

                    }}

                    onMonthChange={(day) => {
                        // this.setState({isShowCalendar:false});
                        console.log('AlarmListPlayerPage onMonthChange pressed' + day.month)
                        let ary = day.dateString.split('-');
                        let month = ary[0]+ary[1];
                        this.checkMonth(month);
                    }}
                    hideArrows={false}
                    disabledByDefault={true}
                    hideExtraDays={true}
                    maxDate={new Date()}
                    current={this.state.dateTime}  //设置选中时间
                    markedDates={{
                        [this.state.dateTime]: {
                            selected: true,
                            marked: false,
                            disableTouchEvent: true,
                            selectedColor: imiThemeManager.theme.primaryColor
                        },...this.state.dateData
                    }
                    }
                    theme={{
                        arrowColor: '#ffffff', //左右箭头的颜色
                        backgroundColor: '#000000',
                        calendarBackground: '#000000',
                        todayTextColor: imiThemeManager.theme.primaryColor,
                        dayTextColor: '#ffffff', //有数据的天
                        textDisabledColor: '#888888', //没数据的天
                        monthTextColor: '#ffffff',//标题yyyy-MM的颜色
                        textMonthFontWeight: 'bold',//标题yyyy-MM的字重
                        textDayHeaderFontWeight: 'bold',//周几的字重
                        textSectionTitleColor: '#ffffff',//周几的颜色
                        textDayHeaderFontSize:12,//周大小 字体设置 英文会突出
                    }}
                    // monthFormat={'yyyy-MM'}
                    // renderHeader={() => {return(<Text style={{color:"white"}}>{this.state.dateText}</Text>)}}
                    // renderHeader={(date) => {return(<Text style={{color:"#fff"}}>{moment(date).format('YYYY-MM')}</Text>)}}
                />

            </ModalView>
        );
    };

    _onDayPress() {
        console.log('_onDayPress');
        let m = this.state.dateTime.substring(5, 7);
        let d = this.state.dateTime.substring(8);
        this.topSelectBarRoot.funOnPress(m + "/" + d);

        this.props.onDayPress && this.props.onDayPress(this.state.dateTime)
    }

    render() {
        console.log("回看开始时间 startIime="+parseInt(this.state.currentDate.getTime()/ 1000)+",endTime"+parseInt((this.state.currentDate.getTime()+ 24 * 60 * 60 * 1000 - 1) / 1000))
        let showModel=  LetDevice.model == 'a1Od0SjKPGt'||LetDevice.model == 'a1Godgpvr3D'||LetDevice.model == 'a1zcQKoHQ83';
        return (
            <View ref={(ref) => this.componentContainer = ref} pointerEvents="box-none" style={{flex: 1, backgroundColor: "#FFFFFF", flexDirection: "column"}}>
                <View style={{flex: 1}} >
                    <IMIVideoView
                        style={{flex: 1}}
                        ref={ref => {
                            this.IMIVideoView = ref;
                            this.props.videoRef && this.props.videoRef(this.IMIVideoView);
                        }}
                        mute={this.state.mute}
                        playerClass={IMICameraVideoView.PlayerClass.VOD}
                        dataSource={{
                            playerClass: IMICameraVideoView.PlayerClass.VOD,
                            did: LetDevice.deviceID,
                            vod: {
                                // 先暂时去掉currentPlayTime，解决在回看页面点击其他按钮页面卡住的问题，如果加上currentPlayTime是优化移动录制自动跳转到下个时间的
                                // startTime: parseInt(this.state.currentDate.getTime()/ 1000)+currentPlayTime,
                                startTime: parseInt(this.state.currentDate.getTime()/ 1000),
                                endTime: parseInt((this.state.currentDate.getTime()+ 24 * 60 * 60 * 1000 - 1) / 1000)
                            },
                            offsetTime: 0
                        }}
                        onPrepared={() => {
                            this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.PREPARED);
                        }}
                        onVideoViewClick={()=>{
                            this.state.isFullScreen && this.state.showFullScreenTools ? this._onCloseFullScreenTools() : this._onPressFullScreenTools();
                            this.props.onVideoClick && this.props.onVideoClick();
                        }}
                        onEventChange={this._onEventChange}
                        onPlayCompletion={()=>{
                            console.log('播放结束---')
                            //超出时间轴
                            if (this.state.dataArray.length>0){
                                let time = this.state.dataArray[this.state.dataArray.length-1];
                                let nowTime = new Date(this.state.currentDate.getFullYear(), this.state.currentDate.getMonth(), this.state.currentDate.getDate(), 0, 0, 0).getTime();
                                let tempEndTime = playEndTime +80;// xy 打印log发现调用播放完成的时候其实并未真正播放结束，少了4s-8s 保险起见加了10s判断 否则会有问题
                                let tempStopTime = nowTime/1000+tempEndTime;
                                // let tempStopTime = nowTime/1000+currentPlayTime;
                                console.log('播放结束时间------',time.EndTime,tempEndTime,tempStopTime,nowTime/1000,);
                                if (time.EndTime <= tempStopTime){
                                    // this.IMIVideoView&&this.IMIVideoView.pause();
                                    this.setState({isPlay: false, showPauseView: true,isPlayFinish:true}, () => {
                                        this.IMIVideoView&&this.IMIVideoView.stop();
                                        // this.IMIVideoView&&this.IMIVideoView.pause();
                                    });
                                    console.log('停止播放');
                                    console.log('结束播放时间的currentPlay',currentPlayTime);

                                } else {
                                    console.log('重新开始播放');
                                    retryNum = 0;
                                    this.setState({isPlay: true, showPauseView: false}, () => {
                                        this.IMIVideoView&&this.IMIVideoView.resume()
                                    });
                                }
                            }

                        }}
                        onErrorChange={(event)=>{
                            console.log("直播流-----onErrorChange------   event.code " + event.code, event);
                            // //通话意外报错关闭 - 如果是通话意外报错关闭，则直接提示
                             //IMILog.logD("王 回看错误 PayBack onErrorChange >",event.toString())
                            do {
                                //判断如果是通话报错则此处进行判断是否为占线
                                if (event.code !== PLAYER_EVENT_CODE.ERROR_EVENT_STREAM_CLOSED_UNEXPECTEDLY) {
                                    continue
                                }
                                //判断是否为对讲模式
                                if (event.extra.arg1 !== CAMERA_PLAYER_MODE.MODE_SPEAK) {
                                    continue
                                }
                                //暂时阿里SDK无法返回指定错误码，此处暂时使用info 对比
                                if (event.extra.arg2 === "voice intercom existed") {
                                    showToast(stringsTo('call_busy_tips'));
                                } else {
                                    showToast(stringsTo('call_connect_error'));
                                }
                                return
                            } while (false);

                            this.setState({
                                isPlay:false,
                                showErrorView: true,
                                showPauseView: false,
                                isLoading:false,
                                errorCode: event.code,
                            });
                            if(this.state.recording){ //直播流暂停时，停止录像
                                if(CONST.isAndroid){
                                    this._stopRecord();
                                }else{ //因为IOS会自动停止视频录制，所以直接转存即可
                                    this._saveVideoToPhotosAlbum();
                                }
                            }
                            this.props.onVodPlayerStatusChange && this.props.onVodPlayerStatusChange(VOD_PLAYER_STATUS.ERROR);//为何回看调用直播的条用
                        }}
                        onRecordTimeChange={this._onRecordTimeChange}
                     /*   lensCorrect={this.props.lensCorrect}*/
                    />
                    {/*全屏?横屏UI:竖屏UI(navBar)*/}
                    <View pointerEvents="box-none" style={{position: "absolute", width: "100%", height: "100%", flexDirection: "column", alignItems: "center"}}>

                        {
                            this._renderSnapshotView()
                        }
                        {
                            this.props.loadingView ? this.props.loadingView(this.state.isLoading) : this._loadingView()
                        }
                        {
                            this.props.errorView ? this.props.errorView(this.state.showErrorView) : this._errorView()
                        }
                        {
                            this.props.pauseView ? this.props.pauseView(this.state.showPauseView) : this._pauseView()
                        }
                        {
                            this._noDataView()
                        }
                        {
                            this.state.isFullScreen ? this._renderLandscapeScreenVideoViewArea() : this._renderPortraitScreenVideoViewArea()
                        }
                        {
                          this._renderRecordingView()
                        }
                        {
                            this.props.videoSubView ? this.props.videoSubView(this.state.isFullScreen) : null
                        }
                    </View>
                </View>
                {/*全屏?null:渲染外层传入的UI*/}
                {this.state.isFullScreen ? null : this._renderPortraitScreenPlayerToolBarArea()}
                {showModel&&this.state.isFullScreen?this.getCalendarLandscapeView():this.getCalendarView()}
            </View>
        );
    }
}
