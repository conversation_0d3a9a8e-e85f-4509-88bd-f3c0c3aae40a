{"simplifyAbilityDTOs": [], "abilityDsl": {"events": [{"outputData": [{"identifier": "AlarmSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警开关"}, {"identifier": "WiFI_RSSI", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "-1", "step": "1"}, "type": "int"}, "name": "信号强度"}, {"identifier": "TempPasswordInfo", "dataType": {"specs": [{"identifier": "index", "dataType": {"specs": {"min": "0", "max": "16", "step": "1"}, "type": "int"}, "name": "密码索引"}, {"identifier": "timestamp", "dataType": {"specs": {}, "type": "date"}, "name": "时间戳"}], "type": "struct"}, "name": "临时密码信息"}, {"identifier": "isDoublePwd", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "双重验证"}, {"identifier": "VideoInterval", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "180", "step": "15"}, "type": "int"}, "name": "录像间隔"}, {"identifier": "PirRecordingTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "60", "step": "1"}, "type": "int"}, "name": "Pir录像时长"}, {"identifier": "StayTime", "dataType": {"specs": {"unit": "s", "min": "-1", "unitName": "秒", "max": "20", "step": "1"}, "type": "int"}, "name": "逗留侦测设置"}, {"identifier": "WDRSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "宽动态"}, {"identifier": "UnlockRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "OpenTime", "dataType": {"specs": {}, "type": "date"}, "name": "开锁时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁用户"}, {"identifier": "OpenType", "dataType": {"specs": {"22": "nfc加单结构光", "23": "结构光加结构光", "24": "添加密码", "25": "编辑密码", "26": "删除密码", "27": "添加指纹", "28": "编辑指纹", "29": "删除指纹", "30": "添加NFC", "31": "编辑NFC", "10": "nfc开锁", "32": "删除NFC", "11": "nfc加指纹", "33": "添加人脸", "12": "指纹加nfc", "34": "编辑人脸", "13": "nfc加密码", "35": "删除人脸", "14": "密码加nfc", "15": "nfc加nfc", "16": "单结构光", "17": "单结构光加指纹", "18": "单结构光加密码", "19": "单结构光加nfc", "0": "单密码开锁", "1": "单指纹开锁", "2": "临时密码开锁", "3": "密码加密码开锁", "4": "密码加指纹开锁", "5": "指纹加指纹开锁", "6": "App蓝牙开锁", "7": "门内开锁", "8": "门内上锁", "9": "门外上锁", "20": "指纹加单结构光", "21": "密码加单结构光"}, "type": "enum"}, "name": "开锁类型"}, {"identifier": "Message", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁信息"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "开锁记录"}, {"identifier": "SpeakerSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "扬声器开关"}, {"identifier": "StatusLightSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "状态灯开关"}, {"identifier": "FireAlarmUnlock", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "火警解锁"}, {"identifier": "LockPasswordInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "密码ID"}, {"identifier": "name", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "密码名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "10", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "密码信息列表"}, {"identifier": "StayDetectionNotification", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "逗留侦测推送开关"}, {"identifier": "StorageRecordMode", "dataType": {"specs": {"0": "不录像", "1": "事件录像", "2": "全天录像"}, "type": "enum"}, "name": "存储介质录像模式"}, {"identifier": "WiFI_SNR", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "127", "step": "1"}, "type": "int"}, "name": "信噪比"}, {"identifier": "EncryptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "视频加密开关"}, {"identifier": "EncryptTypeList", "dataType": {"specs": {"item": {"type": "int"}, "size": "128"}, "type": "array"}, "name": "加密类型列表"}, {"identifier": "WIFI_AP_BSSID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "热点BSSID"}, {"identifier": "CrySwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "哭声检测开关"}, {"identifier": "StorageStatus", "dataType": {"specs": {"0": "未插卡", "1": "正常", "2": "未格式化", "3": "正在格式化"}, "type": "enum"}, "name": "存储介质状态"}, {"identifier": "BluetoothMac", "dataType": {"specs": {"length": "24"}, "type": "text"}, "name": "设备的蓝牙MAC地址"}, {"identifier": "WIFI_Channel", "dataType": {"specs": {"min": "1", "unitName": "无", "max": "255", "step": "1"}, "type": "int"}, "name": "信道"}, {"identifier": "DeformationCorrect", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "enum"}, "name": "镜头畸变矫正开关"}, {"identifier": "VoiceDetectionSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "声音侦测灵敏度"}, {"identifier": "AlarmFrequencyLevel", "dataType": {"specs": {"0": "低频", "1": "中频", "2": "高频"}, "type": "enum"}, "name": "报警频率"}, {"identifier": "LockNfcInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfcID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfc名称"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "1", "max": "15", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "NFC信息列表"}, {"identifier": "SubStreamVideoQuality", "dataType": {"specs": {"1": "标清", "2": "高清"}, "type": "enum"}, "name": "辅码流视频质量"}, {"identifier": "SecurityRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "WarningType", "dataType": {"specs": {"11": "防火报警", "1": "挟持密码报警", "2": "挟持指纹报警", "3": "密码试探开门报警", "4": "指纹试探开门报警", "5": "防撬报警", "6": "低电量报警", "7": "开门未关超时报警", "8": "NFC试探开门报警", "9": "结构光试探开门报警", "10": "组合试探开门报警"}, "type": "enum"}, "name": "报警类型"}, {"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "用户名称"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "安全事件列表"}, {"identifier": "MicSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "麦克风开关"}, {"identifier": "DoorBellCallInterval", "dataType": {"specs": {"unit": "″", "min": "-1", "max": "60", "step": "1"}, "type": "int"}, "name": "按铃呼叫通知间隔"}, {"identifier": "DayNightMode", "dataType": {"specs": {"0": "白天模式", "1": "夜晚模式", "2": "自动模式"}, "type": "enum"}, "name": "日夜模式"}, {"identifier": "AlarmNotifyPlan", "dataType": {"specs": {"item": {"specs": [{"identifier": "BeginTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段开始秒数"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段结束秒数"}, {"identifier": "DayOfWeek", "dataType": {"specs": {"0": "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六"}, "type": "enum"}, "name": "每周日期"}], "type": "struct"}, "size": "128"}, "type": "array"}, "name": "报警提醒计划"}, {"identifier": "AlarmPromptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警提示开关"}, {"identifier": "CatEyeVersionInfo", "dataType": {"specs": [{"identifier": "android_version", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "Android版本号"}, {"identifier": "build_info", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "编译的版本号信息"}, {"identifier": "product_name", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "产品名称"}], "type": "struct"}, "name": "猫眼版本号信息"}, {"identifier": "StorageTotalCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "总存储空间"}, {"identifier": "LockFaceInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "人脸ID"}, {"identifier": "name", "dataType": {"specs": {"length": "40"}, "type": "text"}, "name": "人脸名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "人脸信息列表"}, {"identifier": "OSDSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "时间水印开关"}, {"identifier": "DirectUploadSupport", "dataType": {"specs": {"0": "不支持", "1": "支持"}, "type": "bool"}, "name": "是否支持直存"}, {"identifier": "RingBellWakeScreen", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃猫眼亮屏"}, {"identifier": "StreamVideoQuality", "dataType": {"specs": {"0": "流畅", "1": "标清", "2": "高清"}, "type": "enum"}, "name": "主码流视频质量"}, {"identifier": "LockFingerInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹ID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户指纹", "1": "临时指纹", "2": "报警指纹"}, "type": "enum"}, "name": "指纹属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "15", "step": "1"}, "type": "int"}, "name": "指纹长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "指纹信息列表"}, {"identifier": "WIFI_Band", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "频段"}, {"identifier": "ImageFlipState", "dataType": {"specs": {"0": "正常状态", "1": "翻转状态"}, "type": "enum"}, "name": "画面翻转状态"}, {"identifier": "RingBellCallSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃呼叫"}, {"identifier": "LockVersionInfo", "dataType": {"specs": [{"identifier": "LockSerialNumber", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁序列号"}, {"identifier": "LockMainVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁主控固件版本号"}, {"identifier": "LockSubVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁转接板版本号"}, {"identifier": "FingerprintVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "指纹版本号"}, {"identifier": "TouchVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "触摸板版本号"}], "type": "struct"}, "name": "门锁设备版本信息"}, {"identifier": "MotionDetectSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "移动侦测灵敏度"}, {"identifier": "BatteryLevel", "dataType": {"specs": {"unit": "%", "min": "0", "unitName": "百分比", "max": "100", "step": "1"}, "type": "int"}, "name": "电池电量"}, {"identifier": "StorageRemainCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "剩余存储空间"}, {"identifier": "DeviceLocation", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "经纬度设置"}, {"identifier": "VoiceIntercomType", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "999", "step": "1"}, "type": "int"}, "name": "语音对讲类型列表"}, {"identifier": "PreRecordSupport", "dataType": {"specs": {"0": "不支持", "1": "支持"}, "type": "bool"}, "name": "是否支持预录"}, {"identifier": "RingBellPushSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃推送开关"}, {"identifier": "Warning", "dataType": {"specs": {"min": "0", "max": "1", "step": "1"}, "type": "int"}, "name": "触发报警开关"}, {"identifier": "PirLevel", "dataType": {"specs": {"min": "0", "max": "3", "step": "1"}, "type": "int"}, "name": "Pir触发等级"}, {"identifier": "DeviceTimeZoneID", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "设备时区信息"}, {"identifier": "DoorStatus", "dataType": {"specs": {"0": "关门", "1": "开门"}, "type": "enum"}, "name": "门的状态"}, {"identifier": "NordicBtVersion", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "Noridc蓝牙版本号"}, {"identifier": "WifiMac", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "设备的WifiMAC地址"}, {"identifier": "InnerVolume", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "门内音量"}], "identifier": "post", "method": "thing.event.property.post", "name": "post", "type": "info", "required": true, "desc": "属性上报"}, {"outputData": [{"identifier": "AlarmType", "dataType": {"specs": {"1": "移动侦测", "2": "声音侦测", "3": "按门铃", "4": "检测到异常"}, "type": "enum"}, "name": "告警类型"}, {"identifier": "AlarmPicID", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "报警抓图ID"}, {"identifier": "Data", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "告警内容"}], "identifier": "AlarmEvent", "method": "thing.event.AlarmEvent.post", "name": "侦测报警", "type": "alert", "required": false}, {"outputData": [{"identifier": "Count", "dataType": {"specs": {"min": "1", "max": "100", "step": "1"}, "type": "int"}, "name": "个数"}, {"identifier": "DataType", "dataType": {"specs": {"length": "512"}, "type": "text"}, "name": "上传类型_META_云存储_LOG_日志_TCARD_T卡"}, {"identifier": "FileType", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "文件类型"}], "identifier": "RequireUploadUrl", "method": "thing.event.RequireUploadUrl.post", "name": "请求文件上传URL", "type": "info", "required": false, "desc": "请求文件上传URL"}, {"outputData": [{"identifier": "Data", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "信令数据"}], "identifier": "P2PSignalUpstream", "method": "thing.event.P2PSignalUpstream.post", "name": "P2P信令上报", "type": "info", "required": false}, {"outputData": [], "identifier": "onPlayBackEnd", "method": "thing.event.onPlayBackEnd.post", "name": "回看播放结束事件", "type": "info", "required": false, "desc": "回看播放结束事件"}, {"outputData": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "人脸ID"}], "identifier": "FaceUpload", "method": "thing.event.FaceUpload.post", "name": "请求上传人脸图片", "type": "info", "required": false}, {"outputData": [{"identifier": "CameraId", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "摄像机ID"}, {"identifier": "StartTime", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "Sid", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "Session"}, {"identifier": "VideoPath", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "视频路径"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "截图路径"}, {"identifier": "IndexPath", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "索引路径"}, {"identifier": "VideoKey", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "视频密钥"}, {"identifier": "SessionIndex", "dataType": {"specs": {"min": "0", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "Session下标"}, {"identifier": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "截图密钥"}, {"identifier": "IndexKey", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "索引密钥"}, {"identifier": "VideoSize", "dataType": {"specs": {"unit": "MB", "min": "0.01", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "视频大小"}, {"identifier": "ThumbSize", "dataType": {"specs": {"unit": "MB", "min": "0.01", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "截图大小"}, {"identifier": "IndexSize", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "索引大小"}, {"identifier": "IntelligentTypeList", "dataType": {"specs": [{"identifier": "Move", "dataType": {"specs": {"0": "无移动侦测", "1": "有移动侦测"}, "type": "bool"}, "name": "移动"}, {"identifier": "HumanShape", "dataType": {"specs": {"0": "无人形侦测", "1": "有人形侦测"}, "type": "bool"}, "name": "人形"}, {"identifier": "HumanFace", "dataType": {"specs": {"0": "无人脸侦测", "1": "有人脸侦测"}, "type": "bool"}, "name": "人脸"}, {"identifier": "Cry", "dataType": {"specs": {"0": "无哭声检测", "1": "有哭声检测"}, "type": "bool"}, "name": "哭声"}, {"identifier": "Goods", "dataType": {"specs": {"0": "无物品", "1": "有物品"}, "type": "bool"}, "name": "物品"}, {"identifier": "Car", "dataType": {"specs": {"0": "无车辆", "1": "有车辆"}, "type": "bool"}, "name": "车辆"}, {"identifier": "Animal", "dataType": {"specs": {"0": "无动物", "1": "有动物"}, "type": "bool"}, "name": "动物"}, {"identifier": "<PERSON><PERSON>", "dataType": {"specs": {"0": "无逗留", "1": "有逗留"}, "type": "bool"}, "name": "逗留"}, {"identifier": "Bell", "dataType": {"specs": {"0": "无按铃", "1": "有按铃"}, "type": "bool"}, "name": "按铃"}, {"identifier": "Demolition", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "异常事件"}], "type": "struct"}, "name": "智能报警类型"}, {"identifier": "SessionType", "dataType": {"specs": {"0": "Session开始-第一片", "1": "Session结束-最后一片", "2": "Session仅一片", "3": "Session中间片"}, "type": "enum"}, "name": "Session类型"}, {"identifier": "Tag", "dataType": {"specs": {"length": "512"}, "type": "text"}, "name": "获取URL时候的TAG"}], "identifier": "CloudStorageReportMeta", "method": "thing.event.CloudStorageReportMeta.post", "name": "云存储上报元数据", "type": "info", "required": false, "desc": "上报视频元数据"}, {"outputData": [{"identifier": "state", "dataType": {"specs": {"min": "0", "max": "10", "step": "1"}, "type": "int"}, "name": "状态码"}, {"identifier": "data", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "此次步骤返回的值"}], "identifier": "onAddGatewaySubDeviceState", "method": "thing.event.onAddGatewaySubDeviceState.post", "name": "网关添加子设备状态回调", "type": "info", "required": false, "desc": "//上报添加状态给插件端 添加状态，0：没开始添加，1：设备端收到开始添加消息 2：搜索设备中 3：注册 4：登陆以及物模型通道 5：添加完成结束"}, {"outputData": [{"identifier": "WarningType", "dataType": {"specs": {"11": "防火报警", "1": "挟持密码报警", "2": "挟持指纹报警", "3": "密码试探开门报警", "4": "指纹试探开门报警", "5": "防撬报警", "6": "低电量报警", "7": "开门未关超时报警", "8": "NFC试探开门报警", "9": "结构光试探开门报警", "10": "组合试探开门报警"}, "type": "enum"}, "name": "报警类型"}, {"identifier": "Battery", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "电池电量"}, {"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间"}, {"identifier": "PushMessage", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "告警文案"}], "identifier": "LockWarningEvent", "method": "thing.event.LockWarningEvent.post", "name": "门锁报警事件", "type": "alert", "required": false}, {"outputData": [{"identifier": "ErrorCode", "dataType": {"specs": {"0": "恢复正常", "1": "网络异常", "2": "存储介质异常"}, "type": "enum"}, "name": "故障代码"}], "identifier": "Error", "method": "thing.event.Error.post", "name": "故障上报", "type": "error", "required": false}, {"outputData": [{"identifier": "Type", "dataType": {"specs": {"11": "nfc-指纹", "22": "nfc-单结构光", "12": "指纹-nfc", "23": "单结构光-结构光", "13": "nfc-密码", "14": "密码-nfc", "15": "nfc-nfc", "16": "单结构光", "17": "单结构光-指纹", "18": "单结构光-密码", "19": "单结构光-nfc", "0": "密码", "1": "指纹", "2": "一次性密码", "3": "密码-密码", "4": "密码-指纹", "5": "指纹-指纹", "6": "App蓝牙", "7": "门内开锁", "8": "门内上锁", "9": "门外上锁", "20": "指纹-单结构光", "10": "nfc开锁", "21": "密码-单结构光"}, "type": "enum"}, "name": "开锁类型"}, {"identifier": "ID", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "密码指纹id"}, {"identifier": "Time", "dataType": {"specs": {}, "type": "date"}, "name": "开锁时间"}, {"identifier": "PushMessage", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "Push消息文案"}], "identifier": "LockUnlockEvent", "method": "thing.event.LockUnlockEvent.post", "name": "门锁开锁事件", "type": "info", "required": false, "desc": "开锁事件"}, {"outputData": [{"identifier": "OTA_Status", "dataType": {"specs": {"0": "未在进行固件升级", "1": "正在进行固件升级"}, "type": "bool"}, "name": "固件升级状态"}], "identifier": "ota_status", "method": "thing.event.ota_status.post", "name": "固件升级", "type": "info", "required": false}, {"outputData": [{"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间"}, {"identifier": "PushMessage", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "告警文案"}, {"identifier": "Battery", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "电池电量"}], "identifier": "FireWarningEvent", "method": "thing.event.FireWarningEvent.post", "name": "防火报警事件", "type": "info", "required": false}, {"outputData": [{"identifier": "SubDeviceInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "pk", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "pk"}, {"identifier": "protocol", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "protocol"}, {"identifier": "dsp", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "dsp"}, {"identifier": "pid", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "pid"}, {"identifier": "did", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "did"}, {"identifier": "id", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "id"}, {"identifier": "detail", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "detail"}], "type": "struct"}, "size": "10"}, "type": "array"}, "name": "SubDeviceInfoList"}], "identifier": "SubNoBindDeviceListEvent", "method": "thing.event.SubNoBindDeviceListEvent.post", "name": "上报发现的设备list信息", "type": "info", "required": false, "desc": "detail字段存储额外的附属信息（例如威斯丹丽的：deviceType、zoneId），字符串内容可以由固件端定义（比如可以用json字符串），云端统一以字符串形式存储和下发"}, {"outputData": [{"identifier": "param1", "dataType": {"specs": {"0": "0", "1": "1"}, "type": "bool"}, "name": "param1"}], "identifier": "CryEvent", "method": "thing.event.CryEvent.post", "name": "哭声检测上报", "type": "alert", "required": false}, {"outputData": [{"identifier": "Action", "dataType": {"specs": {"0": "创建", "1": "修改", "2": "删除"}, "type": "enum"}, "name": "操作"}, {"identifier": "ID", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "指纹id"}, {"identifier": "Index", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "索引key"}, {"identifier": "Result", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "操作结果"}, {"identifier": "Error", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "错误信息"}, {"identifier": "Name", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "指纹名称"}, {"identifier": "CreateTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "ValidityType", "dataType": {"specs": {"0": "1天", "1": "1周", "2": "自定义时间段", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时效类型"}, {"identifier": "StartDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "开始日期时间"}, {"identifier": "EndDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "结束日期时间"}, {"identifier": "Repeat", "dataType": {"specs": {"length": "8"}, "type": "text"}, "name": "重复"}], "identifier": "LockSetFingerprintEvent", "method": "thing.event.LockSetFingerprintEvent.post", "name": "门锁设置指纹事件", "type": "info", "required": false}, {"outputData": [{"identifier": "PublicKey", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "PublicKey"}, {"identifier": "Sign", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "Sign"}], "identifier": "CurveKeyUpgrading", "method": "thing.event.CurveKeyUpgrading.post", "name": "<PERSON>urve<PERSON><PERSON>同步更新", "type": "info", "required": false, "desc": "1、PublicKey传Base64加密后的Curve-publicKey\n2、Sign传Hmac256加密publicKey和deviceSecret后生成的Sign再Base64编码"}, {"outputData": [{"identifier": "Result", "dataType": {"specs": {"0": "初始化失败", "1": "初始化完毕"}, "type": "bool"}, "name": "结果"}], "identifier": "DeviceInitResult", "method": "thing.event.DeviceInitResult.post", "name": "设备初始化结果", "type": "info", "required": false, "desc": "设备初始化结果"}, {"outputData": [{"identifier": "NendPush", "dataType": {"specs": {"0": "不需要", "1": "需要"}, "type": "enum"}, "name": "是否推送"}, {"identifier": "VoiceType", "dataType": {"specs": {"1": "门铃", "2": "锁已开", "3": "锁已关", "4": "滴滴音"}, "type": "enum"}, "name": "播放语音选择"}, {"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间戳"}, {"identifier": "PushMessage", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "推送文案"}, {"identifier": "NeedCall", "dataType": {"specs": {"0": "否", "1": "是"}, "type": "enum"}, "name": "是否呼叫"}], "identifier": "DoorBellEvent", "method": "thing.event.DoorBellEvent.post", "name": "门铃事件", "type": "info", "required": false}, {"outputData": [], "identifier": "SubBindDeviceListEvent", "method": "thing.event.SubBindDeviceListEvent.post", "name": "请求已绑定子设备列表", "type": "info", "required": false}, {"outputData": [{"identifier": "timeStamp", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "timeStamp"}], "identifier": "BizProductConfigInfoE", "method": "thing.event.BizProductConfigInfoE.post", "name": "获取厂商产品配置信息", "type": "info", "required": false, "desc": "获取厂商产品配置信息"}, {"outputData": [{"identifier": "Result", "dataType": {"specs": {"0": "成功", "1": "网络异常", "2": "鉴权错误", "3": "文件内容错误"}, "type": "enum"}, "name": "上传结果"}, {"identifier": "ErrorMessage", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "错误描述"}, {"identifier": "PicInfo", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "图片信息"}], "identifier": "PicUploadResult", "method": "thing.event.PicUploadResult.post", "name": "图片上传结果", "type": "info", "required": false}, {"outputData": [{"identifier": "protocol", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "protocol"}, {"identifier": "dsp", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "dsp"}, {"identifier": "pid", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "pid"}, {"identifier": "did", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "did"}, {"identifier": "id", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "id"}], "identifier": "BindSubDevice", "method": "thing.event.BindSubDevice.post", "name": "添加设备到网关", "type": "info", "required": false, "desc": "使用三元组添加设备到网关"}, {"outputData": [{"identifier": "currentData", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "currentData"}], "identifier": "LoginStart", "method": "thing.event.LoginStart.post", "name": "触发登录", "type": "info", "required": false}, {"outputData": [{"identifier": "DeleteItems", "dataType": {"specs": {"item": {"specs": [{"identifier": "ItemType", "dataType": {"specs": {"1": "密码", "2": "指纹", "3": "人脸", "4": "nfc"}, "type": "enum"}, "name": "删除项类型"}, {"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "删除项id"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "删除项"}], "identifier": "DeleteLockItemEvent", "method": "thing.event.DeleteLockItemEvent.post", "name": "删除云端列表记录", "type": "info", "required": false, "desc": "删除云端密码/指纹/nfc/人脸记录"}, {"outputData": [{"identifier": "OTA_Status", "dataType": {"specs": {"0": "未在进行固件升级", "1": "正在进行固件升级"}, "type": "bool"}, "name": "固件升级状态"}], "identifier": "OTA_Success_Event", "method": "thing.event.OTA_Success_Event.post", "name": "固件升级成功", "type": "info", "required": false}, {"outputData": [{"identifier": "Action", "dataType": {"specs": {"0": "创建", "1": "修改", "2": "删除"}, "type": "enum"}, "name": "操作"}, {"identifier": "ID", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "密码id"}, {"identifier": "Index", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "索引key"}, {"identifier": "Result", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "操作结果"}, {"identifier": "Error", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "错误信息"}, {"identifier": "Type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码类型"}, {"identifier": "CodeMask", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "密码掩饰值"}, {"identifier": "Name", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "密码名称"}, {"identifier": "CreateTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "ValidityType", "dataType": {"specs": {"0": "1天", "1": "1周", "2": "自定义时间段", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时效类型"}, {"identifier": "StartDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "开始日期时间"}, {"identifier": "EndDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "结束日期时间"}, {"identifier": "Repeat", "dataType": {"specs": {"length": "8"}, "type": "text"}, "name": "重复"}], "identifier": "LockSetPasswordEvent", "method": "thing.event.LockSetPasswordEvent.post", "name": "门锁设置密码事件", "type": "info", "required": false, "desc": "门锁密码设置的上报事件"}], "profile": {"productKey": "a1yMb5JVWDa"}, "properties": [{"identifier": "AlarmSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警开关", "accessMode": "rw", "required": false}, {"identifier": "WiFI_RSSI", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "-1", "step": "1"}, "type": "int"}, "name": "信号强度", "accessMode": "rw", "required": false}, {"identifier": "TempPasswordInfo", "dataType": {"specs": [{"identifier": "index", "dataType": {"specs": {"min": "0", "max": "16", "step": "1"}, "type": "int"}, "name": "密码索引"}, {"identifier": "timestamp", "dataType": {"specs": {}, "type": "date"}, "name": "时间戳"}], "type": "struct"}, "name": "临时密码信息", "accessMode": "rw", "required": false, "desc": "记录当前临时密码的索引和生成时间戳"}, {"identifier": "isDoublePwd", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "双重验证", "accessMode": "rw", "required": false, "desc": "是否开启了双重验证 关：0，开：1"}, {"identifier": "VideoInterval", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "180", "step": "15"}, "type": "int"}, "name": "录像间隔", "accessMode": "rw", "required": false, "desc": "取值 0,15,30,60,180"}, {"identifier": "PirRecordingTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "60", "step": "1"}, "type": "int"}, "name": "Pir录像时长", "accessMode": "rw", "required": false, "desc": "0 代表自动模式PIR侦测到看护区域内热量发生变化，通过本地人形侦测到有人形，才启动拍摄视频，一直拍摄至人形离开看护区域为止（最长60s）\n5~60 自定义模式：5秒至60秒，每秒进行调节"}, {"identifier": "StayTime", "dataType": {"specs": {"unit": "s", "min": "-1", "unitName": "秒", "max": "20", "step": "1"}, "type": "int"}, "name": "逗留侦测设置", "accessMode": "rw", "required": false, "desc": "逗留侦测设置 取值 -1，0，5，10，15，20 （-1表示关闭，默认是0）"}, {"identifier": "WDRSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "宽动态", "accessMode": "rw", "required": false, "desc": "宽动态    0 关闭 1开启"}, {"identifier": "UnlockRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "OpenTime", "dataType": {"specs": {}, "type": "date"}, "name": "开锁时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁用户"}, {"identifier": "OpenType", "dataType": {"specs": {"22": "nfc加单结构光", "23": "结构光加结构光", "24": "添加密码", "25": "编辑密码", "26": "删除密码", "27": "添加指纹", "28": "编辑指纹", "29": "删除指纹", "30": "添加NFC", "31": "编辑NFC", "10": "nfc开锁", "32": "删除NFC", "11": "nfc加指纹", "33": "添加人脸", "12": "指纹加nfc", "34": "编辑人脸", "13": "nfc加密码", "35": "删除人脸", "14": "密码加nfc", "15": "nfc加nfc", "16": "单结构光", "17": "单结构光加指纹", "18": "单结构光加密码", "19": "单结构光加nfc", "0": "单密码开锁", "1": "单指纹开锁", "2": "临时密码开锁", "3": "密码加密码开锁", "4": "密码加指纹开锁", "5": "指纹加指纹开锁", "6": "App蓝牙开锁", "7": "门内开锁", "8": "门内上锁", "9": "门外上锁", "20": "指纹加单结构光", "21": "密码加单结构光"}, "type": "enum"}, "name": "开锁类型"}, {"identifier": "Message", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁信息"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "开锁记录", "accessMode": "rw", "required": false, "desc": "开锁记录上传云端"}, {"identifier": "SpeakerSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "扬声器开关", "accessMode": "rw", "required": false}, {"identifier": "StatusLightSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "状态灯开关", "accessMode": "rw", "required": false}, {"identifier": "FireAlarmUnlock", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "火警解锁", "accessMode": "rw", "required": false, "desc": "防火报警时是否自动开锁，0为不会自动开锁，1为会自动开锁"}, {"identifier": "LockPasswordInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "密码ID"}, {"identifier": "name", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "密码名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "10", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "密码信息列表", "accessMode": "rw", "required": false}, {"identifier": "StayDetectionNotification", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "逗留侦测推送开关", "accessMode": "rw", "required": false}, {"identifier": "StorageRecordMode", "dataType": {"specs": {"0": "不录像", "1": "事件录像", "2": "全天录像"}, "type": "enum"}, "name": "存储介质录像模式", "accessMode": "rw", "required": false}, {"identifier": "WiFI_SNR", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "127", "step": "1"}, "type": "int"}, "name": "信噪比", "accessMode": "rw", "required": false}, {"identifier": "EncryptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "视频加密开关", "accessMode": "rw", "required": false}, {"identifier": "EncryptTypeList", "dataType": {"specs": {"item": {"type": "int"}, "size": "128"}, "type": "array"}, "name": "加密类型列表", "accessMode": "r", "required": false}, {"identifier": "WIFI_AP_BSSID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "热点BSSID", "accessMode": "rw", "required": false}, {"identifier": "CrySwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "哭声检测开关", "accessMode": "rw", "required": false}, {"identifier": "StorageStatus", "dataType": {"specs": {"0": "未插卡", "1": "正常", "2": "未格式化", "3": "正在格式化"}, "type": "enum"}, "name": "存储介质状态", "accessMode": "rw", "required": false}, {"identifier": "BluetoothMac", "dataType": {"specs": {"length": "24"}, "type": "text"}, "name": "设备的蓝牙MAC地址", "accessMode": "rw", "required": false, "desc": "设备的蓝牙MAC地址"}, {"identifier": "WIFI_Channel", "dataType": {"specs": {"min": "1", "unitName": "无", "max": "255", "step": "1"}, "type": "int"}, "name": "信道", "accessMode": "rw", "required": false}, {"identifier": "DeformationCorrect", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "enum"}, "name": "镜头畸变矫正开关", "accessMode": "rw", "required": false}, {"identifier": "VoiceDetectionSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "声音侦测灵敏度", "accessMode": "rw", "required": false}, {"identifier": "AlarmFrequencyLevel", "dataType": {"specs": {"0": "低频", "1": "中频", "2": "高频"}, "type": "enum"}, "name": "报警频率", "accessMode": "rw", "required": false}, {"identifier": "LockNfcInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfcID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfc名称"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "1", "max": "15", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "NFC信息列表", "accessMode": "rw", "required": false, "desc": "nfc信息存储物模型"}, {"identifier": "SubStreamVideoQuality", "dataType": {"specs": {"1": "标清", "2": "高清"}, "type": "enum"}, "name": "辅码流视频质量", "accessMode": "rw", "required": false}, {"identifier": "SecurityRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "WarningType", "dataType": {"specs": {"11": "防火报警", "1": "挟持密码报警", "2": "挟持指纹报警", "3": "密码试探开门报警", "4": "指纹试探开门报警", "5": "防撬报警", "6": "低电量报警", "7": "开门未关超时报警", "8": "NFC试探开门报警", "9": "结构光试探开门报警", "10": "组合试探开门报警"}, "type": "enum"}, "name": "报警类型"}, {"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "用户名称"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "安全事件列表", "accessMode": "rw", "required": false, "desc": "安全事件列表"}, {"identifier": "MicSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "麦克风开关", "accessMode": "rw", "required": false}, {"identifier": "DoorBellCallInterval", "dataType": {"specs": {"unit": "″", "min": "-1", "max": "60", "step": "1"}, "type": "int"}, "name": "按铃呼叫通知间隔", "accessMode": "r", "required": false}, {"identifier": "DayNightMode", "dataType": {"specs": {"0": "白天模式", "1": "夜晚模式", "2": "自动模式"}, "type": "enum"}, "name": "日夜模式", "accessMode": "rw", "required": false}, {"identifier": "AlarmNotifyPlan", "dataType": {"specs": {"item": {"specs": [{"identifier": "BeginTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段开始秒数"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段结束秒数"}, {"identifier": "DayOfWeek", "dataType": {"specs": {"0": "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六"}, "type": "enum"}, "name": "每周日期"}], "type": "struct"}, "size": "128"}, "type": "array"}, "name": "报警提醒计划", "accessMode": "rw", "required": false, "desc": "报警时间段列表，其中的BeginTine和EndTime分别代表时间段开始和结束的秒数，0表示00:00:00，86399表示23:59:59"}, {"identifier": "AlarmPromptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警提示开关", "accessMode": "rw", "required": false}, {"identifier": "CatEyeVersionInfo", "dataType": {"specs": [{"identifier": "android_version", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "Android版本号"}, {"identifier": "build_info", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "编译的版本号信息"}, {"identifier": "product_name", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "产品名称"}], "type": "struct"}, "name": "猫眼版本号信息", "accessMode": "r", "required": false, "desc": "猫眼版本号信息"}, {"identifier": "StorageTotalCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "总存储空间", "accessMode": "rw", "required": false}, {"identifier": "LockFaceInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "人脸ID"}, {"identifier": "name", "dataType": {"specs": {"length": "40"}, "type": "text"}, "name": "人脸名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "人脸信息列表", "accessMode": "rw", "required": false}, {"identifier": "OSDSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "时间水印开关", "accessMode": "rw", "required": false, "desc": "时间水印开关 0 关闭 1开启"}, {"identifier": "DirectUploadSupport", "dataType": {"specs": {"0": "不支持", "1": "支持"}, "type": "bool"}, "name": "是否支持直存", "accessMode": "r", "required": false}, {"identifier": "RingBellWakeScreen", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃猫眼亮屏", "accessMode": "rw", "required": false, "desc": "0关闭该功能，1开启该功能"}, {"identifier": "StreamVideoQuality", "dataType": {"specs": {"0": "流畅", "1": "标清", "2": "高清"}, "type": "enum"}, "name": "主码流视频质量", "accessMode": "rw", "required": false}, {"identifier": "LockFingerInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹ID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户指纹", "1": "临时指纹", "2": "报警指纹"}, "type": "enum"}, "name": "指纹属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "15", "step": "1"}, "type": "int"}, "name": "指纹长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "指纹信息列表", "accessMode": "rw", "required": false, "desc": "指纹信息列表"}, {"identifier": "WIFI_Band", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "频段", "accessMode": "rw", "required": false}, {"identifier": "ImageFlipState", "dataType": {"specs": {"0": "正常状态", "1": "翻转状态"}, "type": "enum"}, "name": "画面翻转状态", "accessMode": "rw", "required": false}, {"identifier": "RingBellCallSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃呼叫", "accessMode": "rw", "required": false, "desc": "按铃呼叫设置：0 关闭呼叫开关，1打开呼叫开关"}, {"identifier": "LockVersionInfo", "dataType": {"specs": [{"identifier": "LockSerialNumber", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁序列号"}, {"identifier": "LockMainVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁主控固件版本号"}, {"identifier": "LockSubVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁转接板版本号"}, {"identifier": "FingerprintVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "指纹版本号"}, {"identifier": "TouchVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "触摸板版本号"}], "type": "struct"}, "name": "门锁设备版本信息", "accessMode": "rw", "required": false}, {"identifier": "MotionDetectSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "移动侦测灵敏度", "accessMode": "rw", "required": false}, {"identifier": "BatteryLevel", "dataType": {"specs": {"unit": "%", "min": "0", "unitName": "百分比", "max": "100", "step": "1"}, "type": "int"}, "name": "电池电量", "accessMode": "r", "required": false}, {"identifier": "StorageRemainCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "剩余存储空间", "accessMode": "rw", "required": false}, {"identifier": "DeviceLocation", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "经纬度设置", "accessMode": "rw", "required": false, "desc": "设置经纬度， longitude,latitude   例子：121.400,31.166（保留小数点后三位, 英文,分割）"}, {"identifier": "VoiceIntercomType", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "999", "step": "1"}, "type": "int"}, "name": "语音对讲类型列表", "accessMode": "r", "required": false}, {"identifier": "PreRecordSupport", "dataType": {"specs": {"0": "不支持", "1": "支持"}, "type": "bool"}, "name": "是否支持预录", "accessMode": "r", "required": false}, {"identifier": "RingBellPushSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃推送开关", "accessMode": "rw", "required": false}, {"identifier": "Warning", "dataType": {"specs": {"min": "0", "max": "1", "step": "1"}, "type": "int"}, "name": "触发报警开关", "accessMode": "rw", "required": false, "desc": "插件端的报警按钮，为1的时候开始设备端进行报警，为0的时候关闭报警"}, {"identifier": "PirLevel", "dataType": {"specs": {"min": "0", "max": "3", "step": "1"}, "type": "int"}, "name": "Pir触发等级", "accessMode": "rw", "required": false, "desc": "3:far  2:middle 1:near"}, {"identifier": "DeviceTimeZoneID", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "设备时区信息", "accessMode": "rw", "required": false, "desc": "设备时区，app统一下发，智慧门项目暂不处理"}, {"identifier": "DoorStatus", "dataType": {"specs": {"0": "关门", "1": "开门"}, "type": "enum"}, "name": "门的状态", "accessMode": "rw", "required": false, "desc": "门的状态 0 为关，1为开"}, {"identifier": "NordicBtVersion", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "Noridc蓝牙版本号", "accessMode": "rw", "required": false}, {"identifier": "WifiMac", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "设备的WifiMAC地址", "accessMode": "rw", "required": false}, {"identifier": "InnerVolume", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "门内音量", "accessMode": "rw", "required": false, "desc": "内容推荐"}], "schema": "https://iotx-tsl.oss-ap-southeast-1.aliyuncs.com/schema.json", "services": [{"outputData": [], "identifier": "set", "inputData": [{"identifier": "AlarmSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警开关"}, {"identifier": "WiFI_RSSI", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "-1", "step": "1"}, "type": "int"}, "name": "信号强度"}, {"identifier": "TempPasswordInfo", "dataType": {"specs": [{"identifier": "index", "dataType": {"specs": {"min": "0", "max": "16", "step": "1"}, "type": "int"}, "name": "密码索引"}, {"identifier": "timestamp", "dataType": {"specs": {}, "type": "date"}, "name": "时间戳"}], "type": "struct"}, "name": "临时密码信息"}, {"identifier": "isDoublePwd", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "双重验证"}, {"identifier": "VideoInterval", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "180", "step": "15"}, "type": "int"}, "name": "录像间隔"}, {"identifier": "PirRecordingTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "60", "step": "1"}, "type": "int"}, "name": "Pir录像时长"}, {"identifier": "StayTime", "dataType": {"specs": {"unit": "s", "min": "-1", "unitName": "秒", "max": "20", "step": "1"}, "type": "int"}, "name": "逗留侦测设置"}, {"identifier": "WDRSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "宽动态"}, {"identifier": "UnlockRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "OpenTime", "dataType": {"specs": {}, "type": "date"}, "name": "开锁时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁用户"}, {"identifier": "OpenType", "dataType": {"specs": {"22": "nfc加单结构光", "23": "结构光加结构光", "24": "添加密码", "25": "编辑密码", "26": "删除密码", "27": "添加指纹", "28": "编辑指纹", "29": "删除指纹", "30": "添加NFC", "31": "编辑NFC", "10": "nfc开锁", "32": "删除NFC", "11": "nfc加指纹", "33": "添加人脸", "12": "指纹加nfc", "34": "编辑人脸", "13": "nfc加密码", "35": "删除人脸", "14": "密码加nfc", "15": "nfc加nfc", "16": "单结构光", "17": "单结构光加指纹", "18": "单结构光加密码", "19": "单结构光加nfc", "0": "单密码开锁", "1": "单指纹开锁", "2": "临时密码开锁", "3": "密码加密码开锁", "4": "密码加指纹开锁", "5": "指纹加指纹开锁", "6": "App蓝牙开锁", "7": "门内开锁", "8": "门内上锁", "9": "门外上锁", "20": "指纹加单结构光", "21": "密码加单结构光"}, "type": "enum"}, "name": "开锁类型"}, {"identifier": "Message", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁信息"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "开锁记录"}, {"identifier": "SpeakerSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "扬声器开关"}, {"identifier": "StatusLightSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "状态灯开关"}, {"identifier": "FireAlarmUnlock", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "火警解锁"}, {"identifier": "LockPasswordInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "密码ID"}, {"identifier": "name", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "密码名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "10", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "密码信息列表"}, {"identifier": "StayDetectionNotification", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "逗留侦测推送开关"}, {"identifier": "StorageRecordMode", "dataType": {"specs": {"0": "不录像", "1": "事件录像", "2": "全天录像"}, "type": "enum"}, "name": "存储介质录像模式"}, {"identifier": "WiFI_SNR", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "127", "step": "1"}, "type": "int"}, "name": "信噪比"}, {"identifier": "EncryptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "视频加密开关"}, {"identifier": "WIFI_AP_BSSID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "热点BSSID"}, {"identifier": "CrySwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "哭声检测开关"}, {"identifier": "StorageStatus", "dataType": {"specs": {"0": "未插卡", "1": "正常", "2": "未格式化", "3": "正在格式化"}, "type": "enum"}, "name": "存储介质状态"}, {"identifier": "BluetoothMac", "dataType": {"specs": {"length": "24"}, "type": "text"}, "name": "设备的蓝牙MAC地址"}, {"identifier": "WIFI_Channel", "dataType": {"specs": {"min": "1", "unitName": "无", "max": "255", "step": "1"}, "type": "int"}, "name": "信道"}, {"identifier": "DeformationCorrect", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "enum"}, "name": "镜头畸变矫正开关"}, {"identifier": "VoiceDetectionSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "声音侦测灵敏度"}, {"identifier": "AlarmFrequencyLevel", "dataType": {"specs": {"0": "低频", "1": "中频", "2": "高频"}, "type": "enum"}, "name": "报警频率"}, {"identifier": "LockNfcInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfcID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfc名称"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "1", "max": "15", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "NFC信息列表"}, {"identifier": "SubStreamVideoQuality", "dataType": {"specs": {"1": "标清", "2": "高清"}, "type": "enum"}, "name": "辅码流视频质量"}, {"identifier": "SecurityRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "WarningType", "dataType": {"specs": {"11": "防火报警", "1": "挟持密码报警", "2": "挟持指纹报警", "3": "密码试探开门报警", "4": "指纹试探开门报警", "5": "防撬报警", "6": "低电量报警", "7": "开门未关超时报警", "8": "NFC试探开门报警", "9": "结构光试探开门报警", "10": "组合试探开门报警"}, "type": "enum"}, "name": "报警类型"}, {"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "用户名称"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "安全事件列表"}, {"identifier": "MicSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "麦克风开关"}, {"identifier": "DayNightMode", "dataType": {"specs": {"0": "白天模式", "1": "夜晚模式", "2": "自动模式"}, "type": "enum"}, "name": "日夜模式"}, {"identifier": "AlarmNotifyPlan", "dataType": {"specs": {"item": {"specs": [{"identifier": "BeginTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段开始秒数"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段结束秒数"}, {"identifier": "DayOfWeek", "dataType": {"specs": {"0": "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六"}, "type": "enum"}, "name": "每周日期"}], "type": "struct"}, "size": "128"}, "type": "array"}, "name": "报警提醒计划"}, {"identifier": "AlarmPromptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警提示开关"}, {"identifier": "StorageTotalCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "总存储空间"}, {"identifier": "LockFaceInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "人脸ID"}, {"identifier": "name", "dataType": {"specs": {"length": "40"}, "type": "text"}, "name": "人脸名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "人脸信息列表"}, {"identifier": "OSDSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "时间水印开关"}, {"identifier": "RingBellWakeScreen", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃猫眼亮屏"}, {"identifier": "StreamVideoQuality", "dataType": {"specs": {"0": "流畅", "1": "标清", "2": "高清"}, "type": "enum"}, "name": "主码流视频质量"}, {"identifier": "LockFingerInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹ID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户指纹", "1": "临时指纹", "2": "报警指纹"}, "type": "enum"}, "name": "指纹属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "15", "step": "1"}, "type": "int"}, "name": "指纹长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "指纹信息列表"}, {"identifier": "WIFI_Band", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "频段"}, {"identifier": "ImageFlipState", "dataType": {"specs": {"0": "正常状态", "1": "翻转状态"}, "type": "enum"}, "name": "画面翻转状态"}, {"identifier": "RingBellCallSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃呼叫"}, {"identifier": "LockVersionInfo", "dataType": {"specs": [{"identifier": "LockSerialNumber", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁序列号"}, {"identifier": "LockMainVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁主控固件版本号"}, {"identifier": "LockSubVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁转接板版本号"}, {"identifier": "FingerprintVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "指纹版本号"}, {"identifier": "TouchVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "触摸板版本号"}], "type": "struct"}, "name": "门锁设备版本信息"}, {"identifier": "MotionDetectSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "移动侦测灵敏度"}, {"identifier": "StorageRemainCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "剩余存储空间"}, {"identifier": "DeviceLocation", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "经纬度设置"}, {"identifier": "RingBellPushSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃推送开关"}, {"identifier": "Warning", "dataType": {"specs": {"min": "0", "max": "1", "step": "1"}, "type": "int"}, "name": "触发报警开关"}, {"identifier": "PirLevel", "dataType": {"specs": {"min": "0", "max": "3", "step": "1"}, "type": "int"}, "name": "Pir触发等级"}, {"identifier": "DeviceTimeZoneID", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "设备时区信息"}, {"identifier": "DoorStatus", "dataType": {"specs": {"0": "关门", "1": "开门"}, "type": "enum"}, "name": "门的状态"}, {"identifier": "NordicBtVersion", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "Noridc蓝牙版本号"}, {"identifier": "WifiMac", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "设备的WifiMAC地址"}, {"identifier": "InnerVolume", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "门内音量"}], "method": "thing.service.property.set", "name": "set", "required": true, "callType": "async", "desc": "属性设置"}, {"outputData": [{"identifier": "AlarmSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警开关"}, {"identifier": "WiFI_RSSI", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "-1", "step": "1"}, "type": "int"}, "name": "信号强度"}, {"identifier": "TempPasswordInfo", "dataType": {"specs": [{"identifier": "index", "dataType": {"specs": {"min": "0", "max": "16", "step": "1"}, "type": "int"}, "name": "密码索引"}, {"identifier": "timestamp", "dataType": {"specs": {}, "type": "date"}, "name": "时间戳"}], "type": "struct"}, "name": "临时密码信息"}, {"identifier": "isDoublePwd", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "双重验证"}, {"identifier": "VideoInterval", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "180", "step": "15"}, "type": "int"}, "name": "录像间隔"}, {"identifier": "PirRecordingTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "60", "step": "1"}, "type": "int"}, "name": "Pir录像时长"}, {"identifier": "StayTime", "dataType": {"specs": {"unit": "s", "min": "-1", "unitName": "秒", "max": "20", "step": "1"}, "type": "int"}, "name": "逗留侦测设置"}, {"identifier": "WDRSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "宽动态"}, {"identifier": "UnlockRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "OpenTime", "dataType": {"specs": {}, "type": "date"}, "name": "开锁时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁用户"}, {"identifier": "OpenType", "dataType": {"specs": {"22": "nfc加单结构光", "23": "结构光加结构光", "24": "添加密码", "25": "编辑密码", "26": "删除密码", "27": "添加指纹", "28": "编辑指纹", "29": "删除指纹", "30": "添加NFC", "31": "编辑NFC", "10": "nfc开锁", "32": "删除NFC", "11": "nfc加指纹", "33": "添加人脸", "12": "指纹加nfc", "34": "编辑人脸", "13": "nfc加密码", "35": "删除人脸", "14": "密码加nfc", "15": "nfc加nfc", "16": "单结构光", "17": "单结构光加指纹", "18": "单结构光加密码", "19": "单结构光加nfc", "0": "单密码开锁", "1": "单指纹开锁", "2": "临时密码开锁", "3": "密码加密码开锁", "4": "密码加指纹开锁", "5": "指纹加指纹开锁", "6": "App蓝牙开锁", "7": "门内开锁", "8": "门内上锁", "9": "门外上锁", "20": "指纹加单结构光", "21": "密码加单结构光"}, "type": "enum"}, "name": "开锁类型"}, {"identifier": "Message", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "开锁信息"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "开锁记录"}, {"identifier": "SpeakerSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "扬声器开关"}, {"identifier": "StatusLightSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "状态灯开关"}, {"identifier": "FireAlarmUnlock", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "火警解锁"}, {"identifier": "LockPasswordInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "密码ID"}, {"identifier": "name", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "密码名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "10", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "密码信息列表"}, {"identifier": "StayDetectionNotification", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "逗留侦测推送开关"}, {"identifier": "StorageRecordMode", "dataType": {"specs": {"0": "不录像", "1": "事件录像", "2": "全天录像"}, "type": "enum"}, "name": "存储介质录像模式"}, {"identifier": "WiFI_SNR", "dataType": {"specs": {"min": "-127", "unitName": "无", "max": "127", "step": "1"}, "type": "int"}, "name": "信噪比"}, {"identifier": "EncryptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "视频加密开关"}, {"identifier": "EncryptTypeList", "dataType": {"specs": {"item": {"type": "int"}, "size": "128"}, "type": "array"}, "name": "加密类型列表"}, {"identifier": "WIFI_AP_BSSID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "热点BSSID"}, {"identifier": "CrySwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "哭声检测开关"}, {"identifier": "StorageStatus", "dataType": {"specs": {"0": "未插卡", "1": "正常", "2": "未格式化", "3": "正在格式化"}, "type": "enum"}, "name": "存储介质状态"}, {"identifier": "BluetoothMac", "dataType": {"specs": {"length": "24"}, "type": "text"}, "name": "设备的蓝牙MAC地址"}, {"identifier": "WIFI_Channel", "dataType": {"specs": {"min": "1", "unitName": "无", "max": "255", "step": "1"}, "type": "int"}, "name": "信道"}, {"identifier": "DeformationCorrect", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "enum"}, "name": "镜头畸变矫正开关"}, {"identifier": "VoiceDetectionSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "声音侦测灵敏度"}, {"identifier": "AlarmFrequencyLevel", "dataType": {"specs": {"0": "低频", "1": "中频", "2": "高频"}, "type": "enum"}, "name": "报警频率"}, {"identifier": "LockNfcInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfcID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "nfc名称"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "length", "dataType": {"specs": {"min": "1", "max": "15", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "NFC信息列表"}, {"identifier": "SubStreamVideoQuality", "dataType": {"specs": {"1": "标清", "2": "高清"}, "type": "enum"}, "name": "辅码流视频质量"}, {"identifier": "SecurityRecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "WarningType", "dataType": {"specs": {"11": "防火报警", "1": "挟持密码报警", "2": "挟持指纹报警", "3": "密码试探开门报警", "4": "指纹试探开门报警", "5": "防撬报警", "6": "低电量报警", "7": "开门未关超时报警", "8": "NFC试探开门报警", "9": "结构光试探开门报警", "10": "组合试探开门报警"}, "type": "enum"}, "name": "报警类型"}, {"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "用户名称"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "安全事件列表"}, {"identifier": "MicSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "麦克风开关"}, {"identifier": "DoorBellCallInterval", "dataType": {"specs": {"unit": "″", "min": "-1", "max": "60", "step": "1"}, "type": "int"}, "name": "按铃呼叫通知间隔"}, {"identifier": "DayNightMode", "dataType": {"specs": {"0": "白天模式", "1": "夜晚模式", "2": "自动模式"}, "type": "enum"}, "name": "日夜模式"}, {"identifier": "AlarmNotifyPlan", "dataType": {"specs": {"item": {"specs": [{"identifier": "BeginTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段开始秒数"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "86399", "step": "1"}, "type": "int"}, "name": "时间段结束秒数"}, {"identifier": "DayOfWeek", "dataType": {"specs": {"0": "周日", "1": "周一", "2": "周二", "3": "周三", "4": "周四", "5": "周五", "6": "周六"}, "type": "enum"}, "name": "每周日期"}], "type": "struct"}, "size": "128"}, "type": "array"}, "name": "报警提醒计划"}, {"identifier": "AlarmPromptSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "bool"}, "name": "报警提示开关"}, {"identifier": "CatEyeVersionInfo", "dataType": {"specs": [{"identifier": "android_version", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "Android版本号"}, {"identifier": "build_info", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "编译的版本号信息"}, {"identifier": "product_name", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "产品名称"}], "type": "struct"}, "name": "猫眼版本号信息"}, {"identifier": "StorageTotalCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "总存储空间"}, {"identifier": "LockFaceInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "人脸ID"}, {"identifier": "name", "dataType": {"specs": {"length": "40"}, "type": "text"}, "name": "人脸名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码属性"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "人脸信息列表"}, {"identifier": "OSDSwitch", "dataType": {"specs": {"0": "关闭", "1": "开启"}, "type": "enum"}, "name": "时间水印开关"}, {"identifier": "DirectUploadSupport", "dataType": {"specs": {"0": "不支持", "1": "支持"}, "type": "bool"}, "name": "是否支持直存"}, {"identifier": "RingBellWakeScreen", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃猫眼亮屏"}, {"identifier": "StreamVideoQuality", "dataType": {"specs": {"0": "流畅", "1": "标清", "2": "高清"}, "type": "enum"}, "name": "主码流视频质量"}, {"identifier": "LockFingerInfoList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹ID"}, {"identifier": "name", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "指纹名称"}, {"identifier": "type", "dataType": {"specs": {"0": "用户指纹", "1": "临时指纹", "2": "报警指纹"}, "type": "enum"}, "name": "指纹属性"}, {"identifier": "length", "dataType": {"specs": {"min": "0", "max": "15", "step": "1"}, "type": "int"}, "name": "指纹长度"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "20"}, "type": "text"}, "name": "重复"}, {"identifier": "userType", "dataType": {"specs": {"0": "普通用户", "1": "管理员"}, "type": "enum"}, "name": "用户类型"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "指纹信息列表"}, {"identifier": "WIFI_Band", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "频段"}, {"identifier": "ImageFlipState", "dataType": {"specs": {"0": "正常状态", "1": "翻转状态"}, "type": "enum"}, "name": "画面翻转状态"}, {"identifier": "RingBellCallSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃呼叫"}, {"identifier": "LockVersionInfo", "dataType": {"specs": [{"identifier": "LockSerialNumber", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁序列号"}, {"identifier": "LockMainVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁主控固件版本号"}, {"identifier": "LockSubVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "门锁转接板版本号"}, {"identifier": "FingerprintVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "指纹版本号"}, {"identifier": "TouchVersion", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "触摸板版本号"}], "type": "struct"}, "name": "门锁设备版本信息"}, {"identifier": "MotionDetectSensitivity", "dataType": {"specs": {"0": "关闭", "1": "最低档", "2": "低档", "3": "中档", "4": "高档", "5": "最高档"}, "type": "enum"}, "name": "移动侦测灵敏度"}, {"identifier": "BatteryLevel", "dataType": {"specs": {"unit": "%", "min": "0", "unitName": "百分比", "max": "100", "step": "1"}, "type": "int"}, "name": "电池电量"}, {"identifier": "StorageRemainCapacity", "dataType": {"specs": {"unit": "MB", "min": "0", "unitName": "兆字节", "max": "2147483647", "step": "0.01"}, "type": "double"}, "name": "剩余存储空间"}, {"identifier": "DeviceLocation", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "经纬度设置"}, {"identifier": "VoiceIntercomType", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "999", "step": "1"}, "type": "int"}, "name": "语音对讲类型列表"}, {"identifier": "PreRecordSupport", "dataType": {"specs": {"0": "不支持", "1": "支持"}, "type": "bool"}, "name": "是否支持预录"}, {"identifier": "RingBellPushSwitch", "dataType": {"specs": {"0": "关", "1": "开"}, "type": "bool"}, "name": "按铃推送开关"}, {"identifier": "Warning", "dataType": {"specs": {"min": "0", "max": "1", "step": "1"}, "type": "int"}, "name": "触发报警开关"}, {"identifier": "PirLevel", "dataType": {"specs": {"min": "0", "max": "3", "step": "1"}, "type": "int"}, "name": "Pir触发等级"}, {"identifier": "DeviceTimeZoneID", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "设备时区信息"}, {"identifier": "DoorStatus", "dataType": {"specs": {"0": "关门", "1": "开门"}, "type": "enum"}, "name": "门的状态"}, {"identifier": "NordicBtVersion", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "Noridc蓝牙版本号"}, {"identifier": "WifiMac", "dataType": {"specs": {"length": "100"}, "type": "text"}, "name": "设备的WifiMAC地址"}, {"identifier": "InnerVolume", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "门内音量"}], "identifier": "get", "inputData": ["AlarmSwitch", "WiFI_RSSI", "TempPasswordInfo", "isDoublePwd", "VideoInterval", "PirRecordingTime", "StayTime", "WDRSwitch", "UnlockRecordList", "SpeakerSwitch", "StatusLightSwitch", "FireAlarmUnlock", "LockPasswordInfoList", "StayDetectionNotification", "StorageRecordMode", "WiFI_SNR", "EncryptSwitch", "EncryptTypeList", "WIFI_AP_BSSID", "CrySwitch", "StorageStatus", "BluetoothMac", "WIFI_Channel", "DeformationCorrect", "VoiceDetectionSensitivity", "AlarmFrequencyLevel", "LockNfcInfoList", "SubStreamVideoQuality", "SecurityRecordList", "MicSwitch", "DoorBellCallInterval", "DayNightMode", "AlarmNotifyPlan", "AlarmPromptSwitch", "CatEyeVersionInfo", "StorageTotalCapacity", "LockFaceInfoList", "OSDSwitch", "DirectUploadSupport", "RingBellWakeScreen", "StreamVideoQuality", "LockFingerInfoList", "WIFI_Band", "ImageFlipState", "RingBellCallSwitch", "LockVersionInfo", "MotionDetectSensitivity", "BatteryLevel", "StorageRemainCapacity", "DeviceLocation", "VoiceIntercomType", "PreRecordSupport", "RingBellPushSwitch", "Warning", "PirLevel", "DeviceTimeZoneID", "DoorStatus", "NordicBtVersion", "WifiMac", "InnerVolume"], "method": "thing.service.property.get", "name": "get", "required": true, "callType": "async", "desc": "属性获取"}, {"outputData": [], "identifier": "StopVoiceIntercom", "inputData": [], "method": "thing.service.StopVoiceIntercom", "name": "停止语音对讲", "required": false, "callType": "async"}, {"outputData": [], "identifier": "Reboot", "inputData": [], "method": "thing.service.Reboot", "name": "重启", "required": true, "callType": "async"}, {"outputData": [], "identifier": "StopPTZAction", "inputData": [], "method": "thing.service.StopPTZAction", "name": "停止PTZ控制", "required": false, "callType": "async"}, {"outputData": [], "identifier": "GetSubDeviceInfo", "inputData": [{"identifier": "productKey", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "productKey"}, {"identifier": "deviceName", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "deviceName"}, {"identifier": "deviceSecret", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "deviceSecret"}], "method": "thing.service.GetSubDeviceInfo", "name": "获取三元组", "required": false, "callType": "async"}, {"outputData": [{"identifier": "wifiName", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "WI-FI名称"}, {"identifier": "WifiStrength", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "WI-FI强度"}, {"identifier": "wifiIp", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "wifiIp"}, {"identifier": "WifiMac", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "WifiMac地址"}], "identifier": "NetInfomation", "inputData": [], "method": "thing.service.NetInfomation", "name": "网络信息", "required": false, "callType": "sync"}, {"outputData": [{"identifier": "upgradeStatus", "dataType": {"specs": {"min": "0", "max": "2", "step": "1"}, "type": "int"}, "name": "升级状态"}], "identifier": "OTAUpgrade", "inputData": [{"identifier": "upgradeMode", "dataType": {"specs": {"min": "0", "max": "2", "step": "1"}, "type": "int"}, "name": "升级方式"}, {"identifier": "upgradeTime", "dataType": {"specs": {"unit": "ms", "min": "0", "max": "999999999999999999999999", "step": "1"}, "type": "float"}, "name": "定时升级时间"}], "method": "thing.service.OTAUpgrade", "name": "固件OTA升级", "required": false, "callType": "sync", "desc": "升级方式 取值范围0-2（0不升级 1立即升级，2定时升级）\n\n升级状态 取值范围0-2（0本地没有升级固件，1正在下载升级固件，2本地已有升级固件）"}, {"outputData": [{"identifier": "StorageExitStatus", "dataType": {"specs": {"min": "0", "max": "1", "step": "1"}, "type": "int"}, "name": "存储卡推出状态"}], "identifier": "StorageExit", "inputData": [], "method": "thing.service.StorageExit", "name": "推出存储卡", "required": false, "callType": "sync", "desc": "推出状态 0失败，1成功"}, {"outputData": [], "identifier": "PlayQuickResponseRing", "inputData": [{"identifier": "RingType", "dataType": {"specs": {"min": "1", "max": "100", "step": "1"}, "type": "int"}, "name": "铃声类型"}], "method": "thing.service.PlayQuickResponseRing", "name": "播放快捷回复声音", "required": false, "callType": "async"}, {"outputData": [], "identifier": "UploadLogTime", "inputData": [{"identifier": "expirationTime", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "日志上报过期时间"}], "method": "thing.service.UploadLogTime", "name": "设备log上报", "required": false, "callType": "async", "desc": "expirationTime （utc/ms）\n {\n    \"expirationTime\":\"1656665450000\" \n}"}, {"outputData": [], "identifier": "StartP2PStreaming", "inputData": [{"identifier": "StunUrl", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "STUN服务地址"}, {"identifier": "SignalUrl", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "信号服务地址"}], "method": "thing.service.StartP2PStreaming", "name": "开始P2P直播", "required": false, "callType": "async"}, {"outputData": [], "identifier": "CameraBinding", "inputData": [{"identifier": "product_key", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "product_key"}, {"identifier": "device_name", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "device_name"}, {"identifier": "timeout", "dataType": {"specs": {"unit": "s", "min": "45", "unitName": "秒", "max": "120", "step": "1"}, "type": "int"}, "name": "绑定超时时间"}, {"identifier": "result", "dataType": {"specs": {"0": "绑定成功", "1": "绑定超时", "2": "绑定失败", "3": "被其他账号绑定"}, "type": "enum"}, "name": "绑定结果"}, {"identifier": "cmd", "dataType": {"specs": {"0": "开始绑定", "1": "绑定结果", "2": "终止绑定", "3": "获取token"}, "type": "enum"}, "name": "命令"}], "method": "thing.service.CameraBinding", "name": "摄像机绑定状态", "required": false, "callType": "sync"}, {"outputData": [], "identifier": "StartVod", "inputData": [{"identifier": "FileName", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "录像名"}, {"identifier": "SessionID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "会话ID"}, {"identifier": "PushUrl", "dataType": {"specs": {"length": "512"}, "type": "text"}, "name": "推流地址"}, {"identifier": "Scheme", "dataType": {"specs": {"0": "RTMP", "1": "RTSP"}, "type": "enum"}, "name": "流协议"}, {"identifier": "EncryptKey", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "密钥"}, {"identifier": "EncryptType", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "9999", "step": "1"}, "type": "int"}, "name": "加密类型"}], "method": "thing.service.StartVod", "name": "开始录像观看", "required": false, "callType": "async"}, {"outputData": [], "identifier": "StopVod", "inputData": [{"identifier": "SessionID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "会话ID"}], "method": "thing.service.StopVod", "name": "停止录像观看", "required": false, "callType": "async"}, {"outputData": [{"identifier": "TimeList", "dataType": {"specs": {"item": {"specs": [{"identifier": "BeginTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "结束时间"}, {"identifier": "Type", "dataType": {"specs": {"1": "主动录像", "2": "报警录像", "3": "计划录像"}, "type": "enum"}, "name": "录像类型"}], "type": "struct"}, "size": "128"}, "type": "array"}, "name": "时间列表"}], "identifier": "QueryRecordTimeList", "inputData": [{"identifier": "BeginTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "结束时间"}, {"identifier": "QuerySize", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "128", "step": "1"}, "type": "int"}, "name": "查询数量"}, {"identifier": "Type", "dataType": {"specs": {"0": "所有类型", "1": "主动录像", "2": "报警录像", "3": "计划录像"}, "type": "enum"}, "name": "录像类型"}], "method": "thing.service.QueryRecordTimeList", "name": "查询录像时间列表", "required": false, "callType": "sync"}, {"outputData": [{"identifier": "RecordID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "录像ID"}], "identifier": "StartRecord", "inputData": [{"identifier": "RecordType", "dataType": {"specs": {"0": "本地录像", "1": "云端录像"}, "type": "enum"}, "name": "录像方式"}, {"identifier": "RecordDuration", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "999999", "step": "1"}, "type": "int"}, "name": "录制时长"}, {"identifier": "PreRecordDuration", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "20", "step": "1"}, "type": "int"}, "name": "预录时长"}, {"identifier": "UploadUrl", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "上传地址"}], "method": "thing.service.StartRecord", "name": "开始录像", "required": false, "callType": "sync"}, {"outputData": [], "identifier": "StopRecord", "inputData": [{"identifier": "RecordID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "录像ID"}], "method": "thing.service.StopRecord", "name": "停止录像", "required": false, "callType": "async"}, {"outputData": [{"identifier": "PicID", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "图片ID"}], "identifier": "TriggerPicCapture", "inputData": [{"identifier": "UploadUrl", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "上传地址"}], "method": "thing.service.TriggerPicCapture", "name": "触发设备抓图", "required": true, "callType": "async"}, {"outputData": [], "identifier": "ReceiveUploadUrl", "inputData": [{"identifier": "UrlCollection", "dataType": {"specs": {"item": {"specs": [{"identifier": "UploadUrl", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "上传URL"}, {"identifier": "Path", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "路径PATH"}, {"identifier": "ExpireTimestamp", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "上传链接失效时间戳"}], "type": "struct"}, "size": "10"}, "type": "array"}, "name": "接收文件上传Url"}, {"identifier": "FileTag", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "请求头"}, {"identifier": "UseTag", "dataType": {"specs": {"0": "不使用", "1": "使用"}, "type": "bool"}, "name": "是否使用请求头"}, {"identifier": "Code", "dataType": {"specs": {"0": "不下发", "1": "下发"}, "type": "bool"}, "name": "是否下发上传连接"}, {"identifier": "DataType", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "上传数据类型META_LOG_TCARD等"}, {"identifier": "FileType", "dataType": {"specs": {"length": "64"}, "type": "text"}, "name": "文件类型"}], "method": "thing.service.ReceiveUploadUrl", "name": "接收文件上传Url", "required": false, "callType": "async", "desc": "接收文件上传Url"}, {"outputData": [], "identifier": "FormatStorageMedium", "inputData": [], "method": "thing.service.FormatStorageMedium", "name": "格式化存储介质", "required": false, "callType": "async"}, {"outputData": [{"identifier": "Action", "dataType": {"specs": {"0": "创建", "1": "修改", "2": "删除", "3": "录入"}, "type": "enum"}, "name": "操作"}, {"identifier": "ID", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "指纹id"}, {"identifier": "Result", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "操作结果"}, {"identifier": "Error", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "错误信息"}], "identifier": "LockSetFingerprint", "inputData": [{"identifier": "Action", "dataType": {"specs": {"0": "创建", "1": "修改", "2": "删除", "3": "录入"}, "type": "enum"}, "name": "操作"}, {"identifier": "ID", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "指纹id"}, {"identifier": "Index", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "索引key"}, {"identifier": "Name", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "指纹名称"}, {"identifier": "ValidityType", "dataType": {"specs": {"0": "1天", "1": "1周", "2": "自定义时间段", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时效类型"}, {"identifier": "StartDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "开始日期时间"}, {"identifier": "EndDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "结束日期时间"}, {"identifier": "Repeat", "dataType": {"specs": {"length": "8"}, "type": "text"}, "name": "重复"}], "method": "thing.service.LockSetFingerprint", "name": "门锁设置指纹", "required": false, "callType": "sync", "desc": "设置指纹，增，删，改"}, {"outputData": [{"identifier": "faceInfoItemList", "dataType": {"specs": {"item": {"specs": [{"identifier": "id", "dataType": {"specs": {"min": "0", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "人脸ID"}, {"identifier": "name", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "用户名称"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "重复"}], "type": "struct"}, "size": "100"}, "type": "array"}, "name": "人脸信息"}, {"identifier": "result", "dataType": {"specs": {"0": "操作失败", "1": "操作成功"}, "type": "bool"}, "name": "操作结果"}], "identifier": "FaceManagerService", "inputData": [{"identifier": "Action", "dataType": {"specs": {"0": "创建", "1": "修改", "2": "删除", "3": "查询"}, "type": "enum"}, "name": "操作类型"}, {"identifier": "id", "dataType": {"specs": {"min": "0", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "人脸ID"}, {"identifier": "name", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "用户名称"}, {"identifier": "validityType", "dataType": {"specs": {"0": "一天", "1": "一周", "2": "自定义时间", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时间类型"}, {"identifier": "timeString", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "自定义时间"}, {"identifier": "createTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "startData", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "endData", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "repeat", "dataType": {"specs": {}, "type": "date"}, "name": "重复"}], "method": "thing.service.FaceManagerService", "name": "人脸信息管理", "required": false, "callType": "sync"}, {"outputData": [], "identifier": "StartPTZAction", "inputData": [{"identifier": "ActionType", "dataType": {"specs": {"0": "左", "1": "右", "2": "上", "3": "下", "4": "上左", "5": "上右", "6": "下左", "7": "下右", "8": "放大", "9": "缩小"}, "type": "enum"}, "name": "动作类型"}, {"identifier": "Speed", "dataType": {"specs": {"0": "慢速", "1": "中速", "2": "快速"}, "type": "enum"}, "name": "速度"}], "method": "thing.service.StartPTZAction", "name": "开始PTZ控制", "required": false, "callType": "async"}, {"outputData": [], "identifier": "SubBindDeviceListService", "inputData": [{"identifier": "subBindDeviceListParam", "dataType": {"specs": {"item": {"specs": [{"identifier": "productKey", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "productKey"}, {"identifier": "deviceName", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "deviceName"}, {"identifier": "deviceSecret", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "deviceSecret"}, {"identifier": "id", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "id"}, {"identifier": "detail", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "detail"}, {"identifier": "userBound", "dataType": {"specs": {"0": "未绑定", "1": "已绑定"}, "type": "bool"}, "name": "是否已绑定用户"}, {"identifier": "identityId", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "identityId"}], "type": "struct"}, "size": "10"}, "type": "array"}, "name": "subBindDeviceListParam"}], "method": "thing.service.SubBindDeviceListService", "name": "下发已绑定的子设备三元组列表", "required": false, "callType": "async", "desc": "detail字段存储额外的附属信息（例如威斯丹丽的：deviceType、zoneId），字符串内容可以由固件端定义（比如可以用json字符串），云端统一以字符串形式存储和下发"}, {"outputData": [], "identifier": "requestAddChildrenDevices", "inputData": [{"identifier": "pk", "dataType": {"specs": {"length": "200"}, "type": "text"}, "name": "pk"}, {"identifier": "dsp", "dataType": {"specs": {"length": "200"}, "type": "text"}, "name": "dsp"}, {"identifier": "pid", "dataType": {"specs": {"length": "200"}, "type": "text"}, "name": "pid"}, {"identifier": "did", "dataType": {"specs": {"length": "200"}, "type": "text"}, "name": "did"}, {"identifier": "devicetype", "dataType": {"specs": {"length": "32"}, "type": "text"}, "name": "devicetype"}, {"identifier": "zoneid", "dataType": {"specs": {"length": "32"}, "type": "text"}, "name": "zoneid"}, {"identifier": "epnumber", "dataType": {"specs": {"unit": "pcs", "min": "1", "unitName": "个", "max": "99", "step": "1"}, "type": "int"}, "name": "epnumber"}, {"identifier": "manufacturer", "dataType": {"specs": {"length": "32"}, "type": "text"}, "name": "manufacturer"}], "method": "thing.service.requestAddChildrenDevices", "name": "请求开始添加网关子设备", "required": false, "callType": "async"}, {"outputData": [], "identifier": "BizProductConfigInfoS", "inputData": [{"identifier": "code", "dataType": {"specs": {"min": "-1", "max": "1000000", "step": "1"}, "type": "int"}, "name": "code"}, {"identifier": "message", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "message"}, {"identifier": "result", "dataType": {"specs": [{"identifier": "pkConfigJson", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "pkConfigJson"}], "type": "struct"}, "name": "result"}], "method": "thing.service.BizProductConfigInfoS", "name": "获取厂商产品配置信息结果", "required": false, "callType": "async"}, {"outputData": [{"identifier": "Action", "dataType": {"specs": {"0": "创建", "1": "修改", "2": "删除"}, "type": "enum"}, "name": "操作"}, {"identifier": "ID", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "密码id"}, {"identifier": "Result", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "操作结果"}, {"identifier": "Error", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "错误信息"}], "identifier": "LockSetPassword", "inputData": [{"identifier": "Action", "dataType": {"specs": {"0": "创建", "1": "修改", "2": "删除"}, "type": "enum"}, "name": "操作"}, {"identifier": "ID", "dataType": {"specs": {"min": "0", "max": "9999", "step": "1"}, "type": "int"}, "name": "密码id"}, {"identifier": "Index", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "索引值"}, {"identifier": "Name", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "密码名称"}, {"identifier": "Type", "dataType": {"specs": {"0": "用户密码", "1": "临时密码", "2": "报警密码"}, "type": "enum"}, "name": "密码类型"}, {"identifier": "Length", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "密码长度"}, {"identifier": "Code", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "密码值"}, {"identifier": "CodeMask", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "密码掩饰值"}, {"identifier": "CreateTime", "dataType": {"specs": {}, "type": "date"}, "name": "创建时间"}, {"identifier": "ValidityType", "dataType": {"specs": {"0": "1天", "1": "1周", "2": "自定义时间段", "3": "定时", "4": "永久"}, "type": "enum"}, "name": "时效类型"}, {"identifier": "StartDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "开始日期时间"}, {"identifier": "EndDateTime", "dataType": {"specs": {}, "type": "date"}, "name": "结束日期时间"}, {"identifier": "Repeat", "dataType": {"specs": {"length": "8"}, "type": "text"}, "name": "重复"}], "method": "thing.service.LockSetPassword", "name": "门锁设置密码", "required": false, "callType": "sync", "desc": "设置密码，增，删，改"}, {"outputData": [{"identifier": "RecordFlags", "dataType": {"specs": {"length": "32"}, "type": "text"}, "name": "月录像标识"}], "identifier": "QueryMonthRecord", "inputData": [{"identifier": "Month", "dataType": {"specs": {"length": "16"}, "type": "text"}, "name": "月份"}], "method": "thing.service.QueryMonthRecord", "name": "查询设备端月录像", "required": false, "callType": "sync", "desc": "月份格式为yyyyMM，如\"201806\"。输出参数为字符串，长度为当月天数，1表示当天有录像，0表示当天无录像。"}, {"outputData": [], "identifier": "StartVodByTime", "inputData": [{"identifier": "EncryptType", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "9999", "step": "1"}, "type": "int"}, "name": "加密类型"}, {"identifier": "EncryptKey", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "密钥"}, {"identifier": "Scheme", "dataType": {"specs": {"0": "RTMP", "1": "RTSP"}, "type": "enum"}, "name": "流协议"}, {"identifier": "PushUrl", "dataType": {"specs": {"length": "512"}, "type": "text"}, "name": "推流地址"}, {"identifier": "SessionID", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "会话ID"}, {"identifier": "BeginTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "″", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "结束时间"}], "method": "thing.service.StartVodByTime", "name": "开始录像按时间观看", "required": false, "callType": "async"}, {"outputData": [], "identifier": "StartVoiceIntercom", "inputData": [{"identifier": "IntercomUrl", "dataType": {"specs": {"length": "521"}, "type": "text"}, "name": "对讲地址"}, {"identifier": "Key", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "加解密秘钥"}, {"identifier": "EncryptType", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "9999", "step": "1"}, "type": "int"}, "name": "加解密类型"}], "method": "thing.service.StartVoiceIntercom", "name": "开始语音对讲", "required": false, "callType": "async"}, {"outputData": [], "identifier": "CloudResponse", "inputData": [{"identifier": "IMICode", "dataType": {"specs": {"length": "512"}, "type": "text"}, "name": "响应码"}, {"identifier": "IMIMessage", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "响应message"}], "method": "thing.service.CloudResponse", "name": "云端通用Response", "required": false, "callType": "async", "desc": "IMICode: \n    200 : 响应正常\n   其他响应为异常情况 具体之后继续补充"}, {"outputData": [], "identifier": "StopPushStreaming", "inputData": [{"identifier": "StreamType", "dataType": {"specs": {"0": "主码流", "1": "辅码流"}, "type": "enum"}, "name": "停止直播"}], "method": "thing.service.StopPushStreaming", "name": "停止直播", "required": true, "callType": "async"}, {"outputData": [{"identifier": "WarnEventList", "dataType": {"specs": {"item": {"specs": [{"identifier": "WarnType", "dataType": {"specs": {"1": "挟持密码报警", "2": "挟持指纹报警", "3": "密码试探开门报警", "4": "指纹试探开门报警", "5": "防撬报警", "6": "低电量报警", "7": "开门未关超时报警", "8": "NFC试探开门报警", "9": "结构光试探开门报警"}, "type": "enum"}, "name": "报警类型"}, {"identifier": "DateTime", "dataType": {"specs": {}, "type": "date"}, "name": "时间"}], "type": "struct"}, "size": "10"}, "type": "array"}, "name": "安全事件记录"}], "identifier": "WarningEventList", "inputData": [{"identifier": "BeginTime", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "QuerySize", "dataType": {"specs": {"min": "1", "max": "100", "step": "1"}, "type": "int"}, "name": "查询数量"}, {"identifier": "WarningType", "dataType": {"specs": {"1": "挟持密码报警", "2": "挟持指纹报警", "3": "密码试探开门报警", "-1": "全部", "4": "指纹试探开门报警", "5": "防撬报警", "6": "低电量报警", "7": "开门未关超时报警", "8": "NFC试探开门报警", "9": "结构光试探开门报警", "10": "组合试探开门报警"}, "type": "enum"}, "name": "报警类型"}], "method": "thing.service.WarningEventList", "name": "安全事件记录", "required": false, "callType": "sync"}, {"outputData": [], "identifier": "LoginResult", "inputData": [{"identifier": "code", "dataType": {"specs": {"min": "-1", "max": "1000000", "step": "1"}, "type": "int"}, "name": "code"}, {"identifier": "result", "dataType": {"specs": [{"identifier": "uid", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "uid"}, {"identifier": "authCode", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "authCode"}], "type": "struct"}, "name": "result"}, {"identifier": "message", "dataType": {"specs": {"length": "256"}, "type": "text"}, "name": "message"}], "method": "thing.service.LoginResult", "name": "登录成功", "required": false, "callType": "async"}, {"outputData": [], "identifier": "P2PSignalDownstream", "inputData": [{"identifier": "Data", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "信令数据"}], "method": "thing.service.P2PSignalDownstream", "name": "P2P信令下发", "required": false, "callType": "async"}, {"outputData": [], "identifier": "FaceUploadResponse", "inputData": [{"identifier": "url", "dataType": {"specs": {"length": "1024"}, "type": "text"}, "name": "链接"}], "method": "thing.service.FaceUploadResponse", "name": "人脸图片上传响应", "required": false, "callType": "async"}, {"outputData": [], "identifier": "cancelAddChildrenDevices", "inputData": [], "method": "thing.service.cancelAddChildrenDevices", "name": "取消添加子设备", "required": false, "callType": "async"}, {"outputData": [{"identifier": "RecordList", "dataType": {"specs": {"item": {"specs": [{"identifier": "FileName", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "录像名"}, {"identifier": "BeginTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "结束时间"}, {"identifier": "Size", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "文件大小"}, {"identifier": "Type", "dataType": {"specs": {"1": "主动录像", "2": "报警录像", "3": "计划录像"}, "type": "enum"}, "name": "录像类型"}], "type": "struct"}, "size": "128"}, "type": "array"}, "name": "录像列表"}], "identifier": "QueryRecordList", "inputData": [{"identifier": "BeginTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "2147483647", "step": "1"}, "type": "int"}, "name": "结束时间"}, {"identifier": "Type", "dataType": {"specs": {"0": "所有类型", "1": "主动录像", "2": "报警录像", "3": "计划录像"}, "type": "enum"}, "name": "录像类型"}, {"identifier": "QuerySize", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "128", "step": "1"}, "type": "int"}, "name": "查询数量"}], "method": "thing.service.QueryRecordList", "name": "查询录像列表", "required": false, "callType": "sync"}, {"outputData": [{"identifier": "Result", "dataType": {"specs": {"0": "成功", "1": "设备不支持", "2": "设备资源不足", "3": "推流连接失败"}, "type": "enum"}, "name": "开始直播"}], "identifier": "StartPushStreaming", "inputData": [{"identifier": "PushUrl", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "开始直播"}, {"identifier": "StreamType", "dataType": {"specs": {"0": "主码流", "1": "辅码流"}, "type": "enum"}, "name": "开始直播"}, {"identifier": "Scheme", "dataType": {"specs": {"0": "RTMP", "1": "RTSP"}, "type": "enum"}, "name": "开始直播"}, {"identifier": "EncryptKey", "dataType": {"specs": {"length": "255"}, "type": "text"}, "name": "开始直播"}, {"identifier": "EncryptType", "dataType": {"specs": {"min": "0", "unitName": "无", "max": "9999", "step": "1"}, "type": "int"}, "name": "开始直播"}, {"identifier": "PreTime", "dataType": {"specs": {"unit": "s", "min": "0", "unitName": "秒", "max": "60", "step": "1"}, "type": "int"}, "name": "开始直播"}], "method": "thing.service.StartPushStreaming", "name": "开始直播", "required": true, "callType": "async"}, {"outputData": [], "identifier": "SubNoBindDeviceListService", "inputData": [{"identifier": "subNoBindDeviceListParam", "dataType": {"specs": {"item": {"specs": [{"identifier": "productKey", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "productKey"}, {"identifier": "deviceName", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "deviceName"}, {"identifier": "deviceSecret", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "deviceSecret"}, {"identifier": "id", "dataType": {"specs": {"length": "10240"}, "type": "text"}, "name": "id"}], "type": "struct"}, "size": "10"}, "type": "array"}, "name": "subNoBindDeviceListParam"}], "method": "thing.service.SubNoBindDeviceListService", "name": "下发子设备三元组", "required": false, "callType": "async"}, {"outputData": [{"identifier": "LockOpenList", "dataType": {"specs": {"item": {"specs": [{"identifier": "OpenTime", "dataType": {"specs": {}, "type": "date"}, "name": "开锁时间"}, {"identifier": "UserName", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "开锁用户"}, {"identifier": "Message", "dataType": {"specs": {"length": "30"}, "type": "text"}, "name": "开锁信息"}, {"identifier": "OpenType", "dataType": {"specs": {"0": "单密码开锁", "1": "单指纹开锁", "2": "临时密码开锁", "3": "双密码开锁", "4": "密码加指纹开锁", "5": "指纹加指纹开锁", "6": "蓝牙开锁"}, "type": "enum"}, "name": "开锁类型"}], "type": "struct"}, "size": "10"}, "type": "array"}, "name": "开锁记录"}], "identifier": "LockOpenRecordList", "inputData": [{"identifier": "BeginTime", "dataType": {"specs": {}, "type": "date"}, "name": "开始时间"}, {"identifier": "EndTime", "dataType": {"specs": {}, "type": "date"}, "name": "结束时间"}, {"identifier": "QuerySize", "dataType": {"specs": {"min": "0", "max": "100", "step": "1"}, "type": "int"}, "name": "查询数量"}, {"identifier": "OpenType", "dataType": {"specs": {"0": "单密码开锁", "1": "单指纹开锁", "2": "临时密码开锁", "3": "双密码开锁", "-1": "全部类型", "4": "密码加指纹开锁", "5": "指纹加指纹开锁", "6": "蓝牙开锁"}, "type": "enum"}, "name": "开锁类型"}], "method": "thing.service.LockOpenRecordList", "name": "门锁记录", "required": false, "callType": "sync", "desc": "返回开锁时间，开锁用户，开锁信息，开锁类型"}, {"outputData": [], "identifier": "ReceiveCloudPublicKey", "inputData": [{"identifier": "CloudPublicKey", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "CloudPublicKey-Base64编码"}, {"identifier": "CloudSign", "dataType": {"specs": {"length": "128"}, "type": "text"}, "name": "CloudSign-Base64编码"}], "method": "thing.service.ReceiveCloudPublicKey", "name": "接收服务端CurvePublicKey", "required": false, "callType": "async"}, {"outputData": [], "identifier": "VoiceMessageDownstream", "inputData": [{"identifier": "MessageData", "dataType": {"specs": {"length": "2048"}, "type": "text"}, "name": "语音消息"}], "method": "thing.service.VoiceMessageDownstream", "name": "语音消息下发", "required": false, "callType": "async"}]}}