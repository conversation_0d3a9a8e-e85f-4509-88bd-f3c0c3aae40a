// IMIRenderViewManager
import React from 'react';

import {<PERSON>ton, ScrollView, StyleSheet, Text, View, } from 'react-native';

import {cameraStyles} from "../../../imilab-rn-sdk/components/camera/CameraStyle";


import {PLAYER_EVENT_CODE, PlayerClass} from "../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import {CameraMethod} from "../../../imilab-rn-sdk/components/camera/CameraMethod";
import {LetDevice} from "../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {IntercomModeKey,INTERCOM_MODE} from "../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMICameraVideoView from "../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIVideoView from "../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import IMIRenderViewManager from "../../../imilab-rn-sdk/native/camera-kit/IMIRenderViewManager";
import IMIFile from "../../../imilab-rn-sdk/native/local-kit/IMIFile";
import IMVodPlayView from '../../../imilab-rn-sdk/native/camera-kit/IMVodPlayView';
import IMP2pClient from '../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import { IMILog } from '../../../imilab-rn-sdk';

const PLAYER_TYPE = PlayerClass.LIVE;
// const PLAYER_TYPE = PlayerClass.VOD;

const TAG = "DoubleCameraDemoPage - ";

export default class DoubleCameraDemoPage extends React.Component {

    constructor(props) {
        super(props);

        this.state = {
            fullScreen: false,
            recording: false,
            speaking: false,
            starting: true,
            bps: 0,
            devInfoText: "",
            devPropText: "",
            devSupport: false,
            isMute: true,
            timeRecord: 0,
            // 增加cameraNumber设置当前镜头数
            dataSource: {iotId: LetDevice.deviceID, playerClass: PlayerClass.LIVE, cameraNumber: "2"}
        };
        this.cameraGLView = null;
    }

    /***  ·······        系统函数 start       ·······   *****/

    render() {
        return (
            <View style={styles.main}>

                {this._initVideoRootLayout()}

                {this._testTNDemoLayout()}

            </View>
        )
    }

    componentDidMount() {
        console.log('componentDidMount-----componentWillUnmount----', new Date());
        this._initVideoPrepare();
        this._doDevInfoText();
        this._doDevPropTextText()
    }

    componentWillUnmount() {
        LetDevice.removeInfoChangeListener();
        LetDevice.removePropertyChangeListener();
        console.log('componentWillUnmount---------', new Date());
        if (this.cameraGLView) {
            this.cameraGLView.destroy();
        }
    }


    /***  ·······    系统函数 end      ·······   *****/

    //视频视图root layout
    _initVideoRootLayout() {

        let bpsTxt = (Number.parseInt(this.state.bps) + 'kb/s');

        console.log('_renderVideoView' + bpsTxt);

        return (
            <View
                style={this.state.fullScreen ? cameraStyles.videoContainerFull : cameraStyles.videoContainerNormal}>

                {this._renderVideoView()}

                <View style={{
                    position: 'absolute',
                    flex: 1,
                    width: '100%',
                }}>
                    <Text> bps--- {bpsTxt} </Text>
                    <Text> PlayerClass----{PLAYER_TYPE}  </Text>
                    <Text> record time---{this.state.timeRecord}(ms) </Text>
                </View>
            </View>
        )
    }


    /**
     * 测试按钮布局
     * @returns {*}
     * @private
     */
    _testTNDemoLayout() {
        return (
            <ScrollView>
                <View style={styles.demoBtnContainer}>
                <Button
                        onPress={this._doStart.bind(this)}
                        title={'start'}
                        color='#000000B3'
                    />
                    <Button
                        onPress={this._doStartStop.bind(this)}
                        title={this.state.starting ? 'stop' : 'start'}
                        color='#e74d4d'
                    />

                    <Button
                        onPress={this._getAllProperties.bind(this)}
                        title={'获取设备所有属性'}
                        color='#e74d4d'
                    />

                    <Button
                        onPress={this._doStartRecord.bind(this)}
                        title={this.state.recording ? '停止录制' : '开始录制'}
                    />

                    <Button
                        onPress={this._doStartSpeak.bind(this)}
                        title={this.state.speaking ? '停止对讲' : '开始对讲'}
                        color='#e74d4d'
                    />

                    <Button
                        onPress={this._doScreenShot.bind(this)}
                        title='截图（保存至传入的参数路径中）'
                        color='#000000B3'
                    />
                    <Button
                        title={this.state.isMute ? '非静音' : '静音'}
                        color='#000000B3'
                        onPress={this._doIsMute.bind(this)}
                    />
                    <Button
                        onPress={this._doSetSleep.bind(this)}
                        title='休眠'
                    />
                    <Button
                        onPress={this._doSetSleepClose.bind(this)}
                        title='解除休眠'
                    />
                    <Button
                        onPress={this.gotoVodDemo.bind(this)}
                        title='进入回看'
                    />

                </View>
            </ScrollView>
        )
    }
    gotoVodDemo() {
        this.cameraGLView.stop();
        this.props.navigation.navigate('DoubleVodDemo');
    }

    _doDevPropTextText = () => {
        this.mPropertyChangeListener = LetDevice.addPropertyChangeListener(data => {
            console.log(`_doDevPropTextText eventData : ${data} + data ${JSON.stringify(data)}`);
            this.setState({devPropText: JSON.stringify(data)});
        });
    };

    _doDevInfoText = () => {
        this.mDevInfoChangeListener =  LetDevice.addInfoChangeListener(data => {
            console.log(`_doDevInfoText eventData : ${data} + data ${JSON.stringify(data)}`);
            this.state.devInfoText = JSON.stringify(data);
            this.setState({devInfoText: JSON.stringify(data)});
        });
    };

    _doCheckSupportMethod() {
        let supportMethod = LetDevice.isSupportMethod(CameraMethod.IMI_ImageFlipState);
        console.log(TAG + 'supportMethod - --- > ' + supportMethod);
        this.setState({devSupport: supportMethod});
    }

    _doSetPropertiesSwitch() {
        LetDevice.propertyOn(CameraMethod.IMI_ImageFlipState).then((data) => {
            alert(data);
            console.log(TAG + '_doSetPropertiesSwitch', data);
        }).catch((error) => {
            alert(error);
            console.log(error);
        });
    }

    _doSetProperties() {
        //两种格式均可以支持
        let params = '{"StatusLightSwitch": 1}';
        // let params = {"StatusLightSwitch": 1};

        LetDevice.setPropertyCloud(params).then(function (data) {
            alert(data);
            console.log(TAG + 'setPropertyCloud data : ', data);
        }).catch((error) => {
            alert(error);
            console.log(error)
        });
    }

    _doSetSleep() {
        const paramJson = JSON.stringify({value: false});
        IMILog.logI('10001 -wake_up-setProperties', paramJson);
        LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
            .then(() => {
                alert(`成功：${res}`);
                console.log("设备休眠成功", res);
            })
            .catch(() => {
                alert(JSON.stringify(err));
                console.log("设备休眠", JSON.stringify(err));
            })
            .finally(() => {
            });
    }

    
    _doSetSleepClose() {
        const paramJson = JSON.stringify({value: true});
        IMILog.logI('10001 -wake_up-setProperties', paramJson);
        LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
            .then(() => {
                // this.setState({isSleep: false}, () => {
                    this.IMIVideoView && this.IMIVideoView.prepare();
                // });
            alert(`成功：${res}`);
                console.log("设备休眠成功", res);
            })
            .catch(() => {
                alert(JSON.stringify(err));
                console.log("设备休眠", JSON.stringify(err));
            })
            .finally(() => {
            });
    }


    _doResume() {
        this.cameraGLView.resume();
    }

    _doStart() {
        console.log('start');
        // this.setDataSource();
        console.log('start---');
        this.cameraGLView.prepare();
        this.cameraGLView.start();
    }

    _doStartStop() {
        if (this.state.starting) {
            this.cameraGLView.stop();
        } else {
            this.cameraGLView.prepare();
            this.cameraGLView.start();
        }
        this.setState({starting: !this.state.starting})
    }

    _doIsMute() {
        if (this.state.starisMuteting) {
            this.cameraGLView.setIsMute(false);
        } else {
            this.cameraGLView.setIsMute(true);
        }
        this.setState({isMute: !this.state.isMute})
    }

    _getAllProperties() {
        LetDevice.getAllProperties(true,LetDevice.deviceID).then((res) => {
            alert(`getAllProperties:${res}`);
            console.log("getAllProperties success", res);
        }).catch((err) => {
            alert(JSON.stringify(err));
            console.log("getAllProperties error", JSON.stringify(err));
        });
    }

    /**
     * 开始录制
     * @private
     */
    _doStartRecord() {
        if (!this.state.recording) {
            let timestamp = new Date().getTime();
            //todo 后续通过代码获取手机根目录
            let path = `${IMIFile.storageBasePath}/${LetDevice.deviceID}/${timestamp}.mp4`;
            this.cameraGLView.startRecord(0,path, (eventData) => {
                console.log(`startRecord eventData : ${eventData} `);
            });
        } else {
            this.cameraGLView.stopRecord(0,(eventData) => {
                console.log(`stopRecord eventData : ${eventData} `);
                alert('停止录像成功');
            });
        }
        this.setState({recording: !this.state.recording});
    };

    /**
     * 截图到本地
     * @private
     */
    _doScreenShot = () => {
        let cameraId = 0;
        const screenShotPath = `${IMIFile.storageBasePath}/snapshot111.jpg`;
        console.log("  screenShotPath " + screenShotPath + " IMIVideoView  " + this.cameraGLView);
        this.cameraGLView.screenShot(cameraId,screenShotPath).then(_ => {
            console.log("截图方法调用成功");
        })
    };


    /**
     * 开始对讲
     * @private
     */
    _doStartSpeak = () => {
        if (!this.state.speaking) {
            this.cameraGLView.startSpeak();
        } else {
            this.cameraGLView.stopSpeak();
        }
        this.setState({speaking: !this.state.speaking});
    };

    //初始化VideoView
    _renderVideoView() {

        console.log('使用Live  播放器进行播放 ');
        return (
            <View  style={{flex: 1, backgroundColor: 'blue'}}>
                <View style={{flex: 1, backgroundColor: 'blue'}}>
                    <IMICameraVideoView
                            playerClass={PlayerClass.LIVE}
                            style={{height: "100%", width: "100%"}}
                            ref={this._assignRoot}
                            optionMap={{[IntercomModeKey]: INTERCOM_MODE.SingleTalk}}
                            dataSource={this.state.dataSource}
                            // mute={true}
                            onPrepared={this._onPrepared.bind(this)}
                            onEventChange={this._onEventChange.bind(this)}
                            onErrorChange={this._onErrorChange.bind(this)}
                            onRecordTimeChange={this._onRecord.bind(this)}
                            onCommCallback={this._onCommCallback.bind(this)}
                        />
                </View>
                <View style={{flex: 1, backgroundColor: 'blue'}}>
                    <IMIRenderViewManager style={{height: "100%", width: "100%", backgroundColor: 'red'}} ref={this._assignRootManager}></IMIRenderViewManager>
                </View>
            </View>
            
        )
    }
    // 设置资源
    setDataSource() {
        console.log('PLAYER_TYPE setDataSource-----',PLAYER_TYPE);
        // if (PLAYER_TYPE === PlayerClass.VOD) {
        //     this.cameraGLView && this.cameraGLView.setDataSource({iotId: LetDevice.deviceID, playerClass: PlayerClass.VOD, cameraNumber: 2, start_time: 1754989308, end_time:1754996505, offset:0});
        // } else {
            // this.cameraGLView && this.cameraGLView.setDataSource({iotId: LetDevice.deviceID, playerClass: PlayerClass.LIVE, cameraNumber: 2});
        // }
        if (this._rootManagerView && this._rootManagerView.setCameraId) {
            console.log('this._rootManagerView setDataSource---setScale--');
            this._rootManagerView.setCameraId(1);
        }
    }

    _assignRootManager = component => {
        if (!this._rootManagerView) {
            this._rootManagerView = component;
        }
    };

    _assignRoot = (component) => {
        this.cameraGLView = component;
    };

    //初始化VideoView
    _initVideoPrepare() {
        console.log(`_initVideoPrepare ${this.cameraGLView}`);
        this.cameraGLView && this.cameraGLView.addExtraRenderView(this._rootManagerView);
        this.setDataSource();
        setTimeout(() => {
            this.cameraGLView.prepare();
        }, 200);
    }

    /***   ---------------------------    监听器 start      --------------------------   *****/
    _onCommCallback(data){
        console.log(`_onCommCallback ${JSON.stringify(data)}`);
        if (data.code == 0) {
            const screenShotPath = `${IMIFile.storageBasePath}/snapshot111.jpg`;
            IMIFile.saveImageToPhotosAlbum(screenShotPath, LetDevice.deviceID).then(_ => {
                alert("保存成功");
            }).catch(code => {
                alert("code" + JSON.stringify(code));
            });
        } else {
            alert("error code" + JSON.stringify(data.code));
        }
    }
    _onPrepared(data) {
        console.log(`_onPrepared code : ${data}  `);
        this.cameraGLView.start();
    }

    _onEventChange(event) {
        if (event.code === PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            console.log(`_onEventChange  ------------------- : ${JSON.stringify(event)} `);
            this.setState({bps: event.extra.arg1})
        }
    }

    _onErrorChange(data) {
        console.log(`_onErrorChange code : ${data} `);
    }

    _onRecord(eventData) {
        console.log(` _onRecord  eventData : ${eventData} + data`);
        this.setState({timeRecord: eventData.extra});
    }

    /***   ---------------------------    监听器 end      --------------------------   *****/

    // 获取回看数据
    getRecordData() { 
        this.operateTimeStamps = String(new Date().getTime());
        IMP2pClient.operationFile('0', [0,1], [], [], this.operateTimeStamps);
    }

}

const styles = StyleSheet.create({
    demoBtnContainer: {
        flex: 1,
        backgroundColor: '#F5FCFF',
    },
    main: {
        display: 'flex',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        backgroundColor: 'white',
        height: "100%"
    }
});
