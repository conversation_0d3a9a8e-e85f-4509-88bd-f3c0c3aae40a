// IMIRenderViewManager
import React from 'react';

import {<PERSON>ton, FlatList, ScrollView, StyleSheet, Text, View, } from 'react-native';

import {cameraStyles} from "../../../imilab-rn-sdk/components/camera/CameraStyle";


import {PLAYER_EVENT_CODE, PlayerClass} from "../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import {CameraMethod} from "../../../imilab-rn-sdk/components/camera/CameraMethod";
import {LetDevice} from "../../../imilab-rn-sdk/native/iot-kit/IMIDevice";
import {IntercomModeKey,INTERCOM_MODE} from "../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMICameraVideoView from "../../../imilab-rn-sdk/native/camera-kit/IMICameraVideoView";
import IMIVideoView from "../../../imilab-rn-sdk/native/camera-kit/IMIVideoView";
import IMIRenderViewManager from "../../../imilab-rn-sdk/native/camera-kit/IMIRenderViewManager";
import IMIFile from "../../../imilab-rn-sdk/native/local-kit/IMIFile";
import IMVodPlayView from '../../../imilab-rn-sdk/native/camera-kit/IMVodPlayView';
import IMP2pClient from '../../../imilab-rn-sdk/native/local-kit/IMP2pClient';
import { IMIPermission } from '../../../imilab-rn-sdk';
import { byteArrayToInt4, byteArrayToLong8 } from './GenericUtils';
import moment from 'moment';
import { XText, XView } from 'react-native-easy-app';
// const PLAYER_TYPE = PlayerClass.LIVE;
const PLAYER_TYPE = PlayerClass.VOD;

const TAG = "DoubleVodDemo - ";

export default class DoubleVodDemo extends React.Component {

    constructor(props) {
        super(props);

        this.state = {
            fullScreen: false,
            recording: false,
            speaking: false,
            starting: true,
            bps: 0,
            devInfoText: "",
            devPropText: "",
            devSupport: false,
            speed: 1,
            timeRecord: 0,
            recordData: [],
            dataSource: undefined,
            isEmpty: true
        };
        this.cameraGLView = null;
    }

    /***  ·······        系统函数 start       ·······   *****/

    render() {
        return (
            <View style={styles.main}>

                {this._initVideoRootLayout()}

                {this._testTNDemoLayout()}

            </View>
        )
    }

    UNSAFE_componentWillMount() {
        this.count = 1;
        this.leftData = null;
        this.allBackList = {}; //所有的视频列表
        this.allBackListDetail = {};
        this.timeLineLists = [];
        this.lastBackDay = 0; // 最后一天的毫秒值（时间按照大到小排序）
        this.lastestTime = 0;
      }
    componentDidMount() {
        console.log('componentDidMount-----componentWillUnmount----', new Date());
        // this._initVideoPrepare();
        this._doDevInfoText();
        this._doDevPropTextText()
        if (PLAYER_TYPE === PlayerClass.VOD){
            this.onP2pSendStateListener = IMP2pClient.onFileOperateAddListener(e => {
                console.log('onFileOperateAddListener----------', e)
                if (e.iotId != LetDevice.deviceID) {
                return
                }
                if (e.code === 0 && e.data) {
                    console.log('有流到来 code', e.code);
                    if (!this.getLoadData) {
                        this.getLoadData = new Date().getTime();
                    }
                    console.log('有流到来', JSON.stringify(e));
                    this.loadData(e);
                }
            });
        }
    }

    componentWillUnmount() {
        LetDevice.removeInfoChangeListener();
        LetDevice.removePropertyChangeListener();
        console.log('componentWillUnmount---------', new Date());
        if (this.cameraGLView) {
            this.cameraGLView.destroy();
        }
    }


    /***  ·······    系统函数 end      ·······   *****/

    //视频视图root layout
    _initVideoRootLayout() {

        let bpsTxt = (Number.parseInt(this.state.bps) + 'kb/s');

        return (
            <View
                style={this.state.fullScreen ? cameraStyles.videoContainerFull : cameraStyles.videoContainerNormal}>

                {this._renderVideoView()}

                <View style={{
                    position: 'absolute',
                    flex: 1,
                    width: '100%',
                }}>
                    <Text> bps--- {bpsTxt} </Text>
                    <Text> PlayerClass----{PLAYER_TYPE}  </Text>
                    <Text> record time---{this.state.timeRecord}(ms) </Text>
                </View>
            </View>
        )
    }


    /**
     * 测试按钮布局
     * @returns {*}
     * @private
     */
    _testTNDemoLayout() {
        return (
            <ScrollView>
                <View style={styles.demoBtnContainer}>
                    <Button
                        onPress={this.getRecordData.bind(this)}
                        title='获取回看数据（获取后会自动播放）'
                    />
                    <Button
                        onPress={this._doStartStop.bind(this)}
                        title={this.state.starting ? 'stop' : 'start'}
                        color='#e74d4d'
                    />

                    <Button
                        onPress={this._doStartRecord.bind(this)}
                        title={this.state.recording ? '停止录制' : '开始录制'}
                    />

                   <Button
                        onPress={this._doStartSpeed.bind(this)}
                        title={`回看速度${this.state.speed}`}
                        color='#e74d4d'
                    />

                    <Button
                        onPress={this._doScreenShot.bind(this)}
                        title='截图（保存至传入的参数路径中）'
                        color='#000000B3'
                    />
                    {/* <Button
                        onPress={this._doSetSleep.bind(this)}
                        title='休眠'
                    />
                    <Button
                        onPress={this._doSetSleepClose.bind(this)}
                        title='解除休眠'
                    /> */}


                </View>
                <View style={styles.demoBtnContainer}> 
                    <FlatList
                        style={{width: "100%", height: 400}}
                        data={this.state.recordData}
                        horizontal={false}
                        renderItem={({item, index}) => {
                            return this._renderRecordItem(item, index);
                        }}
                    />
                </View>
            </ScrollView>
        )
    }
    /**
     * { 
                startTime: timestamp * 1000,
                duration: duration,
                eventType: event_type,
                endTime: timestamp * 1000 + duration,
                camera_id, 
                timestamp, 
                event_type, 
                pic_loc
            }
     * @param {*} item 
     * @param {*} index 
     */
    _renderRecordItem = (item, index) => { 
        // console.log('_renderRecordItem---', item);
        // 根据上面数据渲染item
        return <XView style={{flex: 1, flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 10}} > 
        <Button title={`播${item.timestamp}`}  onPress={() => {
            if (PLAYER_TYPE === PlayerClass.VOD) {
                this.cameraGLView && this.cameraGLView.stop();
                this.setState({
                    dataSource: {iotId: LetDevice.deviceID, playerClass: PlayerClass.VOD, cameraNumber: '2', start_time: `${item.timestamp}`, end_time: `${item.timestamp+ item.duration}`, offset:`${0}`},
                    isEmpty: !this.state.isEmpty
                })
                console.log('播放 _renderRecordItem---', item);
                this.cameraGLView && this.cameraGLView.prepare();
            }
        }} />
        <Button title={"镜"+ item.camera_id} />
        <Button title={"下频"} onPress={() => {
            this._downloadPress(item);
            console.log('下载视频 _renderRecordItem---', item);
            // IMVodPlayView.operationFile(item.camera_id, 1, [item.startTime], item.pic_loc, [item.endTime])
        }}/>
        <Button title='下图' onPress={() => {
            alert('下载图片');
            IMP2pClient.operationFile([item.camera_id],[['3']], [[String(item.timestamp)]], [[item.pic_loc]]);
            // IMVodPlayView.operationFile(item.camera_id, 2, [item.startTime], item.pic_loc, [item.endTime])
        }}/>
        {/* <Button title='删频' onPress={() => {
            console.log('删除视频 _renderRecordItem---', item);
            // IMVodPlayView.operationFile(item.camera_id, 3, [item.startTime], item.pic_loc, [item.endTime])
        }}/> */}
        <Button title='删视频' onPress={() => {
            console.log('删除图片 _renderRecordItem---', item);
            this.deletImage(item);
            // IMVodPlayView.operationFile(item.camera_id, 4, [item.startTime], item.pic_loc, [item.endTime])
        }}/>
        </XView>

    }

    deletImage(item) {
        if (this.downloadFlag) {
          console.log('拦截重复点击')
          return
        }
        console.log('deletImage-----');
        IMP2pClient.operationFile([item.camera_id],[['2']], [[String(item.timestamp)]], [[item.pic_loc]]);
        // 每隔两秒去问询一次删除进度
        this.deleteTime && clearInterval(this.deleteTime);
        this.deleteTime = setInterval(() => {
          IMP2pClient.getChannelState(data => {
            if (parseInt(data, 10) === 1) {
              IMP2pClient.operationFile([item.camera_id],[['4']], [], []);
            } else {
              // p2p通道断开
              this.downloadFlag = false;
              console.log('p2p通道断开-----');
              this.deleteTime && clearInterval(this.deleteTime);
            }
          })
        }, 2000)
      }

    _doDevPropTextText = () => {
        this.mPropertyChangeListener = LetDevice.addPropertyChangeListener(data => {
            console.log(`_doDevPropTextText eventData : ${data} + data ${JSON.stringify(data)}`);
            this.setState({devPropText: JSON.stringify(data)});
        });
    };

    _doDevInfoText = () => {
        this.mDevInfoChangeListener =  LetDevice.addInfoChangeListener(data => {
            console.log(`_doDevInfoText eventData : ${data} + data ${JSON.stringify(data)}`);
            this.state.devInfoText = JSON.stringify(data);
            this.setState({devInfoText: JSON.stringify(data)});
        });
    };

    _doCheckSupportMethod() {
        let supportMethod = LetDevice.isSupportMethod(CameraMethod.IMI_ImageFlipState);
        console.log(TAG + 'supportMethod - --- > ' + supportMethod);
        this.setState({devSupport: supportMethod});
    }

    _doSetPropertiesSwitch() {
        LetDevice.propertyOn(CameraMethod.IMI_ImageFlipState).then((data) => {
            alert(data);
            console.log(TAG + '_doSetPropertiesSwitch', data);
        }).catch((error) => {
            alert(error);
            console.log(error);
        });
    }

    _doSetProperties() {
        //两种格式均可以支持
        let params = '{"StatusLightSwitch": 1}';
        // let params = {"StatusLightSwitch": 1};

        LetDevice.setPropertyCloud(params).then(function (data) {
            alert(data);
            console.log(TAG + 'setPropertyCloud data : ', data);
        }).catch((error) => {
            alert(error);
            console.log(error)
        });
    }

    _doSetSleep() {
            const paramJson = JSON.stringify({value: false});
            console.log('10001 -wake_up-setProperties', paramJson);
            LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
                .then(() => {
                    alert(`成功：${res}`);
                    console.log("设备休眠成功", res);
                })
                .catch(() => {
                    alert(JSON.stringify(err));
                    console.log("设备休眠", JSON.stringify(err));
                })
                .finally(() => {
                });
        }
    
        
    _doSetSleepClose() {
        const paramJson = JSON.stringify({value: true});
        console.log('10001 -wake_up-setProperties', paramJson);
        LetDevice.setProperties(true, LetDevice.deviceID, '10001', paramJson)
            .then(() => {
                // this.setState({isSleep: false}, () => {
                    this.IMIVideoView && this.IMIVideoView.prepare();
                // });
            alert(`成功：${res}`);
                console.log("设备休眠成功", res);
            })
            .catch(() => {
                alert(JSON.stringify(err));
                console.log("设备休眠", JSON.stringify(err));
            })
            .finally(() => {
            });
    }


    _doResume() {
        this.cameraGLView.resume();
    }

    _doStart() {
        console.log('start');
        // this.setDataSource();
        console.log('start---');
        this.cameraGLView.prepare();
        this.cameraGLView.start();
    }

    _doStartStop() {
        if (this.state.starting) {
            this.cameraGLView.stop();
        } else {
            // start 无效
            // this.cameraGLView.start();
            this.cameraGLView.prepare();
        }
        this.setState({starting: !this.state.starting})
    }

    _getAllProperties() {
        LetDevice.getAllProperties(true,LetDevice.deviceID).then((res) => {
            alert(`getAllProperties:${res}`);
            console.log("getAllProperties success", res);
        }).catch((err) => {
            alert(JSON.stringify(err));
            console.log("getAllProperties error", JSON.stringify(err));
        });
    }

    /**
     * 开始录制
     * @private
     */
    _doStartRecord() {
        if (!this.state.recording) {
            let timestamp = new Date().getTime();
            //todo 后续通过代码获取手机根目录
            let path = `${IMIFile.storageBasePath}/${LetDevice.deviceID}/${timestamp}.mp4`;
            this.cameraGLView.startRecord(0,path, (eventData) => {
                console.log(`startRecord eventData : ${eventData} `);
            });
        } else {
            this.cameraGLView.stopRecord(0,(eventData) => {
                console.log(`stopRecord eventData : ${eventData} `);
            });
        }
        this.setState({recording: !this.state.recording});
    };

    /**
     * 截图到本地
     * @private
     */
    _doScreenShot = () => {
        let cameraId = 0;
        const screenShotPath = `${IMIFile.storageBasePath}/snapshot111.jpg`;
        console.log("  screenShotPath " + screenShotPath + " IMIVideoView  " + this.cameraGLView);
        this.cameraGLView.screenShot(cameraId,screenShotPath).then(_ => {
            console.log("截图方法调用成功");
        })
    };


    /**
     * 下载
     * @private
     */
    _doDownload = () => {
        // this.cameraGLView.download().then(_ => {
        //     console.log("下载方法调用成功");
        // })
    };
  
    /**
     * 删除
     * @private
     */
    _doDelete = () => {
        // this.cameraGLView.download().then(_ => {
        //     console.log("下载方法调用成功");
        // })
    };
    /**
     * 倍数
     * @private
     */
    _doStartSpeed = () => {
        let speed = this.state.speed;
        if (speed == 1) {
            speed = 2;
        } else if (speed == 2) {
            speed = 4;
        } else if (speed == 4) {
            speed = 8;
        } else if (speed == 8) {
            speed = 16;
        } else {
            speed = 1;
        }
        this.cameraGLView.speed(speed);
        this.setState({speed: speed});
    };
    // "startTime":1546300800,"endTime":1546300951},
    _renderVodVideoView() {
        if (!this.state.dataSource) {
            return null;
        }
        return (
            <View  style={{flex: 1 }}>
                <View style={{flex: 1 }}>
                    <IMVodPlayView
                        style={{height: "100%", width: "100%"}}
                        ref={this._assignRoot}
                        playerClass={PlayerClass.VOD}
                        // mute={true}
                        dataSource={this.state.dataSource}
                        onPrepared={this._onPrepared.bind(this)}
                        onEventChange={this._onEventChange.bind(this)}
                        onErrorChange={this._onErrorChange.bind(this)}
                        onRecordTimeChange={this._onRecord.bind(this)}
                        onCommCallback={this._onCommCallback.bind(this)}
                    />
                </View>
                <View style={{flex: 1 }}>
                    <IMIRenderViewManager style={{height: "100%", width: "100%"}} ref={this._assignRootManager}></IMIRenderViewManager>
                </View>
            </View>
        )
    }

    //初始化VideoView
    _renderVideoView() {
        console.log(TAG + '  _renderVideoView     -- >  state ' + this.state.recordData);
        if (this.state.recordData?.length <= 0){
            return <View/>
        }
        if (PLAYER_TYPE === PlayerClass.VOD) {
            console.log('_renderVideoView 使用vod 播放器进行播放 ');
            return this._renderVodVideoView();
        }
        return <View/>
    }
    // 设置资源
    setDataSource() {
        console.log('PLAYER_TYPE setDataSource-----',PLAYER_TYPE, this.lastestTime);
        this.lastestTime = Math.floor(Date.now() / 1000) - 15*60;
        console.log('PLAYER_TYPE ---lastestTime--',PLAYER_TYPE, this.lastestTime);
        this.setState({
            dataSource: {iotId: LetDevice.deviceID, playerClass: PlayerClass.VOD, cameraNumber: '2', start_time: `${this.lastestTime}`, end_time: `${0}`, offset:'0'}
        })
        // if (PLAYER_TYPE === PlayerClass.VOD) {
        //     this.cameraGLView && this.cameraGLView.setDataSource({iotId: LetDevice.deviceID, playerClass: PlayerClass.VOD, cameraNumber: '2', start_time: `${this.lastestTime}`, end_time: `${0}`, offset:'0'});
        // } else {
        //     this.cameraGLView && this.cameraGLView.setDataSource({iotId: LetDevice.deviceID, playerClass: PlayerClass.LIVE, cameraNumber: '2'});
        // }

    }

    _assignRootManager = component => {
        if (!this._rootManagerView) {
            this._rootManagerView = component;
        }
    };

    _assignRoot = (component) => {
        this.cameraGLView = component;
    };

    //初始化VideoView
    _initVideoPrepare() {
        console.log(`_initVideoPrepare ${this.cameraGLView}`);
        this.setDataSource();
        // 俩端统一先addExtraRenderView再prepare
        // 安卓需要先setCameraId然后在addExtraRenderView，否则无法获取CameraId
        if (this._rootManagerView && this._rootManagerView.setCameraId) {
            console.log('this._rootManagerView setDataSource---setScale--');
            this._rootManagerView.setCameraId(1);
        }
        this.cameraGLView && this.cameraGLView.addExtraRenderView(this._rootManagerView);
        // 延迟200ms，然后执行prepare（防止addExtraRenderView和prepare错序）
        setTimeout(() => {
            this.cameraGLView.prepare();
        }, 200);
    }

    /***   ---------------------------    监听器 start      --------------------------   *****/
    _onCommCallback(data){
        console.log(`_onCommCallback ${JSON.stringify(data)}`);
        if (data.code == 0) {
            const screenShotPath = `${IMIFile.storageBasePath}/snapshot111.jpg`;
            IMIFile.saveImageToPhotosAlbum(screenShotPath, LetDevice.deviceID).then(_ => {
                alert("保存成功");
            }).catch(code => {
                alert("code" + JSON.stringify(code));
            });
        } else {
            alert("error code" + JSON.stringify(data.code));
        }
    }
    _onPrepared(data) {
        console.log(`_onPrepared code : ${data}  `);
        this.cameraGLView.start();
    }

    _onEventChange(event) {
        if (event.code === PLAYER_EVENT_CODE.PLAYER_EVENT_ON_VIDEO_INFO) {
            console.log(`_onEventChange  ------------------- : ${JSON.stringify(event)} `);
            this.setState({bps: event.extra.arg1})
        }
    }

    _onErrorChange(data) {
        console.log(`_onErrorChange code : ${data} `);
    }

    _onRecord(eventData) {
        console.log(` _onRecord  eventData : ${eventData} + data`);
        this.setState({timeRecord: eventData.extra});
    }

    /***   ---------------------------    监听器 end      --------------------------   *****/

    // 获取回看数据
    getRecordData() { 
        this.operateTimeStamps = String(new Date().getTime());
        IMP2pClient.operationFile(['0','1'], ['0','0'], [['0'],['0']], [[],[]], this.operateTimeStamps);
    }
    /***   ---------------------------    回看数据处理      --------------------------   *****/
    loadData = e => {
        //进来请求最近所有的数据
        this.queryThreeMonthData(e.data, this.count);
    };
        /**
     * 暂时缓存最近三个月每天是否有回看视频的情况
     * 请求最近三个月的每个月每天是否有回看的标记数组
     */
    queryThreeMonthData(data, count) {
        const rawData = window.atob(data);
        const uint8Arraypart = new Uint8Array(rawData.length);
        for (let i = 0; i < rawData.length; i++) {
            uint8Arraypart[i] = rawData.charCodeAt(i);
        }
        if (byteArrayToInt4(uint8Arraypart, 4) == 1) {
            this.downloadTime && clearTimeout(this.downloadTime);
            this.downloadVideo(data)
            return;
        }
        if (byteArrayToInt4(uint8Arraypart, 4) !== 0) {
            try {
                const messageResult = JSON.parse(rawData.slice(4, -1));
                if (messageResult.cmd_type === 0 && messageResult.code === 4) {
                    // nodata
                    return false;
                }

                // 获取视频文件索引失败
                if (messageResult.cmd_type === -1 && (messageResult.code === 1001 || messageResult.code === 1004 || messageResult.code === 1011)) {
                    return false;
                }

            } catch (error) {
                console.log(error)
            }
            return;
        }
        this.count++;
        const uint8Array = uint8Arraypart.slice(84);
        const list = uint8Arraypart.slice(0, 84);
        
        let currentFlag = 0;
        list.slice(47, 55).forEach((num, index) => {
            currentFlag += num * Math.pow(256, index);
            });
        if (String(currentFlag) !== this.operateTimeStamps && currentFlag !== 0 ) {
            console.log('不属于这一次数据', JSON.stringify({currentFlag: currentFlag, operateTimeStamps: this.operateTimeStamps, origin: JSON.stringify(list.slice(47, 55))}));
            return false;
        }
        this.waitingData && clearTimeout(this.waitingData);
        if (byteArrayToLong8(list, 39) !== 0) {
            console.log('最新数据来拉', JSON.stringify({time: new Date().getTime()}));
            this.loadMoreData(uint8Array);
            return; 
        }
        const selectTime = this.state.dateData;
        let newDate = null;
        //1970年时间戳，为了去除不正确时间,只有时间不是近几年的 就可以认为失败
        const failureTime = new Date('Wed Jun 10 1970 15:00:00 GMT+0800').getTime();
        const notEmpty = this.allBackList && Object.keys(this.allBackList) && Object.keys(this.allBackList).length > 0;
        const timeLineList = [];
        for (let i = 0; i < uint8Array?.byteLength; i += 28) {
            const camera_id = byteArrayToInt4(uint8Array, i); // 镜头id
            const timestamp = byteArrayToLong8(uint8Array, i + 4); // 开始时间utc , 单位s
            const duration = byteArrayToInt4(uint8Array, i + 12); // 时长
            // const event_type = byteArrayToLong8(uint8Array, i + 16); //时间类型
            const event_type = byteArrayToInt4(uint8Array, i + 16); //时间类型
            const pic_loc = byteArrayToInt4(uint8Array, i + 20); //时间类型
            if (failureTime > timestamp * 1000) {
                continue;
            }
            // 获取最新的回看时间
            if (i === 0 && count === 1) {
                newDate = new Date(timestamp * 1000);
            }

            timeLineList.push({ 
                startTime: timestamp * 1000,
                duration: duration,
                eventType: event_type,
                endTime: timestamp * 1000 + duration,
                camera_id, 
                timestamp, 
                event_type, 
                pic_loc
            })
            //最后一天
            this.lastBackDay = timestamp;
            const timeKey = moment(new Date(timestamp * 1000)).format('yyyy-MM-DD');
            const timeKey1 = moment(new Date(timestamp * 1000)).format('HH') + ':00';
            // console.log('timeKey---', timeKey);
            // console.log('timeKey1----', timeKey1);
        }

    
        let backList = null;
        let findIndex = null;

        // 时间轴最新的
        if (count === 1) {
            try {
                backList = JSON.parse(JSON.stringify(timeLineList))
            } catch (error) {
                console.log('获取JSON解析报错了', JSON.stringify({test: 5}));
                backList = [];
            }
            const lists = timeLineList.reverse();
            this.timeLineLists = lists;

            // 五分钟间隔
            // findIndex = findOneMinuteIndex(backList);
            // 参考米家直接取最新的开始时间
            findIndex = 0;
            this.timeBeingPlayed = backList ? backList[findIndex]?.timestamp : 0;
            this.currentTime = this.timeBeingPlayed;

            this.lastestTime = backList[0]?.timestamp;
            // this.loadMoreDataTimeOut && clearInterval(this.loadMoreDataTimeOut);
            // // 从获取第一包数据开始每隔1分钟获取一次新数据
            // this.loadMoreDataTimeOut = setInterval(() => {
            //     console.log('要求最新数据的时间到了', JSON.stringify({count: count, currentCount: this.count}));
            //     this.needMoreData();
            // }, 61 * 1000);
            if (this.lastestTime > 0) {
                this.setState({recordData: this.timeLineLists});
                // 延迟初始化，否则安卓计算宽高有问题，iOS无影响
                setTimeout(() => {
                    this._initVideoPrepare();
                }, 100);
            }
        } else {
            const lists = timeLineList.reverse();
            this.timeLineLists.unshift(...lists);
            // console.log('timeLineLists----', this.timeLineLists);
            // 按照startTime倒序排序
            this.timeLineLists.sort((a, b) => {
                // 倒序排序：后发生的事件排在前面
                return b.timestamp - a.timestamp;
            });
            this.setState({recordData: this.timeLineLists});
        }
        // changeTimeLineLists(this.timeLineLists);
        // showLoading(false);
        this.currentData = backList ? backList : this.state.backList;
        // this.setState(
        // {
        //     currentDate: newDate ? newDate : this.state.currentDate,
        //     dateData: count === 1 ? selectTime : this.state.dateData,
        //     palyStartTime: backList ? backList[findIndex] : this.state.palyStartTime,
        //     backList: this.timeLineLists,
        //     isLoading: !this.currentCanPlay ? this.state.isLoading : notEmpty ? this.state.isLoading : count === 1,
        //     hasData: true,
        //     noData: false,
        // },
        // () => {
        //     if (count === 1) {
        //     this.timelineViewNew && this.timelineViewNew.scrollToTimestamp(this.timeBeingPlayed * 1000);
        //     }
        // },
        // );
    }

    // 发送最新数据
    needMoreData() {
        console.log('要求最新数据', JSON.stringify({time: this.lastestTime, operateTimeStamps: this.operateTimeStamps}));
        // IMP2pClient.operationFile('0', [String(this.lastestTime)], [], this.operateTimeStamps);
        // 双目数据（多目）
        IMP2pClient.operationFile(['0','1'], ['0','0'], [[String(this.lastestTime)],[String(this.lastestTime)]], [[],[]], this.operateTimeStamps);
        // 单目数据
        // IMP2pClient.operationFile(['0'], ['0'], [[String(this.lastestTime)]], [[]], this.operateTimeStamps);
    }
    // 加载最新数据
    loadMoreData(uint8Array) {
        //1970年时间戳，为了去除不正确时间,只有时间不是近几年的 就可以认为失败
        const failureTime = new Date('Wed Jun 10 1970 15:00:00 GMT+0800').getTime();
        const timeLineList = [];
        for (let i = 0; i < uint8Array?.byteLength; i += 28) {
            const camera_id = byteArrayToInt4(uint8Array, i); // 镜头id
            const timestamp = byteArrayToLong8(uint8Array, i + 4); // 开始时间utc , 单位s
            const duration = byteArrayToInt4(uint8Array, i + 12); // 时长
            // const event_type = byteArrayToLong8(uint8Array, i + 16); //时间类型
            const event_type = byteArrayToInt4(uint8Array, i + 16); //时间类型
            const pic_loc = byteArrayToInt4(uint8Array, i + 20); //时间类型
            if (failureTime > timestamp * 1000) {
                continue;
            }
            // 获取最新的回看时间
            if (i === 0) {
                this.lastestTime = timestamp;
            }

            timeLineList.push({ 
                startTime: timestamp * 1000,
                duration: duration,
                eventType: event_type,
                endTime: timestamp * 1000 + duration,
                camera_id, 
                timestamp, 
                event_type, 
                pic_loc
            })

        
        // changeAllBackList(this.allBackList);
        // changeAllBackListDetail(this.allBackListDetail);
        }

        const list = timeLineList.reverse();
        // this.timeLineLists = getTimeLineLists()
        this.timeLineLists.push(...list);
        // this.timelineViewNew.initData(this.timeLineLists);
        // changeTimeLineLists(this.timeLineLists)
        console.log('111this.timeLineLists', this.timeLineLists);
        this.timeLineLists.sort((a, b) => {
            // 倒序排序：后发生的事件排在前面
            return b.timestamp - a.timestamp;
        });
        this.setState({
            recordData: this.timeLineLists,
        });
        // if (!this.state.hasData) {
        //     this.setState({
        //         hasData: true,
        //         noData: false,
        //         hideVideo: false
        //     })
        // }
    }

    // 下载
    _downloadPress = (item) => {
        console.log('_downloadPress  item', item);
        if (this.downloadFlag) {
            console.log('拦截重复点击')
            return
        }
        this.downloadFlag = true;
        //需要检测权限
        IMIPermission.startCheckPermission(IMIPermission.PERMISSION_READ_EXTERNAL_STORAGE, status => {
            if (status === 0) {
            IMIPermission.startCheckPermission(IMIPermission.PERMISSION_WRITE_EXTERNAL_STORAGE, status2 => {
                if (status2 === 0) {
                    this.isfinish = false;
                    this.currentVideoLength = 0;
                    IMP2pClient.operationFile([item.cameraId],['1'], [[String(item.timestamp)]], [[String(item.picLoc)]]);
                    this.downloadTime = setTimeout(() => {
                        this.downloadFlag = false;
                    }, 10000)
                } else if (status2 === -1) {
                    this.downloadFlag = false;
                    alert(stringsTo('storage_permission_denied'));
                }
            });
            } else if (status === -1) {
                this.downloadFlag = false;
                alert(stringsTo('storage_permission_denied'));
            }
        });
    };
    downloadVideo(data) {
        const rawData = window.atob(data);
        const uint8Arraypart = new Uint8Array(rawData.length);
        for (let i = 0; i < rawData.length; i++) {
            uint8Arraypart[i] = rawData.charCodeAt(i);
        };
        const uint8Array = uint8Arraypart;
        const camera_id = byteArrayToInt4(uint8Array, 35); // 镜头id
        const timestamp = byteArrayToLong8(uint8Array, 39); // 开始时间utc , 单位s
        const cur_pack_len = byteArrayToInt4(uint8Array, 47);
        const seq = byteArrayToInt4(uint8Array, 51);
        const total_file_len = byteArrayToInt4(uint8Array, 55);
        const saveFilePath = `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/${camera_id}_${timestamp}.mp4`;
        console.log('===========', this.preSeq, cur_pack_len, seq, total_file_len, this.currentVideoLength);
        
        this.downloading && clearTimeout(this.downloading)
    
        if (this.isfinish) {
          return
        }
    
        this.currentVideoLength += uint8Array.slice(84).length;
        if (this.preSeq && Math.abs(this.preSeq - seq) > 1 || this.currentVideoLength >= total_file_len) {
          this.isfinish = true
        }
        if (!this.isfinish) {
          this.downloading = setTimeout(() => {
            console.log('超时直接保存-------------')
            console.log('视频超时20s直接保存', JSON.stringify({preSeq: this.preSeq, unit: '', saveFilePath: `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, isFinish: '0'}));
            this.saveFile('', `${TMP_SNAPSHOT_PATH_PREFIX}${LetDevice.deviceID}/video/11111.mp4`, '0')
          }, 20000)
        }
        this.preSeq = seq;
        console.log('视频下载传递', JSON.stringify({isfinish: this.isfinish, cur_pack_len, total_file_len, saveFilePath, currentVideoLength: this.currentVideoLength}));
        this.saveFile(this.uint8ArrayToBase64(uint8Array.slice(84)), saveFilePath, this.isfinish ? '0' : '-1')
      }
    
    saveFile = (unit, saveFilePath, isFinish) => {
        IMP2pClient.downloadPlaybackVideo(unit, saveFilePath, isFinish).then(res => {
            console.log('downloadPlaybackVideo ===========', res)
                if (res !== '-1') {
                this.downloading && clearTimeout(this.downloading)
                this.downloadFlag = false;
                this.preSeq = 0;
                // 推送到相册
                IMIFile.saveVideoToPhotosAlbum(`${saveFilePath}`, LetDevice.deviceID)
                    .then(_ => {
                        console.log('saveVideoToPhotosAlbum===========', _);
                    })
                    .catch(error => {
                        console.log('saveVideoToPhotosAlbum error===========', error);
                    });
                }
        }).catch(e => {
            console.log("downloadPlaybackVideo --11", e);
            this.downloadFlag = false;
            this.preSeq = 0;
            this.downloading && clearTimeout(this.downloading)
            
        });
    }

}

const styles = StyleSheet.create({
    demoBtnContainer: {
        flex: 1,
        backgroundColor: '#F5FCFF',
    },
    main: {
        display: 'flex',
        flexDirection: 'column',
        flexWrap: 'nowrap',
        backgroundColor: 'white',
        height: "100%"
    }
});
