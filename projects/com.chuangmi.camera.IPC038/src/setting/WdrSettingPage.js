import React from 'react';

import {StyleSheet, View,Image} from 'react-native';

import NavigationBar from "../../../../imi-rn-commonView/NavigationBar/NavigationBar";
import ListItmeWithSwitch from "../../../../imilab-design-ui/src/widgets/settingUI/ListItmeWithSwitch";
import Separator from "../../../../imilab-design-ui/src/widgets/settingUI/Separator"
import I18n, {stringsTo} from '../../../../globalization/Localize';
import BaseDeviceComponent from "../../../../imilab-rn-sdk/components/BaseDeviceComponent";
import {XText,XView} from "react-native-easy-app";
import {LetDevice} from "../../../../imilab-rn-sdk";
import {showLoading, showToast} from "../../../../imilab-design-ui";
/**
 * 宽动态范围模式设置页面
 */

export default class WdrSettingPage extends BaseDeviceComponent {

    static propTypes = {};

    constructor(props, context) {
        super(props, context);
        this.state = {
            wdrSwitch:true
        }
    }
    componentDidMount() {

        LetDevice.getPropertyCloud('WDRSwitch').then((value) => {
            console.log('getPropertyCloud$$$$$$$$$$$$' + value);
            this.setState({
                wdrSwitch: value==1?true:false
            });
        }).catch(error => {
            console.log(JSON.stringify(error))
        });

    }

    render() {


        return (<XView style={styles.container}>

            <NavigationBar
                title={I18n.t('wdrMode')}
                left={[{key: NavigationBar.ICON.BACK, onPress: ()=>this.props.navigation.pop()}]}
                right={[]}
            />
            <Separator/>

                <ListItmeWithSwitch title={I18n.t('wdrMode')} value={this.state.wdrSwitch}
                                    onValueChange={(value) => {
                                        if(value){
                                            showLoading(stringsTo('commWaitText'), true);
                                            LetDevice.propertyOn("WDRSwitch").then(()=>{
                                                showLoading(false);
                                                this.setState({
                                                    wdrSwitch:value
                                                });
                                                showToast(I18n.t('settings_set_success'));
                                                console.log('成功');
                                            }).catch(err=>{
                                                this.setState({
                                                    wdrSwitch:!value
                                                });
                                                showLoading(false);
                                                showToast(I18n.t('operationFailed'));
                                            });
                                        }else{
                                            showLoading(stringsTo('commWaitText'), true);
                                            LetDevice.propertyOff("WDRSwitch").then(()=>{
                                                showLoading(false);
                                                this.setState({
                                                    wdrSwitch:value
                                                });
                                                showToast(I18n.t('settings_set_success'));

                                                console.log('off-成功');
                                            }).catch(err=>{
                                                this.setState({
                                                    wdrSwitch:!value
                                                });
                                                showLoading(false);
                                                showToast(I18n.t('operationFailed'));
                                            });
                                        }
                                    }}/>
            <XText style={styles.smallTextStyle} allowFontScaling={false} text={I18n.t('wdrFunctionHint')}
                   numberOfLines={3}/>

            <View style={styles.imageContainer}>
                <View style={styles.imageItemStyle}>
                    <Image style={styles.imageStyle} source={require('../../resources/images/wdr_normal.png')}></Image>
                    <XText style={styles.textBelowImageStyle} text={I18n.t("wdr_before")}/>
                </View>
                <View style={styles.imageItemStyle}>
                    <Image style={styles.imageStyle} source={require('../../resources/images/wdr_mode.png')}></Image>
                    <XText style={styles.textBelowImageStyle} allowFontScaling={false} text={I18n.t('wdr_after')}/>
                </View>
            </View>


            <XText style={styles.smallTextStyle} allowFontScaling={false} text={I18n.t('wdrHint')+"\n"} numberOfLines={6}/>


        </XView>);
    }

}


const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: "#FFFFFF",
    },
    smallTextStyle: {
        color: '#7F7F7F',
        fontSize: 12,
        marginTop: 13,
        marginHorizontal: 14,
    },
    imageContainer:{
        flexDirection: 'row',
        marginTop:29,
        marginBottom:14
    },
    imageItemStyle:{
        flex: 1,
        alignItems: 'center'
    },
    imageStyle:{
        width:159,
        height:89
    },
    textBelowImageStyle:{
        color:"#333333",
        fontSize: 14,
        marginTop:14
    }
});
