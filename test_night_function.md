# 夜视功能设置页面双摄兼容测试

## 功能验证清单

### 1. 双摄设备判断逻辑
- [x] 添加 `this.isTwoCamera` 判断逻辑
- [x] 基于 `DeviceTemplatesUtils.getConfigProject('camera', LetDevice.model).cameraNumber === '2'`

### 2. 物模型映射更新
- [x] 双摄设备：全彩夜视=3，黑白夜视=2，智能夜视=4
- [x] 单摄设备：保持原有映射（全彩夜视=2，黑白夜视=0，智能夜视=1）

### 3. 新文案添加
- [x] 中文文案：
  - `dual_camera_full_color_title`: "全彩夜视"
  - `dual_camera_full_color_subtitle`: "夜间开白光灯，可做夜灯使用，24小时彩色成像"
  - `dual_camera_black_white_title`: "黑白夜视"
  - `dual_camera_black_white_subtitle`: "夜间红外补光，隐蔽性高，画面黑白"
  - `dual_camera_smart_title`: "智能夜视"
  - `dual_camera_smart_subtitle`: "夜间黑白夜视，检测到人形时切换为全彩夜视"
- [x] 英文文案对应翻译

### 4. UI优化
- [x] 双摄设备选中状态下，副标题颜色改为绿色(#12AA9C)
- [x] 单摄设备保持原有灰色(#828282)

### 5. 微光全彩开关处理
- [x] 双摄设备隐藏微光全彩开关view
- [x] 双摄设备不请求物模型10005数据

### 6. 返回值处理
- [x] 双摄和单摄设备分别返回对应的文案

## 测试场景

### 单摄设备测试
1. 进入夜视功能设置页面
2. 验证显示三个选项：自动切换、一直打开、一直关闭
3. 验证微光全彩开关显示
4. 验证选中状态UI（主标题绿色，副标题灰色）
5. 验证物模型值：2、0、1

### 双摄设备测试
1. 进入夜视功能设置页面
2. 验证显示三个选项：全彩夜视、黑白夜视、智能夜视
3. 验证微光全彩开关隐藏
4. 验证选中状态UI（主标题和副标题都为绿色）
5. 验证物模型值：3、2、4
6. 验证新文案显示正确

## 兼容性验证
- [x] 不影响现有单摄设备功能
- [x] 双摄设备新功能正常工作
- [x] 物模型10003仍然使用
- [x] 双摄设备不请求物模型10005

## 最新修改内容

### 新增功能
1. **副标题颜色优化**
   - 单摄和双摄设备选中状态下，副标题颜色都改为绿色(#12AA9C)
   - 提升UI一致性和视觉效果

2. **双摄设备智能夜视说明图片**
   - 在智能夜视选项下方添加两个示意图
   - 左图：全彩夜视 (ic_full_color.webp)
   - 右图：黑白夜视 (ic_black_white.webp)
   - 布局规格：
     - 左间距：20px
     - 右间距：20px
     - 图片间距：13px
     - 图片平分剩余宽度
     - 距离上方组件：20px
     - 文案距离图片：14px
     - 文案大小：14px
     - 文案颜色：#333333

## 代码修改总结

### 主要文件修改
1. `projects/com.chuangmi.camera/src/setting/NightFunctionPage.js`
   - 添加双摄判断逻辑
   - 更新物模型映射
   - 优化UI显示（副标题颜色）
   - 隐藏微光全彩开关
   - 调整数据请求逻辑
   - 更新返回值处理
   - 新增智能夜视说明图片组件

2. `globalization/string/cn.js`
   - 添加双摄设备专用中文文案

3. `globalization/string/en.js`
   - 添加双摄设备专用英文文案

### 关键实现点
- 使用 `this.isTwoCamera` 进行设备类型判断
- 条件渲染UI组件
- 动态物模型值映射
- 分离单摄和双摄的业务逻辑
- 响应式图片布局设计
