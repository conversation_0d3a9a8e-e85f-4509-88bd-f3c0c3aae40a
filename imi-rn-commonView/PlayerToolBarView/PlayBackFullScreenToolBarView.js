/**
 * PlayBackFullScreenToolBarView.js 回看的横屏toolbar
 *
 * @property {style} style - 外层样式
 * @property {func} exitPress - 退出事件
 *
 * @property {func} playPress -  播放点击事件
 * @property {boolean} playDisabled - 播放否可点击
 * @property {boolean} isPlay - 是否播放
 *
 * @property {func} mutePress - 静音点击事件
 * @property {boolean} muteDisabled - 静音是否可点击
 * @property {boolean} mute - 是否静音
 *
 * @property {string} speedTitle - 播放倍率文案
 * @property {func} speedPress -  播放倍率点击事件
 * @property {boolean} speedDisabled - 播放倍率是否可点击
 *
 * @property {func} screenshotPress - 截图点击事件
 * @property {boolean} screenshotDisabled - 是否可点击
 *
 * @property {func} recordPress - 录像点击事件
 * @property {boolean} recordDisabled - 是否可点击
 * @property {array} recording - 是否录像中
 *
 * @property {func} fullScreenPress - 全屏点击事件
 * @property {boolean} fullScreenDisabled - 是否可点击
 *
 * @property {array} moreItems - 更多自定义按钮 参考[{item:{isText: true, data: ["流畅"], onPress: null, disabled: true,dataIndex:0},insertIndex:0},]
 *
 * 示例：
 * <PlayBackFullScreenToolBarView
 *  exitPress={this._exitFullScreen}
 *  qualityData={this.state.qualityData}
 *  qualityIndex={this.state.qualityIndex}
 *  screenshotPress={this._onPressScreenshot}
 *  recordPress={this._onPressRecord}
 *  recording={this.state.recording} />
 *
 * <AUTHOR>
 * @date 2021/06/10
 */

import React, {Component} from 'react';
import PropTypes from 'prop-types';
import {View} from 'react-native';
import TouchableOpacityImage from '../TouchableOpacityImage/TouchableOpacityImage';
import TouchableOpacityText from '../TouchableOpacityText/TouchableOpacityText';
import {LetDevice} from '../../imilab-rn-sdk';

export default class PlayBackFullScreenToolBarView extends Component {
  static propTypes = {
    style: PropTypes.any,
    exitPress: PropTypes.func,

    playPress: PropTypes.func,
    playDisabled: PropTypes.bool,
    isPlay: PropTypes.bool,

    mutePress: PropTypes.func,
    muteDisabled: PropTypes.bool,
    mute: PropTypes.bool,

    speedTitle: PropTypes.string,
    speedPress: PropTypes.func,
    speedDisabled: PropTypes.bool,
    speedAccessibilityLabel: PropTypes.string,
    // qualityTitle: PropTypes.string,
    // qualityPress: PropTypes.func,
    // qualityDisabled: PropTypes.bool,

    screenshotPress: PropTypes.func,
    screenshotDisabled: PropTypes.bool,

    recordPress: PropTypes.func,
    recordDisabled: PropTypes.bool,
    recording: PropTypes.bool,

    fullScreenPress: PropTypes.func,
    fullScreenDisabled: PropTypes.bool,

    moreItems: PropTypes.array,
    isShowSpeedItem: PropTypes.bool,
  };

  static defaultProps = {
    exitPress: null,

    playPress: null,
    playDisabled: false,
    isPlay: true,

    speedTitle: '1X',
    speedPress: null,
    speedDisabled: false,
    speedAccessibilityLabel: 'play_back_clarity_show_1X',
    // qualityTitle: "流畅",
    // qualityPress: null,
    // qualityDisabled: false,

    mutePress: null,
    muteDisabled: false,
    mute: true,

    screenshotPress: null,
    screenshotDisabled: false,

    recordPress: null,
    recordDisabled: false,
    recording: false,

    fullScreenPress: null,
    fullScreenDisabled: false,

    moreItems: [],
    isShowSpeedItem: true,
  };

  constructor(props, context) {
    super(props, context);
    this.state = {};
  }

  render() {
    let items = [
      {
        isText: false,
        data: [require('./res/icon_play_white.png'), require('./res/icon_pause_white.png')],
        onPress: this.props.playPress,
        disabled: this.props.playDisabled,
        dataIndex: this.props.isPlay ? 1 : 0,
        accessibilityLabel: ['play_back_pause_full_screen', 'play_back_start_full_screen'],
      },
      {
        isText: false,
        data: [require('./res/landscape_mute.png'), require('./res/landscape_volume.png')],
        onPress: this.props.mutePress,
        disabled: this.props.muteDisabled,
        dataIndex: this.props.mute ? 0 : 1,
        accessibilityLabel: ['play_back_voice_off_full_screen', 'play_back_voice_on_full_screen'],
      },
      {
        isText: true,
        data: [this.props.speedTitle],
        onPress: this.props.speedPress,
        disabled: this.props.speedDisabled,
        dataIndex: 0,
        accessibilityLabel: [this.props.speedTitle],
      },
      {
        isText: false,
        data: [require('./res/landscape_screenshot.png')],
        onPress: this.props.screenshotPress,
        disabled: this.props.screenshotDisabled,
        dataIndex: 0,
        accessibilityLabel: ['play_back_screenshots_off_full_screen'],
        key: 'shot',
      },
      {
        isText: false,
        data: [require('./res/landscape_recording.png'), require('./res/landscape_record.png')],
        onPress: this.props.recordPress,
        disabled: this.props.recordDisabled,
        dataIndex: this.props.recording ? 0 : 1,
        accessibilityLabel: ['play_back_record_screen_on_full_screen', 'play_back_record_screen_off_full_screen'],
        key: 'record'
      },
      {
        isText: false,
        data: [require('./res/icon_fullscreen.png')],
        onPress: this.props.fullScreenPress,
        disabled: this.props.fullScreenDisabled,
        dataIndex: 0,
        accessibilityLabel: ['play_back_full_screen'],
      },
    ];
    if (this.props.moreItems.length > 0) {
      this.props.moreItems.forEach((item, index) => {
        if (item.insertIndex < items.length) {
          items.splice(item.insertIndex, 0, item.item);
        } else {
          items.push(item.item);
        }
      });
    }

    // if (LetDevice.model == "a1FKrifIRwH" || LetDevice.model == "a1Ikkj5vsiK" || LetDevice.model == "a1znn6t1et8") {// 026,021e01,036不支持倍速播放
    //     items.splice(2, 1)
    // }

    if (!this.props.isShowSpeedItem) {
      // 026,021e01,036 回看不支持倍速播放  云存支持
      items.splice(2, 1);
    }
    // 云存储不支持录像
    if (this.props.ignoreRecord) {
      // 获取当前所在位置
      let currentIndex = -1;
      Array.isArray(items) && items.map((res, index) => {
        if (res.key === 'record') {
          currentIndex = index
        }
      })
      currentIndex !== -1 && items.splice(currentIndex, 1);
    }
    // 不支持截图
    if (this.props.ignoreShot) {
      // 获取当前所在位置
      let currentIndex = -1;
      Array.isArray(items) && items.map((res, index) => {
        if (res.key === 'shot') {
          currentIndex = index
        }
      })
      currentIndex !== -1 && items.splice(currentIndex, 1);
    }

    return (
      <View
        style={[
          {flexDirection: 'row', width: '100%', height: 50, alignItems: 'center', paddingRight: 30},
          this.props.style,
        ]}>
        {/*将左边距由20改为28，解决某些机型返回键被挖孔遮挡的问题 #6978*/}
        <TouchableOpacityImage
          style={{marginLeft: 28}}
          imageStyle={{width: 30, height: 30}}
          source={require('./res/landscape_back.png')}
          onPress={this.props.exitPress}
          accessibilityLabel={'play_back_back_full_screen'}
        />
        <View style={{flex: 1}} />
        {items.map((item, index) => {
          return item.isText ? (
            <TouchableOpacityText
              key={`PlayBackFullScreenToolBarItem${index}`}
              title={item.data[item.dataIndex]}
              style={{marginHorizontal: 3, opacity: item.disabled ? 0.2 : 1}}
              textStyle={{
                color: item.disabled ? '#999999' : '#FFFFFF',
                fontSize: item.data[item.dataIndex].length > 3 ? 13 : 15,
                fontWeight: 'bold',
                padding: 4,
              }}
              disabled={item.disabled}
              onPress={item.onPress}
              accessibilityLabel={
                item.accessibilityLabel ? item.accessibilityLabel[item.dataIndex] + '_full_screen' : ''
              }
            />
          ) : (
            <TouchableOpacityImage
              key={`PlayBackFullScreenToolBarItem${index}`}
              style={{marginHorizontal: 7, opacity: item.disabled ? 0.2 : 1}} //全屏状态内宿未设置透明度
              imageStyle={{width: 30, height: 30}}
              source={item.data[item.dataIndex]}
              disabled={item.disabled}
              onPress={item.onPress}
              accessibilityLabel={
                item.accessibilityLabel ? item.accessibilityLabel[item.dataIndex] + '_full_screen' : ''
              }
            />
          );
        })}
      </View>
    );
  }
}
