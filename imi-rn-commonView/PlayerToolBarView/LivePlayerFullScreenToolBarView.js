/**
 * LivePlayerFullScreenToolBarView.js 直播的横屏toolbar
 *
 * @property {style} style - 外层样式
 * @property {func} exitPress - 退出事件
 *
 * @property {string} qualityTitle - 清晰度文案
 * @property {func} qualityPress - 清晰度点击事件
 * @property {boolean} qualityDisabled - 清晰度是否可点击
 *
 * @property {func} mutePress - 静音点击事件
 * @property {boolean} muteDisabled - 静音是否可点击
 * @property {boolean} mute - 是否静音
 *
 * @property {func} screenshotPress - 截图点击事件
 * @property {boolean} screenshotDisabled - 是否可点击
 *
 * @property {func} recordPress - 录像点击事件
 * @property {boolean} recordDisabled - 是否可点击
 * @property {array} recording - 是否录像中
 *
 * @property {array} moreItems - 更多自定义按钮 参考[{item:{isText: true, data: ["流畅"], onPress: null, disabled: true,dataIndex:0},insertIndex:0},]
 *
 * 示例：
 * <LivePlayerFullScreenToolBarView
 *  exitPress={this._exitFullScreen}
 *  qualityData={this.state.qualityData}
 *  qualityIndex={this.state.qualityIndex}
 *  screenshotPress={this._onPressScreenshot}
 *  recordPress={this._onPressRecord}
 *  recording={this.state.recording} />
 *
 * <AUTHOR>
 * @date 2020/11/24
 */

import React, {Component} from "react";
import PropTypes from 'prop-types';
import {TouchableOpacity, View} from "react-native";
import TouchableOpacityImage from "../TouchableOpacityImage/TouchableOpacityImage";
import TouchableOpacityText from "../TouchableOpacityText/TouchableOpacityText";
import {isAndroid, isIos} from "../../imilab-rn-sdk/utils/Utils";
import {XText} from "react-native-easy-app";

export default class LivePlayerFullScreenToolBarView extends Component {

    static propTypes = {
        style: PropTypes.any,
        exitPress: PropTypes.func,

        qualityTitle: PropTypes.string,
        qualityPress: PropTypes.func,
        qualityDisabled: PropTypes.bool,

        mutePress: PropTypes.func,
        muteDisabled: PropTypes.bool,
        mute: PropTypes.bool,

        screenshotPress: PropTypes.func,
        screenshotDisabled: PropTypes.bool,

        recordPress: PropTypes.func,
        recordDisabled: PropTypes.bool,
        recording: PropTypes.bool,

        accessibilityLabel: PropTypes.string,
        moreItems: PropTypes.array,
        showGreyDisableIcon:PropTypes.bool
    };

    static defaultProps = {
        exitPress: null,

        qualityTitle: "流畅",
        accessibilityLabel:'home_page_clarity_show_low',
        qualityPress: null,
        qualityDisabled: false,

        mutePress: null,
        muteDisabled: false,
        mute: true,

        screenshotPress: null,
        screenshotDisabled: false,

        recordPress: null,
        recordDisabled: false,
        recording: false,

        moreItems: [],
        showGreyDisableIcon:false
    };

    constructor(props, context) {
        super(props, context);
        this.state = {}
    }


    render() {
        let items = [{
            isText: true,
            data: [this.props.qualityTitle],
            onPress: this.props.qualityPress,
            disabled: this.props.qualityDisabled,
            dataIndex: 0,
            accessibilityLabel:[this.props.qualityTitle],
        }, {
            isText: false,
            data: [require("./res/landscape_mute.png"), require("./res/landscape_volume.png")],
            onPress: this.props.mutePress,
            disabled: this.props.muteDisabled,
            dataIndex: this.props.mute ? 0 : 1,
            accessibilityLabel:["home_page_voice_off","home_page_voice_on"]
        }, {
            isText: false,
            data: [require("./res/landscape_screenshot.png")],
            onPress: this.props.screenshotPress,
            disabled: this.props.screenshotDisabled,
            dataIndex: 0,
            accessibilityLabel:["home_page_screenshots"]
        }, {
            isText: false,
            data: [require("./res/landscape_recording.png"), require("./res/landscape_record.png")],
            onPress: this.props.recordPress,
            disabled: this.props.recordDisabled,
            dataIndex: this.props.recording ? 0 : 1,
            accessibilityLabel:["home_page_record_screen_on","home_page_record_screen_off"]
        }];
        if (this.props.moreItems.length > 0) {
            this.props.moreItems.forEach((item, index) => {
                if (item.insertIndex < items.length) {
                    items.splice(item.insertIndex, 0, item.item);
                } else {
                    items.push(item.item)
                }
            });
        }

        return (
            <View style={[{flexDirection: "row", width: "100%", height: 50, alignItems: "center", paddingRight: 30}, this.props.style]}>
                {/*将左边距由20改为28，解决某些机型返回键被挖孔遮挡的问题 #6978*/}
                <TouchableOpacityImage style={{marginLeft: 28}} imageStyle={{width: 30, height: 30}}
                                       source={require("./res/landscape_back.png")}
                                       onPress={this.props.exitPress}
                                       accessibilityLabel ={"home_page_full_screen_back"}
                />
                <View style={{flex: 1}}/>
                {
                    items.map((item, index) => {
                        return (
                            item.isText ? (
                                <TouchableOpacity key={`playerFullScreenToolBarItem${index}`}
                                                      style={{marginHorizontal: 3,marginRight:14}}
                                                      disabled={item.disabled}
                                                      onPress={item.onPress}
                                                      accessibilityLabel={item.accessibilityLabel?item.accessibilityLabel[item.dataIndex]+"_full_screen":""}
                                >
                                    <View style={{
                                        // justifyContent: "center",
                                        // alignItems: "center",
                                        // borderWidth: 1,
                                        // borderRadius: 5,
                                        // borderColor:item.disabled ? "#999999" : "#FFFFFF",
                                        // minHeight: 20,
                                        // minWidth:30,
                                        // paddingHorizontal:2
                                        justifyContent: "center",
                                        alignItems: "center",
                                        borderWidth: 1.67,
                                        borderRadius: 2.67,
                                        borderColor: "#FFFFFF",
                                        minHeight: 20,
                                        minWidth:30,
                                        paddingVertical:isAndroid()?0:3, //Android会自动给字体加margin,为了保证双端看起来高度一致，分别处理
                                        paddingHorizontal: 2,
                                        opacity: item.disabled ? 0.2 : 1
                                    }}>
                                        <XText style={{
                                            color: "#FFFFFF",
                                            fontSize: item.data[item.dataIndex].length > 3 ? 11 : 11, fontWeight: "bold",
                                            textAlign: 'center', fontFamily: isIos() ? null : ''
                                        }}
                                               text={item.data[item.dataIndex]}
                                        />
                                    </View>
                                </TouchableOpacity>
                            ) : (
                                <TouchableOpacityImage key={`playerFullScreenToolBarItem${index}`}
                                                       style={{marginHorizontal: 7, opacity: item.disabled ? 0.2 : 1}}
                                                       imageStyle={{width: 30, height: 30,marginRight:4,tintColor: (item.disabled && this.props.showGreyDisableIcon)? "#999999" : null}}
                                                       source={item.data[item.dataIndex]}
                                                       disabled={item.disabled}
                                                       onPress={item.onPress}
                                                       accessibilityLabel={item.accessibilityLabel?item.accessibilityLabel[item.dataIndex]+"_full_screen":""}
                                />
                            )
                        );
                    })
                }
            </View>
        );
    }

}
