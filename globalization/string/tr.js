const localeConfig = {
    calendarLocaleConfig: {
        formatAccessibilityLabel: 'dddd d \'of\' MMM<PERSON> \'of\' yyyy',
        monthNames: [
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON>',
            'May',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>'
        ],
        monthNamesShort: [
            '<PERSON>ca<PERSON>',
            '<PERSON><PERSON>',
            'Mar',
            '<PERSON><PERSON>',
            'May',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON>yl',
            '<PERSON><PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>'
        ],
        dayNames: ['<PERSON>', '<PERSON>zt', '<PERSON>', '<PERSON><PERSON>', 'Per', 'Cum', 'Cts'],
        dayNamesShort: ['<PERSON>', 'Pzt', 'Sal', 'Çar', 'Per', 'Cum', 'Cts']
    }
}

export default {
    ...localeConfig,
    Sunday: "Paz",
    Monday: "Pzt",
    Tuesday: "Sal",
    Wednesday: "Çar",
    Thursday: "Per",
    Friday: "Cum",
    Saturday: "Cts",
    SundayShort: "Paz",
    MondayShort: "Pzt",
    TuesdayShort: "Sal",
    WednesdayShort: "Çar",
    ThursdayShort: "Per",
    FridayShort: "Cum",
    SaturdayShort: "Cts",
    MonthAndDay: "M/D",
    popo_setting_camera_text: "Kamera ayarları",
    alarmSettingText: "Ev gözetimi ayarları",
    playBackText: "Kaydı oynat",
    cameraTestTipsTitle2: "Ağ bağlantı hızı testi",
    help_callback: "Yardım ve geri bildirim",
    quality_360: "360P",
    quality_25k_v2: "2.5K",
    sd_360p: "360p",
    hd_1080p: "2k",
    quality_1080: "1080P",
    resolution_sd: "SD",
    resolution_qhd: "QHD",
    quality_1440p: "1440P",
    quality_sd_new: "360P",
    quality_uhd: "2K",
    quality_sd: "SD",
    quality_fhd: "HD",
    quality_common_sd: "480P",
    quality_fhd_3k: "3K HD",
    quality_common_3K: "3K",
    quality_2k: "2K",
    all_events_str: "Tüm olaylar",
    peopleDetectStr: "İnsan şekli etkinliği",
    bellEventStr: "Kapı zili etkinliği",
    detectDistanceModalSubStr033: "Hassasiyet ayarı yapılırken PIR tarafından hareket algılandığında LED turuncu renkte yanıp sönecektir.",
    doorkeyAreaDetectAlarmSubStr: "Kilit alanda birisi tespit edildiğinde kapı zili 10 saniye boyunca akustik-optik alarm verecektir.",
    doorbellSetting: "Kapı zili ayarları",
    record_files_sdcard: "Micro SD kartı",
    gatewaySettingTitle: "Hub ayarları",
    wifiSettingText: "Wi-Fi ayarları",
    gateWayLightEffectText: "Hub LED’lerinin tanımı",
    quality_low: "Akıcı",
    network_not_connected: "Ağ bağlantısı anormal, lütfen bağlantıyı kontrol edin ve tekrar deneyin",
    device_offline: "Cihaz çevrim dışı",
    power_off: "Cihaz uyku modunda",
    onlyDoInLive: "Lütfen işlemden önce canlı yayını açın",
    imi_speaking_block: "Konuşma yapılıyor, çıkılamaz",
    screen_recording: "Bu özellik kayıt sırasında kullanılamaz",
    saved_system_album: "Zapisano w albumie telefonu",
    action_fail: "İşlem başarısız",
    storage_permission_denied: "Depolama izni yok",
    save_system_album_failed: "Kayıt süresi çok kısa",
    save_album_failed: "Video kaydı başarısız oldu",
    commLoadingText: "Yükleniyor, lütfen bekleyin...",
    error_code_common_retry: "Yeniden denemek için dokunun",
    error_help: "Yardımı görüntüle",
    camera_calling: "Konuşma yapılıyor, çıkılamaz",
    call_busy_error: "Çağrı meşgul",
    call_busy_tips: "Diğer cihaz görüşmede, lütfen daha sonra tekrar deneyin",
    call_connect_error: "Ağ hatası, lütfen daha sonra tekrar deneyin",
    net_connect_error: "Ağ hatası",
    device_offline_aready: "Cihaz çevrim dışı,",
    offlineTime: "Çevrimdışı zamanı",
    connect_err: "Bağlantı başarısız",
    connect_error_help_one: "1. Lütfen cep telefonu ve kameranın ağ bağlantısının düzgün çalışıp çalışmadığını kontrol edin; yönlendiriciyi ve kamerayı yeniden başlatmanız önerilir.",
    offline_help_tip_reconect: "yeniden bağlan",
    dot_for_modal: ".",
    connect_error_help_two: "2. WiFi şifresi değiştirilmiş veya ağ bağlantısı değişmişse lütfen  ",
    connect_error_help_three: "3. Yukarıdakilerle sorununuzu çözemiyorsanız lütfen ",
    connect_error_help_four: "4. Yukarıdakiyle sorun çözülmezse lütfen",
    offline_help_tip_feed_question: "geri bildirim",
    know_button: "Anladım",
    offline_help_tip_one: "1. Lütfen kameranın ağ bağlantısının düzgün çalışıp çalışmadığını kontrol edin; yönlendiriciyi ve kamerayı yeniden başlatmanız önerilir.",
    offline_help_two: "2. Güç kaynağı normalse gösterge ışığını kontrol edin:",
    offline_help_two_first: "Mavi ışığın yavaş/hızlı yanıp sönmesi: Lütfen kameranın bir ağa bağlı olup olmadığını kontrol edin. WLAN adı veya şifresi değiştirildiyse lütfen",
    offline_help_two_two: "Sarı ışığın hızla yanıp sönmesi: Kamera sıfırlandı, lütfen",
    offline_help_tip_bind: "Yeniden bağla",
    offline_help_two_three: "Sarı ışığın yavaşça yanıp sönmesi: Kamera güncelleniyor, lütfen erişim sağlamadan önce güncellemenin tamamlanmasını bekleyin. Kapatmayın.",
    offline_three1: "3. Lütfen cihazı yönlendiriciye mümkün olduğunca yakın tutmaya ve duvar gibi engelleri azaltmaya çalışın. İkisi birbirine ne kadar yakınsa, cihazın",
    offline_three2: "teorik olarak ağ RSSI değeri o kadar yüksek olur",
    offline_three3: "ve bu da daha güçlü bir ağ sinyali anlamına gelir. RSSI değerinin -50’den büyük olması önerilir.",
    offline_help_tip_the_first: "Sabit sarıysa: lütfen ",
    offline_help_tip_connect_service: "Müşteri hizmetleriyle iletişime geçin",
    offline_help_tip_the_thd: " bir yazılım yükseltme paketi almak için.",
    offline_help_tip_the_sec: "Sabit veya yanıp sönen maviyse: lütfen ",
    offline_help_tip_forth: "4. Lütfen kamerayı kontrol edin ",
    offline_help_tip_rssi: "ağ bilgisi ",
    offline_help_tip_forth_first: "RSSI değeri, değer -50'den düşükse, lütfen yönlendiriciye yaklaştırmayı deneyin.",
    offline_help_four: "4. Yukarıdakilerle sorununuzu çözemiyorsanız lütfen",
    offline_help_five: "5. Yukarıdakiyle sorun çözülmezse lütfen",
    offline_help_tip_the: "3. Lütfen kamera göstergesini kontrol edin:",
    offline_help_tip_fth: "5. Yukarıdakilerle sorununuzu çözemiyorsanız lütfen ",
    alarm_turn_on_str: "Lütfen ev gözetimini etkinleştirin",
    fees_for_renewal: "Yenile",
    toolbar_text_resolution: "Çözünürlük",
    toolbar_text_sound: "Ses",
    toolbar_text_snap: "Enstantane fotoğraf",
    toolbar_text_record: "Kaydet",
    toolbar_text_fullscreen: "Tam Ekran",
    select_all: "Hepsini seç",
    unselect_all: "Tüm seçimleri kaldır",
    cancel: "Iptal",
    commWaitText: "Lütfen bekleyin...",
    ok_button: "Tamam",
    dialog_never_show: "Bir daha gösterme",
    cruise_seconds: "sn",
    am: "ÖĞLEDEN ÖNCE",
    pm: "ÖĞLEDEN SONRA",
    year: "Y",
    month: "M",
    day: "D",
    hour: "H",
    minute: "M",
    give_away: "Özgür",
    cloud_give_price_str: "Değer",
    cloud_give_ok: "Elde etmek",
    picker_start_time: "Başlat",
    picker_end_time: "Bitir",
    alarm_direction_custom: "Özel zaman",
    step_preview: "Geri",
    account_next: "Sonraki",
    stayDetectStr: "Hareket algılama",
    video_downloading: "İndiriliyor...",
    noAlarmVideo: "Kullanılabilir video yok",
    cloud_time_out_str: "Akıllı planın süresi doldu",
    buy_again: "Yenile",
    cloudExpireShowImageHint: "Bulut video yedeklemesini etkinleştirmek için bulut depolama alanı satın alın >",
    network_available_need_check: "Ağ erişilemez durumda. Lütfen ağ ayarlarını kontrol edin",
    housekeeping_no_event: "Henüz içerik yok",
    commLoadingFailText: "Yüklenemedi, tekrar deneyin",
    commLoadingMoreDataText: "Daha fazla veri yükleniyor, lütfen bekleyin...",
    commNoMoreDataText: "Tüm veriler yüklendi",
    commLoadingClickText: "Yenilemek için dokunun",
    abnormal_event_fire: "Kapınız yangını algılıyor",
    abnormal_event_damaged: "Kapınız zorlandı",
    abnormal_event_anti_password: "Şifre ele geçirmeye karşı uyarı",
    abnormal_event_anti_fingerprint: "Parmak izi hırsızlığına karşı uyarı",
    abnormal_event_password: "Uyarı kilidini açmak için şifre denemesi",
    abnormal_event_fingerprint: "Uyarı kilidini açmak için parmak izi denemesi",
    abnormal_event_nfc: "Uyarı kilidini açmak için NFC denemesi",
    abnormal_event_face: "Uyarı kilidini açmak için yüz denemesi",
    abnormal_event_multiple: "Uyarının kilidini açmak için çift doğrulama denemesi",
    abnormal_event_door_time_out: "Kapı uzun süre kapalı kalmadı",
    abnormal_event: "Anormal etkinlik",
    lingerChangeText: "Kişi olayı",
    alarm_change_string: "Gözetlenen alanda hareket algılandı",
    doorbellPressTitle: "Birisi kapı ziline basıyor",
    downloadingDeleteError: "İndiriliyor, lütfen daha sonra deneyin",
    delete_success: "Silme başarılı",
    delete_failed: "Silme başarısız",
    video_downloaded: "İndirme tamamlandı",
    save_failed: "Kaydedildi",
    delete: "Silin",
    chargeGuide: "Şarj uyarısı",
    category_doorbell: "zil",
    category_camera: "Güvenlik Kamerası",
    chargeGuideSubtitle: "Kameranın 5V/2A güç adaptörü ile tamamen şarj olması yaklaşık 5 saat sürecektir.",
    normalCharge: "Şarj etme yöntemi 1",
    hubCharging: "Şarj etme yöntemi 2",
    hubConnectHintTitle: "İyi bir sinyale sahip olmak için şunlar önerilir:",
    hubConnectHintSubtitle: "1. Hub’ı yerden daha yükseğe yerleştirmeye çalışın. \n2. Hub, Ethernet kablosuyla bağlandığında modemin yaklaşık 0,5 metre uzağına yerleştirin. \n3. Hub'ı duvarlardan yaklaşık 0,5 metre uzak bir noktaya yerleştirin. \n4. Hub’ı köşelere yerleştirmekten kaçının.",
    button_finish: "Tamamlandı",
    button_add_device: "Daha fazla cihaz ekleyin",
    cameraTestTips2: "Kurulum yerindeki ağ ortamı zayıfsa, bağlantı hatalarına veya video aksaklıklarına neden olabilir",
    noSignalText: "Sinyal yok",
    perfectText: "Mükemmel",
    strongText: "Güçlü",
    goodText: "İyi",
    weakText: "Zayıf",
    cameraTestStrong: "Mevcut konum, %{code} kurulumu için iyi",
    cameraTestGood: "Mevcut konum, %{code} kurulumu için uygun",
    cameraTestWeak: "Mevcut konumda sinyal zayıf, lütfen %{code} cihazını hub’a daha yakın bir konuma yerleştirin",
    cameraTestNoSignal: "Mevcut konumda sinyal yok. Lütfen %{code}'u hub'ınıza yaklaştırın",
    no_sd_card_go_buy_cloud: "Bulut Depolama Satın Alın",
    downloadTip: "İndirilecek videoyu oynatın",
    alarm_download_downloading: "İndiriliyor...",
    storage_no_sdcard_please_buy: "Micro SD kart yok, lütfen takın veya satın alın",
    no_sd_card_tips_text: "Bulutta saklanan video daha güvenli olacaktır.",
    cameraTestWifiSpeedTitle: "Kurulum talimatları",
    cameraTestTipsTitle1: "Kurulum kılavuzu",
    cameraTestTips1: "%{code} cihazını hub'a mümkün olduğunca yakın bir yere yerleştirin. Kurulumdan önce sinyal testi için telefonunuzu ve %{code} cihazınızı yanınızda taşıyın.",
    re_play: "Yeniden oynat",
    doorbellIndicatorLightPageTitle: "Kapı zili göstergesi açıklaması",
    doorbellPressLight: "Dış kapı zili çalıyor",
    doorbellPressSubtitle: "Kapı zili tuşuna tıklayın, tuşun kenarındaki ışıklı gösterge yanıp sönmeye başlar ve kapı zili sesi duyulur",
    cameraOnSubtitle: "Mavi ışığı görene kadar sync düğmesine basın ve 2 saniye basılı tutun, ardından açılma müziğini duyacaksınız.",
    cameraOffSubtitle: "Kırmızı ışık bir kez yanıp sönene kadar sync düğmesine basın ve 8 saniye basılı tutun, ardından kapanma müziğini duyacaksınız.",
    doorbellUsbChargeTitle: "Kapı zilini şarj etmek için USB’yi kullanın",
    usbChargeSubtitle: "Kırmızı ışık üç kez yanıp söner ve şarja başlar",
    doorbefullChargeTitle: "Kapı zili şarjı tamamlandı (tam doldu)",
    fullChargeSubtitle: "Sabit yeşil",
    cameraResetSubTitle: "Gösterge mavi renkte yanıp sönene kadar sync düğmesine basın ve 5 saniye basılı tutun, ardından düğmeyi bırakın ve sıfırlamaya başlayın. Kamera, sıfırlama işlemi sırasında yeniden açılacaktır.",
    reset_success_text: "Sıfırlama başarılı",
    resetSuccessSubtitle: "Yanıp sönen mavi",
    pairedSuccessTitle: "Eşleme başarılı",
    pairedSuccessSubtitle: "5 saniye boyunca sabit mavi",
    pairedFailTitle: "Eşleme başarısız",
    pairedFailSubtitle: "Mavi ve turuncu 3 kez sırayla yanıp sönüyor",
    pirSensitivityAdjustSubtitle: "PIR hassasiyetini ayarlarken, PIR birini algıladığında gösterge bir kez turuncu renkte yanıp sönecektir. Eğer bir süre birisi tespit edilirse gösterge sürekli turuncu renkte yanıp sönecektir.",
    pirGetPeopleTitle: "kamera bir hareket algıladığında",
    pirGetPeopleSubtitle: "Turuncu ışık bir kez yanıp söner",
    pirAlwaysGetPeopleTitle: "kamera sürekli olarak hareket algıladığında",
    pirAlwaysGetPeopleSubtitle: "Turuncu ışık sürekli olarak yanıp söner",
    gateWayLightPageTitle: "Hub LED’lerinin Açıklaması",
    light_blue: "Mavi gösterge",
    light_blue_flash: "Yanıp sönen mavi",
    light_blue_flash_case: "Ağ bağlantısı devam ediyor (bağlı değil)",
    light_blue_on: "Sabit mavi",
    light_blue_on_case: "Ağ bağlantısı başarılı (bağlı)",
    light_orange: "Turuncu gösterge",
    light_orange_flash: "Yanıp sönen turuncu",
    light_orange_flash_case: "Bağlantı için bekleniyor (cihaz ve uygulamanın bağlantı işlemi devam ediyor)",
    light_orange_on: "Sabit turuncu",
    light_orange_on_case1: "Başlatılıyor",
    light_orange_on_case3: "Aygıt yazılımı güncellemesi devam ediyor",
    light_blue_orange_mix_head: "Mavi/",
    light_blue_orange_mix_tail: "Turuncu gösterge",
    light_blue_orange_mix_flash: "Mavi ve turuncu 3 kez sırayla yanıp sönüyor",
    light_blue_orange_mix_flash_case: "Aygıt yazılımı güncellemesi başarısız",
    gateway_reset: "Hub sıfırlama",
    gateway_reset_hint1: "Hub bağlantı işlemi sırasında, gösterge ışığı turuncu renkte yanıp sönmezse lütfen hub’ı sıfırlayın.",
    gateway_reset_hint2: "Hub’ın arkasındaki reset düğmesine basın ve gösterge ışığı sabit turuncuya dönüşene kadar 5 saniye basılı tutun, sonra düğmeyi bırakın. Sıfırlama işleminde hub yeniden başlar. Gösterge, turuncu renkte yanıp sönmeye başladığında bu, sıfırlama başarılı olur ve bağlantı işlemine yeniden başlanabilir.",
    indicatorLightPageTitle: "Kamera LED’i Tanımı",
    cameraOnOffLight: "Kameranın açılması/kapatılması",
    cameraOnTitle: "Kameranın açılması",
    cameraOffTitle: "Kameranın kapatılması",
    cameraChargeTitle: "Kameranın şarj edilmesi",
    usbChargeTitle: "Kamerayı şarj etmek için USB’yi kullanın",
    fullChargeTitle: "Kameranın şarjı tam dolu.",
    sunChargeTitle: "Kamerayı şarj etmek için güneş panelini kullanın",
    cameraResetTitle: "Kameranın sıfırlanması",
    pirSensitivityAdjustTitle: "Hareket algılama hassasiyeti ayarı",
    injectSuccess: "Çıkarma başarılı",
    sdcard_format_success: "Biçimlendirme başarılı",
    sdcard_format_fail: "Biçimlendirme başarısız",
    sdCardFormating: "SD kart biçimlendirme devam ediyor",
    injectFailed: "Çıkarma başarısız",
    noSdCardTitle: "Şu anda SD kart yok",
    injectSdCardTitle: "SD kartı güvenli bir şekilde çıkarın",
    injectSdCardSubtitle: "Kayıt dosyasının bozulmasını önlemek için SD kartı güvenli bir şekilde çıkarın",
    sdCardFormat: "SD kartı biçimlendirin",
    sdCardDamaged: "Hafıza kartı hatası",
    injectSdCardHint: "SD kartı güvenli bir şekilde çıkarmayı onaylıyor musunuz?",
    formatTitle: "SD kartı biçimlendirmeyi onaylıyor musunuz?",
    formatMessage: "Biçimlendirme sonrasında SD kart boşaltılmış olacaktır",
    stayTimeStr: "Algılama Modu",
    houseDetectedPeopleStr: "Evin önünde biri algılandığında",
    peopleRecordPowerMoreStr: "Hemen kaydedin",
    peopleRecordPowerMoreSubStr: "Hareket algılama tetiklendiğinde videoyu kaydedin.  (yüksek güç tüketimi)",
    peopleRecordPowerCommonStr: "Ayarlanan zamandan sonra kaydedin",
    peopleRecordPowerCommonSubStr: "Yalnızca biri uzun süre kaldığında kaydedin. (genel güç tüketimi)",
    peopleRecordPowerLessStr: "Kapatın",
    peopleRecordPowerLessSubStr: "Hareket algılama kapalı (daha az güç tüketimi)",
    firstSetStayTimeFailed: "Varsayılan ayarlar kullanıldı, lütfen ayarlamak için [Ev Gözetim Ayarlarına] gidin",
    commTitleSettingText: "Ayarlar",
    feature_set: "İşlev ayarı",
    comm_setting_title: "Genel ayarlar",
    device_name: "Cihaz adı",
    check_update: "Güncellemeleri kontrol et",
    shareUser_tip: "Paylaşılan cihaz için izin yok",
    shared_setting: "Paylaşılan cihazlar",
    confirm_deletion_device: "Cihazı silmeyi onaylıyor musunuz?",
    preset_sleep_set: "Uyku perspektifi ayarları",
    comm_setting_remove_device: "Cihazı sil",
    update_device_name: "Cihaz adını değiştir",
    input_name: "İsim girin",
    settings_set_success: "Ayar başarılı",
    play_back_text_all: "Tüm videoları oynat ",
    wake_up: "Uyan",
    operationFailed: "Ayarlanamadı",
    people_event: "İnsan Algılama",
    move_event: "Hareket algılama",
    alarm_loud_switch: "Ses Algılama",
    no_human_event: "Gözetimsiz Algılama",
    fence_detect_switch: "Çitten geçiş algılama",
    cry_event: "Ağlama algılama",
    keyAreaDetectStr: "Etkinlik bölgesi algılama",
    moveEvent: "Hareket algılama",
    peopleEvent: "İnsan algılama",
    soundEvent: "Yüksek ses algılama",
    bottom_house_keeping: "Olaylar",
    str_housekeeping_tip_guide_people: "o Gözetlenen alanda insan tespit edildi",
    str_housekeeping_tip_guide_sound: "o Anormal ses algılandı",
    str_housekeeping_tip_guide_fence: "o Gözetlenen alanda çitten geçiş tespiti",
    str_housekeeping_tip_guide_nobody: "o Gözetlenen alanda kimse görünmedi",
    str_housekeeping_tip_guide_move: "o Gözetlenen alanda tespit edilen değişiklikler",
    str_housekeeping_tip_guide_cry: "o Ağlama algılandı",
    str_housekeeping_tip_guide_important: "o kilit bölge insan tespit edildi.",
    str_housekeeping_tip_title: "Ev gözetimi etkinleştirildiğinde, kamera \n uyarı resimlerini kaydeder ve size bildirimler gönderir",
    go_to_open: "Aç",
    delete_alert: "Silmeyi onaylıyor musunuz?",
    delete_title_loading: "Sil...",
    cloudTip: "Sınırsız depolama için bulut depolamayı etkinleştirerek daha fazla etkinlik tanınırlığı sağlayın",
    x_flat_list_no_more_data: "Tüm veriler yüklendi",
    downLoadTitle: "İndir",
    select_tip: "Dosyaları seçin",
    delete_title: "Sil",
    save_success: "Kaydedildi",
    buy_cloud_for_info: "Bulut depolama alanı satın alın ve bulut yedeklemesini etkinleştirin",
    download_system_album: "İndirme başarılı",
    video_download_fail: "İndirme başarısız",
    delete_alert_tip: "Bu video şunları içerir",
    delete_alert_tip1: "etkinlik(ler), silmeye devam edilsin mi?",
    bottom_house_video_keeping: "Video detayları",
    currentPlay: "Şu anda oynatılan",
    loadMsgInfo: "Mesajlar",
    loadMsgInfoError: "Yüklenen mesajlar",
    bottom_video_album: "Albüm",
    select_title_1: "Seç",
    album_video_play: "Oynat",
    album_video_pause: "Duraklat",
    album_video_mute: "Sessiz",
    album_video_voice: "Ses",
    album_video_full_screen: "Tam ekran",
    video_download_fail_warning: "Video yükleniyor, lütfen daha sonra tekrar deneyin",
    bottom_cloud_storage: "IMI Bulut",
    change_event_str: "Olayı değiştir",
    open_setting: "Ayarla",
    no_video_data_new: "Kullanılabilir video yok",
    playback_no_video_data_tip: "Hey, bugün video yok. Başka bir günü dene",
    date_format_yyyy_mm_dd: "yyyy/MM/dd",
    panoramicSuccess: "Panoramik resim başarılı",
    panoramicError: "Panoramik resim oluşturulamadı, lütfen daha sonra tekrar deneyin",
    direction_end_009: "Artık dönemem",
    isDataUsageTip: "Mobil ağ, otomatik duraklatma altında",
    Panoramic_loading: "Panoramik resim oluşturuluyor……",
    set_onekey_tit: "tek tuşla alarm şu an ayarlansın mı?",
    set_onekey_msg: "Ayarladıktan sonra, alarm ışığını ve sesli uyarıyı tetiklemek için düğmeye basabilirsiniz.",
    set_onekey_sure: "şimdi ayarla",
    popo_setting_storage_text: "Depolama Ayarları",
    sdcard_error_out: "Micro SD kart çıkarıldı，Lütfen Micro SD kartı tekrar takın veya çıkarı！",
    audio_permission_denied: "Ses kaydetme yetkisi yok",
    panoramicing_tip: "Panoramik resim oluşturuluyor……",
    camera_guide_for_zoomed_str: "Yakınlaştırmak/uzaklaştırmak için \n çift tıklayın/sıkıştırma hareketi yapın",
    camera_guide_for_panoramic_str: "Panorama moduna girmek için buraya dokunun",
    expiredHint: "   Bulut depolama paketinizin süresi bugün bitiyor, daha sonra videoları görüntüleyemeyecek ya da kaydedemeyeceksiniz",
    sdcard_tip_cancel: "Tamam",
    sd_need_format_no_index_massage: "Normal şekilde kaydetmek için SD kartı biçimlendirin, SD kart şimdi biçimlendirilsin mi?",
    sd_need_format_has_old_index_massage: "Cihaz yazılımı yükseltildi, daha iyi oynatma deneyimi elde etmek için SD kartı biçimlendirin, SD kart şimdi biçimlendirilsin mi?",
    is_the_calibration: "Kalibre ediliyor…",
    calibration_completed: "Kamera kalibrasyonu başarılı",
    netWorkError: "Ağ bağlantısı kesildi",
    operate_time_out: "İşlem zaman aşımı",
    hotspot_connect_hint: "Lütfen “imi_xxxxxx”e bağlanın ve ardından geri dönün",
    goto_wifi: "WiFi Seçin",
    play_back_text: "Kaydı çal",
    sdcard_status7: "Hafıza kartı başlatılamadı",
    toolbar_text_sleep: "Uyku",
    quality_auto: "Oto",
    sdcard_format_title_tips: "Bunun için lütfen SD kartı biçimlendirin!",
    sdcard_format_title_tips_content: "Bellek kartı başlatılmamış, normal kullanımdan önce biçimlendirilmesi gerekiyor. Formatlama, bellek kartındaki tüm verileri silecektir. Formatlamaya devam etmek istiyor musunuz?",
    upgrade_state_content: "Cihazda yazılımın son sürümü tespit edildi",
    upgrade_state_content_end: "Şimdi yükseltin",
    sleepTipTitle: "Cihaz uyku modu",
    sleepTipContent: "Kamera çalışmayı ve video bilgilerini kaydetmeyi durduracaktır",
    cloud_will_time_out_str: "Akıllı planın süresi dolmak üzere",
    cloud_time_out_effect_str: "Akıllı plan hizmeti, süresi dolduğunda kullanılamaz",
    temporarily_not: "Geçici olarak hayır",
    formatting_btn: "Biçimlendirme",
    storageCardFormating: "Biçimlendirdikten sonra Micro SD kart boş olacaktır...",
    waitFailedTip: "Lütfen daha sonra tekrar deneyin",
    targetPushTitle_subtitle: "çözmek için tıklayın",
    targetPush_sdcard_format: "SD kullanılmadan önce başlangıç durumuna getirilmelidir!",
    sdcard_status_error: "Hafıza kartı hatası",
    get_success: "Başarıyla Alındı",
    Panoramic_tip: "Panoramaya tıklayarak kamera görüntüsünü kontrol edebilirsiniz",
    Panoramic_title: "Panorama oluştur",
    Panoramic_title_reset: "Panoramayı tekrar çekmek istiyor musunuz?",
    storage_services: "Bulut depolama hizmeti",
    storage_services_content: "Bulut depolama hizmeti nedir?\n",
    storage_services_title: "Bulut depolama ayrıcalıkları",
    imi_cloud_storage_rights_title1: "7/24 Bakım",
    imi_cloud_storage_rights_detail1: "Video indirme",
    imi_cloud_storage_rights_title2: "Akıllı Algılama",
    imi_cloud_storage_rights_detail2: "Hiçbir ayrıntı kaçırmama",
    imi_cloud_storage_rights_title3: "Bulut depolama",
    imi_cloud_storage_rights_detail3: "İstediğiniz zaman oynatma",
    imi_cloud_storage_rights_title4: "Şifreli yükleme",
    imi_cloud_storage_rights_detail4: "Gizlilik önlemleri",
    storage_after: "Daha sonra",
    imi_cloud_experience_now: "Denemek",
    play_back_tit: "Video oynatıcısı",
    play_back_change_time: "Geçiş süresi",
    alarmText: "Olaylar",
    goto_live_view: "Canlı",
    str_housekeeping_tip_value: "o kişi gözetim alanında algılandı\no ekran değişiklikleri gözetim alanında algılandı\no Anormal ses algılandı",
    common_error: "Yüklenemedi, cihaz durumunu kontrol edin",
    sdCardName: "SD kart",
    no_video_data_failed: "Video dosyası alınamadı",
    retry_connect: "Yeniden bağlan",
    collapse: "Daralt",
    noData: "Veri yok, lütfen yeniden seçin",
    playback_no_event: "Etkinlik yok",
    keyArea: "Aktivite bölgesi",
    change_date: "Tarih değiştir",
    todayTitle: "Bugün",
    preDayTitle: "Dün",
    sdcard_status0: "İyi",
    sdcard_status1: "Mikro SD kart yok",
    sdcard_status6: "Hafıza kartı dolu",
    sdcard_status3: "Hata",
    sdcard_status4: "Biçimlendirme",
    sdcard_status5: "Micro SD kartı çıkarın",
    sdcard_status9: "Düzeltiliyor",
    sdcard_status10: "Çıkartılıyor",
    sdcard_status2: "Yeterli bellek yok",
    sdcard_status_normal_new1: "Hafıza kartı durumu",
    play_back_text_all_title: "Tüm videolar",
    max_download_limit: "Yalnızca tek bir indirme desteklenir",
    delete_failed_limit: "Bir seferde en fazla 50 işlem",
    delete_connect_failed: "Cihaz bağlantısı kesildi",
    delete_failed_inPlay: "Oynatılmakta olan videolar silinemez",
    sensitivity_for_high_tit: "Yüksek hassasiyet",
    sensitivity_for_low_tit: "Düşük hassasiyet",
    alarm_sensitivity: "Uyarı hassasiyeti",
    sensitivity_for_high_subtit: "İnsanlardan veya nesnelerden gelen hareketi algılar, tek bir hareket bile gözden kaçmaz",
    sensitivity_for_low_subtit: "Büyük sesler algılandığında alarm çal",
    date_picker_time_title: "Lütfen başlangıç ve bitiş zamanını seçin",
    alarm_time_set_time: "Gözetim süresini seçin",
    time_equal: "Başlangıç ​​zamanı bitiş zamanına eşit olamaz",
    alarm_time_night: "Gece gözetimi",
    alarm_time_all: "24 saat gözetim",
    alarm_time_day: "Gündüz gözetimi",
    alarm_time_set: "İzleme süresi",
    full_color_vision_title: "Renkli gece görüşü",
    alarm_time_24: "24 saat",
    alarm_time_info: "Hareket veya insan algılandığında uyar",
    alarm_time_208: "20.00 - 08.00 ertesi gün",
    fullColor_smart_tit: "Akıllı Gece Görüşü",
    alarm_time_820: "8.00-20.00",
    noDIYTimeTip: "Lütfen özel bir zaman seçin",
    voice_for_wu: "OLUMSUZ",
    voice_for_warning: "Alarm sesi",
    voice_for_dingdong: "Kapı zili sesi",
    voice_for_welcome: "Merhaba, hoş geldiniz",
    voice_for_area: "Gözetleme alanına girdiniz",
    voice_for_closedoor: "Arkanızdan kapıyı kapatın lütfen. Teşekkürler",
    voice_for_safe: "Lütfen güvende olun",
    voice_for_stairs: "Merdivenlerden yukarı ve aşağı çıkarken lütfen güvenlik konusuna dikkat edin",
    voice_for_dangerArea: "Tehlikeli bölge, lütfen mevcut bölgeden ayrılın",
    allDay_time: "24 saat gözetim",
    potlight_flash: "Işık yanıp sönsün",
    potlight_not_bright: "Işık yanmasın",
    potlight_long_bright: "Işık yansın",
    day_time: "Gündüz gözetimi",
    night_time: "Gece gözetimi",
    voice_for_custom: "Özel",
    soundLightAlarm: "Sesli ve ışıklı alarm",
    only_people_detected: "Sadece insanı algıla",
    effective_time: "Bir gözetim dönemi seçin",
    tip_voice_selected: "İstem sesini seçin",
    potlight_alarm_mode: "Alarm lambası ayarları",
    potlight_contain_time: "Belirlenen süre",
    click_warning_tit: "Tek tuşla alarm",
    local_device_connect_wifi: "WiFi Seçin",
    comm_setting_faq: "Ortak sorun",
    angelMoreSetting: "Daha fazla ayar >>",
    voice_for_enter_name: "İsim girin",
    max_device_name: "İsim en fazla 15 karakter içerebilir",
    customer_phone: "Phone Number：************",
    customer_email: "E-mail: <EMAIL>",
    customer_wx: "WeChat Official Accounts：创米数联",
    customer_web: "Web sitesi: www.imilabglobal.com",
    voice_for_add: "Ekle",
    voice_for_edit: "Düzenle",
    imi_save: "Kaydet",
    voice_for_name: "İsim",
    imi_input_text_tip: "Bu karakter desteklenmiyor",
    voice_for_tip_tit_time: "Alarm sesi seçimi",
    voice_for_re_record: "Yeniden kayıt",
    voice_for_click_record: "Bas ve Konuş",
    nursingTimeSetting: "Gözetim süresi ayarı",
    show_upgrade_app: "Lütfen uygulamayı en son sürüme yükseltin",
    imi_fence_tit: "Bir nesnenin/insanın ok yönünde çit çizgisini geçtiği tespit edildiğinde, bu bir çitten geçiş tespit durumu olarak algılanır.",
    imi_switch_dection: "Ok dönüşü",
    alarm_event_tit: "Algılama olayı",
    move_track: "Hareket takibi",
    settings_alarm_human_track_title: "İnsan takip etme",
    no_human: "Gözetimsiz Algılama",
    alarm_event_manager: "Ev gözetim yönetimi",
    alarmTimeSetting: "Uyarı aralığı",
    time_minutes: "dakika",
    settings_alarm_push_time_sub: "Müdahale olduğunu düşünüyorsanız, lütfen zaman aralığını artırın.",
    message_push_manager: "Olay bildirim yönetimi",
    message_push_phone: "Hareket algılandığında telefonunuza anlık bildirim alın.",
    tip_time_minute: "dakika",
    recordType: "Kayıt modu",
    recordType1: "Görü,ntü",
    recordType2: "Video",
    recordTypeSub: "Video moduna geçmek için bulut depolamayı etkinleştirin",
    alarm_info_set: "Alarm türü ayarları",
    alarm_sound_detection: "Yüksek ses algılama",
    detection_sensitivity: "Algılama hassasiyeti",
    alarm_info_push: "Alarm mesajı bildirimi",
    message_push_switch: "Alarm mesajı bildirim anahtarı",
    preset_opened: "Açıldı",
    preset_closed: "Kapat",
    audio_broadcast: "Sesli hatırlatma",
    black_white_vision_title: "Siyah beyaz gece görüşü",
    settings_light_title: "Durum ışığı",
    Data_usage_warning: "Akış koruması",
    data_usage_warning_intro: "Mobil ağlarda otomatik olarak oynatılmaz",
    intelligent_cruise: "AI Gezinme",
    settings_switch_off: "KAPALI",
    cruise_all_view: "Panorama",
    cruise_favorite: "Ön ayar",
    sleep_set: "Uyku ayarlar",
    setttings_infared: "Gece görüş ayarları",
    settings_flip_title: "Görüntüyü döndür",
    settings_flip_subtitle: "Lütfen kamera ters takıldığında bu seçeneği etkinleştirin",
    imageSetting: "Görüntü ayarları",
    setting_picture_setting: "Diğer görüntü ayarları",
    imi_camera_correct_pos: "Kamera Kalibrasyonu",
    setting_reset: "Cihazı yeniden başlat",
    calibration_to_continue_30: "Kamera kalibrasyonunun tamamlanması yaklaşık 30 saniye sürecektir. Devam edilsin mi?",
    calibration_to_continue: "Kamera kalibrasyonunun tamamlanması yaklaşık 25 saniye sürecektir. Devam edilsin mi?",
    calibration_failure: "Kamera kalibrasyonu başarısız",
    setting_reset_msg: "Cihazın yeniden başlatılması biraz zaman alacaktır. Yeniden başlatmayı onaylıyor musunuz?",
    settings_watermark_title: "Filigran",
    pictureCorrection: "Lens bozukluğunu düzeltme",
    pictureCorrectionSubtitle: "Bu özellik açıldığında bozulma azalır, ancak görüş alanı daralır.",
    wdrMode: "Geniş Dinamik Alan (WDR) Modu",
    audio_broadcast_switch: "Sesli hatırlatma",
    rn_version: "Eklenti sürüm No.:",
    network_info: "Ağ bilgisi",
    wifi_signal_0: "Yeni WiFi'de sinyal yok",
    wifi_signal_2: "Cihazın ağ bağlantısı zayıf.",
    wifi_signal_5: "Cihazın ağ bağlantısı iyi.",
    wifi_name: "WiFi adı",
    wifi_strength: "WiFi gücü",
    wifi_rssi: "RSSI",
    wifi_loss: "Packet loss değeri",
    wifi_mode: "Mevcut mod",
    wifi_mode_type2: "LAN bağlantısı",
    wifi_mode_type1: "WAN bağlantısı",
    wifi_ip_address: "IP adresi",
    wifi_mac_address: "MAC adresi",
    settings_light_full_color_title: "Düşük Işık Tam Renkli",
    settings_light_full_color_subtitle: "Ortam ışığı düşük olduğunda bile renkli görüntüler hâlâ görülebilir",
    fullColor_title3: "Otomatik olarak geçiş yap",
    fullColor_subTit: "Geceleri LED ışık olmak üzere 24 saat renkli görüntü izleme",
    fullColor_title2: "Açık",
    fullColor_black_subTit: "yeterli ışık olmadığında kızılötesi görüntü kullanılacaktır",
    fullColor_title1: "Kapalı",
    fullColor_smart_subTit: "Geceleri kızılötesi görüntüdür, ancak insan algılandığında tam renkli gece görüşüne geçer",
    empty_start_end_tips: "Lütfen bir başlangıç ve bitiş zamanı seçin",
    add_time_period: "Zaman dilimi ekle",
    not_set: "Ayarlanacak",
    plug_timer_repeat: "Tekrarla",
    do_once: "Bir kere çalıştır",
    do_everyday: "Günlük",
    do_weekday: "Hafta içi",
    do_weekend: "Hafta sonu",
    do_custom: "Özel",
    nobody_detect_name: "Gezinme süresi adı",
    date_picker_time_hint: "Başlangıç zamanı, bitiş zamanına eşit veya ondan sonra olamaz",
    nobody_time_no_more_than_10: "Zaman dilimleri 10'dan fazla olamaz",
    nobody_push_warning: "* Imilab Home’un bildirim fonksiyonunun açık olduğundan emin olun. Bu süre zarfında kimse görünmezse cep telefonuna mesaj gönderilmez.",
    delete_time_warning: "Bu zaman dilimi silinsin mi?",
    save_the_open: "Kaydet ve aç",
    presetAngleSleep: "Uyku perspektifi ",
    Preset_Sleep_subTitle: "Cihaz bu önceden ayarlanmış konumda uyur",
    preset_sleep_set_holder: "Mevcut uyku perspektifi yok, başlatmak için PTZ'ye tıklayın",
    save_current_angle_sleep: "Mevcut uyku perspektifi kaydedilsin ve etkinleştirilsin mi?",
    action_success: "İşlem başarılı",
    Preset_Sleep: "Önceden Ayarlanmış Uyku",
    closeStr: "Kapalı",
    settings_switch_on: "AÇIK",
    voice_for_max: "5 adede kadar özel ses desteği",
    alarm_only_people_detected: "İnsan algılama",
    settings_alarm_push: "Hareket Algılama bildirimi",
    alarm_loud_push: "Ses algılama anlık bildirimi",
    fence_detect_push: "Çitten geçiş algılama",
    alarm_crying_push: "Ağlama algılama bildirimi",
    setting_record_model_always: "Sürekli kayıt",
    setting_record_model_close: "Kayıt almayı devre dışı bırak",
    storageCardHinting: "Hafıza kartının çıkarılması",
    sd_suspended: "Duraklat",
    sdcard_status_normal_new: "Normal",
    sdcard_status_more_new: "Bellek alanı kaldı",
    sd_storage_switch: "Depolama anahtarı",
    setting_record_model: "Kayıt modu",
    record_quality: "Kayıt çözünürlüğü",
    storageCardFormat: "Mikro SD kartı biçimlendir",
    sd_card_use_hint_062: "Hafıza kartı kullanım ipuçları\n1. Destek Sınıfı 4 veya daha yüksek hızlı hafıza kartı\n2. Hafıza kartı formatı FAT323'tür. Orijinal hafıza kartları kullandığınızdan emin olun; kopya, kalitesiz ve yenilenmiş kartlar için uyumluluk garanti edilmez\n4. Maksimum hafıza kartı kapasitesi 256G'yi destekler",
    sdcard_tip1_new: "1.Lütfen SD kartı çıkarmadan önce çıkarın.",
    sdcard_tip2_new: "2.Yerel videoları kolayca görüntülemek için Micro SD karttaki videolar şifrelenmez, lütfen Micro SD kartınızı güvenli bir şekilde saklayın.",
    sdcard_out_already: "Mikro SD kart takılı değil",
    sdcard_status_abnormal: "Anormal",
    sdcard_status8: "Yeterli alan yok, lütfen kartı değiştirin",
    setting_record_model_always_title: "Tüm kayıtları Micro SD kartta saklayın",
    setting_record_model_close_title: "Kaydı bitirdikten sonra kamera videoları kaydetmez ve Micro SD karta kaydetmez",
    storageCardHint: "SD kartı güvenli bir şekilde açmayı onaylıyor musunuz?",
    setting_record_model_move: "Yalnızca hareket algılandığında kayıt yapın",
    sdcard_status_more: "Bellek alanı kaldı",
    sdcard_exit: "Mikro SD kartı çıkarın",
    sdcard_tip1: "1.Lütfen fişten çekmeden önce SD kartı çıkarın",
    sdcard_tip2: "2.Micro SD karttaki videolar, yerel videoların kolayca görüntülenmesi için şifrelenmez, lütfen Micro SD kartınızı güvenli bir şekilde saklayın.",
    setting_record_model_move_title: "Depolama alanından tasarruf etmek için videolar yalnızca hareket algılandığında kaydedilecektir",
    more_store_setting: "Bellek Yönetimi",
    settings_sdcard_title: "Micro SD kart durumu",
    sleep_title: "Uyku",
    Preset_Sleep_subTitle2: "Cihaz Önceden Ayarlanmış Uyku işlemini gerçekleştirir",
    timer_sleep_title: "Uyku programı",
    upload_grade_success: "Başarıyla yükseltildi",
    list_item_curr_version: "Güncel sürüm",
    list_item_latest_version_now: "Mevcut sürüm en son sürümdür",
    getError: "Alınamadı, lütfen daha sonra tekrar deneyin",
    upgrade_button: "Şimdi yükseltin",
    list_item_version_status_4: "Yükleniyor",
    list_item_latest_version_uploading_title: "Güncelleniyor…",
    list_item_latest_version_uploading: "Güncelleniyor, güncelleme tamamlanmadan kapatmayın veya kullanmayın",
    list_item_latest_version_upload_finish: "Güncellemeden sonra cihaz yeniden başlatılacaktır",
    upload_grade_timeout: "Yükseltme zaman aşımına uğradı",
    upload_grade_error: "Yükseltme başarısız oldu",
    list_item_latest_version: "En son sürüm",
    light_blue_orange_mix_flash_success: "OTA başarıyla yükseltildi",
    list_item_latest_version_log: "Güncelleme günlüğü",
    comm_config_wifi_title: "Wi-Fi çalışan cihazı seçin",
    wifiSettingTipsText: "Cihaz yalnızca 2,4 GHz Wi-Fi bağlantılarını destekler. Wi-Fi adında yalnızca İngilizce karakterler ve sayılar kullanılabilir",
    comm_config_wifi_ssid_hint1: "Wi-Fi şimdi ayarlanmadı",
    comm_config_wifi_password_hint: "Wi-Fi şifresini girin",
    local_device_bind_wifi_failed: "Cihaz bağlanamadı, lütfen cihazı yeniden başlatın ve daha sonra tekrar deneyin",
    local_device_bind_wifi_success: "Cihaz başarıyla bağlandı ve eklenti çıkmak üzere",
    local_device_bind_wifi_binding: "Cihaz bağlanıyor...",
    wdrFunctionHint: "WDR modu açıkken videonun loş ve aşırı ışıklı kısımları daha fazla ayrıntıyı muhafaza edebilir.",
    wdr_before: "WDR kapalı",
    wdr_after: "WDR açık",
    wdrHint: "Not: \n1. WDR, siyah beyaz gece görüş modunda çalışmaz. \n2. WDR, filigran renklendirmesini etkileyecektir.",
    sdCardRemain: "Kullanılan: %{code}GB",
    sdCardTotal: "Toplam: %{code}GB",
    sdCardLeft: "%{code} gün boyunca bir döngü halinde kayıt yapılması bekleniyor.",
    onPlayErrorText: "Canlı yayın hatası (%{kod})",
    onPlayErrorMaxText: "Cihaz maksimum bağlantı sınırını (%{kod}) aştı.",
    expiredCountdownHint: "Bulut depolama paketinizin süresi, %{code} gün sonra bitecek; bitiş tarihinden sonra videoları izleyemeyecek ya da kaydedemeyeceksiniz",
    on_live_play_error: "Kamera açılamadı (%{code}), lütfen daha sonra tekrar deneyin!",
    select_title_3: "%{code} öğeleri seçildi",
    click_too_fast: "Çok sık dokunuldu"
};
