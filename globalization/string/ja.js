const localeConfig = {
    calendarLocaleConfig: {
        formatAccessibilityLabel: 'dddd d \'of\' MMMM \'of\' yyyy',
        monthNames: [
            '一月',
            '二月',
            '三月',
            '四月',
            '五月',
            '六月',
            '七月',
            '八月',
            '九月',
            '十月',
            '十一月',
            '十二月'
        ],
        monthNamesShort: [
            '一月',
            '二月',
            '三月',
            '四月',
            '五月',
            '六月',
            '七月',
            '八月',
            '九月',
            '十月',
            '十一月',
            '十二月'
        ],
        dayNames: ['日', '月', '火', '水', '木', '金', '土'],
        dayNamesShort: ['日', '月', '火', '水', '木', '金', '土']
    }
}

export default {
    ...localeConfig,
    Sunday: "日",
    Monday: "月",
    Tuesday: "火",
    Wednesday: "水",
    Thursday: "木",
    Friday: "金",
    Saturday: "土",
    SundayShort: "日",
    MondayShort: "月",
    TuesdayShort: "火",
    WednesdayShort: "水",
    ThursdayShort: "木",
    FridayShort: "金",
    SaturdayShort: "土",
    MonthAndDay: "M/D",
    popo_setting_camera_text: "カメラ設定",
    alarmSettingText: "ホームモニタリング",
    playBackText: "ローカルビデオ",
    cameraTestTipsTitle2: "テスト信号",
    help_callback: "ヘルプとフィードバック",
    quality_360: "360P",
    quality_25k_v2: "2.5K",
    sd_360p: "360p",
    hd_1080p: "2k",
    quality_1080: "1080P",
    resolution_sd: "SD",
    resolution_qhd: "QHD",
    quality_1440p: "1440P",
    quality_sd_new: "360P",
    quality_uhd: "2K",
    quality_sd: "SD",
    quality_fhd: "HD",
    quality_common_sd: "480P",
    quality_fhd_3k: "3K HD",
    quality_common_3K: "3K",
    quality_2k: "2K",
    all_events_str: "すべてのイベント",
    peopleDetectStr: "ヒューマンシェイプイベント",
    bellEventStr: "ドアベルイベント",
    detectDistanceModalSubStr033: "感度調整時、PIRが動きを検知するとLEDがオレンジ点滅します。",
    doorkeyAreaDetectAlarmSubStr: "キーエリアで人が検知されると、ドアベルは10秒間の光音アラームを発します。",
    doorbellSetting: "ドアベル設定",
    record_files_sdcard: "Micro SDカード",
    gatewaySettingTitle: "ハブ設定",
    wifiSettingText: "WIFI設定",
    gateWayLightEffectText: "ハブLEDの説明",
    quality_low: "平滑",
    network_not_connected: "ネットワーク接続に異常がありますので、接続を確認してからやり直してください",
    device_offline: "デバイスがオフラインです",
    power_off: "デバイスは既にハイバネーション状態に入っています",
    onlyDoInLive: "操作前にライブ画面を開いてください",
    imi_speaking_block: "通話中には使用できません",
    screen_recording: "録画中はできません",
    saved_system_album: "端末のアルバムに保存ずみ",
    action_fail: "操作に失敗しました",
    storage_permission_denied: "保存許可なし",
    save_system_album_failed: "録音時間が短すぎる",
    save_album_failed: "ビデオ録画に失敗しました",
    commLoadingText: "読み込み中です。しばらくお待ち下さい。",
    error_code_common_retry: "タップしてやり直す",
    error_help: "ヘルプを表示する",
    camera_calling: "通話中には使用できません",
    call_busy_error: "通話中",
    call_busy_tips: "他のデバイスが通話中です。後ほど再度試してみてください。",
    call_connect_error: "ネットワークエラーです。後ほど再度試してみてください。",
    net_connect_error: "ネットワークエラーです",
    device_offline_aready: "デバイスはオフラインです、",
    offlineTime: "オフライン時間",
    connect_err: "接続に失敗しました",
    connect_error_help_one: "1.携帯電話とカメラのネットワーク接続が正常に動作しているかどうかを確認してください。ルータとカメラを再起動することをお勧めします。",
    offline_help_tip_reconect: "再接続",
    dot_for_modal: ".",
    connect_error_help_two: "2.WiFiパスワードが変更された場合、またはネットワークが変更された場合は、ください",
    connect_error_help_three: "3.上記の操作で解決できなかった場合、ください",
    connect_error_help_four: "4．上記で問題が解決しない場合、",
    offline_help_tip_feed_question: "ご連絡",
    know_button: "わかりました",
    offline_help_tip_one: "1.カメラのネットワーク接続が正常に動作しているかどうかを確認してください。ルータとカメラを再起動することをお勧めします。",
    offline_help_two: "2．電源供給が正常な場合、インジケーターライトを確認してください：",
    offline_help_two_first: "青ライトがゆっくり/速く点滅：カメラがネットワークに接続されているか確認してください。WLAN名またはパスワードが変更された場合、",
    offline_help_two_two: "黄ライトが速く点滅：カメラがリセットされました、",
    offline_help_tip_bind: "再バインド",
    offline_help_two_three: "黄ライトがゆっくり点滅：カメラはアップグレード中です、アクセスする前にアップグレードが完了するまでお待ちください。電源を切らないでください。",
    offline_three1: "3．デバイスをルーターにできるだけ近づけ、壁などの障害物を減らすようにしてください。両者が近いほど、デバイスの",
    offline_three2: "ネットワークRSSI値は",
    offline_three3: "理論的にはより強いネットワーク信号になります。RSSI値は-50より大きいことが推奨されます。",
    offline_help_tip_the_first: "黄色に点灯：ください ",
    offline_help_tip_connect_service: "カスタマーサービスに連絡する",
    offline_help_tip_the_thd: "ソフトウェアのアップグレードパッケージを取得する。",
    offline_help_tip_the_sec: "青色に点灯/青色に点滅：ください ",
    offline_help_tip_forth: "4.カメラを確認してください。 ",
    offline_help_tip_rssi: "ネットワーク情報 ",
    offline_help_tip_forth_first: "RSSI値が-50以下である場合は、ルータに近づけてみてください。",
    offline_help_four: "4．上記で解決できない場合、",
    offline_help_five: "5．上記で問題が解決しない場合、",
    offline_help_tip_the: "3.カメラインジケーターを確認してください。",
    offline_help_tip_fth: "5.上記の操作で解決できなかった場合、ください",
    alarm_turn_on_str: "自宅監視を有効化してください",
    fees_for_renewal: "更新",
    toolbar_text_resolution: "解像度",
    toolbar_text_sound: "音",
    toolbar_text_snap: "スナップショット",
    toolbar_text_record: "録画",
    toolbar_text_fullscreen: "フルスクリーン",
    select_all: "すべて選択",
    unselect_all: "全ての選択を解除",
    cancel: "キャンセル",
    commWaitText: "しばらくお待ちください。",
    ok_button: "もちろん",
    dialog_never_show: "二度と表示しない",
    cruise_seconds: "秒",
    am: "午前",
    pm: "午後",
    year: "年",
    month: "月",
    day: "日",
    hour: "時",
    minute: "分",
    give_away: "無料",
    cloud_give_price_str: "値",
    cloud_give_ok: "得る",
    picker_start_time: "開始時刻",
    picker_end_time: "終了時刻",
    alarm_direction_custom: "時間カスタマイズ",
    step_preview: "戻る",
    account_next: "次へ",
    stayDetectStr: "モーション検知",
    video_downloading: "ダウンロード中...",
    noAlarmVideo: "利用可能なビデオはありません",
    cloud_time_out_str: "スマートプランは既に失効しています",
    buy_again: "更新",
    cloudExpireShowImageHint: "ビデオのクラウドバックアップ  >",
    network_available_need_check: "ネットワークを利用できません。ネットワーク設定を確認してください",
    housekeeping_no_event: "現在コンテンツがありません",
    commLoadingFailText: "ロードに失敗しました。再試行してください",
    commLoadingMoreDataText: "もっと読み込む。しばらくお待ちください。",
    commNoMoreDataText: "すべてのデータが読み込まれました",
    commLoadingClickText: "タップして更新",
    abnormal_event_fire: "ドアが火災を検知しました",
    abnormal_event_damaged: "ドアがこじ開けられました",
    abnormal_event_anti_password: "ハイジャック防止パスワードアラート",
    abnormal_event_anti_fingerprint: "ハイジャック防止指紋アラート",
    abnormal_event_password: "パスワード試行による解錠アラート",
    abnormal_event_fingerprint: "指紋試行による解錠アラート",
    abnormal_event_nfc: "NFC試行による解錠アラート",
    abnormal_event_face: "顔認証試行による解錠アラート",
    abnormal_event_multiple: "二重認証試行による解錠アラート",
    abnormal_event_door_time_out: "ドアが長時間閉まっていません",
    abnormal_event: "異常イベント",
    lingerChangeText: "パーソンイベント",
    alarm_change_string: "監視領域において、画像の変化が検知されました",
    doorbellPressTitle: "誰かがドアベルを押しました",
    downloadingDeleteError: "ダウンロード、後でお試しください",
    delete_success: "正常に削除しました",
    delete_failed: "削除に失敗しました",
    video_downloaded: "ダウンロードが完了しました",
    save_failed: "保存に失敗しました",
    delete: "削除",
    chargeGuide: "バッテリーの充電",
    category_doorbell: "ドアベル",
    category_camera: "カメラ",
    chargeGuideSubtitle: "空から5V / 2A電源で完全に充電されるまで約5時間かかります",
    normalCharge: "通常の充電",
    hubCharging: "ハブ充電",
    hubConnectHintTitle: "良好な信号伝送を実現するには、次のことをお勧めします。",
    hubConnectHintSubtitle: "1.imilabスマートハブを地面より高い場所に設置してみてください。\n2.イーサネットケーブルが接続されている場合は、ルーターから0.5m離れた場所にimilabスマートハブを設置します。\n3.壁から0.5m離れた場所にimilabスマートハブを設置します。 \n4.壁の隅にimilabスマートハブを設置することは避けてください。",
    button_finish: "完了",
    button_add_device: "デバイスを追加する",
    cameraTestTips2: "インストール場所のネットワーク環境が悪い場合、\n接続障害またはビデオグリッチが発生する可能性があります",
    noSignalText: "信号なし",
    perfectText: "最適",
    strongText: "強い",
    goodText: "良い",
    weakText: "弱い",
    cameraTestStrong: "現在地は%{code}の設置にいいです",
    cameraTestGood: "現在地は%{code}の設置に適しています",
    cameraTestWeak: "現在地の信号が弱いので、ハブの近くに%{code}を置いてください",
    cameraTestNoSignal: "現在地には信号がありません。%{code}をハブに近づけてください。",
    no_sd_card_go_buy_cloud: "スマートプランの購入",
    downloadTip: "ダウンロードする動画を再生してください",
    alarm_download_downloading: "ダウンロード中...",
    storage_no_sdcard_please_buy: "マイクロSDカードはありません。挿入または購入してください",
    no_sd_card_tips_text: "ビデオ全体にアクセスするためには、スマートプランを購入してください。",
    cameraTestWifiSpeedTitle: "インストール手順",
    cameraTestTipsTitle1: "インストールガイド",
    cameraTestTips1: "1.カメラをできるだけハブの近くに設置します; \n2.インストールする前に、スマートフォンとカメラでインターネット速度をテストします",
    re_play: "再生",
    doorbellIndicatorLightPageTitle: "ドアベルインジケーター説明",
    doorbellPressLight: "外のドアベルが鳴っています",
    doorbellPressSubtitle: "ドアベルキーをクリックすると、ドアベルキーエッジインジケーターにランニングライト効果が現れ、ドアベルの音が聞こえます",
    cameraOnSubtitle: "青いライトが表示されるまで同期ボタンを2秒間押し続けると、電源投入時の音楽が聞こえます。",
    cameraOffSubtitle: "赤いライトが1回点滅するまで同期ボタンを8秒間押し続けて、電源オフの音楽が聞こえます。",
    doorbellUsbChargeTitle: "USBでドアベルを充電する",
    usbChargeSubtitle: "赤が3回点滅し、充電を開始します",
    doorbefullChargeTitle: "ドアベル充電完了 (満充電)",
    fullChargeSubtitle: "ソリッドグリーン",
    cameraResetSubTitle: "LEDが青色に点滅するまで同期ボタンを5秒間押し続け、ボタンを放してリセットを開始します。リセットプロセス中にカメラが再起動します。",
    reset_success_text: "正常にリセットしました",
    resetSuccessSubtitle: "青点滅",
    pairedSuccessTitle: "正常にペアリング",
    pairedSuccessSubtitle: "5秒間青色に点灯",
    pairedFailTitle: "ペアリングに失敗しました",
    pairedFailSubtitle: "青とオレンジが交互に3回点滅します",
    pirSensitivityAdjustSubtitle: "PIRの感度調整を行う場合、PIRが動きを検出するとオレンジ色に1回点滅し、動きを継続的に検出するとオレンジ色に点滅し続けます。",
    pirGetPeopleTitle: "PIRは動きを検出します",
    pirGetPeopleSubtitle: "オレンジが1回点滅",
    pirAlwaysGetPeopleTitle: "PIRは動きを検出し続けます",
    pirAlwaysGetPeopleSubtitle: "オレンジ色のライトが継続的に点滅します",
    gateWayLightPageTitle: "ハブLEDの説明",
    light_blue: "ブルーライト",
    light_blue_flash: "青点滅",
    light_blue_flash_case: "ネットワーク接続が進行中です（接続されていません）",
    light_blue_on: "ソリッドブルー",
    light_blue_on_case: "ネットワーク接続が成功しました（接続されました）",
    light_orange: "オレンジライト",
    light_orange_flash: "オレンジ色に点滅",
    light_orange_flash_case: "接続を待機しています（デバイスとアプリのバインドが進行中です）",
    light_orange_on: "ソリッドオレンジ",
    light_orange_on_case1: "ハブが起動しています",
    light_orange_on_case3: "ファームウェアの更新が進行中です",
    light_blue_orange_mix_head: "ブルー",
    light_blue_orange_mix_tail: "/オレンジライト",
    light_blue_orange_mix_flash: "ブルーとオレンジが交互に3回点滅",
    light_blue_orange_mix_flash_case: "ファームウェアの更新に失敗しました",
    gateway_reset: "ハブのリセット",
    gateway_reset_hint1: "ハブのバインドプロセス中に、インジケータライトがオレンジ色に点滅していない場合は、ハブをリセットしてください。",
    gateway_reset_hint2: "ハブの背面にあるリセットボタンを5秒間押し続け、インジケータライトがオレンジ色に変わるまで押し続けてから、ボタンを放します。リセット?プロセスが続いている間に、ハブが再起動します。インジケーターがオレンジ色に点滅し始めると、リセットが成功し、バインドを開始できることを意味します。",
    indicatorLightPageTitle: "カメラのLEDの説明",
    cameraOnOffLight: "カメラの電源のオン/オフ",
    cameraOnTitle: "カメラの電源を入れる",
    cameraOffTitle: "カメラの電源を切る",
    cameraChargeTitle: "カメラの充電",
    usbChargeTitle: "USBを使用してカメラを充電します",
    fullChargeTitle: "カメラは完全に充電されています。",
    sunChargeTitle: "ソーラーパネルを使用してカメラを充電します",
    cameraResetTitle: "カメラのリセット",
    pirSensitivityAdjustTitle: "カメラPIR検出感度調整",
    injectSuccess: "取り出しに成功しました",
    sdcard_format_success: "正常に初期化しました",
    sdcard_format_fail: "初期化に失敗しました",
    sdCardFormating: "SDカードのフォーマットは進行中です",
    injectFailed: "取り出しに失敗しました",
    noSdCardTitle: "SDカードなし",
    injectSdCardTitle: "SDカードを安全に取り出します",
    injectSdCardSubtitle: "録音ファイルの破損を防ぐためにSDカードを安全に取り出します",
    sdCardFormat: "SDカードのフォーマット",
    sdCardDamaged: "メモリカードエラー",
    injectSdCardHint: "SDカードを安全に取り出してもよろしいですか？",
    formatTitle: "SDカードをフォーマットしますか？",
    formatMessage: "フォーマットしたSDカードが空白になります",
    stayTimeStr: "検出モード",
    houseDetectedPeopleStr: "家の前で誰かが発見されたとき",
    peopleRecordPowerMoreStr: "すぐに記録する",
    peopleRecordPowerMoreSubStr: "モーション検出がトリガーされたら記録します。  （高消費電力）",
    peopleRecordPowerCommonStr: "設定時間後に記録する",
    peopleRecordPowerCommonSubStr: "誰かが長期間滞在した場合にのみ記録します。（全体の電力消費）",
    peopleRecordPowerLessStr: "記録しないでください",
    peopleRecordPowerLessSubStr: "モーション検知がオフになります（消費電力が少なくなります）",
    firstSetStayTimeFailed: "デフォルト設定を使用しますので、[モーション検知]に移動して調整してください",
    commTitleSettingText: "設定項目",
    feature_set: "機能設定",
    comm_setting_title: "一般設定",
    device_name: "デバイス名",
    check_update: "更新の確認",
    shareUser_tip: "共有デバイスの許可なし",
    shared_setting: "デバイスの共有",
    confirm_deletion_device: "デバイスの削除を確定しますか？",
    preset_sleep_set: "スリープ視点設定",
    comm_setting_remove_device: "デバイスの削除",
    update_device_name: "デバイス名の修正",
    input_name: "名前を入力",
    settings_set_success: "正常に設定しました",
    play_back_text_all: "すべての動画を再生 ",
    wake_up: "スリープ解除",
    operationFailed: "設定に失敗しました",
    people_event: "人体検知",
    move_event: "動きの検知",
    alarm_loud_switch: "音声検知",
    no_human_event: "無人偵察",
    fence_detect_switch: "フェンス検知",
    cry_event: "泣き声検知",
    keyAreaDetectStr: "アクティビティゾーンの検出",
    moveEvent: "動体検知",
    peopleEvent: "人体検出",
    soundEvent: "大きな音検知",
    bottom_house_keeping: "イベント",
    str_housekeeping_tip_guide_people: "o 監視領域において人を検知",
    str_housekeeping_tip_guide_sound: "o 異常音が検出されました",
    str_housekeeping_tip_guide_fence: "o 監視エリア内のフェンス検知",
    str_housekeeping_tip_guide_nobody: "o 監視区域には誰も現れなかった",
    str_housekeeping_tip_guide_move: "o 監視領域において画面変更検知なし",
    str_housekeeping_tip_guide_cry: "o 泣き声検知なし",
    str_housekeeping_tip_guide_important: "o キーエリアで検出された人。",
    str_housekeeping_tip_title: "一旦自宅監視が有効になると、カメラ，は警告画像を保存して、あなたに通知を送信します。",
    go_to_open: "開くために",
    delete_alert: "削除を確定しますか？",
    delete_title_loading: "削除...",
    cloudTip: "無制限のストレージとより多くのイベント認識を提供するクラウドストレージを有効化",
    x_flat_list_no_more_data: "ロードされたすべてのデータ",
    downLoadTitle: "ダウンロード",
    select_tip: "ファイルを選択",
    delete_title: "削除",
    save_success: "正常に保存しました",
    buy_cloud_for_info: "スマートプランの購入，クラウドバックアップを有効にする",
    download_system_album: "ダウンロードに成功しました",
    video_download_fail: "ダウンロードに失敗しました",
    delete_alert_tip: "この動画は",
    delete_alert_tip1: "件のイベントを含みます、削除を続行しますか？",
    bottom_house_video_keeping: "動画詳細",
    currentPlay: "現在再生中",
    loadMsgInfo: "メッセージ",
    loadMsgInfoError: "メッセージ読み込み中",
    bottom_video_album: "アルバム",
    select_title_1: "選択してください",
    album_video_play: "再生",
    album_video_pause: "一時停止",
    album_video_mute: "ミュート",
    album_video_voice: "サウンド",
    album_video_full_screen: "全画面表示",
    video_download_fail_warning: "動画のアップロードが進行中で、しばらくしてからもう一度お試しください",
    bottom_cloud_storage: "スマートプラン",
    change_event_str: "イベントの切替え",
    open_setting: "設定",
    no_video_data_new: "利用可能な動画がありません",
    playback_no_video_data_tip: "おやおや、今日はビデオが提供されていません。他の日に切替えてください",
    date_format_yyyy_mm_dd: "yyyy/MM/dd",
    panoramicSuccess: "パノラマできました",
    panoramicError: "パノラマ画像生成失敗、後で再試行してください",
    direction_end_009: "回転できません",
    isDataUsageTip: "自動一時停止中のモバイルネットワーク",
    Panoramic_loading: "パノラマ画像生成中……",
    set_onekey_tit: "ワンボタンアラームを設定しますか？",
    set_onekey_msg: "設定後、ボタンを押すとアラームライトとプロンプト音が作動します。",
    set_onekey_sure: "いますぐ設定",
    popo_setting_storage_text: "ストレージ設置",
    sdcard_error_out: "MicroSDカードが排出されます，MicroSDカードを再度挿入または引き出してください!",
    audio_permission_denied: "録音許可なし",
    panoramicing_tip: "パノラマ画像生成中……",
    camera_guide_for_zoomed_str: "ダブルタップ/ピンチして \n 画面を拡大/縮小します",
    camera_guide_for_panoramic_str: "ここをタップしてパノラマモードに入ります",
    expiredHint: "   クラウドストレージプランは本日有効期限が切れ、有効期限が切れると監視ビデオを表示または記録できなくなります",
    sdcard_tip_cancel: "わかりました",
    sd_need_format_no_index_massage: "SDカードを初期化すると、正常に記録できます。今すぐSDカードを初期化して空にします？",
    sd_need_format_has_old_index_massage: "デバイスソフトウェアがアップグレードされました。SDカードを初期化すると、より良い再生体験ができます。今SDカードを初期化して空にします？",
    is_the_calibration: "較正中...",
    calibration_completed: "カメラが正常に較正されました",
    netWorkError: "ネットワーク接続中断",
    operate_time_out: "操作タイムアウト",
    hotspot_connect_hint: "「imi_xxxxxx」に接続してから戻ってください",
    goto_wifi: "無線LANを選択",
    play_back_text: "リプレイ",
    sdcard_status7: "メモリカードが初期化されていません",
    toolbar_text_sleep: "スリープモード",
    quality_auto: "自動",
    sdcard_format_title_tips: "SDカードをフォーマットしてください!",
    sdcard_format_title_tips_content: "メモリーカードは初期化されておらず、正常に使用する前にフォーマットする必要があります。フォーマットするとメモリーカード上のすべてのデータが消去されます。フォーマットを続行しますか？",
    upgrade_state_content: "デバイスで最新のファームウェアバージョンが検出されました",
    upgrade_state_content_end: "今すぐアップグレード",
    sleepTipTitle: "デバイススリープ",
    sleepTipContent: "カメラは動作と動画情報の記録を停止します",
    cloud_will_time_out_str: "スマートプランが失効します",
    cloud_time_out_effect_str: "一旦失効してしまうと、スマートプランサービスが提供されなくなります",
    temporarily_not: "一時的にではない",
    formatting_btn: "フォーマット",
    storageCardFormating: "マイクロSDカードがフォーマットされています...",
    waitFailedTip: "後でもう一度やり直してください",
    targetPushTitle_subtitle: "クリックして解決",
    targetPush_sdcard_format: "SDカードは使用前に初期化する必要があります!",
    sdcard_status_error: "メモリカードエラー",
    get_success: "正常に取得されました",
    Panoramic_tip: "パノラマをクリックすると、カメラビューを制御できます",
    Panoramic_title: "パノラマの生成",
    Panoramic_title_reset: "パノラマを再度撮影しますか？",
    storage_services: "クラウドストレージサービス",
    storage_services_content: "クラウドストレージサービスとは何ですか?\n",
    storage_services_title: "クラウドストレージの権限",
    imi_cloud_storage_rights_title1: "24時間対応",
    imi_cloud_storage_rights_detail1: "動画ダウンロード",
    imi_cloud_storage_rights_title2: "スマート検出",
    imi_cloud_storage_rights_detail2: "細部にまでこだわる",
    imi_cloud_storage_rights_title3: "クラウドに保存",
    imi_cloud_storage_rights_detail3: "いつでも再生",
    imi_cloud_storage_rights_title4: "暗号化されたアップロード",
    imi_cloud_storage_rights_detail4: "プライバシー保護",
    storage_after: "後で",
    imi_cloud_experience_now: "経験",
    play_back_tit: "動画再生",
    play_back_change_time: "時間を切り替える",
    alarmText: "イベント",
    goto_live_view: "ライブビデオ",
    str_housekeeping_tip_value: "o 監視エリアで人が検知されました\no 監視エリアで画面変化が検知されました\no 異常音が検知されました",
    common_error: "ロードに失敗しました。デバイスのステータスを確認してください",
    sdCardName: "SDカード",
    no_video_data_failed: "動画ファイル取得失敗",
    retry_connect: "再接続",
    collapse: "折りたたむ",
    noData: "データなし、再選択してください",
    playback_no_event: "イベントなし",
    keyArea: "アクティビティゾーン",
    change_date: "日付切替え",
    todayTitle: "今日",
    preDayTitle: "昨日",
    sdcard_status0: "良好",
    sdcard_status1: "Micro SDカードなし",
    sdcard_status6: "メモリーカード容量不足",
    sdcard_status3: "エラー",
    sdcard_status4: "フォーマット中",
    sdcard_status5: "Micro SDカードを取り出します",
    sdcard_status9: "修復中",
    sdcard_status10: "取り出し中",
    sdcard_status2: "保存容量がなくなりました",
    sdcard_status_normal_new1: "メモリーカードステータス",
    play_back_text_all_title: "すべての動画",
    max_download_limit: "ダウンロードは1つのみサポート",
    delete_failed_limit: "一度に最大50操作",
    delete_connect_failed: "デバイス接続切断",
    delete_failed_inPlay: "再生中の動画は削除できません",
    sensitivity_for_high_tit: "高感度",
    sensitivity_for_low_tit: "低感度",
    alarm_sensitivity: "アラート感度",
    sensitivity_for_high_subtit: "人や物の動きを検知し、1つの動きも見逃しません",
    sensitivity_for_low_subtit: "大きな音が検知されたときにアラーム",
    date_picker_time_title: "開始時刻と終了時刻を選択してください",
    alarm_time_set_time: "監視期間を選択してください",
    time_equal: "終了時刻は開始時刻",
    alarm_time_night: "夜間監視",
    alarm_time_all: "24時間監視",
    alarm_time_day: "昼間監視",
    alarm_time_set: "監視時間",
    full_color_vision_title: "カラーナイトビジョン",
    alarm_time_24: "24時間",
    alarm_time_info: "動体または人が検知されたときにアラート",
    alarm_time_208: "20:00 - 翌日08:00",
    fullColor_smart_tit: "インテリジェントナイトビジョン",
    alarm_time_820: "8:00-20:00",
    noDIYTimeTip: "カスタム時間を選択してください",
    voice_for_wu: "ありません",
    voice_for_warning: "アラーム音",
    voice_for_dingdong: "ドアベル音",
    voice_for_welcome: "こんにちは、ようこそ",
    voice_for_area: "監視エリアに入りました",
    voice_for_closedoor: "どうぞお入りください、ドアを閉めてください。ありがとうございます",
    voice_for_safe: "お気をつけてください",
    voice_for_stairs: "階段に気をつけてください",
    voice_for_dangerArea: "危険ですから立ち入らないでください",
    allDay_time: "24時間監視",
    potlight_flash: "ライト点滅",
    potlight_not_bright: "ライトなし",
    potlight_long_bright: "常時点灯",
    day_time: "昼間監視",
    night_time: "夜間監視",
    voice_for_custom: "カスタム",
    soundLightAlarm: "視聴覚アラーム",
    only_people_detected: "人間のみを検知します",
    effective_time: "監視期間を選択する",
    tip_voice_selected: "プロンプト音を選択する",
    potlight_alarm_mode: "アラームランプの設定",
    potlight_contain_time: "継続期間",
    click_warning_tit: "ワンボタンアラーム",
    local_device_connect_wifi: "無線LANを選択",
    comm_setting_faq: "よくある質問",
    angelMoreSetting: "その他の設定>>",
    voice_for_enter_name: "名前を入力",
    max_device_name: "名前は15文字を超えてはいけません",
    customer_phone: "Phone Number：************",
    customer_email: "Eメール: <EMAIL>",
    customer_wx: "WeChat Official Accounts：创米数联",
    customer_web: "Webページ: www.imilabglobal.com",
    voice_for_add: "１つ追加",
    voice_for_edit: "編集",
    imi_save: "保存",
    voice_for_name: "名称",
    imi_input_text_tip: "このキャラクターはサポートされていません",
    voice_for_tip_tit_time: "アラーム音の選択",
    voice_for_re_record: "再録音",
    voice_for_click_record: "ホールドアンドトーク",
    nursingTimeSetting: "監視期間の設定",
    show_upgrade_app: "アプリを最新バージョンにアップグレードしてください",
    imi_fence_tit: "物体/人間が矢印の方向にフェンスラインを横切ると、フェンス検知イベントと見なされます。",
    imi_switch_dection: "矢印の反転",
    alarm_event_tit: "家庭監視イベント",
    move_track: "動きの追跡",
    settings_alarm_human_track_title: "人体検知",
    no_human: "無人検査",
    alarm_event_manager: "在宅監視管理",
    alarmTimeSetting: "アラート間隔",
    time_minutes: "分",
    settings_alarm_push_time_sub: "送信される通知の数が多すぎる場合は、その間隔を広げて干渉を減らしてください。",
    message_push_manager: "イベント通知管理",
    message_push_phone: "動きが検出されたら、スマートフォンにプッシュします。",
    tip_time_minute: "分",
    recordType: "録画モード",
    recordType1: "画像",
    recordType2: "動画",
    recordTypeSub: "動画モードに切り替えるにはクラウドストレージを有効化",
    alarm_info_set: "アラームタイプ設定",
    alarm_sound_detection: "大きな音検知",
    detection_sensitivity: "検知感度",
    alarm_info_push: "アラームメッセージプッシュ",
    message_push_switch: "アラームメッセージプッシュスイッチ",
    preset_opened: "オープン",
    preset_closed: "閉鎖",
    audio_broadcast: "音声お知らせ",
    black_white_vision_title: "黒と白の暗視",
    settings_light_title: "ステータスインジケーター",
    Data_usage_warning: "流れの保護",
    data_usage_warning_intro: "モバイルネットワークでは自動再生されません",
    intelligent_cruise: "インテリジェント クルーズ",
    settings_switch_off: "有効化にしていません",
    cruise_all_view: "パノラマ",
    cruise_favorite: "プリセット",
    sleep_set: "睡眠設定",
    setttings_infared: "ナイトビジョン設定",
    settings_flip_title: "画像の回転",
    settings_flip_subtitle: "カメラが逆さまになっている場合はこのオプションを有効にしてください",
    imageSetting: "畫面設定",
    setting_picture_setting: "更なる画像設定",
    imi_camera_correct_pos: "ジンバルの較正",
    setting_reset: "デバイスの再起動",
    calibration_to_continue_30: "カメラの較正には約30秒かかります。続行しますか？",
    calibration_to_continue: "カメラの較正には約25秒かかります。続行しますか？",
    calibration_failure: "カメラの較正に失敗しました",
    setting_reset_msg: "デバイスの再起動には少し時間がかかります。再起動しますか？",
    settings_watermark_title: "タイムスタンプの透かし",
    pictureCorrection: "歪み補正",
    pictureCorrectionSubtitle: "オンにすると、ビデオの歪みは減少しますが、FOVも減少します。",
    wdrMode: "WDRモード",
    audio_broadcast_switch: "音声お知らせ",
    rn_version: "プラグインバージョン番号：",
    network_info: "ネットワーク情報",
    wifi_signal_0: "新しいWiFiには信号がありません",
    wifi_signal_2: "デバイスのネットワーク接続が悪い。",
    wifi_signal_5: "デバイスのネットワーク接続が良い。",
    wifi_name: "WiFi名",
    wifi_strength: "WiFi の強さ",
    wifi_rssi: "RSSI（電波強度）",
    wifi_loss: "パケット損失率",
    wifi_mode: "現在のモード",
    wifi_mode_type2: "LAN接続",
    wifi_mode_type1: "WAN接続",
    wifi_ip_address: "IPアドレス",
    wifi_mac_address: "MAC アドレス",
    settings_light_full_color_title: "弱光フルカラー",
    settings_light_full_color_subtitle: "周囲光が低い場合でも、カラー画像を見ることができます",
    fullColor_title3: "自動切換え",
    fullColor_subTit: "夜間のLEDライト、24時間のカラー画像監視",
    fullColor_title2: "オン",
    fullColor_black_subTit: "赤外線画像は、十分な光がない場合に使用されます",
    fullColor_title1: "オフ",
    fullColor_smart_subTit: "夜間赤外線画像で、人間が検知されるとフルカラー暗視に切り替えます",
    empty_start_end_tips: "開始時間と終了時間を選択してください",
    add_time_period: "期間を加える",
    not_set: "固まる",
    plug_timer_repeat: "繰返す",
    do_once: "一度実行",
    do_everyday: "毎日",
    do_weekday: "平日",
    do_weekend: "週末",
    do_custom: "カスタム",
    nobody_detect_name: "スパン名",
    date_picker_time_hint: "開始時刻は終了時刻以降にすることはできません",
    nobody_time_no_more_than_10: "時間帯は 10を超えてはいけません!",
    nobody_push_warning: "* imilab Homeのプッシュ機能を有効にしてください。誰もこの期間中に表示され、携帯電話にメッセージをプッシュします。",
    delete_time_warning: "設定項目を削除?",
    save_the_open: "保存してオンにします",
    presetAngleSleep: "スリープ視点 ",
    Preset_Sleep_subTitle: "デバイスはこのプリセットされた位置でスリープします",
    preset_sleep_set_holder: "現在スリープの視点はありません、PTZをクリックして設定を開始します",
    save_current_angle_sleep: "現在のスリープの視点を保存し、有効にしますか?",
    action_success: "操作成功",
    Preset_Sleep: "プリセットスリープ",
    closeStr: "終了時間",
    settings_switch_on: "有効かにします",
    voice_for_max: "最大5つのカスタムオーディオをサポートします",
    alarm_only_people_detected: "人体検知",
    settings_alarm_push: "動きが検知された場合にメッセージを送信する",
    alarm_loud_push: "音声検知に関する通知のプッシュ送信",
    fence_detect_push: "フェンス検知",
    alarm_crying_push: "泣き声が検知された場合にメッセージを送信する",
    setting_record_model_always: "常に録画",
    setting_record_model_close: "録画を無効にする",
    storageCardHinting: "メモリーカード取り出し中",
    sd_suspended: "一時停止",
    sdcard_status_normal_new: "普通",
    sdcard_status_more_new: "残り領域",
    sd_storage_switch: "ストレージスイッチ",
    setting_record_model: "録画モード",
    record_quality: "Record quality",
    storageCardFormat: "Micro SDカードをフォーマットする",
    sd_card_use_hint_062: "メモリーカード使用上のヒント\n1．クラス4以上の速度のメモリカードをサポートします\n2．メモリカードのフォーマットはFAT323です。必ず本物のメモリカードを使用してください、コピーキャット、劣った、再生されたカードとの互換性は保証されません\n4．最大256G容量メモリカードをサポートします",
    sdcard_tip1_new: "1.SDカードを抜く前に取り出してください",
    sdcard_tip2_new: "2.Micro SDカード上のビデオは、ローカルビデオを簡単に表示するために暗号化されていません。Micro SDカードを安全に保管してください。",
    sdcard_out_already: "Micro SDカードがマウントされていません",
    sdcard_status_abnormal: "異常な",
    sdcard_status8: "容量不足、カードを交換してください",
    setting_record_model_always_title: "すべての録音をMicroSDカードに保存してください",
    setting_record_model_close_title: "録画を終了した後、カメラはビデオを録画してMicroSDカードに保存しません",
    storageCardHint: "SDカードを安全に取り出してもよろしいですか？",
    setting_record_model_move: "動きが検知された場合のみに録画",
    sdcard_status_more: "残りメモリスペース",
    sdcard_exit: "マイクロSDカードをアンマウントします",
    sdcard_tip1: "1. SDカードを取り外す前に、必ず取り出し操作を行ってください",
    sdcard_tip2: "2. ローカル動画を簡単に閲覧できるように、Micro SDカード上の動画は暗号化されていません、Micro SDカードは安全に保管してください。",
    setting_record_model_move_title: "保存領域を節約するために、画像変化が検知された場合のみに録画します",
    more_store_setting: "ストレージ管理",
    settings_sdcard_title: "Micro SDカードのステータス",
    sleep_title: "スリープモード",
    Preset_Sleep_subTitle2: "デバイスがプリセットスリープを実行します",
    timer_sleep_title: "時限ハイバネーション",
    upload_grade_success: "アップグレード成功",
    list_item_curr_version: "現バージョン",
    list_item_latest_version_now: "現在のバージョンが最新です",
    getError: "取得失敗、後で再試行してください",
    upgrade_button: "今すぐアップグレード ",
    list_item_version_status_4: "インストール中",
    list_item_latest_version_uploading_title: "更新中...",
    list_item_latest_version_uploading: "更新中、更新が完了するまで電源を切ったり使用したりしないでください",
    list_item_latest_version_upload_finish: "更新後、デバイスは再起動します",
    upload_grade_timeout: "アップグレードタイムアウト",
    upload_grade_error: "アップグレードに失敗しました",
    list_item_latest_version: "最新バージョン",
    light_blue_orange_mix_flash_success: "OTAアップグレード成功",
    list_item_latest_version_log: "更新ログ",
    comm_config_wifi_title: "デバイスには動作している WiFi を選択してください",
    wifiSettingTipsText: "このデバイスは2.4GHzWIFI接続のみをサポートします\nWIFI名に使用できるのは英語の文字と数字のみです",
    comm_config_wifi_ssid_hint1: "現在 Wi-Fi が設定されていません",
    comm_config_wifi_password_hint: "Wi-Fi パスワードを入力してください",
    local_device_bind_wifi_failed: "デバイスのバインドに失敗しました、デバイスを再起動して後で再試行してください",
    local_device_bind_wifi_success: "デバイスがバインドされ、プラグインが終了します",
    local_device_bind_wifi_binding: "デバイスをバインド中…",
    wdrFunctionHint: "WDRモードがオンの場合、ビデオの薄暗い部分と露出過度の部分はより多くの詳細を保持できます。",
    wdr_before: "正常モード",
    wdr_after: "ワイドダイナミック範囲モード",
    wdrHint: "注意：\n1.WDRは白黒暗視モードでは機能しません。\n2.WDRは、ビデオの透かしの色に影響します。",
    sdCardRemain: "使用済み：％{code} GB",
    sdCardTotal: "合計：％{code} GB",
    sdCardLeft: "%{code}日間ループ録画が期待されます。",
    onPlayErrorText: "ライブストリームエラー (%{code})",
    onPlayErrorMaxText: "デバイスは最大接続数 (%{code}) を超えました。",
    expiredCountdownHint: "クラウドストレージパッケージの有効期限はあと%{code}日で、期限切れ後は動画の閲覧や録画ができなくなります",
    on_live_play_error: "カメラの起動に失敗しました (%{code})、後で再試行してください！",
    select_title_3: "%{code}項目選択済み",
    click_too_fast: "タップが頻繁すぎます"
};
