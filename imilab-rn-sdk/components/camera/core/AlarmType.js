/*
 * 作者：sunhongda
 * 文件：AlarmType.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */
'use strict';

import {INTELLIGENT_TYPE} from "../alarm/api/ALiAlarmEventCloudApi";
export default {
    /** 非机动车 **/
    BIKE:12001,

    /** 无人侦测 **/
    WARN: 21,
    /** 无人侦测 **/
    NOBODY: 20,

    /** 门铃事件 **/
    BELL: 15,

    /** 车辆侦测 **/
    VEHICLE: 10005,

    /** 逗留侦测 **/
    Stay: 13,

    /** 笑声侦测 **/
    LAUGHTER: 12,

    /** 哭声侦测 **/
    CRY: 11,

    /** 异响侦测 **/
    ABNORMAL_SOUND: 10,

    /** 重点区域侦测 **/
    KEY_AREA: 9,

    /** 人脸检测 **/
    FACE: 8,

    /** 跌倒侦测 **/
    FALL: 7,

    /** 区域入侵侦测 **/
    CROSSING: 6,

    /** 越界侦测 **/
    INTRUSION: 5,

    /** 宠物侦测 **/
    PET: 4,

    /** 人形侦测类型 **/
    PEOPLE: 3,

    /** 声音侦测 **/
    SOUND: 2,

    /** 移动侦测类型 **/
    MOVE: 1,

    /** 普通视频 **/
    COMMON: 0,

    /** 全部类型 **/
    ALL: 100,

}
