/*
 * 作者：sunhongda
 * 文件：IMIAlarmCloudApi.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 * 更新：20220727
 * 更新者：baiyihao
 * 更新内容 ：新增批量删除云存录像接口
 */

import {LetIMIIotRequest, METHOD} from "../../../../native/iot-kit/IMIIotRequest";
import {AlarmType, LetDevice} from "../../../../index";
import { cloudDeviceService } from "../../../../native/iot-kit/IMIDeviceCloudService";


/****    一、阿里云存列表  ***/
const URL_ALI_CLOUD_QUERY = "/vision/customer/event/query";
/****    一、阿里云存图片列表  ***/
const URL_ALI_CLOUD_QUERY_PICTURE = "/vision/customer/picture/querybytime";
/****    二、阿里云存列表删除图片  ***/
const URL_ALI_CLOUD_DELETE = "/vision/customer/picture/batchdelete";
/****    三、阿里获取物的事件  ***/
const URL_ALI_CLOUD_EVENT_GET = "/thing/events/get";
/****    四、设置业务事件的消息推送间隔  ***/
const URL_ALI_CLOUD_BIZEVENT_SET = "/vision/customer/bizevent/config/set";
/****    五、获取业务事件的消息推送间隔  ***/
const URL_ALI_CLOUD_BIZEVENT_GET = "/vision/customer/bizevent/config/get";
/****    六、阿里云存事件列表删除图片  ***/
const URL_ALI_CLOUD_EVENT_DELETE = "/vision/customer/event/delete/byeventids";
/****    六、阿里云存图片列表删除图片  ***/
const URL_ALI_CLOUD_PICTURE_DELETE = "/vision/customer/picture/batchdelete";
/****    七、查询月录像  ***/
const URL_MONTH_RECORD_CLOUD_QUERY = "/vision/customer/monthrecord/query";
/****    八、阿里云存视频列表  ***/
const URL_ALI_RECORD_CLOUD_QUERY = "/vision/customer/record/query";
/****    九、根据事件IS获取视频详情  ***/
const URL_ALI_VOD_GET_EVENT_ID = "/vision/customer/vod/getbyeventid";
/****    九、根据事件name获取视频详情  ***/
const URL_ALI_VOD_GET_EVENT_NAME = "/vision/customer/vod/getbyfilename";
/****    十、测试推送有图片的返回过少原因  ***/
const URL_ALI_MASSAGE_PUSH_MASSAGE = "/message/center/query/push/message";
/****    十一、测试推送有图片的返回过少原因  ***/
const URL_ALI_PIC_BY_EVENT = "/vision/customer/pic/getbyevent";
/****    十二、下载录像视频  ***/
const URL_ALI_DOWNLOAD_VIDEO = "/vision/customer/vod/cloudfile/get";
/****    十三、批量删除录像视频  ***/
const URL_ALI_CLOUD_VIDEO_DELETE = "/vision/customer/record/batchdelete";
/****    根据segmentId删除  ***/
const URL_ALI_DEL_FILE_BY_SEGMENT_ID = "/v1.0/imilab-01/app/cloudstorage/deleteFileBySegmentId";
/****    根据segmentId批量删除  ***/
const URL_ALI_DEL_FILE_BY_SEGMENT_IDS="v1.0/imilab-01/app/cloudstorage/batchDeleteFileBySegmentIds"
/* 根据iotid获取事件类型 */
const URL_ALI_GET_EVENT_TYPE_LIST="/v1.0/imilab-01/app/cloudstorage/getEventTypeList"
/* 获取看家数据 */
const URL_ALI_GET_EVENT_LIST="/v1.0/imilab-01/app/cloudstorage/event-list"
/**
 * 创米自有云存储  看家小视频 事件接口
 * 创米云文档 [http://doc.imilab.com/organization/repository/editor?id=16&mod=51&itf=158]
 */

const TAG = "IMIAlarmEventCloudApi";

/**
 *
 * 请求头部由 Native 层进行封装 如果 code！=0 失败直接走 then 返回 code message
 * {
    "code":0,
    "message":"ok",
    "result":{
    },
    "status":"succeeded"
}
 */


/**
 * intelligentTypeList :
 *
 * animal    String    动物
 car    String    车辆
 cry    String    哭声
 goods    String    物品
 humanFace    String    人脸
 humanShape    String    人形
 move    String    移动
 */
export const INTELLIGENT_TYPE = {
    animal: 'animal',
    car: 'car',
    cry: 'cry',
    goods: 'goods',
    humanFace: 'humanFace',
    humanShape: 'humanShape',
    move: 'move',
    stay: 'linger',
    bell:'bell',
};
Object.freeze(INTELLIGENT_TYPE);

export const RepCode = {
    /**找不到索要的下载文件 */
    NOT_FOUNT_FILE: 40004,
};
Object.freeze(RepCode);

/*export type IAlarmEventCloudCallback = (progress: String) => void;
export interface IAlarmEventCloudApi {
    eventStorageSessions(eventCloudCallback?: IAlarmEventCloudCallback): Promise<String>;
}*/

export default class ALiAlarmEventCloudApi {


    constructor() {
        // this.instance = null;
    }

    static getInstance() {
        if (!this.instance) {
            this.instance = new ALiAlarmEventCloudApi();
        }
        return this.instance;
    }

    /**
     * type 转换为string
     * @param type
     * @returns {boolean|string}
     * @private
     */
    _eventType2Info(type) {
        return type === AlarmType.Stay && INTELLIGENT_TYPE.stay
            ||
            type === AlarmType.ALL && null;
    }

    /**
     * 获取云存储事件图片列表
     *iotId    String    是    设备iotId
     beginTime    Long    是    查询开始时间戳(毫秒)
     endTime    Long    是    查询结束时间戳(毫秒)
     eventType    Integer    是    事件类型
     pageStart    Integer    是    分页起点
     pageSize    Integer    是    分页大小，即返回的行数
     */
    getAliFileList(iotId, beginTime, endTime,eventType,pageStart,pageSize) {
        const params = {
            Path: URL_ALI_CLOUD_QUERY,
            APIVersion: '2.1.2',
            ParamMap: {
                iotId: iotId,
                beginTime: beginTime,
                endTime: endTime,
                eventType: eventType,
                pageStart: pageStart,
                pageSize: pageSize,

            }
        };
        console.log(TAG + "downloadCloudFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
    /**
     * 获取云存储图片列表
     *iotId    String    是    设备iotId
     beginTime    Long    是    查询开始时间戳(毫秒)
     endTime    Long    是    查询结束时间戳(毫秒)
     eventType    Integer    是    事件类型
     pageStart    Integer    是    分页起点
     pageSize    Integer    是    分页大小，即返回的行数
     */
    getAliPictureList(iotId, beginTime, endTime,pageStart,pageSize) {
        const params = {
            Path: URL_ALI_CLOUD_QUERY_PICTURE,
            APIVersion: '2.1.1',
            ParamMap: {
                iotId: iotId,
                startTime: beginTime,
                endTime: endTime,
                type:0,//0（表示全部）；1（表示原图）；2（表示缩略图）。
                source: 1,//0（表示全部）；1（表示报警抓图）；2（表示主动抓图）；3（表示其他）。
                pageStart: pageStart,
                pageSize: pageSize,

            }
        };
        console.log(TAG + "downloadCloudPicture params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }

  /**
     * 获取云存视频
     *  req:
     iotId    String    是    设备iotId
     */
     getPlayList(iotId, beginTime, endTime, limit) {
       
        return cloudDeviceService.getPlayList(iotId, `${beginTime}`, `${endTime}`, `${limit}`);
    }

    // 下载
    downloadVideo(iotId, segmentId, publicKeyVersion, saveFilePath, saveFileName) {
       
        return cloudDeviceService.downloadVideo(iotId, segmentId, publicKeyVersion, saveFilePath, saveFileName);
    }

    /**
     * 根据事件获取云存视频
     *  req:
     iotId    String    是    设备iotId
     */
     getEventList(iotId, beginTime, endTime, limit,eventType) {
        return cloudDeviceService.getEventList(iotId, `${beginTime}`, `${endTime}`,eventType, `${limit}`);
    }

    /**
     * 月录像视频列表
     * @param iotId
     * @returns {*}
     */
    getMonthRecordList(iotId, month,timeZone,isRespObj = false) {
        const params = {
            Path: URL_MONTH_RECORD_CLOUD_QUERY,
            APIVersion: '2.0.1',
            ParamMap: {
                iotId: iotId,
                month: month,
                timeZone:timeZone

            }
        };
        console.log(TAG + "getMonthRecordList params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params,isRespObj);
    }
    /**
     * 根据时间范围查询云存的录像列表
     *iotId    String    是    设备iotId
     beginTime    Long    是    查询开始时间戳(毫秒)
     endTime    Long    是    查询结束时间戳(毫秒)
     eventType    Integer    是    事件类型
     pageStart    Integer    是    分页起点
     pageSize    Integer    是    分页大小，即返回的行数
     */
    getAliVideoList(iotId, beginTime, endTime,eventType,pageStart,pageSize,isRespObj = false) {
        const params = {
            Path: URL_ALI_RECORD_CLOUD_QUERY,
            APIVersion: '2.1.3',
            ParamMap: {
                iotId:iotId,
                streamType: 0,
                beginTime: beginTime,
                endTime: endTime,
                recordType: eventType,
                pageStart: pageStart,
                pageSize: pageSize,
               needSnapshot: true
            }
        };
        console.log(TAG + "getAliVideoList params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params,isRespObj);
    }

    /**
     * 获取云端下载视频链接
     * 需要转码MP4格式，需要轮询这个接口，转码是否结束
     * @param iotId
     * @param filename
     * @param isRespObj
     * @returns {*}
     */
    downloadAliVideoList(iotId, filename,isRespObj = false) {
        const params = {
            Path: URL_ALI_DOWNLOAD_VIDEO,
            APIVersion: '2.1.0',
            ParamMap: {
                iotId:iotId,
                fileName: filename,
            }
        };
        console.log(TAG + "download video params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params,isRespObj);
    }

    /**
     * 根据ID查询对应视频
     *iotId    String    是    设备iotId
     eventId    String    是   图片ID
     */
    getByEventId(iotId, eventId,isRespObj = false) {
        const params = {
            Path: URL_ALI_VOD_GET_EVENT_ID,
            APIVersion: '2.1.1',
            ParamMap: {
                iotId:iotId,
                eventId: eventId,
                needProlongOriginRecord: true,
            }
        };
        console.log(TAG + "getAliVideoList params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params,isRespObj);
    }
    /**
     * 根据name查询对应视频
     *iotId    String    是    设备iotId
     eventId    String    是   图片ID
     */
    getByEventName(iotId, fileName,isRespObj = false) {
        const params = {
            Path: URL_ALI_VOD_GET_EVENT_NAME,
            APIVersion: '2.0.0',
            ParamMap: {
                iotId:iotId,
                fileName: fileName,
            }
        };
        console.log(TAG + "getByEventName params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params,isRespObj);
    }
    /**
     * 测试查询推送列表
     *iotId    String    是    设备iotId
     eventId    String    是   图片ID
     */
    getPushMassage(iotId,isRespObj = false) {
        const params = {
            Path: URL_ALI_MASSAGE_PUSH_MASSAGE,
            APIVersion: '1.0.1',
            ParamMap: {
                iotId:iotId,
                nextToken:0,
                maxResults:40,
                type: "NOTICE",
                startCreateTime:1644163201000,
                endCreateTime:1644249599000,
                sortType:0,
                messageType: "device",
            }
        };
        console.log(TAG + "getPushMassage params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params,isRespObj);
    }
    /**
     * 测试根据ID查询图片
     *iotId    String    是    设备iotId
     eventId    String    是   图片ID
     */
    getPicByEvent(iotId,list,isRespObj = false) {
        const params = {
            Path: URL_ALI_PIC_BY_EVENT,
            APIVersion: '2.1.0',
            ParamMap: {
                iotId:iotId,
                eventIds:list
            }
        };
        console.log(TAG + "getPushMassage params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params,isRespObj);
    }
    /**
     * 删除阿里图片
     *iotId    String    是    设备iotId
     beginTime    Long    是    查询开始时间戳(毫秒)
     endTime    Long    是    查询结束时间戳(毫秒)
     eventType    Integer    是    事件类型
     pageStart    Integer    是    分页起点
     pageSize    Integer    是    分页大小，即返回的行数
     */
    deleteAliCloudImage(iotId, pictureIdList) {
        const params = {
            Path: URL_ALI_CLOUD_DELETE,
            APIVersion: '2.0.0',
            ParamMap: {
                iotId: iotId,
                pictureIdList: pictureIdList,
            }
        };
        console.log(TAG + "downloadCloudFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }

    /**
     * 下载回看转存视频文件
     *iotId    String    是    设备iotId
     beginTime    Long    是    查询开始时间戳(毫秒)
     endTime    Long    是    查询结束时间戳(毫秒)
     eventType    Integer    是    事件类型
     pageStart    Integer    是    分页起点
     pageSize    Integer    是    分页大小，即返回的行数
     */
    getEventAliCloud(iotId) {
        const params = {
            Path: URL_ALI_CLOUD_EVENT_GET,
            APIVersion: '1.0.4',
            ParamMap: {
                iotId: iotId,
            }
        };
        console.log(TAG + "downloadCloudFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
    /**
     * 设置业务事件的消息推送间隔
     *iotId    String    是    设备iotId
     eventType    Int    是   业务事件类型。目前仅支持侦测告警这类业务事件的设置。1表示为侦测告警。
     alarmType    Int    否    告警类型。业务事件类型为1侦测告警事件时，具体的告警枚举值：1（表示移动侦测）；2（表示声音侦测）；3（表示人形侦测）。
     eventInterval    Int    否    业务事件消息推送间隔。单位秒，默认120。
     */
    setAlarmEventInterval(iotId,eventInterval) {
        const params = {
            Path: URL_ALI_CLOUD_BIZEVENT_SET,
            APIVersion: '1.0.2',
            // Method: METHOD.POST,
            ParamMap: {
                iotId: iotId,
                eventType: 1,
                eventInterval:eventInterval,
            }
        };
        console.log(TAG + URL_ALI_CLOUD_BIZEVENT_SET +"downloadCloudFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }

    /**
     * 针对无人出现侦测设置时间间隔为1秒
     * 设置业务事件的消息推送间隔
     *iotId    String    是    设备iotId
     eventType    Int    是   业务事件类型。目前仅支持侦测告警这类业务事件的设置。1表示为侦测告警。
     alarmType    Int    否    告警类型。业务事件类型为1侦测告警事件时，具体的告警枚举值：1（表示移动侦测）；2（表示声音侦测）；3（表示人形侦测）。
     eventInterval    Int    否    业务事件消息推送间隔。单位秒，默认120。
     */
    setAlarmEventInterval2(iotId) {
        const params = {
            Path: URL_ALI_CLOUD_BIZEVENT_SET,
            APIVersion: '1.0.2',
            // Method: METHOD.POST,
            ParamMap: {
                iotId: iotId,
                eventType: 1,
                alarmType:12,
                eventInterval:0,
            }
        };
        console.log(TAG + URL_ALI_CLOUD_BIZEVENT_SET +"downloadCloudFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }

    /**
     * 设置业务事件的消息推送间隔
     *iotId    String    是    设备iotId
     eventType    Int    是   业务事件类型。目前仅支持侦测告警这类业务事件的设置。1表示为侦测告警。
     alarmType    Int    否    告警类型。业务事件类型为1侦测告警事件时，具体的告警枚举值：1（表示移动侦测）；2（表示声音侦测）；3（表示人形侦测）。
     eventInterval    Int    否    业务事件消息推送间隔。单位秒，默认120。
     */
    getAlarmEventInterval(iotId) {
        const params = {
            Path: URL_ALI_CLOUD_BIZEVENT_GET,
            APIVersion: '1.0.1',
            // Method: METHOD.POST,
            ParamMap: {
                iotId: iotId,
                eventType: 1,
            }
        };
        console.log(TAG + URL_ALI_CLOUD_BIZEVENT_GET +"downloadCloudFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
    /**
     * 删除阿里图片以及事件
     *iotId    String    是    设备iotId
     eventIds    Array    是    事件id数组
     */
    deleteAliEventCloudImage(iotId, eventIds) {
        const params = {
            Path: URL_ALI_CLOUD_EVENT_DELETE,
            APIVersion: '1.0.0',
            ParamMap: {
                iotId: iotId,
                eventIds: eventIds,
            }
        };
        console.log(TAG + "downloadCloudFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
    /**
     * 删除阿里图片
     *iotId    String    是    设备iotId
     eventIds    Array    是    事件id数组
     */
    deleteAliPictureCloudImage(iotId, pictureIds) {
        const params = {
            Path: URL_ALI_CLOUD_PICTURE_DELETE,
            APIVersion: '2.0.0',
            ParamMap: {
                iotId: iotId,
                pictureIdList: pictureIds,
            }
        };
        console.log(TAG + "deleteCloudPictureFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
   /**
     * 根据segmentId批量删除文件
     *iotId    String    是    设备iotId
     eventIds    Array    是    事件id数组
     */
     deleteFileBySegment(iotId, segmentIdList) {
        const params = {
            Path: URL_ALI_DEL_FILE_BY_SEGMENT_IDS,
            ParamMap: {
                iotId,
                segmentIdList,
                productId: LetDevice.model,
            },
            Method: 'POST',
        };
        console.log(TAG + "deleteFileBySegment params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
    /**
     * 根据segmentId删除单个文件
     *iotId    String    是    设备iotId
     eventIds    Array    是    事件id数组
     */
     deleteSingleFileBySegment(iotId, segmentId) {
        const params = {
            Path: URL_ALI_DEL_FILE_BY_SEGMENT_ID,
            ParamMap: {
                iotId,
                segmentId,
                productId: LetDevice.model,
            },
            Method: 'POST',
        };
        console.log(TAG + "deleteFileBySegment params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
    /* 根据iotid获取事件类型 */
    getEventTypeListByIotId(iotId){
        const params = {
            Path: URL_ALI_GET_EVENT_TYPE_LIST,
            ParamMap: {
                iotId,
                productId: LetDevice.model,
            },
            Method: 'GET',
        };
        console.log(TAG + "getEventTypeListByIotId params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }

  /* 获取看家数据 */
  getEventDataListByParams(iotId, beginTime, endTime, eventType, limit, lensNumber){
    const params = {
      Path: URL_ALI_GET_EVENT_LIST,
      ParamMap: {
        iotId,
        beginTime: beginTime,
        endTime: endTime,
        eventType: eventType,
        limit: limit,
        lensNumber: lensNumber,
      },
      Method: 'GET',
    };
    console.log(TAG + "getEventDataListByParams params -> " + JSON.stringify(params));
    return LetIMIIotRequest.sendIotServerRequest(params);
  }
    /**
     * 20220727@baiyihao
     * 删除阿里录像视频
     * iotId           String    是    设备iotId
     * fileNameList    Array     是    录像文件名列表 一次删除上限为100条
     * code    Int
     * 接口返回码。200表示成功。
     *
     * message    String
     * 调用失败时，返回的出错信息。
     *
     * localizedMsg    String
     * 本地语言的错误消息。
     *
     * data     JSON    ->  deletedCount
     * 响应结果。 Int     ->  成功删除的数量。
     */
    deleteAliVideoCloud(iotId, fileNameList) {
        const params = {
            Path: URL_ALI_CLOUD_VIDEO_DELETE,
            APIVersion: '2.0.0',
            ParamMap: {
                iotId: iotId,
                fileNameList: fileNameList,
            }
        };
        console.log(TAG + "deleteCloudVideoFile params -> " + JSON.stringify(params));
        return LetIMIIotRequest.sendIotServerRequest(params);
    }
}


export const aliAlarmEventCloudApi = ALiAlarmEventCloudApi.getInstance();
