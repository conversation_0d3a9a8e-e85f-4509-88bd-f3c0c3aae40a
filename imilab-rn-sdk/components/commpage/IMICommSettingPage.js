/*
 * 作者：sunhongda
 * 文件：IMICommSettingPage.js  模块：imihome_rn  项目：imihome_rn
 * 上海创米科技有限公司  http://www.imilab.com/
 * Copyright (c) 2020
 */

import React from 'react';

import {StyleSheet, View, Text, TextInput, StatusBar, ScrollView, Dimensions, Keyboard} from 'react-native';

import {imiThemeManager, showToast, MessageDialog} from '../../../imilab-design-ui';
import {stringsTo, locales} from '../../../globalization/Localize';
import ListItem from '../../../imilab-design-ui/src/widgets/settingUI/ListItem';
import {IMIGotoPage, LetDevice, LetIMIIotRequest} from '../../index';
import BaseDeviceComponent from '../BaseDeviceComponent';
import NavigationBar from '../../../imi-rn-commonView/NavigationBar/NavigationBar';
import project from '../../../projects/com.chuangmi.camera.IPC031/project';
import {IMINativeLifeCycleEvent} from '../../native/local-kit/IMINativeLifeCycle';
import {XText, XView} from 'react-native-easy-app';
import {isAndroid, isIphoneXSeries} from '../../utils/Utils';
import {METHOD} from '../../native/iot-kit/IMIIotRequest';

const {width} = Dimensions.get('window');
/**
 * 设备通用设置界面 适用于所有设备的设置界面
 *
 *  默认显示条目
 *
 *   - 通用设置
 *   - 常见问题
 *
 */

// route 参数:

/*  需要渲染在通用设置上方 调用者自定义item 数组*/

// diyRenderItemArray: PropTypes.array,

/* 是否显示删除设备btn*/

// showDelDev: PropTypes.bool,

/*  使用默认item 样式 只能传入  [{文案：点击方法 }] */

// defaultStyleRenderItemArray: PropTypes.array,

// String format = MessageFormat.format(FAQ_URL, mModel, Locale.getDefault().getLanguage());
const tag = 'IMICommSettingPage';
/**
 * 通用设置页面
 */
let CHECK_UPDATE_URL = 'link://app/pages/toDeviceUpgradePage'; //检查更新
let DEVICE_SHARE_URL = 'link://app/pages/toDeviceSharePage'; //共享
let COMMON_HELP_URL = 'link://feedback/pages/CommonHelp'; //帮助与反馈

export default class IMICommSettingPage extends BaseDeviceComponent {
  static propTypes = {};

  constructor(props, context) {
    super(props, context);
    this.state = {
      keyBoardHeight: 0, //软键盘高度
      modalVisible: false, //弹出聊天输入
      modalDeleteVisible: false, //是否删除
      deviceName: LetDevice.devNickName,
      showCircle: false, //是否显示升级
    };
    this.inputData = ''; //输入框内容
    this._onChangeText = this._onChangeText.bind(this); //监听输入变化

    let languageStr = locales[0]?.languageCode;
    let tempStr = 'zh';
    if (languageStr.indexOf(tempStr) != -1) {
      //包含zh-hans等简体中文
      if (locales[0].languageTag.toLowerCase().indexOf('zh-tw') != -1) {
        //tag Android:zh-tw iOS:zh-tw-Hant-CN
        tempStr = 'zh-tw';
      }
      this.FAQ_URL = `https://api.imilab.com/service/app/faq/index?model=${LetDevice.model}&locale=${tempStr}`;
    } else {
      this.FAQ_URL = `https://api.imilab.com/service/app/faq/index?model=${LetDevice.model}&locale=${locales[0]?.languageCode}`;
    }
    // this.FAQ_URL = `https://api.imilab.com/service/app/faq/index?model=${LetDevice.model}&locale=${locales[0]?.languageCode}`;
    //03x只要这句，为何？ this.FAQ_URL = `https://api.imilab.com/service/app/faq/index?model=${LetDevice.model}&locale=en`;

    console.log(tag);
  }

  componentWillUnmount() {
    this._onResumeListener && this._onResumeListener.remove();
    this.keyboardDidShowListener.remove();
    this.keyboardDidHideListener.remove();
    this._onResumeListener && this._onResumeListener.remove();
  }

  componentDidMount() {
    
    this.keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', this._keyboardDidShow.bind(this));
    this.keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', this._keyboardDidHide.bind(this));
    this._onResumeListener = IMINativeLifeCycleEvent.onResumeListener.addListener(() => {
      //app页面回到插件
      NavigationBar.setBarStyle('dark-content'); // 修改从云存购买界面返回状态栏显示白色字体问题
      // StatusBar.setBarStyle('dark-content');//
    });
    this.getFirmwareInfo();
  }

  render() {
    // console.log(" IMICommSettingPage   canGoBack  " + navigation.canGoBack() + '  FAQ_URL.format("zh", "cn") ' + this.FAQ_URL);

    let {
      diyRenderItemArray = null,
      defaultStyleRenderItemArray = null,
      showDelDev = false,
      topTitle = null,
      commSettingConfig = null,
      deviceInOnline = LetDevice.isOnline,
    } = this.props.route.params ? this.props.route.params : {};

    let settingConfig = commSettingConfig ? commSettingConfig : {};

    return (
      <View style={styles.container}>
        <NavigationBar
          type={NavigationBar.TYPE.LIGHT}
          backgroundColor={'transparent'}
          title={topTitle ? topTitle : stringsTo('commTitleSettingText')}
          onLongPress={() => showToast('V' + project.versionCode)}
          left={[
            {
              key: NavigationBar.ICON.BACK,
              onPress: _ => this.pageGoBack(),
            },
          ]}
        />

        {this.renderBottomDeleteBtn()}

        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{backgroundColor: '#FAFAFA', marginBottom: isIphoneXSeries() ? 85 : 65}}>
          <Text
            style={{
              color: '#00000080',
              fontSize: 12,
              paddingLeft: 15,
              paddingTop: 5,
              width: '100%',
              backgroundColor: '#ffffff',
            }}>
            {stringsTo('feature_set')}
          </Text>

          {
            // 调用者自定义view 只负责渲染不负责其他任何业务逻辑，包括点击事件等操作 样式等
            diyRenderItemArray && diyRenderItemArray.map((Item, index) => <Item key={index} />)
          }

          {
            // 调用者自定义view 只负责渲染不负责其他任何业务逻辑，包括点击事件等操作 样式等
            defaultStyleRenderItemArray &&
              defaultStyleRenderItemArray.map((item, index) => (
                <ListItem
                  key={index}
                  title={item.title[0]}
                  disabled={(item.dependentOnLine || false) && !deviceInOnline}
                  onPress={() => {
                    item.onPress();
                  }}
                />
              ))
          }

          <View style={{backgroundColor: '#FAFAFA', height: 14, width: '100%'}} />
          <Text
            style={{
              color: '#00000080',
              fontSize: 12,
              paddingLeft: 15,
              paddingTop: 5,
              width: '100%',
              backgroundColor: '#ffffff',
            }}>
            {stringsTo('comm_setting_title')}
          </Text>

          <ListItem
            title={stringsTo('device_name')}
            value={this.state.deviceName}
            onPress={() => {
              this.setState({
                modalVisible: true,
              });
            }}
            accessibilityLabel={'device_name'}
          />
          <ListItem
            title={stringsTo('check_update')}
            showCircle={this.state.showCircle}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }
              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, CHECK_UPDATE_URL);
            }}
            accessibilityLabel={'check_update'}
          />
          <ListItem
            title={stringsTo('shared_setting')}
            onPress={() => {
              if (LetDevice.isShareUser) {
                showToast(stringsTo('shareUser_tip'));
                return;
              }
              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, DEVICE_SHARE_URL);
            }}
            accessibilityLabel={'shared_setting'}
          />
          <ListItem
            title={stringsTo('help_callback')}
            onPress={() => {
              // this.props.navigation.push('WebViewPage', {url: this.FAQ_URL})
              //this.props.navigation.push('WebViewPage', {url: "https://api.imilab.com/service/app/faq/index?model=${LetDevice.model}&locale=en"})
              IMIGotoPage.startNativeDevPageWithRouter(LetDevice.deviceID, COMMON_HELP_URL);
            }}
            accessibilityLabel={'help_callback'}
          />

          <MessageDialog
            title={stringsTo('confirm_deletion_device')}
            visible={this.state.modalDeleteVisible}
            canDismiss={false}
            buttons={[
              {
                text: stringsTo('cancel'),
                accessibilityLabel: 'canceldeleteDevice',
                callback: _ => {
                  this.setState({modalDeleteVisible: false});
                },
              },
              {
                text: stringsTo('ok_button'),
                accessibilityLabel: 'okdeleteDevice',
                callback: _ => {
                  this.setState({modalDeleteVisible: false});
                  //  showLoading(stringsTo('preset_sleep_set'),false);
                  this.DeleteDevice();
                },
              },
            ]}
          />
        </ScrollView>

        {this._updateDeviceNameDialog()}
      </View>
    );
  }

  renderBottomDeleteBtn() {
    return (
      <XView
        style={{
          position: 'absolute',
          height: 48,
          alignItems: 'center',
          backgroundColor: '#EEEEEE',
          justifyContent: 'center',
          left: 20,
          right: 20,
          bottom: isIphoneXSeries() ? 23 : 14,
          borderRadius: 6.67,
        }}
        onPress={() => {
          this.setState({
            modalDeleteVisible: true,
          });
        }}>
        <XText
          style={{fontSize: 15, textAlign: 'center', color: '#EB614B', fontWeight: 'bold'}}
          text={stringsTo('comm_setting_remove_device')}
          accessibilityLabel={'comm_setting_remove_device'}
        />
      </XView>
    );
  }

  /**
   * 修改设备名称的弹框
   * @private
   */
  _updateDeviceNameDialog() {
    return (
      <View>
        <MessageDialog
          title={stringsTo('update_device_name')}
          visible={this.state.modalVisible}
          style={{bottom: isAndroid() ? 0 : this.state.keyBoardHeight}}
          canDismiss={true}
          onDismiss={() => {
            this.setState({modalVisible: false});
          }}
          buttons={[
            {
              text: stringsTo('cancel'),
              accessibilityLabel: 'cancelUpdateDevice',
              callback: _ => {
                this.setState({modalVisible: false});
              },
            },
            {
              text: stringsTo('ok_button'),
              accessibilityLabel: 'okUpdateDevice',
              callback: _ => {
                this.setState({modalVisible: false});
                this.updateDeviceName();
              },
            },
          ]}>
          <View
            style={{
              width: '100%',
              alignItems: 'center',
              justifyContent: 'center',
              flexDirection: 'column',
              paddingVertical: 10,
            }}>
            <TextInput
              style={{
                fontSize: 15,
                borderRadius: 7,
                height: 50,
                width: width * 0.8,
                backgroundColor: '#F1F1F1',
                paddingLeft: 20,
              }}
              placeholderTextColor={'#B2B2B2'}
              placeholder={this.state.deviceName}
              returnKeyType="done"
              clearButtonMode="while-editing"
              enablesReturnKeyAutomatically={true}
              editable={true}
              autoFocus={true}
              maxLength={100}
              keyboardType="default"
              onChangeText={this._onChangeText}
            />
          </View>
        </MessageDialog>
      </View>
    );
  }

  DeleteDevice() {
    //分享账号调用此接口，是删除分享设备，不会使设备解绑
    const params = {
      Path: 'api/app_device/unbind',
      Method: METHOD.POST,
      ParamMap: [
        {
          iotId: LetDevice.deviceID,
          isAli: true,
        },
      ],
    };
    console.log('传值params--', params);
    LetIMIIotRequest.sendUserServerRequest(params)
      .then(data => {
        IMIGotoPage.exit();
      })
      .catch(error => {});
  }

  _onChangeText(inputData) {
    //把获取到的内容，设置给showValue
    this.inputData = inputData;
  }

  _keyboardDidShow(e) {
    this.setState({
      keyBoardHeight: e.endCoordinates.height,
    });
  }

  _keyboardDidHide() {
    this.setState({
      keyBoardHeight: 0,
      // modalVisible:false
    });
  }

  onRequestClose() {
    this.setState({
      modalVisible: false,
    });
  }

  updateDeviceName() {
    if (this.inputData.length == 0) {
      showToast(stringsTo('input_name'));
      return;
    }
    const params = {
      Path: 'api/app_device/nickname',
      Method: 'PUT',
      ParamMap: {
        iotId: LetDevice.deviceID,
        isAli: true,
        nickName: this.inputData ? this.inputData : LetDevice.devNickName,
      },
    };
    LetIMIIotRequest.sendUserServerRequest(params)
      .then(data => {
        LetDevice.setDevNickName(this.inputData);
        this.setState({
          deviceName: this.inputData,
        });
        this.inputData = '';
      })
      .catch(error => {
        this.inputData = '';
      });
  }

  getFirmwareInfo() {
    const params = {
      Path: '/thing/ota/info/queryByUser',
      APIVersion: '1.0.2',
      ParamMap: {
        iotId: LetDevice.deviceID,
        productKey: LetDevice.model,
      },
    };
    LetIMIIotRequest.sendIotServerRequest(params)
      .then(data => {
        //console.warn(' sendIotServerRequest  then->' + data)
        data = JSON.parse(data);
        let versionOnServer = data.version;
        let currentVersion = data.currentVersion;
        let log = data.desc;

        /*let versionOnServer = '031201_1.1.10_0202';
            let currentVersion = '031201_1.1.8_0129';*/

        if (this.compareVersion(currentVersion, versionOnServer)) {
          console.warn(
            ' sendIotServerRequest  then->' + versionOnServer,
            currentVersion,
            '是否有新版本----' + this.compareVersion(currentVersion, versionOnServer),
          );
          this.setState({
            showCircle: true,
          });
        }
      })
      .catch(error => {
        // console.warn('sendIotServerRequest error ' + error)
      });
  }

  compareVersion(curVersion, newVersion) {
    if (this.isEmpty(newVersion) || this.isEmpty(curVersion)) {
      return false;
    } else if (newVersion.equals(curVersion)) {
      return false;
    }

    let newSpilUnderLine = newVersion.split('_');
    let curSpilUnderLine = curVersion.split('_');
    if (newSpilUnderLine.length < 3 || newSpilUnderLine.length < 3) {
      return false;
    }
    let newSpilComma = newSpilUnderLine[1].split('.');
    let curSpilComma = curSpilUnderLine[1].split('.');
    if (newSpilComma.length < 3 || curSpilComma.length < 3) {
      return false;
    }

    let newNum =
      parseInt(newSpilComma[0]) * 1000 +
      parseInt(newSpilComma[1]) * 300 +
      parseInt(newSpilComma[2]) * 10 +
      parseInt(newSpilUnderLine[2]);
    let curNum =
      parseInt(curSpilComma[0]) * 1000 +
      parseInt(curSpilComma[1]) * 300 +
      parseInt(curSpilComma[2]) * 10 +
      parseInt(curSpilUnderLine[2]);
    if (newNum > curNum) {
      return true;
    } else {
      return false;
    }
  }

  isEmpty(obj) {
    if (typeof obj === 'undefined' || obj == null || obj === '') {
      return true;
    } else {
      return false;
    }
  }
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: imiThemeManager.theme.pageBg,
  },
});
