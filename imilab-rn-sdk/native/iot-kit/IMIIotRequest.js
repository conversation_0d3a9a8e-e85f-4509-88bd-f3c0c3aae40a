import {NativeModules} from "react-native";
import {isObj, isString} from "../../utils/TypeUtils";
import {LetDevice} from '../../../imilab-rn-sdk'

// const rtcIMIIotRequest = NativeModules.IMIIotRequest;
const rtcIMIIotRequest = NativeModules.AKRCTRequest;


export const METHOD = {
    GET: 'GET',
    POST: 'POST',
    DELETE: 'DELETE'
};
Object.freeze(METHOD);


/**
 * <AUTHOR>
 * IMIIotRequestModule   基础功能网络请求
 * 注意：- host url TOKEN 等参数 native 进行适配
 *      - 由于原生进行转换map 时 会将int long 等类型均识别为float 所有参数( ParamMap )均用string 代替
 */
export default class IMIIotRequest {
    constructor() {
        // this.instance = null;
    }

    static getInstance() {
        if (!this.instance) {
            this.instance = new IMIIotRequest();
        }
        return this.instance;
    }

    /**
     * 发送一个基础网络请求  （目前是访问创米用户服务器）
     * - 根据native App Debug /release 来决定访问的是正式服还是测试服
     *
     * @param  {Object}   paramMap 参数集合:
     * {
     * Method:default =GET/POST  默认GET 方式 不填写此字段为get请求
     * Path:xxx/xx/xx        > 访问路径
     * ParamMap:{}       > 参数集合Map
     * }
     *  @param isRespObj 返回  Promise resolve 是否格式化为对象
     *  @return  Promise    -> {String}  resolve : "" , {Object}reject:{"code":-1,""message":"fail"}
     */
    sendUserServerRequest(paramMap, isRespObj = false, direct = false) {
        if (paramMap.Path === '/v1.0/imilab-01/device/control/property/getByCached' && !direct) {
            return LetDevice.getSingleProperty(String(paramMap.ParamMap.thingId)).then(resp => {
                return resp;
             })
        }

        console.log('  defineProperty  sendUserServerRequest :');
        if (paramMap.Method === undefined) {
            console.log('  defineProperty   :');
            Object.assign(paramMap, {
                Method: METHOD.GET
            });
        }

        this.toSetParamMap(paramMap);

        console.log('sendUserServerRequest paramMap :' + JSON.stringify(paramMap) + " paramMap.method " + paramMap.Method);
        //@param  {Object}   paramMap 参数集合
       return  rtcIMIIotRequest.sendCloudRequest(paramMap).then(resp => {
           console.log('sendUserServerRequest resp   :', resp);
           if (isRespObj) {
                return JSON.parse(resp);
            } else {
                return resp;
            }
        });
    }
    sendUserServerRequestNoJson(paramMap, isRespObj = false) {

        console.log('sendUserServerRequest paramMap :' + (paramMap) + " paramMap.method " + paramMap.Method);
        //@param  {Object}   paramMap 参数集合
       return  rtcIMIIotRequest.sendCloudRequest(paramMap).then(resp => {
           console.log('sendUserServerRequest resp   :', resp);
           if (isRespObj) {
                return JSON.parse(resp);
            } else {
                return resp;
            }
        });
    }

    /***
     * 访问创米OA服务器
     * @param paramMap {Object}
     *  @param isRespObj 返回  Promise resolve 是否格式化为对象
     *  @return  Promise    -> {String}  resolve : "" ,{Object} reject:{"code":-1,""message":"fail"}
     */
    sendOAServerRequest(paramMap, isRespObj = false) {
        if (paramMap.Method === undefined) {
            console.log('  defineProperty   :');
            Object.assign(paramMap, {
                Method: METHOD.GET
            });
        }
        this.toSetParamMap(paramMap);

        console.log('sendOAServerRequest paramMap :' + JSON.stringify(paramMap) + " paramMap.method " + paramMap.Method);

        return rtcIMIIotRequest.sendOAServerRequest(paramMap).then(resp => {
            if (isRespObj) {
                return JSON.parse(resp);
            } else {
                return resp;
            }
        });
    }

    /***
     * 携带创米token 请求 host 由自己来拼入
     * @param paramMap {Object}
     *  @param isRespObj 返回  Promise resolve 是否格式化为对象
     *  @return  Promise    -> {String}  resolve : "" , {Object}reject:{"code":-1,""message":"fail"}
     */
    sendServerRequest(paramMap, isRespObj = false) {
        if (paramMap.Method === undefined) {
            console.log('  defineProperty   :');
            Object.assign(paramMap, {
                Method: METHOD.GET
            });
        }
        if (paramMap.Host === undefined) {
            console.error("please input host url");
            return
        }
        this.toSetParamMap(paramMap);

        console.log('sendOAServerRequest paramMap :' + JSON.stringify(paramMap) + " paramMap.method " + paramMap.Method);
        return rtcIMIIotRequest.sendOAServerRequest(paramMap).then(resp => {
            if (isRespObj) {
                return JSON.parse(resp);
            } else {
                return resp;
            }
        });
    }


    /**
     * iot 服务器请求（目前访问的阿里云）
     *  @param paramMap {Object} 参数集合
     * {
     * Path:xxx/xx/xx    > 访问路径
     * ParamMap:{}       > 参数集合Map
     * }
     *  @param isRespObj 返回  Promise resolve 是否格式化为对象
     *  @return  Promise    -> {String}  resolve : "" , {Object} reject:{"code":-1,""message":"fail"}
     */
    sendIotServerRequest(paramMap, isRespObj = false) {
        this.toSetParamMap(paramMap);

        return rtcIMIIotRequest.sendCloudRequest(paramMap).then(resp => {
             
            if (isRespObj) {
                return JSON.parse(resp);
            } else {
                return resp;
            }
        });
    }

    /**
     *   转换成string 防止原生将 int long 等类型识别为float
     * @param paramMap {Object}
     *  @return  Promise     -> resolve :{Object} ,{Object} reject:{"code":-1,""message":"fail"}
     */
    toSetParamMap(paramMap) {
        console.log('toSetParamMap ', paramMap);

        let rawParams = paramMap && paramMap['ParamMap'];
        rawParams && Object.assign(paramMap, {
            ParamMap: JSON.stringify(rawParams)
        });
    }

    /**
     * uploadFileToIMICloud 上传文件到创米云
     * @param url
     * @param filePath 包含文件名的文件路径
     * @param fileName 要与指定数据关联的文件名。 这个参数不能空
     * @param mimeType 指定数据的 MIME 类型。这个参数不能空 （例如，JPEG 图像的 MIME 类型是 image/jpeg。）
     * 有关有效 MIME 类型的列表，请参阅 http://www.iana.org/assignments/media-types/
     * by-YM-2021/6/11
     * SDK-10001
     */
    static uploadFileToIMICloud(url, filePath, fileName, mimeType) {
        return rtcIMIIotRequest.uploadFileToIMICloud(url, filePath, fileName, mimeType);
    }
}

/**
 * @export IMIIotRequest
 */
export const LetIMIIotRequest = IMIIotRequest.getInstance();
